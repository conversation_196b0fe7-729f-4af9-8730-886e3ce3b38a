package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.exception.TempSkipException
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.utils.sender.HttpSender
import org.apache.http.impl.client.CloseableHttpClient
import org.jsoup.Jsoup
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotConstant_2011

/**
 * 太平洋精灵 - 代理点、终端及经办人选择请求模板
 */
def autoTask = autoTask as AutoTask
def util = new robot_2011_util()
def tempValues = tempValues as Map
def configs = config as Map
def httpClient = autoTask.httpClient as CloseableHttpClient
def header = [
        'Host'            : 'issue.cpic.com.cn',
        'macAddress'      : null,
        'Content-Type'    : 'application/json;charset=UTF-8',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin'          : 'https://issue.cpic.com.cn',
        'Referer'         : 'https://issue.cpic.com.cn/ecar/view/portal/page/common/partnerselect.html'
]

if (util.checkCookie(autoTask)) {
    initConfig(httpClient, header, tempValues)
    throw new TempSkipException(1, '已登陆，无需执行代理点、终端及经办人选择请求模板')
}

def partnerselectURL = 'https://issue.cpic.com.cn/ecar/view/portal/page/common/partnerselect.html'
def login_header = [
        'Referer': 'https://issue.cpic.com.cn/ecar/view/portal/page/common/login.html'
]
def response = util.doGet(httpClient, partnerselectURL, login_header)
def doc = Jsoup.parse(response as String)
def flag = doc.select('body > div.login-row > div:nth-child(2) > div > div > div > h3').text() == '代理点、终端及经办人选择'
if (!flag) {
    throw new InsReturnException(InsReturnException.Others, '登录已失效，请重新登录')
}

JSONObject queryConfigsResult = initConfig(httpClient, header, tempValues)

if (tempValues.needSM4Flag) {
    header['Content-Type'] = 'text/plain'
}
def branchCode = queryConfigsResult.getString('branchCode')

def queryFastLoginInfoURL = 'https://issue.cpic.com.cn/ecar/auth/queryFastLoginInfo'
def param = [
        'meta'  : [:],
        'redata': [:]
]
def reqBody = JSON.toJSONString(param)
reqBody = Robot2011Util.genBody(tempValues, reqBody)
def queryFastLoginInfoResponseStr = HttpSender.doPostWithRetry(3, httpClient, true,
        queryFastLoginInfoURL, reqBody, null,
        header, "UTF-8", null, null
)
queryFastLoginInfoResponse = Robot2011Util.decodeBody2JsonObject(tempValues, queryFastLoginInfoResponseStr)
def queryFastLoginInfoResult = queryFastLoginInfoResponse.getJSONObject('result')
def fastLogin = queryFastLoginInfoResult.getBoolean('fastLogin')
def partnerCode
def agentCode
if (fastLogin) {
    partnerCode = queryFastLoginInfoResult.getString('partnerCode')
    agentCode = queryFastLoginInfoResult.getString('agentCode')
} else {
    partnerCode = configs['partner_code']
    agentCode = configs['agent_code']
}

def userAuthVos = queryFastLoginInfoResult.getJSONArray('userAuthVos')
def userAuthVo = userAuthVos.find({ userAuthVo ->
    userAuthVo['branchCode'] == branchCode && userAuthVo['partnerCode'] == partnerCode
}) as JSONObject
if (!userAuthVo) {
    throw new InsReturnException(InsReturnException.Others, 'partnerCode 配置错误')
}

def agentAuthVos = userAuthVo.getJSONArray('agentAuthVos')
def agentAuthVo = agentAuthVos.find({ agentAuthVo ->
    agentAuthVo['agentCode'] = agentCode
})
if (!agentAuthVo) {
    throw new InsReturnException(InsReturnException.Others, 'agentCode 配置错误')
}

def agencyVo = userAuthVo.getJSONObject('agencyVo')
def agencyCode = agencyVo.getString('agencyCode')
//def accessToken = userAuthVo.getString('accessToken')
def pid = userAuthVo.getString("pid")
def j_username = queryFastLoginInfoResult.getString('userCode')

param = [
        'meta'  : [:],
        'redata': ['agencyCode': agencyCode]
]
reqBody = JSON.toJSONString(param)
reqBody = Robot2011Util.genBody(tempValues, reqBody)
HttpSender.doPostWithRetry(3, httpClient, true,
        RobotConstant_2011.URL.QUERY_AGENCY_BY_CODE, reqBody, null,
        header, "UTF-8", null, null
)

param = [
        'pid'         : pid,
        'partner_code': partnerCode,
        'j_username'  : j_username,
        'agent_code'  : agentCode,
        'mac_address' : configs['mac_address'] ?: ''
]
header['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'
tempValues['ReqURL'] = 'https://issue.cpic.com.cn/ecar/j_spring_security_check'
reqHeaderss = header
postParameters = param

private static JSONObject initConfig(CloseableHttpClient httpClient, LinkedHashMap<String, String> header, Map tempValues) {
    def param = [
            'meta'  : [:],
            'redata': [
                    'pageCode': 'partner_select'
            ]
    ]
    def resp = HttpSender.doPost(httpClient, true, RobotConstant_2011.URL.QUERY_CONFIGS, JSON.toJSONString(param), null, header, 'UTF-8', null, '')
    def queryConfigsResponse = JSON.parseObject(resp)
    def queryConfigsResult = queryConfigsResponse.getJSONObject('result')

    tempValues.needSM4Flag = queryConfigsResponse.containsKey('b')
    if (tempValues.needSM4Flag) {
        tempValues.put('sm4Key', Robot2011Util.rsaDecode(queryConfigsResponse.getString('b')))
    }
    return queryConfigsResult
}