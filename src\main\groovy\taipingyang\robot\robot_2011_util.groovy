package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.model.RuleInfoKey
import com.cheche365.bc.model.car.*
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.tools.BigDecimalUtil
import com.cheche365.bc.tools.DateCalcUtil
import com.cheche365.bc.tools.PhoneUtil
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.PlatformUtil
import com.cheche365.bc.utils.RedisUtil
import com.cheche365.bc.utils.RuleUtil
import com.cheche365.bc.utils.dama.Dama2Web
import com.cheche365.bc.utils.encrypt.AesDesEncryption
import com.cheche365.bc.utils.encrypt.EncryptEnum
import com.cheche365.bc.utils.sender.HttpSender
import org.apache.http.impl.client.BasicCookieStore
import org.apache.http.impl.client.CloseableHttpClient
import org.apache.http.impl.client.HttpClients
import org.apache.http.impl.cookie.BasicClientCookie
import org.jsoup.Jsoup
import org.slf4j.LoggerFactory
import taipingyang.robot.module.Robot2011NonMotor
import taipingyang.robot.module.Robot2011Util

import java.math.RoundingMode
import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * 品牌: carBrandName, 如 东风日产
 *
 * 车系: carModelFamilyName, 如 轩逸
 *
 * 车型: carModelStandardName, 如 东风日产DFL7160VALH1轿车
 *
 * 初登日期: registDate
 * 新车购置价: price
 * 新车购置价(含税): taxPrice
 *
 * 使用性质: carProperty
 *
 * 是否新车: isNew, string型, "0"表示否, "1"表示是
 * (忽略)是否是新能源汽车: electricCar, string型, "0"表示否, "1"表示是
 */
def do8076BackPlatformInfo(AutoTask autoTask) {
    //获取中间变量中各开发存储的平台信息.
    Map platformInfo = (Map) autoTask.getTempValues().get(PlatformKey.platformBack);
    Map queryPlatformInfoResultObj = autoTask.tempValues.queryPlatformInfoResultObj as Map
    def queryPlatformInfoResultFlag = autoTask.tempValues.queryPlatformInfoResultFlag
    List policiesArray = platformInfo.bizPolicies as List
    def loyalty = ""
    if (policiesArray && policiesArray.size() > 0) { // ("0":新保" "1":续保 "2":转保)
        String insCorpName = policiesArray[0]?.insCorpName
        if (insCorpName) {
            if (insCorpName.contains("太平洋")) {
                loyalty = "1"
            } else {
                loyalty = "2"
            }
        }
    }
    if (queryPlatformInfoResultFlag) {
        doPolicies(queryPlatformInfoResultObj, "lastyearBusiStartDat", "lastyearBusiEndDat", platformInfo, "bizPolicies")
        doPolicies(queryPlatformInfoResultObj, "lastyearStartDate", "lastyearEndDate", platformInfo, "efcPolicies")
    }

    Map definition = platformInfo.definition as Map ?: [:]
    doDefinitionPlatformKey(definition, PlatformKey.noClaimDiscountCoefficient, platformInfo)
    doDefinitionPlatformKey(definition, PlatformKey.selfRate, platformInfo)
//    definition.put(PlatformKey.application_loyalty, loyalty)
    platformInfo.definition = definition
}

def doDefinitionPlatformKey(Map definition, String key, Map platformInfo) {
    def noClaimDiscountCoefficient = platformInfo.get(key)
    if (noClaimDiscountCoefficient) {
        definition.put(key, noClaimDiscountCoefficient)
    }
}

def doPolicies(Map queryPlatformInfoResultObj, String lastYearStartDatType, String lastYearEndDatType, Map platformInfo, String policiesType) {
    def policiesMap = [
            policyStartTime: queryPlatformInfoResultObj?.result?.platformInfoVo?."$lastYearStartDatType" ?: "",
            policyEndTime  : queryPlatformInfoResultObj?.result?.platformInfoVo?."$lastYearEndDatType" ?: ""
    ]
    List policiesArray = platformInfo."$policiesType" as List
    if (policiesArray == null || policiesArray.size() == 0) {
        policiesArray = []
        policiesArray.add(policiesMap)
    } else {
        (policiesArray[0] as Map) << policiesMap
    }
}

def Map treMap(autoTask, map, type) {
    if (autoTask.configs.areaComCode.toString().contains("四川")) {
        Enquiry entity = (Enquiry) autoTask.getTaskEntity()
        if (type == "SUBSTITUTEDRIVINGSPECIALCLAUSE") {
            map.numOrGrade = "30"
        }
        if (type == "VEHICLESAFETYDETECTIONSPECIALCLAUSE") {
            if (entity.order.carInfo.carModelName.toString().contains("插电") || entity.order.carInfo.carModelName.toString().contains("电动")) {
                map.numOrGrade = "D"
            } else {
                map.numOrGrade = "C"
            }
        }
    }
    return map
}

def getCodeVBusiInsurance(AutoTask autoTask) {
    def codeVBusiInsuranceUrl = "https://issue.cpic.com.cn/ecar/partnerTerminal/getCodeViewInfos"
    def codeVBusiInsuranceMap = [
            "meta"  : [
                    "pageNo"  : 1,
                    "pageSize": 5
            ],
            "redata": [
                    "type": "2",
                    "code": autoTask?.configs?.busiInsuranceCode,
                    "name": ""
            ]
    ]
    try {
        def getCodeViewInfosResult = HttpSender.doPostWithRetry(5, autoTask?.httpClient as CloseableHttpClient, true, codeVBusiInsuranceUrl, Robot2011Util.genBody(autoTask.tempValues, JSON.toJSONString(codeVBusiInsuranceMap)), null, Robot2011Util.getDefaultHead(autoTask.tempValues, autoTask.configs), "UTF-8", null, "");
        getCodeViewInfosResult = Robot2011Util.decodeBody(autoTask.tempValues, getCodeViewInfosResult)
        if (!getCodeViewInfosResult) {
            bInsuranceException(autoTask?.configs?.busiInsuranceCode as String)
        } else {
            def codeViewInfoJson = JSON.parseObject(getCodeViewInfosResult)
            def buffer = new StringBuffer()
            return buffer.append(codeViewInfoJson?.result[0]?.code as String).append("/").append(codeViewInfoJson?.result[0]?.name as String).toString()
        }
    } catch (e) {
        bInsuranceException(autoTask?.configs?.busiInsuranceCode as String, e.toString())
    }

}

def bInsuranceException(String code, String str = null) throws InsReturnException {
    def buffer = new StringBuffer()
    buffer.append("业务代码").append(code).append("不存在，请核对后重试,错误-->").append(str ?: "")
    throw new InsReturnException(99, buffer.toString())
}

def dealError(ExistCI, ExistBI, calculateResultObj, autoTask, entity, carModeMsg, TbUtil) {
    //为避免车型锁定重选车型流程中反复更改投保日期，此处先回写起保日期
    if (entity.isRenewal())
        throw new InsReturnException("续保流程提示车型锁定,平台提示:" + carModeMsg)

    if (ExistBI) {
        String start = (String) TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.stStartDate") + ":00";
        String end = TbUtil.getEndDate4return(TbUtil.getDate(start.substring(0, 10), "yyyy-MM-dd"));
        entity.order.suiteInfo.bizSuiteInfo.setStart(start);
        entity.order.suiteInfo.bizSuiteInfo.setEnd(end);
    }
    if (ExistCI) {
        String start = (String) TbUtil.getFromJson(calculateResultObj, "result.compulsoryInsuransVo.stStartDate") + ":00";
        String end = TbUtil.getEndDate4return(TbUtil.getDate(start.substring(0, 10), "yyyy-MM-dd"));
        entity.order.suiteInfo.efcSuiteInfo.setStart(start);
        entity.order.suiteInfo.efcSuiteInfo.setEnd(end);
    }
    String[] error = carModeMsg.split("平台对应车型代码有：");
    if (error.size() > 1) {
        String[] jycord = error[1].split("、");
        List<String> errCarList = null != autoTask.tempValues.errCarList ? autoTask.tempValues.errCarList : new ArrayList<>();
        //清除导致代码冲突的参数
        autoTask.tempValues.remove("errCarList")
        autoTask.tempValues.remove("jyCodeMark")
        autoTask.tempValues.remove("errCarDesc")
        autoTask.tempValues.remove("carsResultList")
        autoTask.tempValues.remove("moldCharacterCode")
        autoTask.tempValues.remove("getCarIndex")

        String moldCharacterCodeReturn = jycord[0];
        autoTask.tempValues.moldCharacterCode = moldCharacterCodeReturn
        autoTask.tempValues.jyCodeMark = moldCharacterCodeReturn
        autoTask.tempValues.errCarList = errCarList
        autoTask.tempValues.finishCalculate = "false"

        autoTask.tempValues.put("getCarInfoUrl", "https://issue.cpic.com.cn/ecar/quickoffer/queryVehicleInfoByVinOther")
        JSONObject getCarInfoParam = JSON.parseObject("{\"meta\":{},\"redata\":{\"searchFlag\":\"1\",\"plateNo\":\"\\u6d59BL1998\",\"carVIN\":\"\",\"moldCharacterCode\":\"LHBAEI0103\"}}");
        getCarInfoParam.getJSONObject("redata").put("plateNo", entity.order.carInfo.plateNum);
        getCarInfoParam.getJSONObject("redata").put("moldCharacterCode", moldCharacterCodeReturn);
        autoTask.tempValues.put("getCarObj", getCarInfoParam);
        InsReturnException errCarException = new InsReturnException(InsReturnException.AllowRepeat, "根据提示的备注信息 " + jycord[0] + " 重选车型")
        errCarException.setStep(2)
        throw errCarException
    } else {
        List<String> errCarList = null != autoTask.tempValues.errCarList ? autoTask.tempValues.errCarList : new ArrayList<>();
        errCarList.add(autoTask.tempValues.moldCharacterCode)
        autoTask.tempValues.errCarList = errCarList
        autoTask.tempValues.finishCalculate = "false"
        if (null != autoTask.tempValues.get("errCarDesc")) {
            if (null != autoTask.configs.get("reSelectTimes")) {
                int reSelectTimes = Integer.parseInt(autoTask.configs.get("reSelectTimes"))
                if (errCarList.size() - 1 < reSelectTimes) {
                    InsReturnException errCarException = new InsReturnException(InsReturnException.AllowRepeat, "重选车型后仍提示车型不一致 进行第" + errCarList.size() + "次重选车型流程")
                    errCarException.setStep(2)
                    throw errCarException
                }
            }
            throw new InsReturnException("重选车型后仍提示车型不一致 " + carModeMsg)
        }
        //测试
        autoTask.tempValues.remove("carsResultList")
        if (autoTask.tempValues.containsKey("jyCodeMark"))
            autoTask.tempValues.remove("jyCodeMark")
        String errCarDesc = TbUtil.getSubStr(carModeMsg, "对应的平台车型(", "(", ")与当前所选车型不一致");
        String carModelNamePT = "";
        if (!errCarDesc.contains(" ")) {
            carModelNamePT = TbUtil.getModelNamewithoutCN(errCarDesc);
            errCarDesc = "";
        } else {
            carModelNamePT = errCarDesc.substring(0, errCarDesc.lastIndexOf(" "));
            carModelNamePT = TbUtil.getModelNamewithoutCN(carModelNamePT)
            errCarDesc = errCarDesc.split(" ")[errCarDesc.split(" ").length - 1];
            errCarDesc = TbUtil.getErrCarDesc(errCarDesc)
        }

        autoTask.tempValues.put("carModelNamePT", carModelNamePT);
        autoTask.tempValues.put("errCarDesc", errCarDesc);

        JSONObject getCarInfoParam = JSON.parseObject("{\"meta\":{},\"redata\":{\"name\":\"大众汽车SVW7147BLD轿车\"}}");
        getCarInfoParam.getJSONObject("redata").put("name", carModelNamePT);
        autoTask.tempValues.put("getCarObj", getCarInfoParam);

        if (autoTask.tempValues.containsKey("getCarIndex"))
            autoTask.tempValues.remove("getCarIndex");
        if (autoTask.tempValues.containsKey("finishGetCar"))
            autoTask.tempValues.remove("finishGetCar");
        InsReturnException errCarException = new InsReturnException(InsReturnException.AllowRepeat, "根据提示的备注信息 " + errCarDesc + " 重选车型")
        errCarException.setStep(2)
        throw errCarException
    }
}

def dealCheckCodeStr(checkCodeStr, autoTask, calculateResultObj) {
    JSONObject jsonObject = JSON.parseObject(autoTask.tempValues.calculateParamJson);
    jsonObject.redata.checkInfoVo = calculateResultObj.result.checkInfoVo
    String checkCode = ""
    try {
        checkCode = Dama2Web.getAuth(checkCodeStr, 1)
    } catch (Exception e) {
        throw new InsReturnException("解析转保验证码失败")
    }
    autoTask.tempValues.existCheckCode = "true"
    // 第一次识别的标志
    autoTask.tempValues.firstCheckCode = checkCode
    jsonObject.redata.checkInfoVo.questionAnswer = checkCode
    autoTask.tempValues.calculateParamJson = jsonObject.toJSONString()
    //因为只要出现重选车型提示，验证码必然存在 所以设置开关
    throw new InsReturnException(InsReturnException.AllowRepeat, "检测到转保验证码,输入验证码后重新报价")
}
//保费回写
def backBaseSuiteInfo(ExistBI, ExistCI, calculateResultObj, autoTask, entity, ecompensationRate, efcEcompensationRate) {
    def TbUtil = new common_2011()
    BaseSuiteInfo baseSuiteInfo = new BaseSuiteInfo();
    def totalCharge = 0
    if (ExistBI) {
        BizSuiteInfo bizSuiteInfo = new BizSuiteInfo();
        String start = (String) TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.stStartDate") + ":00";
        String end = TbUtil.getEndDate4return(TbUtil.getDate(start.substring(0, 10), "yyyy-MM-dd"));
        bizSuiteInfo.setStart(start);
        bizSuiteInfo.setEnd(end);
        String discountRate = (String) TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.premiumRatio");
        bizSuiteInfo.setDiscountRate(BigDecimal.valueOf(Double.parseDouble(discountRate)));
        bizSuiteInfo.setDiscountCharge(BigDecimal.valueOf(Double.parseDouble(TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.premium"))));
        JSONArray resultArray = calculateResultObj.getJSONObject("result").getJSONArray("quoteInsuranceVos");
        Map<String, SuiteDef> suites = new HashMap<>();
        for (int i = 0; i < resultArray.size(); i++) {
            JSONObject risk = resultArray.getJSONObject(i);
            String code = risk.getString("insuranceCode");
            if (autoTask?.tempValues?.NEFlag) {
                code = code.replace("NEWENERGY", "")
            }
            SuiteDef suiteDef = new SuiteDef();
            suiteDef.discountRate = BigDecimal.valueOf(Double.parseDouble(discountRate));
            def robot_2011_special_util = new robot_2011_special_util()
            suiteDef.code = TbUtil.reMap(autoTask, "codeReturn", code) ?: robot_2011_special_util.p2codeMap().get(code)
            if (StringUtil.isEmpty(suiteDef.code))
                throw new InsReturnException("险种信息回写失败:出现无法解析险种" + code)
            if (suiteDef.code.startsWith("Ncf")) {//不计免赔险
                suiteDef.amount = new BigDecimal(0);
                suiteDef.discountCharge = risk.getBigDecimal("nonDeductible")
                suiteDef.orgCharge = risk.getBigDecimal("nonDeductible")
            } else {
                suiteDef.amount = risk.getBigDecimal("amount")
                if ("GLASSBROKENCOVERAGE".equals(code))//玻璃破碎
                    suiteDef.amount = new BigDecimal((String) TbUtil.getFromJson(risk, "factorVos.[0].factorValue"))
                if ("DAMAGELOSSCANNOTFINDTHIRDSPECIALCOVERAGE".equals(code) || "PADDLEDAMAGECOVERAGE".equals(code))//无法找到第三方 涉水
                    suiteDef.amount = new BigDecimal(0);
                suiteDef.discountCharge = risk.getBigDecimal("premium")
                suiteDef.orgCharge = risk.getBigDecimal("standardPremium")
            }

            code = suiteDef.code
            // 道路救援服务特约条款
            if (code == 'RoadsideService') {
                def factorVos = risk.getJSONArray('factorVos')
                def factorVo = factorVos.find({ factorVo -> factorVo['factorKey'] == 'serviceTimes' }) as JSONObject
                suiteDef.amount = factorVo.getString('factorValue').toBigDecimal().intValue()
            }

            // 车辆安全检测特约条款，代为驾驶服务特约条款，代为送检服务特约条款
            if (code in ['VehicleInspection', 'DesignatedDriving', 'SendForInspection']) {
                def applyJson = JSONObject.parseObject(autoTask.applyJson)
                def baseSuiteInfo1 = applyJson.getJSONObject('baseSuiteInfo')
                def bizSuiteInfo1 = baseSuiteInfo1.getJSONObject('bizSuiteInfo')
                def suites1 = bizSuiteInfo1.getJSONArray('suites')
                if (code == 'VehicleInspection') {
                    def suite = suites1.find({ suite -> suite['code'] == 'VehicleInspection' }) as JSONObject
                    def vehicleSafetyDetectionVo = risk.getJSONArray('vehicleSafetyDetectionVos')[0] as JSONObject
                    def vehicleInspectionLevel = vehicleSafetyDetectionVo.getString('numOrGrade')
                    def serviceTimes = vehicleSafetyDetectionVo.getBigDecimal('serviceTimes')
                    suiteDef.amount = serviceTimes
                    if (suite) {
                        suite.put('vehicleInspectionLevel', vehicleInspectionLevel)
                        suite.put('amount', serviceTimes)
                    } else {
                        suite = JSON.parseObject(JSON.toJSONString(suiteDef))
                        suite.put('vehicleInspectionLevel', vehicleInspectionLevel)
                        suite.put('amount', serviceTimes)
                        suites1.add(suite)
                    }
                }

                if (code == 'DesignatedDriving') {
                    def suite = suites1.find({ suite -> suite['code'] == 'DesignatedDriving' }) as JSONObject
                    def substituteDrivingVo = risk.getJSONArray('substituteDrivingVos')[0] as JSONObject
                    def designatedDrivingMile = substituteDrivingVo.getString('numOrGrade')
                    if (suite) {
                        suite.put('designatedDrivingMile', designatedDrivingMile)
                    } else {
                        suite = JSON.parseObject(JSON.toJSONString(suiteDef))
                        suite.put('designatedDrivingMile', designatedDrivingMile)
                        suites1.add(suite)
                    }
                }

                if (code == 'SendForInspection') {
                    def suite = suites1.find({ suite -> suite['code'] == 'SendForInspection' }) as JSONObject
                    def factorVos = risk.getJSONArray('factorVos')
                    def serviceGrade = factorVos.find({ factorVo -> factorVo['factorKey'] == 'serviceGrade' }) as JSONObject
                    def sendForInspectionLevel = serviceGrade.getString('factorValue')
                    def serviceTimes = factorVos.find({ factorVo -> factorVo['factorKey'] == 'serviceTimes' }) as JSONObject
                    serviceTimes = serviceTimes.getString('factorValue')
                    suiteDef.amount = serviceTimes.toBigDecimal().intValue()
                    if (suite) {
                        suite.put('sendForInspectionLevel', sendForInspectionLevel)
                        suite.put('amount', serviceTimes)
                    } else {
                        suite = JSON.parseObject(JSON.toJSONString(suiteDef))
                        suite.put('sendForInspectionLevel', sendForInspectionLevel)
                        suite.put('amount', serviceTimes)
                        suites1.add(suite)
                    }
                }

                autoTask.applyJson = JSON.toJSONString(applyJson)
            }

            suites.put(suiteDef.code, suiteDef)
        }

        bizSuiteInfo.suites = suites;
        if (calculateResultObj?.result?.commercialInsuransVo?.standardPremium) {
            bizSuiteInfo.orgCharge = new BigDecimal(calculateResultObj?.result?.commercialInsuransVo?.standardPremium as String)
        }

        baseSuiteInfo.bizSuiteInfo = bizSuiteInfo;
        JSONObject j = (JSONObject) JSON.toJSON(bizSuiteInfo)
        if (StringUtil.isNoEmpty(ecompensationRate)) {
            ((Map) entity.order.platformInfo?.ruleAttachedInfo).put(RuleInfoKey.geniusItem_cexpectClaimRate, ecompensationRate);
        }

        totalCharge = totalCharge + bizSuiteInfo.discountCharge
    }

    if (ExistCI) {
        EfcSuiteInfo efcSuiteInfo = new EfcSuiteInfo();
        String start = (String) TbUtil.getFromJson(calculateResultObj, "result.compulsoryInsuransVo.stStartDate") + ":00";
        String end = TbUtil.getEndDate4return(TbUtil.getDate(start.substring(0, 10), "yyyy-MM-dd"));
        String sourceEnd = TbUtil.getFromJson(calculateResultObj, "result.compulsoryInsuransVo.stEndDate")
        if (!sourceEnd.contains("00:00")) {
            end = sourceEnd + ":00"
        }
        efcSuiteInfo.setStart(start);
        efcSuiteInfo.setEnd(end);
        efcSuiteInfo.discountCharge = new BigDecimal(TbUtil.getFromJson(calculateResultObj, "result.compulsoryInsuransVo.cipremium"))
        if (StringUtil.isNoEmpty((String) autoTask.tempValues.efcOrgCharge)) {
            efcSuiteInfo.orgCharge = new BigDecimal(autoTask.tempValues.efcOrgCharge)
            efcSuiteInfo.discountCharge.divide(efcSuiteInfo.orgCharge, 4, RoundingMode.HALF_DOWN)
            efcSuiteInfo.discountRate = efcSuiteInfo.discountCharge.divide(efcSuiteInfo.orgCharge, 4, BigDecimal.ROUND_HALF_DOWN);
            autoTask.tempValues.PTMsg?.compulsoryClaimRate = String.valueOf(efcSuiteInfo.discountRate);
        }
        baseSuiteInfo.efcSuiteInfo = efcSuiteInfo;
        totalCharge = totalCharge + efcSuiteInfo.discountCharge
        //车船税
        boolean reWriteTax = true
        //政策原因云南根据前端选择是否缴纳车船税
        if (null == entity.order.suiteInfo.taxSuiteInfo && "云南".equals(autoTask.configs.areaComCode)) {
            reWriteTax = false;
        }
        if (reWriteTax) {
            TaxSuiteInfo taxSuiteInfo = new TaxSuiteInfo();
            taxSuiteInfo.discountCharge = new BigDecimal((String) TbUtil.getFromJson(calculateResultObj, "result.compulsoryInsuransVo.taxAmount"))
            baseSuiteInfo.taxSuiteInfo = taxSuiteInfo;
            if ("true".equals(autoTask.configs.needExtraInfo)) {
                String taxType = (String) TbUtil.getFromJson(calculateResultObj, "result.compulsoryInsuransVo.taxType");
                taxType = TbUtil.reMap(autoTask, "taxDerateType", taxType)
                if (StringUtil.isNoEmpty(taxType)) {
                    JSONObject PTMsg = (JSONObject) autoTask.tempValues.get("PTMsg");
                    PTMsg.put(RuleInfoKey.RULE_ITEM_TAX_DERATE_TYPE, taxType)
                    if (null != entity.order.platformInfo?.definition)
                        ((Map) entity.order.platformInfo?.definition).put(RuleInfoKey.RULE_ITEM_TAX_DERATE_TYPE, taxType)
                    autoTask.tempValues.PTMsg = PTMsg
                }
            }

            totalCharge = totalCharge + taxSuiteInfo.discountCharge
        }
        if (StringUtil.isNoEmpty(efcEcompensationRate)) {
            ((Map) entity.order.platformInfo?.ruleAttachedInfo).put("geniusItem.compulsoryCexpectClaimRate", efcEcompensationRate);
        }
    }
    String totalEcompensationRate = TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.totalEcompensationRate");
//交商合计预期赔付率
    if (StringUtil.isNoEmpty(totalEcompensationRate)) {
        ((Map) entity.order.platformInfo?.ruleAttachedInfo).put("application.expectMixedRatio", efcEcompensationRate);
    }
    entity.order.suiteInfo = baseSuiteInfo;
    entity.totalCharge = new BigDecimal((String) TbUtil.getFromJson(calculateResultObj, "result.totalPremium"))
    if (entity.totalCharge == 0) {
        def logger = LoggerFactory.getLogger('robot_2011_util')
        logger.info('totalCharge --- {}', totalCharge)
        entity.totalCharge = totalCharge
    }

    autoTask.taskEntity = entity;
}

def backBaseSuiteInfoPlus(ExistBI, ExistCI, calculateResultObj, autoTask, entity) {
    def TbUtil = new common_2011()
    //拼接保存保费计算结果步骤参数
    JSONObject saveInsureParam = TbUtil.getSaveInsureParam(autoTask.configs.areaComCode)
    //拼接驾意险
    if (autoTask.tempValues.accidentInsurance) {
        saveInsureParam.redata << autoTask.tempValues.accidentInsurance
        saveInsureParam.redata.isClickQuotation = 1
        saveInsureParam.redata.inType = "T"
        saveInsureParam.redata.isPurchaseNoncar = 1
    }

    saveInsureParam.getJSONObject("redata").put("inquireQID", (String) TbUtil.getFromJson(calculateResultObj, "result.inquireQID"))
    saveInsureParam.getJSONObject("redata").put("quotationNo", (String) TbUtil.getFromJson(calculateResultObj, "result.quotationNo"));
    saveInsureParam.getJSONObject("redata").put("commercial", ExistBI);
    saveInsureParam.getJSONObject("redata").put("compulsory", ExistCI);

    if (ExistBI) {//商业险信息
        saveInsureParam.getJSONObject("redata").put("commercial", true);
        saveInsureParam.getJSONObject("redata").put("commercialInsuransVo", TbUtil.j1InfoToJ2(calculateResultObj.getJSONObject("result").getJSONObject("commercialInsuransVo"), saveInsureParam.getJSONObject("redata").getJSONObject("commercialInsuransVo")));
        saveInsureParam.redata.commercialInsuransVo.stMinSuggestRatio = calculateResultObj.result.commercialInsuransVo.premiumRatio
        saveInsureParam.redata.commercialInsuransVo.stMaxSuggestRatio = calculateResultObj.result.commercialInsuransVo.premiumRatio
        def quoteInsuranceVos = TbUtil.getInsureQuoteInsuranceVos(calculateResultObj.getJSONObject("result").getJSONArray("quoteInsuranceVos"), autoTask)
        quoteInsuranceVos.each {
            if ('ROADRESCUESPECIALCLAUSE' == it.insuranceCode) {
                it.factorVos = [
                        [
                                factorKey  : 'serviceTimes',
                                factorValue: it.amount
                        ]
                ]
            } else if (it.insuranceCode in [
                    'DAMAGECARDEDUCTIBLESPECIALCLAUSE',
                    'THIRDPARTYLIABILITYDEDUCTIBLESPECIALCLAUSE',
                    'INCARDRIVERLIABILITYDEDUCTIBLESPECIALCLAUSE',
                    'INCARPASSENGERLIABILITYDEDUCTIBLESPECIALCLAUSE',
            ]) {
                it.factorVos = [
                        [
                                "factorKey"  : "deductibleRate",
                                "factorValue": it.amount
                        ]
                ]
            }
        }

        saveInsureParam.getJSONObject("redata").put("quoteInsuranceVos", quoteInsuranceVos)
    } else {
        saveInsureParam.getJSONObject("redata").remove("commercialInsuransVo");
        saveInsureParam.getJSONObject("redata").remove("quoteInsuranceVos");
    }
    if (ExistCI) {//交强险及车船税信息
        saveInsureParam.getJSONObject("redata").put("compulsory", true);
        saveInsureParam.getJSONObject("redata").put("compulsoryInsuransVo", TbUtil.j1InfoToJ2(calculateResultObj.getJSONObject("result").getJSONObject("compulsoryInsuransVo"), saveInsureParam.getJSONObject("redata").getJSONObject("compulsoryInsuransVo")));
        saveInsureParam.getJSONObject("redata").getJSONObject("compulsoryInsuransVo").put("taxpayerName", entity.order.carOwnerInfo.name);
        saveInsureParam.getJSONObject("redata").getJSONObject("compulsoryInsuransVo").put("taxpayerRecno", entity.order.carOwnerInfo.idCard);
        boolean needStDeclarationDate = saveInsureParam.getJSONObject("redata").getJSONObject("compulsoryInsuransVo").containsKey("stDeclarationDate");
        String taxType = (String) TbUtil.getFromJson(saveInsureParam, "redata.compulsoryInsuransVo.taxType");

        String default_taxBureauName = StringUtil.isNoEmpty((String) autoTask.configs.default_taxBureauName) ? autoTask.configs.default_taxBureauName : "需配置税务机关名称参数";
        saveInsureParam.getJSONObject("redata").getJSONObject("compulsoryInsuransVo").put("taxBureauName", default_taxBureauName);
//税务机关名称
        if (!"T".equals(taxType)) {
            saveInsureParam.getJSONObject("redata").put("compulsoryInsuransVo", calculateResultObj.getJSONObject("result").getJSONObject("compulsoryInsuransVo"));
            JSONObject compulsoryInsuransVo = saveInsureParam.getJSONObject("redata").getJSONObject("compulsoryInsuransVo");
            //完税
            if ("P".equals(taxType)) {
                //todo 广州市地方税务局越秀分局 需要配置
                if (default_taxBureauName == "广州市地方税务局越秀分局") {
                    compulsoryInsuransVo.put("taxBureauName", default_taxBureauName)
                }
                //不理解上面是什么意思 照抄解决一下
                if (default_taxBureauName == "山东省税务局") {
                    compulsoryInsuransVo.put("taxBureauName", default_taxBureauName)
                }
                if (StringUtil.isEmpty(compulsoryInsuransVo.getString("taxPaidNo"))) {
                    compulsoryInsuransVo.put("taxPaidNo", null != autoTask.configs.taxPaidNo ? autoTask.configs.taxPaidNo : "888888");
                }
                //完税凭证登记日期
                if (StringUtil.isEmpty(compulsoryInsuransVo.getString("stTaxDocumentDate"))) {
                    compulsoryInsuransVo.put("stTaxDocumentDate", DateCalcUtil.getFormatDate(new Date(), "yyyy-MM-dd"))
                }
                //完税凭证地区
                if (StringUtil.isEmpty(compulsoryInsuransVo.getString("taxPaidAreaCode"))) {
                    compulsoryInsuransVo.put("taxPaidAreaCode", entity.order.insureArea.province)
                }
                //删除减免税信息
                if (compulsoryInsuransVo.containsKey("deductionDueType")) {
                    compulsoryInsuransVo.remove("deductionDueType")
                }
                if (compulsoryInsuransVo.containsKey("deductionDueCode")) {
                    compulsoryInsuransVo.remove("deductionDueCode")
                }
                if (compulsoryInsuransVo.containsKey("reductionNo")) {
                    compulsoryInsuransVo.remove("reductionNo")
                }
                if (compulsoryInsuransVo.containsKey("lateFee")) {
                    compulsoryInsuransVo.remove("lateFee")
                }

            }
        }
        if (needStDeclarationDate) {
            saveInsureParam.getJSONObject("redata").getJSONObject("compulsoryInsuransVo").put("stDeclarationDate", DateCalcUtil.getFormatDate(new Date(), "yyyy-MM-dd"));
        }
    } else {
        saveInsureParam.getJSONObject("redata").remove("compulsoryInsuransVo");
    }
//车信息
    JSONObject ecarvo = calculateResultObj.getJSONObject("result").getJSONObject("ecarvo");
    //车辆信息部分回写磐石
    entity.order?.carInfo?.jyCode = ecarvo?.moldCharacterCode
    entity.order?.carInfo?.rbCode = ecarvo?.moldCharacterCode
    entity.order?.carInfo?.price = new BigDecimal(ecarvo?.oriPurchasePrice?.toString()).setScale(2, BigDecimal.ROUND_HALF_UP)
    entity.order?.carInfo?.taxPrice = new BigDecimal(ecarvo?.oriPurchasePrice?.toString()).setScale(2, BigDecimal.ROUND_HALF_UP)
    entity.order?.carInfo?.seatCnt = Integer.valueOf(ecarvo?.seatCount?.toString())
    saveInsureParam.getJSONObject("redata").put("ecarvo", TbUtil.j1InfoToJ2(ecarvo, saveInsureParam.getJSONObject("redata").getJSONObject("ecarvo")));
    if (ecarvo.containsKey("jyFuelType"))
        saveInsureParam.getJSONObject("redata").getJSONObject("ecarvo").put("jyFuelType", ecarvo.get("jyFuelType"));
    if (ecarvo.containsKey("power"))
        saveInsureParam.getJSONObject("redata").getJSONObject("ecarvo").put("power", ecarvo.get("power"));
    if (ecarvo.containsKey("vehicleStyle"))
        saveInsureParam.getJSONObject("redata").getJSONObject("ecarvo").put("vehicleStyle", ecarvo.get("vehicleStyle"));
    if (ecarvo.negotiatedValue) {
        saveInsureParam.redata.ecarvo.prevNegoValue = ecarvo.negotiatedValue
    }

//温州需填写车主地址 手机
    if (ecarvo.containsKey("phoneNumber") || ecarvo.containsKey("ownerAddress")) {
        ecarvo.put("phoneNumber", StringUtil.isNoEmpty(entity.order.carOwnerInfo.mobile) ? entity.order.carOwnerInfo.mobile : autoTask.configs.get("defaultPhone"));
        ecarvo.put("ownerAddress", StringUtil.isNoEmpty(entity.order.carOwnerInfo.address) ? entity.order.carOwnerInfo.address : autoTask.configs.get("defaultAddress"));
    }

    if (entity?.order?.carInfo?.isNew) {
        saveInsureParam.redata.ecarvo.put("saleArea", autoTask.configs.saleAreaCode)
        saveInsureParam.redata.ecarvo.put("saleCompany", autoTask.configs.newCarSaleComName)
        saveInsureParam.redata.ecarvo.put("isSaleBy4S", autoTask.configs.isSaleBy4S)
    }
    //北京需填写车主地址
    if ("北京".contains(autoTask.configs.areaComCode)) {
        ecarvo.put("ownerAddress", StringUtil.isNoEmpty(entity.order.carOwnerInfo.address) ? entity.order.carOwnerInfo.address : autoTask.configs.get("defaultAddress"));
    }
    saveInsureParam.getJSONObject("redata").getJSONObject("ecarvo").put("taxCustomerType", ecarvo.get("ownerProp"));
    if (saveInsureParam.getJSONObject("redata").containsKey("platformVo") && null != autoTask.tempValues.platformVo)
        saveInsureParam.getJSONObject("redata").put("platformVo", autoTask.tempValues.platformVo)
    if ("四川".contains(autoTask.configs.areaComCode)) {
        //15356 【生产环境】四川太平洋精灵报价失败，平台查询出错，系统提示:请先保费计算,获取投保查询码后,方可查询平台信息!
        if (saveInsureParam?.redata?.compulsoryInsuransVo?.taxpayerNo?.toString()?.contains("****")) {
            saveInsureParam?.redata?.compulsoryInsuransVo?.taxpayerNo = entity.order.carOwnerInfo.idCard
            saveInsureParam?.redata?.compulsoryInsuransVo?.taxpayerRecno = entity.order.carOwnerInfo.idCard
        }

    }
    if (autoTask?.tempValues?.NEFlag) {
        saveInsureParam?.redata?.put("productFlag", "5")
        if (autoTask?.tempValues?.chargingPostList) {
            saveInsureParam?.redata?.put("chargingPostList", autoTask?.tempValues?.chargingPostList)
        }
    }
    if (autoTask.tempValues.productCode || "北京".equals(autoTask.configs?.areaComCode?.toString())) {
        autoTask.tempValues.saveInsureParam = saveInsureParam.toJSONString()
    } else {
        autoTask.tempValues.saveInsureParam = StringUtil.chinesetoUnicode(saveInsureParam.toJSONString())
    }
    String quotationNo = TbUtil.getFromJson(calculateResultObj, "result.quotationNo").toString();
//平台查询参数
    JSONObject queryPlatformInfoParam = JSON.parse(TbUtil.getBaseParam());
    queryPlatformInfoParam.getJSONObject("redata").put("quotationNo", quotationNo);
    autoTask.tempValues.put("queryPlatformInfoParam", StringUtil.chinesetoUnicode(queryPlatformInfoParam.toJSONString()));

//报价任务需删除报价单号
    String deleteParamStr = "{\"meta\":{},\"redata\":{\"quotationNo\":\"报价单号\"}}".replace("报价单号", quotationNo);
    String query4quote = "{\"meta\":{\"pageSize\":8},\"redata\":{\"quotationNo\":\"报价单号\",\"insuredNo\":\"\",\"policyNo\":\"\",\"licensePlate\":\"\",\"policyHolder\":\"\",\"insurant\":\"\",\"productType\":\"\",\"quotationState\":\"\",\"dateType\":\"101\",\"startDate\":\"\",\"endDate\":\"\",\"isPrint\":\"\"}}".replace("报价单号", quotationNo);
    autoTask.tempValues.put("deleteParam", StringUtil.chinesetoUnicode(deleteParamStr));
    autoTask.tempValues.put("query4quote", StringUtil.chinesetoUnicode(query4quote));

    def logger = LoggerFactory.getLogger('robot_2011_util')
    logger.info("保存太保报价信息：{}", autoTask.tempValues.saveInsureParam as String)
}

def errMsgException(errMsg, calculateResultObj, AutoTask autoTask, ExistCI, entity) {
    def TbUtil = new common_2011()
    //结果异常
    if ("failed".equals(TbUtil.getFromJson(calculateResultObj, "message.code"))) {
        if (StringUtil.isNoEmpty(errMsg)) {
            //交强险重复投保
            if (errMsg.contains("重复投保") || errMsg.contains("-保险止期") || errMsg.contains("公司投保了同类型的险种")) {
// && errMsg.contains("交强险")
                TbUtil.savePoliciesInfo(errMsg, "CI", autoTask);
                if (errMsg.contains("终保日期") || errMsg.contains("-保险止期") || errMsg.contains("保险期限是[")) {
                    String newStartCI = TbUtil.getSubStr(errMsg, "终保日期 ", " ", " ");
                    if (errMsg.contains("-保险止期")) {
                        String oldStartCI = TbUtil.getSubStr(errMsg, "保险期限是", "是", "－").replace("年", "-").replace("月", "-").replace("日", "").substring(0, 10);
                        newStartCI = TbUtil.dateOperator4String("Y", "+", "1", oldStartCI, "yyyy-MM-dd")
                    }
                    if (errMsg.contains("保险期限是[")) {
                        String oldStartCI = TbUtil.getSubStr(errMsg, "保险期限是[", "[", "]");
                        oldStartCI = oldStartCI.split("-")[0];
                        if (StringUtil.isNoEmpty(oldStartCI) && oldStartCI.length() > 7)
                            oldStartCI = oldStartCI.substring(0, 4) + "-" + oldStartCI.substring(4, 6) + "-" + oldStartCI.substring(6, 8)
                        else
                            throw new InsReturnException(InsReturnException.RepeatInsure, "交强险已起保，无法取得日期信息")
                        newStartCI = TbUtil.dateOperator4String("Y", "+", "1", oldStartCI, "yyyy-MM-dd")
                    }
                    TbUtil.startDateEditor(newStartCI, "CI", autoTask);
                    //15857 【生产环境】山东太保报价失败，[保费计算]保费计算失败，错误信息为[报价引擎提示：费用总值不得小于0或者大于1，请核查跟单费用中配置的规则。]
                    if (autoTask?.configs?.areaComCode == '山东') {
                        autoTask.tempValues.cibi = '1'
                        TbUtil.startDateEditor(newStartCI, "CI", autoTask)
                    }
                    throw new InsReturnException(InsReturnException.AllowRepeat, "检测到交强险重复投保，修改起保日期为" + newStartCI + "继续报价")
                } else {
                    throw new InsReturnException(InsReturnException.RepeatInsure, "交强险已起保，无法取得日期信息")
                }
            } else if (errMsg.contains("录入的校验码有误")) {
                JSONObject jsonObject = JSON.parseObject(autoTask.tempValues.calculateParamJson);
                String checkCode = ""
                def oldCode = ''
                String firstCheckCode = autoTask.tempValues.firstCheckCode as String
                boolean flag = false
                try {
                    if (firstCheckCode) {

                        if (firstCheckCode.contains("0")) {
                            firstCheckCode = firstCheckCode.replace("0", "O")
                            flag = true
                        }
                        if (firstCheckCode.contains("1")) {
                            firstCheckCode = firstCheckCode.replace("1", "L")
                            flag = true
                        }
                        firstCheckCode = firstCheckCode.toLowerCase()
                        if (firstCheckCode.contains("o") && !flag) {
                            firstCheckCode = firstCheckCode.replace("o", "O")
                        }
                        if (firstCheckCode.contains("l") && !flag) {
                            firstCheckCode = firstCheckCode.replace("l", "1")
                        }
                        checkCode = firstCheckCode
                    } else {
                        checkCode = Dama2Web.getAuth((String) TbUtil.getFromJson(jsonObject, "redata.checkInfoVo.checkCode"), 1)
                    }

                    oldCode = checkCode
                } catch (Exception e) {
                    throw new InsReturnException("解析转保验证码失败" + e)
                }
                if (checkCode.equals(jsonObject.redata.checkInfoVo.questionAnswer)) {
                    if ("true" == autoTask.tempValues.retryCheckCode)
                        throw new InsReturnException("重试后仍然失败，原有验证码：" + oldCode + '转换后验证码为:' + checkCode)
                    if (checkCode.contains("O") || checkCode.contains("l"))
                        flag = true
                    checkCode = checkCode.replace("O", "0").replace("l", "1").replace('g', '9').replace('b', '6')
                    //重试标识
                    if (checkCode.contains("0") && !flag) {
                        checkCode = checkCode.replace("0", "O")
                    }
                    if (checkCode.contains("1") && !flag) {
                        checkCode = checkCode.replace("1", "L")
                    }
                    autoTask.tempValues.retryCheckCode = "true"
                }
                jsonObject.redata.checkInfoVo.questionAnswer = checkCode
                autoTask.tempValues.calculateParamJson = jsonObject.toJSONString()
                throw new InsReturnException(InsReturnException.AllowRepeat, "重新转换验证码后继续尝试报价")
            } else if (errMsg.contains("未找到") && errMsg.contains("完税记录，不能以纳税投保")) {
                JSONObject jsonObject = JSON.parseObject(autoTask.tempValues.calculateParamJson);
                jsonObject.getJSONObject("redata").getJSONObject("compulsoryInsuransVo").put("taxType", "B")
                autoTask.tempValues.calculateParamJson = jsonObject.toJSONString()
                throw new InsReturnException(InsReturnException.AllowRepeat, "尝试以补税方式重新报价")
            } else if (errMsg.contains("新能源车辆应当以车船税标志“M”上传")) {
                JSONObject jsonObject = JSON.parseObject(autoTask.tempValues.calculateParamJson);
                jsonObject.getJSONObject("redata").getJSONObject("compulsoryInsuransVo").put("taxType", "M")
                autoTask.tempValues.calculateParamJson = jsonObject.toJSONString()
                throw new InsReturnException(InsReturnException.AllowRepeat, "尝试以补税方式重新报价")
            } else if (errMsg.contains("新能源车辆应当以行驶证车辆类型")) {
                JSONObject jsonObject = JSON.parseObject(autoTask.tempValues.calculateParamJson);
                jsonObject.getJSONObject("redata").getJSONObject("compulsoryInsuransVo").put("taxVehicleType", TbUtil.getSubStr(errMsg, "行驶证车辆类型“", "“", "”"))
                autoTask.tempValues.calculateParamJson = jsonObject.toJSONString()
                throw new InsReturnException(InsReturnException.AllowRepeat, "尝试以补税方式重新报价")
            } else {
                //可重试错误
                String[] errStrs = { "纯风险保费查询接口异常!" };
                //是否可重试错误
                if (null == autoTask.tempValues.err2Retry || !errMsg.equals(autoTask.tempValues.err2Retry)) {
                    for (String err : errStrs) {
                        if (errMsg.contains(err)) {
                            autoTask.tempValues.err2Retry = errMsg
                            throw new InsReturnException(InsReturnException.AllowRepeat, errMsg + "|||||||||||||即将重试计算保费")
                        }
                    }
                }

                throw new InsReturnException("接口调用流程发生异常,平台返回提示:" + errMsg)
            }
        } else {
            throw new InsReturnException("未知异常")
        }
    } else if (StringUtil.isNoEmpty(TbUtil.getErrTip(errMsg))) {
        throw new InsReturnException(TbUtil.getErrTip(errMsg));
    } else {
        //处理商业险重复投保
        String msg = (String) TbUtil.getFromJson(calculateResultObj, "result.message");
        boolean repeatInsureBI = false

        if ((msg.contains("商业险上年保单止期：") || msg.contains("商业险上年保单止期：")) && !"false".equals(autoTask.tempValues.repeatInsureBI)) {
            String startBI = TbUtil.getSubStr(msg, "上年保险止期：", "：", ".")
            if (!startBI) {
                def newStr = "商业险上年保单止期："
                def newStrOff = msg.indexOf(newStr)
                startBI = msg.substring(newStrOff + newStr.length(), newStrOff + 8 + newStr.length())
            }
            if (startBI.length() > 7)
                startBI = startBI.substring(0, 4) + "-" + startBI.substring(4, 6) + "-" + startBI.substring(6, 8)
            String startNow = (String) TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.stStartDate");
            startNow = startNow.length() > 9 ? startNow.substring(0, 10) : DateCalcUtil.getFormatDate(new Date(), "yyyy-MM-dd")
            if (TbUtil.getTimeDifference(startBI, startNow) < -1) {
                repeatInsureBI = true
            }
        }
        if ((StringUtil.isNoEmpty(msg) && msg.contains("重复投保")) || repeatInsureBI) {
            if (!entity?.misc) {
                entity?.misc = [:]
            }
            if (!entity?.misc?.message) {
                entity?.misc?.message = [:]
            }
            entity?.misc?.message?.BIZ_lastyear = msg
            String errMessage = "";
            if ((msg.contains("商业险重复投保")) || repeatInsureBI) {
                //统一日期为yyyy-MM-dd 格式 若有时分秒则为yyyy-MM-dd HH:mm
                Pattern pattern = Pattern.compile("[0-9]{4}[年][0-9]{2}[月][0-9]{2}[日]( [0-9]{2}[时:][0-9]{2}分{0,1}){0,1}");
                Matcher m = pattern.matcher(msg);
                while (m.find()) {
                    String after = m.group().replace("年", "-").replace("月", "-").replace("日", "").replace("时", ":").replace("分", "");
                    msg = msg.replace(m.group(), after);
                }
                //先获取发证日期
                if (ExistCI) {
                    String firstRegDate = DateCalcUtil.getFormatDate(entity.order.carInfo.firstRegDate, "yyyy-MM-dd")
                    String stVehicleLicensingDate = TbUtil.getFromJson(calculateResultObj, "result.compulsoryInsuransVo.stVehicleLicensingDate")
                    if (!stVehicleLicensingDate.equals(firstRegDate))
                        autoTask.tempValues.stVehicleLicensingDate = stVehicleLicensingDate
                }
                //上海每次都会返回 商业险上年保险止期 所以做标记避免无限循环
                autoTask.tempValues.repeatInsureBI = "false"
                String msgBI = msg.contains("交强险") && !msg.contains("交强险保单的车型编码") && !msg.contains("商业险上年保险止期：") && !msg.contains("商业险上年保单止期：") ? msg.substring(0, msg.indexOf("交强险")) : msg;
                TbUtil.savePoliciesInfo(msgBI, "BI", autoTask);
                String newStartBI = TbUtil.getSubStr(msgBI, "终保日期：", "：", ":") ?: TbUtil.getSubStr(msgBI, "终保日期：", "：", " ");
                if (msgBI.contains("商业险上年保险止期：") || msgBI.contains("商业险上年保单止期：")) {
                    newStartBI = TbUtil.getSubStr(msgBI, "上年保险止期：", "：", ".");
                    if (!newStartBI) {
                        def newStr = "商业险上年保单止期："
                        def newStrOff = msgBI.indexOf(newStr)
                        newStartBI = msg.substring(newStrOff + newStr.length(), newStrOff + 8 + newStr.length())
                    }
                    if (newStartBI.length() > 7) {
                        newStartBI = newStartBI.substring(0, 4) + "-" + newStartBI.substring(4, 6) + "-" + newStartBI.substring(6, 8)
                        newStartBI = TbUtil.dateOperator4String("D", "+", "1", newStartBI, "yyyy-MM-dd")
                    }
                }
                if (StringUtil.isEmpty(newStartBI)) {
                    throw new InsReturnException(InsReturnException.RepeatInsure, "商业险已起保，无法取得日期信息")
                }
                //北京报错的格式和其他地区的不同
                def bjCiFlag = "商业险上张保单止期：" // 无法截取日重复投保
                if (autoTask.configs.areaComCode.toString().contains("北京") && msgBI.contains(bjCiFlag)) {
                    //北京返回重复投保有的修改时间能报价成功有的不能
                    newStartBI = msgBI.substring(msgBI.indexOf(bjCiFlag) + bjCiFlag.length(), msgBI.indexOf(bjCiFlag) + bjCiFlag.length() + 10)
                    if (autoTask.tempValues.bj_newStart_flag && (autoTask.tempValues.bj_newStart_flag as int) >= 3) {
                        throw new InsReturnException("北京地区检测到重复投保，修改日期为:" + newStartBI + "平台返回信息:" + msgBI)
                    } else {
                        if (autoTask.tempValues.bj_newStart_flag == null) {
                            autoTask.tempValues.bj_newStart_flag = 1
                        } else {
                            autoTask.tempValues.bj_newStart_flag += 1
                        }
                    }
                }
                newStartBI = newStartBI.trim().substring(0, 10);
                newStartBI = newStartBI.replace("年", "-").replace("月", "-").replace("日", "");
                if (msgBI.contains("23:59") || msgBI.contains("23时59分"))
                    newStartBI = DateCalcUtil.getFormatDate(TbUtil.dateOperator("D", '+', "1", TbUtil.getDate(newStartBI, "yyyy-MM-dd")), "yyyy-MM-dd")
                TbUtil.startDateEditor(newStartBI, "BI", autoTask);
            }

            if (msg.contains("交强险重复投保")) {
                String msgCI = msg.contains("商业险") ? msg.substring(msg.indexOf("交强险")) : msg;
                //平台信息回写
                TbUtil.savePoliciesInfo(msgCI, "CI", autoTask);
                String newStartCI = TbUtil.getSubStr(msgCI, "终保日期 ", " ", " ");
                if (StringUtil.isEmpty(newStartCI))
                    throw new InsReturnException(InsReturnException.RepeatInsure, "交强险已起保，无法取得日期信息")
                TbUtil.startDateEditor(newStartCI, "CI", autoTask);
            }
            throw new InsReturnException(InsReturnException.AllowRepeat, "检测到重复投保，修改起保日期继续报价")
        }
    }
}
//平台信息查询及回写
def backPlatInfo(autoTask, entity, calculateResultObj, ExistBI, ExistCI) {
    def TbUtil = new common_2011()
    def accidentInsuranceUtil = new robot_2011_accident_util()
    String queryPlatformInfoURL = "https://issue.cpic.com.cn/ecar/plarformQuery/queryPlatformInfo";
    String ecompensationRate = TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.ecompensationRate");
//商业险预期赔付率
    String efcEcompensationRate = TbUtil.getFromJson(calculateResultObj, "result.compulsoryInsuransVo.ecompensationRate");
//交强险预期赔付率
    String quotationNo = TbUtil.getFromJson(calculateResultObj, "result.quotationNo").toString();
    String totalEcompensationRate = TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.totalEcompensationRate");
//交商合计预期赔付率
    String getQuotationNoURL = "https://issue.cpic.com.cn/ecar/ecar/quickSave"
    if (!"true".equals(autoTask.tempValues.finishPTserch)) {
        //根据报价信息进行平台会写
        JSONObject PTMsg = (JSONObject) autoTask.tempValues.get("PTMsg");
        PTMsg.put("carAge", (String) autoTask.tempValues.get("monthCount"));//车龄 单位是 月
        PTMsg.put("rateCarPrice", entity.order.carInfo.carPriceType == 2 ? entity.order.carInfo.definedCarPrice : (String) autoTask.tempValues.get("actualAmountStr"));
//实际价值
        PTMsg.put("trafficOffenceDiscount", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.trafficTransgressRate"));
        autoTask.tempValues.put("PTMsg", PTMsg);
        if (ExistBI)
            autoTask.tempValues.put("lastDiscount", (String) TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.premiumRatio"));

        //新车没有平台信息
        if (autoTask.tempValues.plateless != '新车未上牌') {
            //areaComCode == 河北 系统垃圾 需要等待一段时间
            if (autoTask?.configs?.areaComCode == "河北") {
                sleep(50000)
            }
            def queryPlatformInfoResult = HttpSender.doPostWithRetry(5, autoTask?.httpClient as CloseableHttpClient, true, queryPlatformInfoURL, Robot2011Util.genBody(autoTask.tempValues, autoTask.tempValues.queryPlatformInfoParam as String), null, Robot2011Util.getDefaultHead(autoTask.tempValues, autoTask.configs), "UTF-8", null, "");
            queryPlatformInfoResult = Robot2011Util.decodeBody(autoTask.tempValues, queryPlatformInfoResult)
            if (!queryPlatformInfoResult.startsWith("{"))
                throw new InsReturnException("平台查询出错");
            JSONObject queryPlatformInfoResultObj = JSON.parseObject(queryPlatformInfoResult);

            def queryPlatformInfoResultFlag = true
            if ("failed".equals(TbUtil.getFromJson(queryPlatformInfoResultObj, "message.code"))) {
                queryPlatformInfoResultFlag = false
                String message = TbUtil.getFromJson(queryPlatformInfoResultObj, "message.message");
                if (message && message.contains('请先保费计算,获取投保查询码后')) {
                } else {
                    throw new InsReturnException("平台查询出错，系统提示:" + message)
                }
            }

            autoTask.tempValues.queryPlatformInfoResultFlag = queryPlatformInfoResultFlag
            autoTask.tempValues.queryPlatformInfoResultObj = queryPlatformInfoResultObj
            if (queryPlatformInfoResultFlag) {
                def platformInfo = queryPlatformInfoResultObj.getJSONObject("result")

                def efcClaimsInfo = platformInfo.getJSONArray("claimInformationSoryVo")

                //理赔列表
                //交强险
                JSONArray efcClaims = new JSONArray();
                double efcAmount = 0;
                if (efcClaimsInfo) {
                    for (int i = 0; i < efcClaimsInfo.size(); i++) {
                        def j = efcClaimsInfo.getJSONObject(i);
                        JSONObject claim = new JSONObject()
                        claim.put("insCorpCode", j.getString("insuranceCompanySory"))
                        claim.put("policyId", j.getString("insuranceNoSory"))
                        claim.put("caseStartTime", j.getString("insuranceTimeSory"))
                        claim.put("caseEndTime", j.getString("closingTimeSory"))
                        claim.put("claimAmount", j.getString("insuranceMoneySory"))
                        efcAmount += j.getDoubleValue("insuranceMoneySory")
                        efcClaims.add(claim)
                    }
                }
                def bizClaimsInfo = platformInfo.getJSONArray("claimInformationVo")
                JSONArray bizClaims = new JSONArray();
                double bizAmount = 0;
                for (int i = 0; i < bizClaimsInfo.size(); i++) {
                    JSONObject j = bizClaimsInfo.getJSONObject(i);

                    JSONObject claim = new JSONObject();
                    claim.put("insCorpCode", j.getString("insuranceCompany"));
                    claim.put("policyId", j.getString("insuranceNo"));
                    claim.put("caseStartTime", j.getString("insuranceTime"));
                    claim.put("caseEndTime", j.getString("closingTime"));
                    claim.put("claimAmount", j.getString("insuranceMoney"));
                    bizAmount += j.getDoubleValue("insuranceMoney");
                    efcClaims.add(claim);
                }
                //商业险连续承保年数
                def insureYears = queryPlatformInfoResultObj.result.platformInfoVo.insureYears
                if (insureYears) {
                    PTMsg.put(PlatformKey.bizContinuityInsureYears, insureYears)//商业险连续承保年数
                }

                //商业:连续承保期间出险次数
                def claimTimes = queryPlatformInfoResultObj?.result?.platformInfoVo?.claimTimes
                if (claimTimes) {
                    PTMsg.put("bwCommercialClaimTimes", String.valueOf(claimTimes));
                    PTMsg.put(RuleInfoKey.application_commercialClaimTimes, String.valueOf(claimTimes));
                }
                PTMsg.put("noClaimDiscountCoefficientReasons", TbUtil.getFromJson(queryPlatformInfoResultObj, "result.platformInfoVo.claimAdjustReasonName"));
                //无赔款折扣浮动原因
                PTMsg.put("loyaltyReasons", TbUtil.getFromJson(queryPlatformInfoResultObj, "result.platformInfoVo.noClaimAdjustReasonName"));
//客户忠诚度无知吗楞 同无赔款折扣不浮动原因
                if (ExistBI) {
                    if (bizClaims.size() > 0)
                        PTMsg.put("bizClaims", bizClaims);
                    PTMsg.put("claimTimes", bizClaims.size());
                    PTMsg.put("bwLastClaimSum", bizAmount);
                }

                if (ExistCI) {
                    if (efcClaims.size() > 0)
                        PTMsg.put("efcClaims", efcClaims);
                    PTMsg.put("compulsoryClaimTimes", efcClaims.size());
                    PTMsg.put("bwLastCompulsoryClaimSum", efcAmount);
                }
            }

            //自主定价系数：
            Map definition = entity.order.platformInfo?.definition as Map ?: [:]
            def independentPriceRate = TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.independentPriceRate")
            if (independentPriceRate) {
                PTMsg.put(PlatformKey.selfRate, TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.independentPriceRate"));
                accidentInsuranceUtil.doRate(autoTask, independentPriceRate)
                //需求  2744 报价、核保、承保回写时需要抓取最新的NCD、自主定价系数回写给磐石
                definition.put(PlatformKey.selfRate, independentPriceRate)
            }

            //无赔款折扣系数： NCD
            def ncd = calculateResultObj?.result?.commercialInsuransVo?.nonClaimDiscountRate
            if (ncd) {
                //需求  2744 报价、核保、承保回写时需要抓取最新的NCD、自主定价系数回写给磐石
                definition.put(PlatformKey.noClaimDiscountCoefficient, ncd)
                PTMsg.put(PlatformKey.noClaimDiscountCoefficient, ncd)
            }

        }

        (autoTask.tempValues.platformBack as Map).putAll(PTMsg)
        do8076BackPlatformInfo(autoTask)
        PlatformUtil.doBackPlatformInfo(autoTask);
        if (null != entity.order.platformInfo?.definition) {
            //佣金返回商业险预期赔付率
            if (StringUtil.isNoEmpty(ecompensationRate))
                ((Map) entity.order.platformInfo?.definition).put("application.expectLossRatio", ecompensationRate)
            //佣金返回交强险预期赔付率
            if (StringUtil.isNoEmpty(efcEcompensationRate))
                ((Map) entity.order.platformInfo?.definition).put("application.expectTrafficLossRatio", efcEcompensationRate)
            //交商合计预期赔付率
            if (StringUtil.isNoEmpty(totalEcompensationRate))
                ((Map) entity.order.platformInfo?.definition).put("application.expectMixedRatio", totalEcompensationRate)
        }

        autoTask.tempValues.put("finishPTserch", "true");
        //续保时是否调用规则
        boolean doRule = !entity.isRenewal() || !"true".equals(autoTask.configs.get("doNotdoRule4Renewal"))
        if (doRule) {
            RuleUtil.doRuleInfo(autoTask, autoTask.configs.login, "", "")
            JSONObject ruleResultObj = null != (JSONObject) autoTask.tempValues.ruleInfo ? (JSONObject) autoTask.tempValues.ruleInfo : new JSONObject();
            String flag = ruleResultObj.size() > 1 ? "true" : "";
            if (StringUtil.isNoEmpty(flag) && "true".equals(flag)) {
                boolean Encore = false
                JSONObject param = JSON.parseObject(autoTask.tempValues.calculateParamJson);
                String discount = ruleResultObj?.getString(RuleInfoKey.geniusItem_policyDiscount) ?: "";
                String lastDiscount = autoTask.tempValues.get("lastDiscount");

                if (!autoTask.tempValues['inputPolicyDiscount'] && StringUtil.areNotEmpty(discount, lastDiscount) && !"0.0000".equals(BigDecimalUtil.minus(discount, lastDiscount, 4))) {
                    Encore = true
                    if (BigDecimalUtil.add(discount, "0", 0).startsWith("-"))
                        throw new InsReturnException("此类业务需转人工报价或不承保，请业管核实政策（规则返回折扣负数）")
                    //处理调整折扣
                    JSONObject redata = param.getJSONObject("redata");
                    redata.put("quotationNo", (String) TbUtil.getFromJson(calculateResultObj, "result.quotationNo"));
                    redata.put("commercial", ExistBI);
                    redata.put("compulsory", ExistCI);
                    if (ExistBI) {
                        redata.put("quoteInsuranceVos", TbUtil.getInsureQuoteInsuranceVos(calculateResultObj.getJSONObject("result").getJSONArray("quoteInsuranceVos"), autoTask));
                        redata.put("commercialInsuransVo", TbUtil.j1InfoToJ2(calculateResultObj.getJSONObject("result").getJSONObject("commercialInsuransVo"), redata.getJSONObject("commercialInsuransVo")));
                        redata.getJSONObject("commercialInsuransVo").put("nonClaimDiscountRate", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.nonClaimDiscountRate"))
                        redata.getJSONObject("commercialInsuransVo").put("underwritingRate", "")
                        redata.getJSONObject("commercialInsuransVo").put("channelRate", "")
                        redata.getJSONObject("commercialInsuransVo").put("premiumRatio", new BigDecimal(discount).setScale(6, BigDecimal.ROUND_HALF_UP)?.toString())
                        redata.getJSONObject("commercialInsuransVo").put("ruleNo", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.ruleNo"))
                        redata.getJSONObject("commercialInsuransVo").put("isAdjustDiscuteRate", "0")
                        redata.getJSONObject("commercialInsuransVo").put("vehicleRiskLevel", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.vehicleRiskLevel"))
                        redata.getJSONObject("commercialInsuransVo").put("wyCarType", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.wyCarType"))
                        redata.getJSONObject("commercialInsuransVo").put("premiumCalculationScheme", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.premiumCalculationScheme"))
                        redata.getJSONObject("commercialInsuransVo").put("newCarFlag", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.newCarFlag"))
                        redata.getJSONObject("commercialInsuransVo").put("cpicScore", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.cpicScore"))
                        redata.getJSONObject("commercialInsuransVo").put("totalCpicScore", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.totalCpicScore"))
                        redata.getJSONObject("commercialInsuransVo").put("isLinkage", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.isLinkage"))
                        redata.getJSONObject("commercialInsuransVo").put("commContinuedInsuredYears", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.commContinuedInsuredYears"))
                        redata.getJSONObject("commercialInsuransVo").put("localDiscuteRate", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.localDiscuteRate"))
                        redata.getJSONObject("commercialInsuransVo").put("ruleNo", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.ruleNo"))
                        redata.getJSONObject("commercialInsuransVo").put("independentPriceRate", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.independentPriceRate"))
                        redata.getJSONObject("commercialInsuransVo").put("netEcompensationRate", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.netEcompensationRate"))
                        redata.getJSONObject("commercialInsuransVo").put("ncdEcompensationRate", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.ncdEcompensationRate"))
                        redata.getJSONObject("commercialInsuransVo").put("ncdTotalEcompensationRate", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.ncdTotalEcompensationRate"))
                        redata.getJSONObject("commercialInsuransVo").put("changeableFeeRate", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.changeableFeeRate"))
                        redata.getJSONObject("commercialInsuransVo").put("originalPoudage", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.originalPoudage"))
                        redata.getJSONObject("commercialInsuransVo").put("originalBusinessFee", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.originalBusinessFee"))
                        redata.getJSONObject("commercialInsuransVo").put("originalPerFormance", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.originalPerFormance"))
                        redata.getJSONObject("commercialInsuransVo").put("originalChangeableFeeRate", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.originalChangeableFeeRate"))
                        redata.getJSONObject("commercialInsuransVo").put("inferedChangeableFeeRate", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.inferedChangeableFeeRate"))
                        redata.getJSONObject("commercialInsuransVo").put("inferedDiscuteRate", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.inferedDiscuteRate"))
                        redata.getJSONObject("commercialInsuransVo").put("targetPolicyCostRate", TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.targetPolicyCostRate"))

                    }
                    if (ExistCI) {//缺例子
                        String oldTaxType = redata.getJSONObject("compulsoryInsuransVo").getString("taxType");
                        redata.put("compulsoryInsuransVo", TbUtil.j1InfoToJ2(calculateResultObj.getJSONObject("result").getJSONObject("compulsoryInsuransVo"), redata.getJSONObject("compulsoryInsuransVo")));
                        if ("P".equals(redata.getJSONObject("compulsoryInsuransVo").get("taxType"))) {
                            if (StringUtil.isEmpty((String) redata.getJSONObject("compulsoryInsuransVo").get("taxPaidNo"))) {
                                redata.getJSONObject("compulsoryInsuransVo").put("taxPaidNo", null != autoTask.configs.taxPaidNo ? autoTask.configs.taxPaidNo : "888888");
                                redata.compulsoryInsuransVo.stTaxDocumentDate = DateCalcUtil.getFormatDate(new Date(), "yyyy-MM-dd")
                                redata.compulsoryInsuransVo.taxPaidNo = autoTask.configs.taxPaidNo ?: "888888"
                                redata.compulsoryInsuransVo.taxPaidAreaCode = entity.order.insureArea.province
                            }
                        }
                        //报价后缴税类型发生了改变
                        else if (!oldTaxType.equals(redata.getJSONObject("compulsoryInsuransVo").getString("taxType"))) {
                            redata.put("compulsoryInsuransVo", calculateResultObj.getJSONObject("result").getJSONObject("compulsoryInsuransVo"));
                        }
                    }
                    param.put("redata", redata)
                } else {
                    String repairRate = null != ruleResultObj.get(RuleInfoKey.geniusItem_repairDiscount) ? ruleResultObj.getString(RuleInfoKey.geniusItem_repairDiscount) : "";
                    String lastRepairRate = autoTask.tempValues.get("repairRate");
                    if (StringUtil.areNotEmpty(repairRate, lastRepairRate) && !repairRate.equals(lastRepairRate)) {
                        Encore = true
                        JSONArray quoteInsuranceVos = param.getJSONObject("redata").getJSONArray("quoteInsuranceVos");
                        for (int i = 0; i < quoteInsuranceVos.size(); i++) {
                            String code = quoteInsuranceVos.getJSONObject(i).getString("insuranceCode");
                            if ("APPOINTEDREPAIRFACTORYSPECIALCLAUSE".equals(code)) {
                                JSONArray ja = quoteInsuranceVos.getJSONObject(i).getJSONArray("factorVos");
                                for (int j = 0; j < ja.size(); j++) {
                                    if ("repairFactorRate".equals(ja.getJSONObject(j).getString("factorKey")))
                                        quoteInsuranceVos.getJSONObject(i).getJSONArray("factorVos").getJSONObject(j).put("factorValue", repairRate);
                                }
                                break;
                            }
                        }
                        param.getJSONObject("redata").put("quoteInsuranceVos", quoteInsuranceVos);
                    }
                    //山东车价下调
                    String amountRate = ruleResultObj.get(RuleInfoKey.geniusItem_vehicleDemageIns_coverageDiscount)
                    if (StringUtil.isEmpty(amountRate))
                        amountRate = ruleResultObj.get(RuleInfoKey.geniusItem_theftIns_coverageDiscount)
                    if (StringUtil.isEmpty(amountRate))
                        amountRate = ruleResultObj.get(RuleInfoKey.geniusItem_combustionIns_coverageDiscount)

                    if (StringUtil.isNoEmpty(amountRate)) {
                        Encore = true
                        boolean handled = false;
                        String newAmount = "";
                        String codeStr = "DAMAGELOSSCOVERAGE,SELFIGNITECOVERAGE,THEFTCOVERAGE"

                        JSONArray quoteInsuranceVos = param.getJSONObject("redata").getJSONArray("quoteInsuranceVos");
                        for (int i = 0; i < quoteInsuranceVos.size(); i++) {
                            String code = quoteInsuranceVos.getJSONObject(i).getString("insuranceCode");
                            if (codeStr.contains(code)) {
                                if (handled)
                                    quoteInsuranceVos.getJSONObject(i).put("amount", newAmount)
                                else {
                                    newAmount = BigDecimalUtil.multi(quoteInsuranceVos.getJSONObject(i).getString("amount"), amountRate, 0)
                                    quoteInsuranceVos.getJSONObject(i).put("amount", newAmount)
                                    handled = true
                                }
                            }
                        }
                        param.getJSONObject("redata").put("quoteInsuranceVos", quoteInsuranceVos);
                        //以下为重储存车型信息步骤
                        JSONObject quickSaveParam = autoTask.tempValues.quickSaveParam
                        if (autoTask?.configs?.busiInsuranceCode) {
                            quickSaveParam?.redata?.busiInsurance = autoTask?.tempValues?.insuranceCode ?: (autoTask.tempValues.insuranceCode = getCodeVBusiInsurance(autoTask))
                        }
                        quickSaveParam.redata.actualValue = autoTask.tempValues.actualValue
                        quickSaveParam.redata.negotiatedValue = newAmount
                        def logger = LoggerFactory.getLogger('robot_2011_util')
                        quickSaveParam.redata.quotationNo = quotationNo
                        logger.info('quickSaveRequest --- {}', quickSaveParam)
                        def quickSaveResult = HttpSender.doPostWithRetry(5, autoTask?.httpClient as CloseableHttpClient, true,
                                getQuotationNoURL, Robot2011Util.genBody(autoTask.tempValues, StringUtil.chinesetoUnicode(quickSaveParam.toJSONString())), null,
                                Robot2011Util.getDefaultHead(autoTask.tempValues, autoTask.configs), "UTF-8",
                                null, "")
                        quickSaveResult = Robot2011Util.decodeBody(autoTask.tempValues, quickSaveResult)
                        logger.info('quickSaveResponse --- {}', quickSaveResult)

                        JSONObject quickSaveResultObj = JSON.parseObject(quickSaveResult)
                        if (!"success".equals(TbUtil.getFromJson(quickSaveResultObj, "message.code")) || StringUtil.isEmpty((String) TbUtil.getFromJson(quickSaveResultObj, "result.quotationNo"))) {
                            throw new InsReturnException("获取主询价号失败,平台返回如下:" + (String) TbUtil.getFromJson(quickSaveResultObj, "message.message"))
                        }
                        quotationNo = (String) TbUtil.getFromJson(quickSaveResultObj, "result.quotationNo");
                        autoTask.tempValues.put("quotationNo", quotationNo);
                        param.redata.quotationNo = quotationNo
                    }
                }

                autoTask.tempValues.put("calculateParamJson", param.toJSONString());
                if (Encore) {
                    autoTask.tempValues.Encore = "true"
                    autoTask.tempValues.again = "true"
                    throw new InsReturnException(InsReturnException.AllowRepeat, "根据规则返回作出调整重新报价")
                }
            }
        }
    }
}

def aes(key) {
    return AesDesEncryption.builder()
            .cipherAlgorithm(EncryptEnum.AlgorithmEnum.AES_ECB_PKCS5Padding)
            .key(key as String)
            .keyFormat(EncryptEnum.KeyFormatEnum.UTF_8)
            .build()
}

def aesEncrypt(key, content) {
    def aes = aes(key as String)
    return aes.encrypt(content as String)
}

def aesDecrypt(key, content) {
    def aes = aes(key as String)
    return aes.decrypt(content as String)
}

def nonMotorCalculate(autoTask) {
    def logger = LoggerFactory.getLogger('robot-2011-nonMotorCalculate')
    autoTask = autoTask as AutoTask
    def enquiry = autoTask.taskEntity as Enquiry
    def misc = enquiry.misc
    def nonMotor = misc['nonMotor']
    if (!nonMotor) {
        return
    }
    def planCode = nonMotor['productCode'] as String
    def dict = new robot_2011_dict()
    planCode = dict.nonMotorProductCodeMap(planCode)
    if (Robot2011NonMotor.isNewProcess(planCode)) {
        Robot2011NonMotor.nonMotorInsure(autoTask)

        return
    }
    def productCode = dict.nonCarProductCode(planCode)

    def key1 = 'MSBTOM2400000000'
    def key2 = 'TdOvQRBA7Byx5uJE'
    def httpClient = autoTask.httpClient as CloseableHttpClient
    def quotationNo = autoTask.tempValues['quotationNo']
    def queryProductDetailURL = 'https://issue.cpic.com.cn/ecar/msb/queryProductDetail'
    def sendStrParam = [
            'meta'  : [:],
            'redata': [
                    'quotationNo': quotationNo,
                    'userIp'     : 'issue.cpic.com.cn'
            ]
    ]
    if (autoTask?.configs?.detail4) {
        sendStrParam.redata['detail4'] = autoTask.configs?.detail4
        sendStrParam.redata['noCarAgencyCode'] = autoTask.configs?.agentCode
        sendStrParam.redata['agentCode'] = autoTask.configs?.agent_code
    }
    def sendStr = JSON.toJSONString(sendStrParam)
    def header = [
            'Content-Type'    : 'application/json;charset=UTF-8',
            'X-Requested-With': 'XMLHttpRequest',
            'Origin'          : 'https://issue.cpic.com.cn',
            'Referer'         : 'https://issue.cpic.com.cn/ecar/view/portal/page/car_insurance/insurancePlan.html'
    ]
    logger.info('queryProductDetail --- url: {}', queryProductDetailURL)
    logger.info('queryProductDetail --- sendStr: {}', sendStr)
    def queryProductDetailResponse = HttpSender.doPost(httpClient, true,
            queryProductDetailURL, Robot2011Util.genBody(autoTask.tempValues, sendStr), null,
            Robot2011Util.getDefaultHead(autoTask.tempValues, autoTask.configs),
            'UTF-8', null, '')
    queryProductDetailResponse = Robot2011Util.decodeBody(autoTask.tempValues, queryProductDetailResponse)
    logger.info('queryProductDetail --- response: {}', queryProductDetailResponse)
    def queryProductDetailResponseJsonObject = JSON.parseObject(queryProductDetailResponse)
    def result = queryProductDetailResponseJsonObject.getJSONObject('result')
    if (!result) {
        def message = queryProductDetailResponseJsonObject.getJSONObject("message")?.getString("message")
        throw new InsReturnException(message)
    }
    def returnURL = result.getString('returnURL')
    returnURL = new String(Base64.decoder.decode(returnURL))
    def binary = result.getString('binary')
    def decodeHashCode = URLDecoder.decode(binary, 'UTF-8')
    //会出现hashCode为空的状况，实际为链接为 https://dwx.cpic.com.cn/cxNew/dwx/manger/index.html#/products?sourceType=WWCD&busiNo=
    //这时需要调 https://dwx.cpic.com.cn/nonvehicle/napi/otherTip/getCarTempParam 接口换取hashCode
    if (!returnURL.contains('hashCode')) {
        //定义请求参数
        def getCarTempParamURL = 'https://dwx.cpic.com.cn/nonvehicle/napi/otherTip/getCarTempParam'
        def getCarTempParamHeader = [
                'Host'        : 'dwx.cpic.com.cn',
                'Content-Type': 'application/json;charset=UTF-8',
                'Origin'      : 'https://dwx.cpic.com.cn'
        ]
        def getCarTempParamKey = 'TdOvQRBA7Byx5uJE'
        def getCarTempParamBody = "{\"busiNo\":\"$binary\"}"
        logger.info("getCarTempParam --- body: {}", getCarTempParamBody)
        getCarTempParamBody = aesEncrypt(getCarTempParamKey, getCarTempParamBody)

        //发送请求
        logger.info('getCarTempParam --- request: {}', getCarTempParamBody)
        def getCarTempParamResp = HttpSender.doPost(httpClient, true, getCarTempParamURL, getCarTempParamBody, null, getCarTempParamHeader, 'UTF-8', null, '')
        logger.info('getCarTempParam --- response: {}', getCarTempParamResp)

        //报文解密
//        AES aesUtil = new AES("ECB", "PKCS7Padding", getCarTempParamKey.getBytes());
        getCarTempParamResp = Robot2011Util.removeQuotationMarks(getCarTempParamResp)
        getCarTempParamResp = aesDecrypt(getCarTempParamKey, getCarTempParamResp)

        //获取hashCode
        def getCarTempParamResult = JSON.parseObject(getCarTempParamResp)
        if (getCarTempParamResult.getInteger("code") == 0) {
            binary = getCarTempParamResult.getString("data")
            decodeHashCode = binary
        } else {
            throw new InsReturnException(InsReturnException.Others, "太平洋精灵非车险查询错误，无法获取hashCode：${binary}")
        }
    }

    def hashCode = binary
    binary = JSON.parseObject(aesDecrypt(key1, decodeHashCode))
    def branchCode = binary.getString('brchCode')
    def agentCode = binary.getString('agentCode')
    def saleType = binary.getString('saleType')
    def groupOrderId = binary.getString('groupOrderId')
    def handlerList = binary.getJSONArray('handlerList')
    def salesmanCode = binary.getString('salesmanCode')
    def channelCode = binary.getString('channelCode')
    def centralBranchCode = binary.getString('centralBranchCode') // 446200
    def hdlrCode = binary.getString('hdlrCode')
    def autoPartnerCode = binary.getString('autoPartnerCode') ?: ''
    def autoPartnerName = binary.getString('autoPartnerName') ?: ''

    def index = returnURL.indexOf('sourceType=')
    def sourceType = returnURL.substring(index + 'sourceType='.length(), index + 'sourceType='.length() + 4)

    sendStr = JSON.toJSONString([
            'sourceType': sourceType,
            'hashCode'  : decodeHashCode
    ])

    def data = queryNonAutoProducts(logger, sendStr, httpClient)
    def product = data.find({ item -> item['productCode'] == productCode })
    if (!product) {
        throw new InsReturnException(InsReturnException.Others, "太平洋精灵非车险查询错误，不支持该产品代码：${productCode}")
    }

    def initPrdResult = initPrd(productCode, sourceType, hashCode, decodeHashCode, logger, httpClient, key2)

    data = initPrdResult.getJSONObject('data')
    def classesCode = data.getString('classesCode')
    def channelNo = data.getString('showEleInsuranceSourceType').split(/,/)[0]
    def tempId = data.getJSONObject('temp').getString('id')
    def exendJson = data.getJSONObject('temp').getJSONArray('exendJson')
    def findExendJson = { path ->
        def find = exendJson.find({ item -> item['path'] == path })
        return find ? find['value'] : ''
    }

    def getInitParamsURL = 'https://dwx.cpic.com.cn/nonvehicle/napi/insurance/getInitParams'
    def param = [
            'productCode': productCode,
            'sourceType' : sourceType,
            'saleType'   : saleType,
            'classesCode': classesCode,
            'branchCode' : branchCode
    ]
    sendStr = aesEncrypt(key2, JSON.toJSONString(param))
    sendStr = '"' + sendStr + '"'
    header = [
            'Content-Type': 'application/json',
            'Origin'      : 'https://dwx.cpic.com.cn',
            'Referer'     : "https://dwx.cpic.com.cn/cxNew/dwx/mobilemsb/?plcPlanCode=${productCode}&sourceType=${sourceType}&hashCode=${decodeHashCode}&version=undefined&isProductList=1".toString()
    ]
    logger.info('getInitParams --- request: {}', JSON.toJSONString(param))
    def getInitParamsResponse = HttpSender.doPost(httpClient, true, getInitParamsURL, sendStr, null, header, 'UTF-8', null, '')
    if (getInitParamsResponse && getInitParamsResponse.startsWith('{')) {
        def msg = JSON.parseObject(getInitParamsResponse)?.getString("message")
        logger.warn(msg)
        throw new InsReturnException("太保系统异常: ${msg}")
    }
    getInitParamsResponse = getInitParamsResponse.replaceAll('"', '')
    def getInitParamsResult = JSON.parseObject(aesDecrypt(key2, getInitParamsResponse))
    logger.info('getInitParams --- response: {}', getInitParamsResult)
    data = getInitParamsResult.getJSONObject('data')
    def vehicleKindJQMapping = data.getString('vehicleKindJQMapping')
    def vehicleKindSYMapping = data.getString('vehicleKindSYMapping')
    def vehicleUsageJQMapping = data.getString('vehicleUsageJQMapping')
    def vehicleUsageSYMapping = data.getString('vehicleUsageSYMapping')
    def vehicleKindMapping = JSON.parseObject(vehicleKindJQMapping ?: vehicleKindSYMapping)
    def vehicleUsageMapping = JSON.parseObject(vehicleUsageJQMapping ?: vehicleUsageSYMapping)

    def getPlansURL = "https://dwx.cpic.com.cn/nonvehicle/napi/pageData/getPlans?productId=${productCode}&isRenew=false&isNew=true".toString()
    header = ['Referer': "https://dwx.cpic.com.cn/cxNew/dwx/mobilemsb/?plcPlanCode=${productCode}&sourceType=${productCode}&hashCode=${decodeHashCode}&version=undefined&isProductList=1".toString()]
    logger.info('getPlans --- url: {}', getPlansURL)
    def getPlansResponse = HttpSender.doGet(httpClient, getPlansURL, header, null, 'UTF-8', null, false)
    getPlansResponse = (getPlansResponse as String).replaceAll('"', '')
    def getPlansResult = JSON.parseObject(aesDecrypt(key2, getPlansResponse))
    logger.info('getPlans --- response: {}', getPlansResult)
    data = getPlansResult.getJSONObject('data')
    def groups = data.getJSONArray('groups')
    product = groups.find({ item -> item['productCode'] == productCode }) as JSONObject
    if (!product) {
        throw new InsReturnException(InsReturnException.Others, '')
    }

    def planGroupCode = product.getString('planGroupCode')
    def plans = product.getJSONArray('plans')
    def plan = plans.find({ plan -> plan['planCode'] == planCode }) as JSONObject
    def amount = plan.getBigDecimal('sumCoverage')
    def premium = plan.getBigDecimal('premium')

    "https://dwx.cpic.com.cn/nonvehicle/napi/cldproductimginfo/findByType/1?productId=${productCode}"
    // GET
    "https://dwx.cpic.com.cn/nonvehicle/napi/productConfig/findConfig2?productCode=${productCode}&types=diyPlan,config&saleType=${saleType}&sourceType=${sourceType}&dataType=json"

    def getFormConfigURL = "https://dwx.cpic.com.cn/nonvehicle/napi/adsplanconfig/getFormConfig?productCode=${productCode}".toString()
    header = ['Referer': "https://dwx.cpic.com.cn/cxNew/dwx/mobilemsb/?plcPlanCode=${productCode}&sourceType=${sourceType}&hashCode=${decodeHashCode}&version=undefined&isProductList=1".toString()]
    def getFormConfigResponse = HttpSender.doGet(httpClient, getFormConfigURL, header, null, 'UTF-8', null, false)
    result = JSON.parseObject(getFormConfigResponse as String)
    data = result.getJSONObject('data')
    def planList = data.getJSONArray('planList')
    def form = (planList[0] as JSONObject).getJSONObject('form')
    def plcCopies = form.getJSONObject('plcCopies')

    // GET
    "https://dwx.cpic.com.cn/nonvehicle/napi/productConfig/findConfig2?productCode=${productCode}&types=homeMixins&sourceType=${channelNo}&dataType=javascript"
    // GET
    "https://dwx.cpic.com.cn/nonvehicle/napi/cldproductimginfo/findByType/0?productId=${productCode}"
    // GET
    "https://dwx.cpic.com.cn/nonvehicle/napi/productConfig/typesForm?productId=${productCode}&sourceType=${sourceType}&types=form,FixedValueForZT,underWrite&dataType=json"

    def specialHandlerURL = 'https://dwx.cpic.com.cn/nonvehicle/napi/cldPremiumConfig/specialHandler'
    param = [
            'coverProductCode': classesCode,
            'type'            : 'P-E1002',
            'factorVal'       : binary.getInteger('seatCount').toString(),
            'planCode'        : planCode
    ]
    sendStr = aesEncrypt(key2, JSON.toJSONString(param))
    sendStr = '"' + sendStr + '"'
    header = [
            'Content-Type': 'application/json',
            'Origin'      : 'https://dwx.cpic.com.cn',
            'Referer'     : "https://dwx.cpic.com.cn/cxNew/dwx/mobilemsb/?plcPlanCode=${productCode}&sourceType=${sourceType}&hashCode=${decodeHashCode}&version=undefined&isProductList=1".toString()
    ]
    logger.info('specialHandler --- request: {}', JSON.toJSONString(param))
    def specialHandlerResponse = HttpSender.doPost(httpClient, true, specialHandlerURL, sendStr, null, header, 'UTF-8', null, '')
    specialHandlerResponse = specialHandlerResponse.replaceAll('"', '')
    def specialHandlerResult = JSON.parseObject(aesDecrypt(key2, specialHandlerResponse))
    logger.info('specialHandler --- response: {}', specialHandlerResult)
    data = specialHandlerResult.getJSONObject('data')
    if (data) {
        amount = data.getBigDecimal('amount')
        premium = data.getBigDecimal('premium')
    }

    def supplyParam = misc['supplyParam']
    def findFromSupplyParam = { key ->
        if (!supplyParam) {
            return ''
        }

        def find = supplyParam.find({ item -> item['key'] == key })
        return find ? find['value'] as String : ''
    }

    def insurePerson = enquiry.order.insurePerson
    def applicantName = insurePerson.name
    def taskType = autoTask.taskType.split(/-/)[-1]
    def mobile
    def email
    if (taskType in ['insure', 'autoinsure']) {
        mobile = findFromSupplyParam('applicantMobile')
        email = findFromSupplyParam('applicantEmail') ?: autoTask.configs['defaultEmail']
    } else {
        mobile = PhoneUtil.getMobile()
        email = autoTask.configs['defaultEmail']
    }

    def idCardType = insurePerson.idCardType
    def idCard = insurePerson.idCard
    def common_2011 = new common_2011()
    def certificationType = common_2011.getCardType(enquiry, idCardType.toString())
    def persons

    if (idCardType == 0) {
        def callUserCenterURL = "https://dwx.cpic.com.cn/nonvehicle/napi/insurance/callUserCenter?saleType=${saleType}&xpxFlag=0".toString()
        header = [
                'Content-Type': 'application/json',
                'Origin'      : 'https://dwx.cpic.com.cn',
                'Referer'     : "https://dwx.cpic.com.cn/cxNew/dwx/mobilemsb/?plcPlanCode=${productCode}&sourceType=${sourceType}&hashCode=${decodeHashCode}&version=undefined&isProductList=1".toString()
        ]
        def callUserCenterParam = [
                [
                        'userName': applicantName,
                        'mobile'  : mobile,
                        'idType'  : '111',
                        'idCode'  : idCard
                ]
        ]
        sendStr = aesEncrypt(key2, JSON.toJSONString(callUserCenterParam))
        sendStr = '"' + sendStr + '"'
        def callUserCenterResponse = HttpSender.doPost(httpClient, true, callUserCenterURL, sendStr, null, header, 'UTF-8', null, '')
        if (callUserCenterResponse && callUserCenterResponse.startsWith('{')) {
            def msg = JSON.parseObject(callUserCenterResponse)?.getString("message")
            logger.warn(msg)
            throw new InsReturnException("太保系统异常: ${msg}")
        }
        callUserCenterResponse = callUserCenterResponse.replaceAll('"', '')
        def callUserCenterResult = JSON.parseObject(aesDecrypt(key2, callUserCenterResponse))
        int code = callUserCenterResult.getInteger("code")
        if (code == -1) {
            def errorMsg = callUserCenterResult.getString("msg")
            if (errorMsg) {
                throw new InsReturnException("太保系统异常：${errorMsg}")
            }
            throw new InsReturnException("太保系统异常")
        }
        def personId = callUserCenterResult.getJSONObject('data')?.getJSONArray('PID')?.get(0)?.getAt('personId')
        // 君安行交通工具意外险
        if (productCode == 'PD20220923S00015416') {
            persons = [
                    [
                            'personType'       : 1,
                            'branchCode'       : branchCode,
                            'certificationNo'  : idCard,
                            'personName'       : applicantName,
                            'certificationType': '1',
                            'mobilePhone'      : mobile,
                            'emailAddress'     : email,
                            'pid'              : personId,
                            'gender'           : idCard[16].toInteger() % 2 == 0 ? '2' : '1'
                    ],
                    [
                            'personType'            : 2,
                            'branchCode'            : branchCode,
                            'cldPersonInfoAuxoliary': [
                                    'customerWithApplicant': 0,
                                    'isrdIfMainInsured'    : '1'
                            ],
                            'certificationNo'       : idCard,
                            'personName'            : applicantName,
                            'certificationType'     : '1',
                            'mobilePhone'           : mobile,
                            'emailAddress'          : email,
                            'pid'                   : personId,
                            'gender'                : idCard[16].toInteger() % 2 == 0 ? '2' : '1',
                            'customerWithApplicant' : 0
                    ]
            ]
        } else {
            persons = [
                    [
                            'personType'       : 1,
                            'branchCode'       : branchCode,
                            'certificationNo'  : idCard,
                            'personName'       : applicantName,
                            'certificationType': '1',
                            'mobilePhone'      : mobile,
                            'emailAddress'     : email,
                            'pid'              : personId,
                            'gender'           : idCard[16].toInteger() % 2 == 0 ? '2' : '1'
                    ]
            ]
        }
    } else {
        // 君安行交通工具意外险
        if (productCode == 'PD20220923S00015416') {
            persons = [
                    [
                            'personType'       : 1,
                            'branchCode'       : branchCode,
                            'certificationNo'  : idCard,
                            'personName'       : applicantName,
                            'certificationType': certificationType,
                            'mobilePhone'      : mobile,
                            'emailAddress'     : email,
                            'gender'           : enquiry.order.insurePerson.sex == 0 ? '2' : '1'
                    ],
                    [
                            'personType'            : 2,
                            'branchCode'            : branchCode,
                            'cldPersonInfoAuxoliary': [
                                    'customerWithApplicant': 0,
                                    'isrdIfMainInsured'    : '1'
                            ],
                            'certificationNo'       : idCard,
                            'personName'            : applicantName,
                            'certificationType'     : certificationType,
                            'mobilePhone'           : mobile,
                            'emailAddress'          : email,
                            'gender'                : enquiry.order.insurePerson.sex == 0 ? '2' : '1',
                            'customerWithApplicant' : 0
                    ]
            ]
        } else {
            persons = [
                    [
                            'personType'       : 1,
                            'branchCode'       : branchCode,
                            'certificationNo'  : idCard,
                            'personName'       : applicantName,
                            'certificationType': certificationType,
                            'mobilePhone'      : mobile,
                            'emailAddress'     : email,
                            'gender'           : enquiry.order.insurePerson.sex == 0 ? '2' : '1'
                    ]
            ]
        }
    }

    def postCommonBusURL = 'https://dwx.cpic.com.cn/nonvehicle/napi/commonBus/postCommonBus'
    header = [
            'Content-Type': 'application/json',
            'Origin'      : 'https://dwx.cpic.com.cn',
            'Referer'     : "https://dwx.cpic.com.cn/cxNew/dwx/mobilemsb/?plcPlanCode=${productCode}&sourceType=${sourceType}&hashCode=${decodeHashCode}&version=undefined&isProductList=1".toString()
    ]
    param = [
            'planCode'         : planCode,
            'planGroupCode'    : planGroupCode,
            'channelNo'        : channelNo,
            'channelUniqueCode': '',
            'agentCode'        : agentCode,
            'branchCode'       : branchCode,
            'sellChannelCode'  : channelCode,
            'hdlrCode'         : hdlrCode,
            'handlerInfoList'  : handlerList,
            'lifeEmpNo'        : '',
            'msbProductCode'   : productCode,
            'sourceType'       : sourceType,
            'wddm'             : '',
            'saleType'         : saleType,
            'tempId'           : tempId
    ]
    sendStr = aesEncrypt(key2, JSON.toJSONString(param))
    sendStr = '"' + sendStr + '"'
    logger.info('postCommonBus --- request: {}', JSON.toJSONString(param))
    def postCommonBusResponse = HttpSender.doPost(httpClient, true, postCommonBusURL, sendStr, null, header, 'UTF-8', null, '')
    postCommonBusResponse = postCommonBusResponse.replaceAll('"', '')
    def postCommonBusResult
    try {
        postCommonBusResult = JSON.parseObject(aesDecrypt(key2, postCommonBusResponse))
    } catch (Exception e) {
        logger.warn('解密失败', e)
        throw new InsReturnException("解密失败")
    }
    logger.info('postCommonBus --- response: {}', postCommonBusResult)
    def msg = postCommonBusResult.getString('msg')
    data = postCommonBusResult.getJSONObject('data')
    if (!data) {
        throw new InsReturnException(InsReturnException.Others, msg)
    }

    def hdlrDeptCode = data.getString('hdlrDeptCode')
    def hdlrDeptName = data.getString('hdlrDeptName')
    def hdlrDeptGroupName = data.getString('hdlrDeptGroupName')
    def hdlrDeptGroupCode = data.getString('hdlrDeptGroupCode')
    def hdlrSectCode = data.getString('hdlrSectCode')

    def carInfo
    def plateColor = findExendJson('query.plateColor')
    if (plateColor) {
        def plateColorMap = [
                '1': 'CO000',
                '2': 'CO001',
                '3': 'CO002',
                '4': 'CO003',
        ]
        plateColor = plateColorMap.get(plateColor) ?: 'CO000'
        carInfo = [
                'carNumber' : findExendJson('query.factors.E1000.value'),
                'carVin'    : findExendJson('query.factors.KP000.value'),
                'carEngine' : findExendJson('query.engineNo'),
                'carColour' : plateColor,
                'carSeating': findExendJson('query.factors.E1002.value'),
                'extend1'   : findExendJson('query.purchasePriceJY'),
                'extend2'   : findExendJson('query.vehicleKind'),
                'extend3'   : findExendJson('query.vehicleClassName')
        ]
    } else {
        carInfo = [
                'carNumber' : findExendJson('query.factors.E1000.value'),
                'carVin'    : findExendJson('query.factors.KP000.value'),
                'carEngine' : findExendJson('query.engineNo'),
                'carSeating': findExendJson('query.factors.E1002.value'),
                'extend1'   : findExendJson('query.purchasePriceJY'),
                'extend2'   : findExendJson('query.vehicleKind'),
                'extend3'   : findExendJson('query.vehicleClassName')
        ]
    }

    def start = binary.getString('insuranceStartDate')
    def end = binary.getString('insuranceEndDate')
    def policy = [
            'tempId'          : tempId,
            'branchCode'      : branchCode,
            'productName'     : initPrdResult.getJSONObject('data').getString('productName'),
            'sourceType'      : sourceType,
            'payBillOrNot'    : '0',
            'plcElcflag'      : 0,
            'elcIsCombine'    : '0',
            'endDate'         : end,
            'startDate'       : start,
            'groupOrderId'    : groupOrderId,
            'productCode'     : productCode,
            'offsitBranchCode': branchCode,
            'planCode'        : planCode,
            'planGroupCode'   : planGroupCode,
            'plcCopies'       : plcCopies.getString('value').toInteger(),
            'plcPremium'      : premium,
            'plcAmount'       : amount,
            'agentCode'       : agentCode,
            'handlerCode'     : hdlrCode,
            'lifeempNo'       : '',
            'saleType'        : saleType,
            'tbrcode'         : '',
            'autoPartnerName' : autoPartnerName,
            'deptgroupCode'   : hdlrDeptGroupCode,
            'deptCode'        : hdlrDeptCode,
            'channelCode'     : channelCode,
            'agencyyxydm'     : '',
            'recordWay'       : binary.getString('recordWay'),
            'diySaleCode'     : '',
            'autoPartnerCode' : autoPartnerCode,
            'wddm'            : '',
            'yzOrderId'       : '',
            'planName'        : plan.getString('planName'),
            'sectionCode'     : hdlrSectCode
    ]

    def factors = plan.getJSONObject('factors')
    def factorList = []
    factors.each({ k, v ->
        def factorCode = k
        def factorValue = ''
        if (k in ['KP000', 'E1000', 'E1002']) {
            factorValue = findExendJson("query.factors.${k}.value".toString())
        }

        if (k == 'E1001') {
            factorValue = findExendJson('query.engineNo')
        }

        if (k in ['O5000', 'F7000']) {
            def vehicleUsage = binary.getString('vehicleUsage')
            def usage = vehicleUsageMapping.getJSONObject('usage' + vehicleUsage)
            def kind = usage.getJSONObject('kind')
            if (kind) {
                factorCode = factorValue = kind.getJSONArray('k' + binary.getString('vehicleKind'))
                        .getString(k == 'O5000' ? 0 : 1)
            } else {
                factorCode = factorValue = usage.getString(k)
            }
        }

        if (k == 'I2000') {
            def vehicleKind = binary.getString('vehicleKind')
            factorCode = factorValue = vehicleKindMapping.getString('k' + vehicleKind)
        }

        if (k == 'CO000') {
            factorCode = factorValue = k
        }

        if (k == 'A0000') {
            factorCode = factorValue = 'A0004'
        }

        if (k == 'EA000') {
            factorValue = findExendJson('query.factors.E1000.value')
        }

        if (k == 'C3_00') {
            factorCode = factorValue = 'C3_01'
        }

        if (factorValue && factorCode) {
            factorList << [
                    'factorValue': factorValue,
                    'factorCode' : factorCode
            ]
        }
    })

    def underWrite = [
            'plcBase'    : [:],
            'applicant'  : [:],
            "insuredList": [[:]],
            'planList'   : [
                    [
                            'plnBase'   : [
                                    'plnMainClassesCode': classesCode,
                                    'groupInsuranceFlag': plan.getString('groupInsuranceFlag'),
                                    'isByCarSellFlag'   : binary.getString('isByCarSellFlag')
                            ],
                            'specialRyx': [
                                    'factorList': factorList,
                            ],
                            'elcPolicy' : [
                                    'elcEmlFlag': '1',
                                    'elcMsgFlag': '1',
                                    'elcMobile' : mobile,
                                    'elcEmail'  : email
                            ]
                    ]
            ]
    ]
    def policyExtend = [
            'handlerName'       : '',
            'dcLicenseNbr'      : '',
            'dcOrgName'         : '',
            'validateIspqc'     : '1',
            'isCarAndNoCarPrize': '1'
    ]
    def policyExtra = [
            'extend2'            : '',
            'extend3'            : '',
            'extend4'            : '',
            'extend5'            : '',
            'departmentGroupName': hdlrDeptGroupName,
            'departmentName'     : hdlrDeptName
    ]
    def policyList = [
            [
                    'persons'     : persons,
                    'policy'      : policy,
                    'underWrite'  : underWrite,
                    'policyExtend': policyExtend,
                    'policyExtra' : policyExtra,
                    'handlerList' : handlerList
            ]
    ]
    def iscombineProduct = '0'

    def submitPolicyURL = 'https://dwx.cpic.com.cn/nonvehicle/napi/insurance/submitPolicy'
    header = [
            'Content-Type': 'application/json',
            'Origin'      : 'https://dwx.cpic.com.cn',
            'Referer'     : "https://dwx.cpic.com.cn/cxNew/dwx/mobilemsb/?plcPlanCode=${productCode}&sourceType=${sourceType}&hashCode=${decodeHashCode}&version=undefined&isProductList=1".toString()
    ]
    param = [
            'saleType'        : saleType,
            'sourceType'      : sourceType,
            'branchCode'      : branchCode,
            'productCode'     : productCode,
            'groupOrderId'    : groupOrderId,
            'tempId'          : tempId,
            'carInfo'         : carInfo,
            'policyList'      : policyList,
            'iscombineProduct': iscombineProduct
    ]

    sendStr = aesEncrypt(key2, JSON.toJSONString(param))
    sendStr = '"' + sendStr + '"'
    logger.info('submitPolicy --- request: {}', JSON.toJSONString(param))
    def submitPolicyResponse = HttpSender.doPost(httpClient, true, submitPolicyURL, sendStr, null, header, 'UTF-8', null, '')
    if (submitPolicyResponse.startsWith("{")) {
        def errorMsg = JSON.parseObject(submitPolicyResponse)?.getString("message")
        if (errorMsg) {
            throw new InsReturnException("太保系统异常:${errorMsg}")
        }
    }
    submitPolicyResponse = submitPolicyResponse.replaceAll('"', '')
    def submitPolicyResult = JSON.parseObject(aesDecrypt(key2, submitPolicyResponse))
    logger.info('submitPolicy --- response: {}', submitPolicyResult)
    def code = submitPolicyResult.getInteger("code")
    if (code && code == -1) {
        def errMsg = submitPolicyResult.getString("msg")
        if (errMsg) {
            throw new InsReturnException("太保系统异常:" + errMsg)
        }
    }
    data = submitPolicyResult.getJSONObject('data')

    def errMsg = data.getString('errMsg')
    if (errMsg) {
        throw new InsReturnException(InsReturnException.Others, errMsg)
    }

    // TODO
    // https://dwx.cpic.com.cn/nonvehicle/napi/formInfoTemp

    def paymentId = data.getString('paymentId')
    def pushURl = "https://dwx.cpic.com.cn/nonvehicle/napi/cartPush/push?branchCode=${branchCode}&paymentId=${paymentId}&groupOrderId=${groupOrderId}&healthNoticeUrl=".toString()
    header = ['Referer': "https://dwx.cpic.com.cn/cxNew/dwx/mobilemsb/?plcPlanCode=${productCode}&sourceType=${sourceType}&hashCode=${decodeHashCode}&version=undefined&isProductList=1".toString()]
    logger.info('push --- url: {}', pushURl)
    def pushResponse = HttpSender.doGet(httpClient, pushURl, header, null, 'UTF-8', null, false)
    logger.info('push --- response: {}', pushResponse)
    def pushResult = JSON.parseObject(pushResponse as String)
    if (pushResult.getInteger('code') != 0) {
        throw new InsReturnException(InsReturnException.Others, pushResult.getString('data'))
    }
}


def queryMsbProduct(autoTask) {
    def logger = LoggerFactory.getLogger('robot-2011-nonMotorCalculate')
    autoTask = autoTask as AutoTask
    def httpClient = autoTask.httpClient as CloseableHttpClient
    def url = 'https://issue.cpic.com.cn/ecar/msb/queryMsbProduct'
    def sendStr = JSON.toJSONString([
            'meta'  : [:],
            'redata': ['quotationNo': autoTask.tempValues['quotationNo']]
    ])
    def header = [
            'Content-Type'    : 'application/json;charset=UTF-8',
            'X-Requested-With': 'XMLHttpRequest',
            'Origin'          : 'https://issue.cpic.com.cn',
            'Referer'         : 'https://issue.cpic.com.cn/ecar/view/portal/page/car_insurance/insurancePlan.html',
    ]
    logger.info('queryMsbProduct - url:{}', url)
    logger.info('queryMsbProduct - sendStr:{}', sendStr)
    sendStr = Robot2011Util.genBody(autoTask.tempValues, sendStr)
    def requestHeader = Robot2011Util.getDefaultHead(autoTask.tempValues, autoTask.configs)
    def response = HttpSender.doPost(httpClient, true, url, sendStr, null, requestHeader, 'UTF-8', null, '')
    response = Robot2011Util.decodeBody(autoTask.tempValues, response)
    logger.info('queryMsbProduct - response:{}', response)
    def result = JSON.parseObject(response as String)
    return result.getJSONArray('result')
}

def buildHttpClientWithCookie(cookieStr) {
    def cookieStore = new BasicCookieStore()
    def cookieArray = (cookieStr as String).split(/; /)
    cookieArray.each({ item ->
        def split = item.split(/=/)
        if (split.length != 2) {
            return
        }

        def cookie = new BasicClientCookie(split[0], split[1])
        cookie.setDomain('issue.cpic.com.cn')
        if (split[0] in ['sensorsdata2015jssdkcross', 'sajssdk_2015_cross_new_user',
                         'sensorsdata_latest_track_code']) {
            cookie.setDomain('.cpic.com.cn')
        }

        cookie.setPath('/')
        cookieStore.addCookie(cookie)
    })

    return HttpClients.custom().setDefaultCookieStore(cookieStore).build()
}

def checkCookie(autoTask) {
    def config = autoTask.configs
    def j_username = config['login'] ?: config['username']
    def cookieStr = RedisUtil.get('cookieStrKey:2011' + ':' + j_username)
    if (!cookieStr) {
        return false
    }

    autoTask.httpClient = buildHttpClientWithCookie(cookieStr)
    def httpClient = autoTask.httpClient as CloseableHttpClient
    def indexURL = 'https://issue.cpic.com.cn/ecar/view/portal/page/common/index_m.html'
    def indexResponse = doGet(httpClient, indexURL, null) as String
    def indexDoc = Jsoup.parse(indexResponse)
    def login = indexDoc.select('div[data=quotation_merge] > a > h5').text() == '快速投保'
    if (!login) {
        RedisUtil.delete('cookieStrKey:2011' + ':' + j_username)
        throw new InsReturnException(InsReturnException.Others, '登录已失效，请重新配置验证码后重新登录')
    }

    return login
}

def doGet(httpClient, url, header) {
    HttpSender.doGet(httpClient as CloseableHttpClient, url as String, header as Map, null, 'UTF-8', null, false)
}

def doPost(httpClient, url, header, param, body, responseType) {
    def response = HttpSender.doPost(httpClient as CloseableHttpClient, true, url as String, body, param as Map, header as Map, 'UTF-8', null, '')
    switch (responseType) {
        case 'JSONObject': return JSON.parseObject(response)
        case 'JSONArray': return JSON.parseArray(response)
        case 'HTML': return Jsoup.parse(response)
        default: return response
    }
}

def queryCarModel(autoTask) {
    autoTask = autoTask as AutoTask
    def httpClient = autoTask.httpClient as CloseableHttpClient
    def tempValues = autoTask.tempValues
    def enquiry = autoTask.taskEntity as Enquiry
    def carInfo = enquiry.order.carInfo
    def url = 'https://issue.cpic.com.cn/ecar/ecar/queryCarModel'
    def header = [
            'Content-Type': 'application/json;charset=UTF-8'
    ]
    def param = [
            'meta'  : [:],
            'redata': [
                    'name': carInfo.carModelName
            ]
    ]
    if (tempValues['pageNo']) {
        param['meta'] = [
                'pageNo': tempValues['pageNo'] as String
        ]
    }

    def sendStr = JSON.toJSONString(param)
    def response = HttpSender.doPost(httpClient, true, url, sendStr, null, header, 'UTF-8', null, '')
    response = JSON.parseObject(response)
    def result = response.getJSONArray('result')
    def find = result.find({ car ->
        (car as JSONObject).getString('moldCharacterCode') == (carInfo.jyCode ?: carInfo.rbCode)
    })
    if (!find) {
        def carList = autoTask.tempValues['carList'] as JSONArray ?: new JSONArray()
        if (carList) {
            (tempValues['carList'] as JSONArray).addAll(result)
            def meta = response.getJSONObject('meta')
            def pageNo = meta.getString('pageNo')

            tempValues['pageNo'] = (pageNo.toInteger() + 1).toString()
            queryCarModel(autoTask)
        } else {
            carList = tempValues['carList'] as List
            if (!carList) {
                throw new InsReturnException(InsReturnException.Others, '车型查询失败，未查询到相关车型')
            }

            return carList.sort({ car1, car2 -> car1['purchaseValue'] - car2['purchaseValue'] }).get(0)
        }
    }

    return find
}

private static queryNonAutoProducts(def logger, def sendStr, def httpClient) {

    def getPrdsByChannelURL = 'https://dwx.cpic.com.cn/nonvehicle/napi/cldActiveChannel/getPrdsByChannel'
    // def getPrdsByChannelNewURL = 'https://dwx.cpic.com.cn/nonvehicle/napi/cldActiveChannel/getPrdsByChannelNew'

    def header = [
            'Host'        : 'dwx.cpic.com.cn',
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin'      : 'https://dwx.cpic.com.cn'
    ]
    [getPrdsByChannelURL].inject([] as Set) { nonAutoList, it ->
        logger.info('{} --- request: {}', it, sendStr)
        def response = HttpSender.doPost(httpClient, true, it, sendStr, null, header, 'UTF-8', null, '')
        logger.info('{} --- response: {}', it, response)
        def result = JSON.parseObject(response)
        if (result.getInteger('code') != 0) {
            throw new InsReturnException(InsReturnException.Others, "太保精灵非车险查询错误，${result}")
        }
        nonAutoList.addAll(result.getJSONArray('data') ?: [])
        nonAutoList
    }?.toList()
}


private initPrd(productCode, sourceType, hashCode, decodeHashCode, org.slf4j.Logger logger, CloseableHttpClient httpClient, String key2) {

    def initPrdV1URL = "https://dwx.cpic.com.cn/nonvehicle/napi/insurance/initPrd";
    // def initPrdV2URL = "https://dwx.cpic.com.cn/nonvehiclev2/napi/insurance/initPrd";
    def header = ['Referer': "https://dwx.cpic.com.cn/cxNew/dwx/mobilemsb/?plcPlanCode=${productCode}&sourceType=${sourceType}&hashCode=${decodeHashCode}&version=undefined&isProductList=1".toString()]

    for (uri in [initPrdV1URL]) {
        def url = "${uri}?plcPlanCode=${productCode}&sourceType=${sourceType}&hashCode=${hashCode}&version=V001&isProductList=1".toString()
        def initPrdResponse = HttpSender.doGet(httpClient as CloseableHttpClient, url, header, null, 'UTF-8', null, false)
        initPrdResponse = (initPrdResponse as String).replaceAll('"', '')
        def retDecrypted = JSON.parseObject(aesDecrypt(key2, initPrdResponse))
        logger.info("request url: {}, response: {}", url, retDecrypted)
        if (retDecrypted?.get("code") != 14001) {
            return retDecrypted
        }
    }
    throw new InsReturnException(InsReturnException.Others, "太平洋精灵商品初始化失败，产品代码：${productCode}")
}

'2'