package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.LimitUtils
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotCar_2011

@BaseScript BaseScript_Http_Enq _

def accidentApplyJson = JSON.parseObject(autoTask.applyJson)
entity.misc = (entity.misc ?: [:]) << accidentApplyJson.sq
// 驾意险判断
def productCode = entity?.misc?.nonMotor?.productCode
def dict = new robot_2011_dict()
autoTask.tempValues.productCode = dict.nonMotorProductCodeMap(productCode)
//报价险种报文构建
String calculateParamJson = autoTask.tempValues.calculateParamJson

def redata = null
if (StringUtil.isEmpty(calculateParamJson) || "true" != autoTask.tempValues.finishCalculate) {
    def reqBody = RobotCar_2011.makeCalculate(autoTask, entity)
    redata = reqBody.redata as Map<String, Object>
    insureNonMotor(productCode, redata)
    calculateParamJson = JSON.toJSONString(reqBody)
    autoTask.tempValues.put("calculateParamJson", calculateParamJson)
} else {
    def reqBody = JSON.parseObject(calculateParamJson)
    redata = reqBody.redata as Map<String, Object>
}

addNeedResetStartDateFlag(redata)

//某些地区太保限制一分钟内点击报价按钮次数
limitQuote(autoTask)

head Robot2011Util.getDefaultHead(tempValues, config)

Robot2011Util.genBody(tempValues, calculateParamJson)

/**
 * 限制报价次数
 * @param autoTask
 * @param TbUtil
 */
private void limitQuote(AutoTask autoTask) {
    def TbUtil = new common_2011()
    if (StringUtil.isNoEmpty(autoTask.configs.quoteLimit as String) && TbUtil.isNumeric(autoTask.configs.quoteLimit.toString().trim())) {
        LimitUtils.checkLimitTimes("2011", autoTask.configs.login as String, "quote");
        LimitUtils.inc("2011", autoTask.configs.login as String, "quote");
    }
}

private void insureNonMotor(productCode, Map redata) {
    def accidentNewInsuranceUtil = new robot_2011_new_accident_util()
    def accidentF = accidentNewInsuranceUtil.checkNewInitAccident(autoTask)
    //驾意险 ||新的驾意险
    if (productCode && !accidentF) {
        redata.accident = true
        def accidentInsuranceUtil = new robot_2011_accident_util()
        Map accidentInsureMap = accidentInsuranceUtil.getAccidentInsureMap(autoTask)
        redata.accidentInsuransVo = accidentInsureMap
        autoTask.tempValues.accidentInsurance = [accident: true, accidentInsuransVo: accidentInsureMap]
    }
}

private void addNeedResetStartDateFlag(Map<String, Object> redata) {
    if (tempValues.newCarFlag != '1' && redata?.compulsory && redata?.commercial) {
        def ciStartDate = redata.compulsoryInsuransVo.stStartDate
        def biStartDate = redata.commercialInsuransVo.stStartDate
        if (ciStartDate != biStartDate) {
            tempValues.needResetStartDateFlag = '1'
            tempValues.tempBiStartDate = biStartDate
            tempValues.tempCiStartDate = ciStartDate
        } else {
            tempValues.remove('needResetStartDateFlag')
        }
    }
}