package com.cheche365.bc.entity.hbase;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * HBase auto_task_log 表模型
 * 用于存储任务日志信息，包括基本信息和大文本内容
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AutoTaskLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * HBase表名
     */
    public static final String TABLE_NAME = "auto_task_log";

    public static final String CF_DATA = "data";

    // ========== 基本信息字段 (存储在info列族) ==========

    /**
     * 主键ID，对应auto_task的autoTraceId
     */
    private String autoTraceId;

    /**
     * 单号ID
     */
    private String traceKey;

    // ========== 大文本字段 (存储在content列族) ==========

    /**
     * 申请报文 (大文本)
     */
    private String applyJson;

    /**
     * 回写报文 (大文本)
     */
    private String feedbackJson;

    /**
     * 结果信息 (大文本)
     */
    private String resultStr;

    /**
     * 生成HBase的rowKey
     * 格式：[salt]_[autoTraceId]
     *
     * @return rowKey字符串
     */
    public String generateRowKey() {
        if (autoTraceId == null || autoTraceId.isEmpty()) {
            throw new IllegalArgumentException("autoTraceId不能为空");
        }
        int salt = Math.floorMod(autoTraceId.hashCode(), 10);
        return salt + "_" + autoTraceId;
    }

    /**
     * 获取盐值
     *
     * @return 盐值 (0-9)
     */
    public int getSalt() {
        if (autoTraceId == null || autoTraceId.isEmpty()) {
            return 0;
        }
        return Math.floorMod(autoTraceId.hashCode(), 10);
    }
}
