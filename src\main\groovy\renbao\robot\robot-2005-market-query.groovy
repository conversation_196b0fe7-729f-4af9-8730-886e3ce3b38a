package renbao.robot


import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.utils.sender.HttpSender
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import org.apache.http.impl.client.CloseableHttpClient

import java.time.LocalDate

import static renbao.common.Constants.SUCCESS_CODE
import static renbao.robot.common_market.header
import static renbao.robot.common_market.hostPrefix


@BaseScript BaseScript_Http_Enq _




def policyNo = entity?.bizProposeNum ?: entity?.efcProposeNum
assertNotNull('核保或承保查询交强险商业险投保单号不能全部为空', policyNo)

def commonMarket = new common_market()
def licenseNo = order.carInfo.plateNum
def encryptedPolicyNo = commonMarket.marketEncrypt(policyNo)
def url = hostPrefix(autoTask) + "/khyx/newFront/qth/proposalsearch/query.do?prpallProposalRequestBody.licenseNo=${URLEncoder.encode(licenseNo, 'utf-8')}&prpallProposalRequestBody.businessNo=&prpallProposalRequestBody.policyNo=${URLEncoder.encode(encryptedPolicyNo, 'utf-8')}&prpallProposalRequestBody.insuredName=&prpallProposalRequestBody.frameNo=&prpallProposalRequestBody.operateDateStart=${LocalDate.now().plusMonths(-1).toString()}&prpallProposalRequestBody.operateDate=${LocalDate.now().toString()}&prpallProposalRequestBody.underWriteFlag=99&prpallProposalRequestBody.riskCode=1&prpallProposalRequestBody.queryComCode=&page=1&rows=10" as String
def result = HttpSender.doGet(autoTask.httpClient as CloseableHttpClient, url, header(autoTask) as Map<String, String>, null, "UTF-8",null, false)
def resultObj = JSONObject.parseObject(result as String)
assertTrue('投保单号：' + policyNo + '未获取到投保数据，请检查投保单号是否正确', SUCCESS_CODE == resultObj['statusText'])

assertNotEmpty('投保单号：' + policyNo + '未获取到投保数据，请检查投保单号是否正确', resultObj['data']['list'])

def data = resultObj['data']['list']
def proposeInfo = data[0] as JSONObject
if (proposeInfo['relationUPolicyInfo'] && proposeInfo['relationUPolicyInfo'].toString().contains('EBS')) {
    def ebsArr = proposeInfo['relationUPolicyInfo'].toString().split('\\|')
    entity.misc.nonMotor = entity.misc.nonMotor ?: [:]
    if (ebsArr.size() > 1 && ebsArr[1]) {
        entity?.misc?.nonMotor?.accidentProposeCode = ebsArr[1].trim()
    }
    if (ebsArr.size() > 2 && ebsArr[2]) {
        entity?.misc?.nonMotor?.accidentPolicyCode = ebsArr[2].trim()
    }
}

head(header(autoTask))
proposeInfo['policyNoBI'] = entity?.bizProposeNum
proposeInfo['policyNoCI'] = entity?.efcProposeNum
proposeInfo['sameAsVehicleDamageFlag'] = '0'
policyNo = commonMarket.marketEncrypt(proposeInfo['policyNo'])
proposeInfo['policyNo'] = policyNo
def queryUrlPrefix = hostPrefix(autoTask) + "/khyx/newFront/qth/proposalsearch/view.do?"
def urlParam = commonMarket.mapToUrlParams(proposeInfo)
def queryUrl = queryUrlPrefix + urlParam.toString()
tempValues['queryUrl'] = queryUrl

