package renbao.edi


import com.cheche365.bc.exception.InsReturnException
import org.apache.commons.lang3.StringUtils



def script  = new edi_common_2005()
def response = root
script.commonDeal(response)
if (response?.Header?.responsehead?.response_code != 1) {
    def msg = response?.Header?.responsehead?.error_message
    throw new InsReturnException(msg ? msg.toString() : "核保失败")
} else {
    def undwrtForPassList = response?.Body?.COMMITUDWRTRTN?.BIZ_ENTITY?.UndwrtForPassList
    def efcNo
    def bizNo
    def efcStatus
    def bizStatus
    def efcText
    def bizText
    undwrtForPassList.each {
        def undwrtForPass = it.UndwrtForPass
        undwrtForPass.each {
            if (it.ProposalNo.toString().startsWith("TDAA")) {
                bizNo = it?.ProposalNo?.toString()
                bizStatus = it?.State?.toString()
                bizText = it?.HandleText?.toString()
            } else {
                efcNo = it?.ProposalNo?.toString()
                efcStatus = it?.State?.toString()
                efcText = it?.HandleText?.toString()
            }
        }
    }
    if (StringUtils.isAllBlank(bizStatus, efcStatus)) {
        enquiry['taskStatus'] = '33'
        throw new InsReturnException('提核后保司未返回核保结果！')
    }
    def errorMessage
    if (bizText) {
        errorMessage = '商业险提示：' + bizText
    }
    if (efcText) {
        errorMessage = errorMessage ? (errorMessage + ' 交强险提示：' + efcText) : '交强险提示：' + efcText
    }
    if (bizNo && efcNo) {
        //混保
        if (bizStatus == '3' && efcStatus == '3') {
            enquiry['taskStatus'] = '32'
        }
        else if (['1', '3'].contains(bizStatus) && ['1', '3'].contains(efcStatus)) {
            enquiry['taskStatus'] = '34'
        }
        else {
            enquiry['taskStatus'] = '33'
            enquiry['msg'] = ['2', '8', '9'].contains(bizStatus) ? bizText : efcText
            throw new InsReturnException(enquiry['msg'])
        }

    } else if (efcNo) {
        //单交
        if (efcStatus == '3') {
            enquiry['taskStatus'] = '32'
        }
        else if (efcStatus == '1') {
            enquiry['taskStatus'] = '34'
        }
        else {
            enquiry['taskStatus'] = '33'
            throw new InsReturnException(efcText)
        }
    } else {
        //单商
        if (bizStatus == '3') {
            enquiry['taskStatus'] = '32'
        }
        else if (bizStatus == '1') {
            enquiry['taskStatus'] = '34'
        }
        else {
            enquiry['taskStatus'] = '33'
            throw new InsReturnException(bizText)
        }
    }

}


