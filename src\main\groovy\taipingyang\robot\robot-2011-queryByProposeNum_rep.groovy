package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.model.RuleInfoKey
import com.cheche365.bc.model.car.*
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.tools.BigDecimalUtil
import com.cheche365.bc.tools.StringUtil
import taipingyang.robot.module.Robot2011Util

AutoTask autoTask = autoTask
def TbUtil = new common_2011()
Enquiry entity = (Enquiry) autoTask?.taskEntity;

Map header = new HashMap();
header.put("Content-Type", "application/queryInfoResult;charset=utf-8")
String queryInfoResult = autoTask.backRoot;
JSONObject result = Robot2011Util.initResp(autoTask.tempValues)(queryInfoResult)
//下载电子保单
if (!autoTask.taskType.contains("policyDownload")) {
//被保人
    List<InsurePerson> insuredPersons = entity.order.insuredPersons;
    if (null == insuredPersons || insuredPersons.size() < 1) {
        insuredPersons = new ArrayList<InsurePerson>();
        insuredPersons.add(new InsurePerson())
    }
    InsurePerson insured = insuredPersons.get(0);
    insured.name = TbUtil.getFromJson(result, "result.insureVo.insureName").toString();
    String insureIdentifyType = TbUtil.getFromJson(result, "result.insureVo.insureCerType").toString();
    insureIdentifyType = (String) TbUtil.reMap(autoTask, "Certificate4Return", insureIdentifyType)
    if (StringUtil.isEmpty(insureIdentifyType))
        throw new InsReturnException("被保人证件类型不合法(" + insureIdentifyType + ")，回写失败")
    insured.idCardType = Integer.parseInt(insureIdentifyType)

    def idCardDecode = TbUtil.getFromJson(result, "result.insureVo.insureCerNo").toString()
    if (idCardDecode) {
        idCardDecode = Robot2011Util.rsaDecode(idCardDecode)
        if (!idCardDecode.contains("*")) {
            insured.idCard = idCardDecode
        }
    }

    String mobileDecode = TbUtil.getFromJson(result, "result.insureVo.insureTelephone").toString()
    if (mobileDecode) {
        mobileDecode = Robot2011Util.rsaDecode(mobileDecode)
        if (!mobileDecode.contains("*")) {
            insured.mobile = mobileDecode
        }
    }

    insured.address = TbUtil.getFromJson(result, "result.insureVo.insureAddress").toString()
    entity.order.insuredPersons = insuredPersons;
//投保人
    InsurePerson applicantPerson = entity.order.insurePerson;
    if (null == applicantPerson)
        applicantPerson = new InsurePerson();
    applicantPerson.name = TbUtil.getFromJson(result, "result.holdVo.holderName").toString()
    String appIdentifyType = TbUtil.getFromJson(result, "result.holdVo.holderCerType").toString();
    appIdentifyType = (String) TbUtil.reMap(autoTask, "Certificate4Return", appIdentifyType)
    if (StringUtil.isEmpty(appIdentifyType))
        throw new InsReturnException("投保人证件类型不合法(" + appIdentifyType + ")，回写失败")
    applicantPerson.idCardType = Integer.parseInt(appIdentifyType)

    String idCardDecode2 = TbUtil.getFromJson(result, "result.holdVo.holderCerNo").toString()
    if (idCardDecode2) {
        idCardDecode2 = Robot2011Util.rsaDecode(idCardDecode2)
        if (!idCardDecode2.contains("*")) {
            applicantPerson.idCard = idCardDecode2
        }
    }

    String mobileDecode2 = TbUtil.getFromJson(result, "result.holdVo.holderTelephone").toString()
    if (mobileDecode2) {
        mobileDecode2 = Robot2011Util.rsaDecode(mobileDecode2)
        if (!mobileDecode2.contains("*")) {
            applicantPerson.mobile = mobileDecode2
        }
    }
    applicantPerson.address = TbUtil.getFromJson(result, "result.holdVo.holderAddress").toString()
    entity.order.insurePerson = applicantPerson
//车主信息
    CarOwnerInfo carowner = entity.order.carOwnerInfo;
    if (null == carowner)
        carowner = new InsurePerson();
    carowner.name = TbUtil.getFromJson(result, "result.carInfoVo.carOwnerName").toString()
    String carownerIdentifyType = TbUtil.getFromJson(result, "result.carInfoVo.cerType").toString();
    carownerIdentifyType = (String) TbUtil.reMap(autoTask, "Certificate4Return", carownerIdentifyType)
    if (StringUtil.isEmpty(carownerIdentifyType))
        throw new InsReturnException("车主证件类型不合法(" + carownerIdentifyType + ")，回写失败")
    carowner.idCardType = Integer.parseInt(carownerIdentifyType)

    String idCardDecode3 = TbUtil.getFromJson(result, "result.carInfoVo.cerNo").toString()
    if (idCardDecode3) {
        idCardDecode3 = Robot2011Util.rsaDecode(idCardDecode3)
        if (!idCardDecode3.contains("*")) {
            carowner.idCard = idCardDecode3
        }
    }

    entity.order.carOwnerInfo = carowner
//车信息
    CarInfo carInfo = entity.order.carInfo
    if (null == carInfo)
        carInfo = new CarInfo();
    carInfo.plateNum = TbUtil.getFromJson(result, "result.carInfoVo.plateNO").toString()//号牌
    String licenseType = TbUtil.getFromJson(result, "result.carInfoVo.plateType").toString();
    carInfo.plateType = "大型汽车号牌".equals(licenseType) ? 0 : 1//号牌类型
    carInfo.engineNum = TbUtil.getFromJson(result, "result.carInfoVo.engineNo").toString() //发动机号
    carInfo.vin = TbUtil.getFromJson(result, "result.carInfoVo.carVin").toString() //车架号
    TbUtil.transformCarInfo(carInfo);
    String belongsNatureCode = TbUtil.getFromJson(result, "result.carInfoVo.ownerNature").toString();
    carInfo.carUserType = Integer.parseInt((String) TbUtil.reMap(autoTask, "BelongsNatureCode4Return", belongsNatureCode))
    //所属性质
    String syvehicletypecode = TbUtil.getFromJson(result, "result.carInfoVo.carType").toString();
    carInfo.syvehicletypecode = (String) TbUtil.reMap(autoTask, "syvehicletypecode4Return", syvehicletypecode) //车辆种类
    String useNatureCode = TbUtil.getFromJson(result, "result.carInfoVo.useProperties").toString();
    String carKindCode = "";
    if ("301".equals(useNatureCode)) {//企业非营业用车
        if (StringUtil.isNoEmpty(syvehicletypecode)) {
            if (Integer.parseInt(syvehicletypecode) <= 5) {
                useNatureCode = "10";
                carKindCode = "1";
            } else {
                useNatureCode = "12";
                carKindCode = "2";
            }
        } else
            useNatureCode = "";
    } else
        useNatureCode = (String) TbUtil.reMap(autoTask, "usageAttributeCode4Return", useNatureCode)
    carInfo.useProps = Integer.parseInt(useNatureCode);//使用性质
    String carKindCode4return = "";
    String ckcFlag = "," + useNatureCode + ",";
    if (",6,12,".contains(ckcFlag))//货车
        carKindCode4return = "2";
    else if (",15,16,".contains(ckcFlag))//特种车
        carKindCode4return = "3";
    else//客车
        carKindCode4return = "1";
    carInfo.seatCnt = Integer.parseInt((String) TbUtil.getFromJson(result, "result.carInfoVo.seatCount")) //座位数
    carInfo.fullLoad = new BigDecimal(TbUtil.getFromJson(result, "result.carInfoVo.emptyWeight").toString()) //整备质量
    if (StringUtil.isNoEmpty((String) TbUtil.getFromJson(result, "result.carInfoVo.engineCapacity")))
        carInfo.displacement = new BigDecimal(TbUtil.getFromJson(result, "result.carInfoVo.engineCapacity").toString())
    //排量
    if (StringUtil.isNoEmpty((String) result.result.carInfoVo.tonnage))
        carInfo.modelLoad = new BigDecimal(BigDecimalUtil.multi((String) result.result.carInfoVo.tonnage, "1000", 2))
    //排量
    carInfo.carModelName = TbUtil.getFromJson(result, "result.carInfoVo.modelType").toString() //型号
    String specialVehicleIden = TbUtil.getFromJson(result, "result.carInfoVo.specialVehicleIden").toString();
    carInfo.isTransfer = "1".equals(specialVehicleIden)
    if (carInfo.isTransfer)
        carInfo.transferDate = TbUtil.getDate((String) TbUtil.getFromJson(result, "result.carInfoVo.changeRegisterDate"), "yyyy-MM-dd")
//转移登记日期
    carInfo.price = new BigDecimal(TbUtil.getFromJson(result, "result.carInfoVo.newCarPrice").toString()) //新车购置价
    if (!TbUtil.getFromJson(result, "result.carInfoVo.industryActualPrice").equals(TbUtil.getFromJson(result, "result.carInfoVo.negotiatedValue"))) {
//实际价值与协商价值不等
        carInfo.definedCarPrice = new BigDecimal(TbUtil.getFromJson(result, "result.carInfoVo.negotiatedValue").toString())
        carInfo.carPriceType = 2
    }
    carInfo.firstRegDate = TbUtil.getDate((String) TbUtil.getFromJson(result, "result.carInfoVo.registDate"), "yyyy-MM-dd")
//初登日期
    entity.order.carInfo = carInfo;
    JSONObject carInfoJson = (JSONObject) JSON.toJSON(carInfo)

    BaseSuiteInfo baseSuiteInfo = new BaseSuiteInfo();
//商业险信息 commercialAmount
    if (((autoTask.tempValues.get("ExistBI") && autoTask.tempValues.get("ExistBI")) || autoTask.taskType.contains("policyquery")) && result?.result?.insuranceVo?.commercialAmount) {
        //回写商业险业务评分
        if ("true".equals(autoTask.configs.needExtraInfo)) {
            if (null == entity.order.platformInfo)
                entity.order.setPlatformInfo(new HashMap<String, Object>())
            if (null == entity.order.platformInfo.definition)
                entity.order.platformInfo.put("definition", new HashMap())
            def cpicScore = result?.result?.insuranceVo?.cpicScore
            def totalCpicScore = result?.result?.insuranceVo?.totalCpicScore
            def bizRate = result?.result?.carInfoVo?.carSpreading
            def ncdEcompensationRate = result?.result?.insuranceVo?.commNcdEcompensationRate
            def ncdTotalEcompensationRate = result?.result?.insuranceVo?.ncdTotalEcompensationRate
            if (!((Map) entity.order.platformInfo?.definition).get(PlatformKey.bizScore)) {
                ((Map) entity.order.platformInfo?.definition).put(PlatformKey.bizScore, cpicScore)
            }
            ((Map) entity.order.platformInfo?.definition).put("cpicScore", cpicScore)//商业险太保分
            ((Map) entity.order.platformInfo?.definition).put("totalCpicScore", totalCpicScore)//交商合计太保分
            ((Map) entity.order.platformInfo?.definition).put("application.bizRate", bizRate)//私家车车联网分档
            //商业险含NCD标准预期赔付率
            ((Map) entity.order.platformInfo?.definition).put("application.cexpectClaimRate", ncdEcompensationRate)
            ((Map) entity.order.platformInfo?.definition).put("cexpectClaimRate", ncdEcompensationRate)
            ((Map) entity.order.platformInfo?.definition).put("cpicCexpectClaimRate", ncdEcompensationRate)
            //交商合计含NCD标准保费预期赔付率
            ((Map) entity.order.platformInfo?.definition).put("application.compulsoryCexpectClaimRate", ncdTotalEcompensationRate)
            ((Map) entity.order.platformInfo?.definition).put("compulsoryCexpectClaimRate", ncdTotalEcompensationRate)
            ((Map) entity.order.platformInfo?.definition).put("cpicCompulsoryCexpectClaimRate", ncdTotalEcompensationRate)
            //根据地区来
            def areaList = ["山东", "宁波", "温州"]
            if (areaList.contains(autoTask.configs.areaComCode.toString())) {
                if (entity.order.isEfc() && entity.order.isBiz()) {
                    if (entity.order.platformInfo?.definition) {
                        ((Map) entity.order.platformInfo?.definition).put(PlatformKey.bizScore, totalCpicScore)
                    }
                }
            }

            //回写自主核保及自主渠道系数
            String underwritingRate = TbUtil.getFromJson(result, "result.insuranceVo.underwritingRate");
            String channelRate = TbUtil.getFromJson(result, "result.insuranceVo.channelRate");
            if (underwritingRate || channelRate) {
                ((Map) entity.order.platformInfo?.definition).put("geniusItem.channelDiscount", channelRate);
                ((Map) entity.order.platformInfo?.definition).put("geniusItem.insureDiscount", underwritingRate);
                //商业险预期赔付率
                String syecompensationRate = (String) TbUtil.getFromJson(result, "result.insuranceVo.syecompensationRate");
                if (syecompensationRate)
                    ((Map) entity.order.platformInfo?.definition).put("application.expectLossRatio", syecompensationRate);
            }
        }

        BizSuiteInfo bizSuiteInfo = new BizSuiteInfo();
        String[] dateBI = new String[0];
        dateBI = TbUtil.getFromJson(result, "result.insuranceVo.commercialPeriod").toString().split("至");
        if (dateBI.length > 1) {
            bizSuiteInfo.start = dateBI[0]//商业险起始日期
            bizSuiteInfo.end = dateBI[1] //商业险结束日期
        }
        //商险折扣
        if (result?.result?.insuranceVo?.commercialRate) {
            bizSuiteInfo.discountRate = new BigDecimal(result?.result?.insuranceVo?.commercialRate as String)
        }
        //商业险保单保费合计
        if (result?.result?.insuranceVo?.commercialAmount) {
            bizSuiteInfo.discountCharge = new BigDecimal(result?.result?.insuranceVo?.commercialAmount as String)
        }
        JSONArray riskArray = result.getJSONObject("result").getJSONArray("list");
        BigDecimal discount = BigDecimal.valueOf(result?.result?.insuranceVo?.commercialRate as double)
        //商业险折扣
        Map<String, SuiteDef> suites = new HashMap<>();
        for (int i = 0; i < riskArray.size(); i++) {
            JSONObject riskObj = riskArray.getJSONObject(i);
            SuiteDef suiteDef = new SuiteDef();
            def insCode = riskObj.getString("insuranceCode")
            if (insCode.contains("NEWENERGY")) {
                insCode = insCode.replace("NEWENERGY", "")
            }
            def insType = riskObj.getString("insuranceType")
            def robot_2011_special_util = new robot_2011_special_util()
            String code = TbUtil.reMap(autoTask, "TBmrk2v4return", insCode) ?: TbUtil.reMap(autoTask, "code4Query", insType) ?: robot_2011_special_util.p2codeMap()."$insCode"
            if (!code) {
                continue;
            }
            suiteDef.code = code;
            suiteDef.discountRate = discount
            suiteDef.discountCharge = riskObj.getBigDecimal("insurancePremium")
            String amount = riskObj.getString("insuranceAmount");
            //乘客责任险捉取的保额为 10000元X4座
            if ("Passenger" == code && amount.contains("元"))
                suiteDef.amount = new BigDecimal(amount.split("元")[0])
            else if ("SpecifyingPlant" == code && amount.contains("费率"))
                suiteDef.amount = new BigDecimal(0)
            else if (code in ['RoadsideService', 'SendForInspection']) {
                //客户星级：10星  服务次数：2次
                def times = amount.find(/服务次数：\d+次/)?.find(/\d+/)
                suiteDef.amount = (times ? times.toBigDecimal() : 0d)
            } else if ('VehicleInspection' == code) {
                //客户星级：10星  </br>C档：1次;</br>
                def times = amount.find(/\w档：\d+次/)?.find(/\d+/)
                suiteDef.amount = (times ? times.toBigDecimal() : 0d)
            } else if ('DesignatedDriving' == code) {
                //客户星级：10星  </br>30KM以内：1次  约定免费：0次;</br>
                def times = amount.find(/以内：\d+次/)?.find(/\d+/)
                suiteDef.amount = (times ? times.toBigDecimal() : 0d)
            } else if (StringUtil.isNoEmpty(amount)) {
                amount = amount.replaceAll(/客户星级：\d+星 */, '')
                if (amount.isNumber()) {
                    suiteDef.amount = amount.toBigDecimal()
                } else {
                    suiteDef.amount = (amount.find(/\d+/) ?: '0').toBigDecimal()
                }
            } else {
                suiteDef.amount = new BigDecimal(0)
            }
            suites.put(code, suiteDef);
            //不计免赔
            if (StringUtil.isNoEmpty((String) riskObj.get("nonDeductible"))) {
                SuiteDef suiteDefNcf = new SuiteDef();
                suiteDefNcf.code = "Ncf" + code;
                suiteDefNcf.discountRate = discount
                suiteDefNcf.discountCharge = riskObj.getBigDecimal("nonDeductible")
                suites.put(suiteDefNcf.code, suiteDefNcf);
            }
        }
        bizSuiteInfo.suites = suites
        baseSuiteInfo.bizSuiteInfo = bizSuiteInfo
        JSONObject j = (JSONObject) JSON.toJSON(bizSuiteInfo)
    }

//交强险信息
//该模板是公用模板 应根据抓来的数据去判断 而不是自己臆想,
//竟然有需求？
    if ((entity.efcProposeNum && result?.result?.insuranceVo?.compulsoryAmount) || autoTask.taskType.contains("policyquery")) {
        //回写交强险险业务评分
        if ("true".equals(autoTask.configs.needExtraInfo)) {
            if (null == entity.order.platformInfo)
                entity.order.setPlatformInfo(new HashMap<String, Object>())
            if (null == entity.order.platformInfo.definition)
                entity.order.platformInfo.put("definition", new HashMap())
            //交强险评分 compCpicScore
            ((Map) entity.order.platformInfo?.definition).put(PlatformKey.TRAFFIC_SCORE, TbUtil.getFromJson(result, "result.insuranceVo.compCpicScore"))
            ((Map) entity.order.platformInfo?.definition).put("compCpicScore", TbUtil.getFromJson(result, "result.insuranceVo.compCpicScore"))
            //车船税类型
            String taxType = (String) TbUtil.getFromJson(result, "result.insuranceVo.taxType");
            taxType = TbUtil.reMap(autoTask, "taxDerateType", taxType)
            if (StringUtil.isNoEmpty(taxType))
                ((Map) entity.order.platformInfo?.definition).put(RuleInfoKey.RULE_ITEM_TAX_DERATE_TYPE, taxType);
            //交强险预期赔付率
            String jqecompensationRate = (String) TbUtil.getFromJson(result, "result.insuranceVo.jqecompensationRate");
            if (StringUtil.isNoEmpty(jqecompensationRate))
                ((Map) entity.order.platformInfo?.definition).put("application.expectTrafficLossRatio", jqecompensationRate);
            if (result?.result?.insuranceVo?.totalCpicScore) {
                ((Map) entity.order.platformInfo?.definition).put('application.totalScore', result?.result?.insuranceVo?.totalCpicScore)
            }
        }
        EfcSuiteInfo efcSuiteInfo = new EfcSuiteInfo();
        String[] dateCI = TbUtil.getFromJson(result, "result.insuranceVo.compulsoryPeriod").toString().split("至");
        if (dateCI.length > 1) {
            efcSuiteInfo.start = dateCI[0]//交强险起始日期
            efcSuiteInfo.end = dateCI[1] //交强险结束日期
        }
        efcSuiteInfo.discountCharge = new BigDecimal(TbUtil.getFromJson(result, "result.insuranceVo.compulsoryAmount").toString())
        efcSuiteInfo.amount = new BigDecimal(122000);
        efcSuiteInfo.discountRate = new BigDecimal(TbUtil.getFromJson(result, "result.insuranceVo.syecompensationRate").toString() ?: "1").setScale(1, BigDecimal.ROUND_DOWN)
        baseSuiteInfo.efcSuiteInfo = efcSuiteInfo;
        JSONObject j = (JSONObject) JSON.toJSON(efcSuiteInfo)
        //车船税信息
        TaxSuiteInfo taxSuiteInfo = new TaxSuiteInfo();
        taxSuiteInfo.discountCharge = new BigDecimal(TbUtil.getFromJson(result, "result.insuranceVo.tax").toString())
        baseSuiteInfo.taxSuiteInfo = taxSuiteInfo;
    }
//佣金回写预期赔付率
//商业险预期赔付率
    String ecompensationRate = TbUtil.getFromJson(result, "result.insuranceVo.syecompensationRate");
    entity.order.suiteInfo = baseSuiteInfo;
    entity.totalCharge = new BigDecimal((String) TbUtil.getFromJson(result, "result.insuranceVo.totalPremium"))
//回写新非车险信息
    if (result?.result?.msbList && !autoTask.taskType.contains("autoinsure")) {
        def accidentNewInsuranceUtil = new robot_2011_new_accident_util()
        accidentNewInsuranceUtil.accident(autoTask, null, result?.result?.msbList[0])
    }
    //自核没有去回写非车险单号 2022年3月21日增加自核回写非车单号 承保查询不行
    if (result?.result?.msbList && !autoTask.taskType.contains("approvedquery")) {
        if (!entity?.misc?.nonMotor) {
            if (autoTask?.tempValues?.nonMotor) {
                entity?.misc?.nonMotor = autoTask.tempValues.nonMotor
            } else {
                entity?.misc?.nonMotor = [:]
            }
        }
        entity?.misc?.nonMotor?.accidentProposeCode = entity.bizProposeNum //投保单号
    }

//回写驾意险信息
    if (result.result.accident) {

        def accidentInsuranceUtil = new robot_2011_accident_util()
        def nonMotor = accidentInsuranceUtil.callBackAccident(autoTask, result.result);
    }

    if (result?.result?.insuranceVo?.independentPriceRate) {
        //添加自主定价系数
        if (!entity.order.platformInfo) {
            entity.order.platformInfo = [:]
            entity.order.platformInfo.definition = [:]
        }
        entity.order.platformInfo.definition.selfRate = new BigDecimal(result?.result?.insuranceVo?.independentPriceRate).setScale(4, BigDecimal.ROUND_DOWN).toString()
    }

    def inType = TbUtil.getFromJson(result, "result.carInfoVo.inType")?.toString()
    if (inType) {
        /* (Story#12907)[http://192..1.212/index.php?m=story&f=view&id=12907] start */
        inType = inType == '新保' ? '0' : inType == '续保' ? '1' : inType == '转保' ? '2' : ''
        if (!entity.order.platformInfo) {
            entity.order.platformInfo = [:]
            entity.order.platformInfo.definition = [:]
        }

        entity.order.platformInfo.definition[PlatformKey.application_loyalty] = inType
        entity.order.platformInfo.definition['CpicInsType'] = inType
        /* (Story#12907)[http://192..1.212/index.php?m=story&f=view&id=12907] end */
    }
    autoTask.taskEntity = entity
    autoTask.tempValues.finishQuery = "true"
} else {
    autoTask.tempValues.policyDownloadResult = result
}
