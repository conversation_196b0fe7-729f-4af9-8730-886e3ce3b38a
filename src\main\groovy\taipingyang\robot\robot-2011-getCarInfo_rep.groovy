package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.tools.DateCalcUtil
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.sender.HttpSender
import common.scripts.ScriptUtil
import org.apache.http.impl.client.CloseableHttpClient
import org.slf4j.LoggerFactory
import taipingyang.robot.module.*

import java.time.Instant
import java.time.ZoneId
import java.util.regex.Matcher
import java.util.regex.Pattern

def logger = LoggerFactory.getLogger('robot-2011-getCarInfo_rep')
AutoTask autoTask = autoTask
Enquiry entity = (Enquiry) autoTask?.taskEntity
def TbUtil = new common_2011()
def bjhyVehicleCode = autoTask.configs.areaComCode.toString().contains("北京")
def firstIfpsvehicleCar = ''
boolean ifpsvehicleFlag = autoTask?.configs?.ifpsvehicle == '1'

//2019-12-09 太保官网查车接口升级 在这里只需要把car对象转换，把相关的entity对象的值改变即可->queryGDVehicleInfo:'true'
/**  http://issue.cpic.com.cn/ecar/ecar/queryPlatformBodyByVinAndVehicle
 *
 */
def queryPlatformBodyByVinAndVehicleJson = null
if (ifpsvehicleFlag) {//1168
    queryPlatformBodyByVinAndVehicleJson = queryPlatformBodyByVinAndVehicle(entity, TbUtil, autoTask)
}

//判断查找方式 用车型名称查找与用车型代码查找 返回的数据结构不同
boolean serchByName = !autoTask.tempValues.get("getCarInfoUrl").toString().endsWith("ByVinOther")
//以下为转接

String rbCode = autoTask.tempValues.moldCharacterCode ?: entity.order.carInfo.jyCode//精油车型编码
//初登日期
String enrollDate = DateCalcUtil.getFormatDate(entity.order.carInfo.firstRegDate, "yyyy-MM-dd")
JSONArray cars
JSONObject car
String serchingResult = (String) autoTask?.backRoot

serchingResult = Robot2011Util.decodeBody(autoTask.tempValues, serchingResult)

JSONObject serchingCarResultObj = JSON.parseObject(serchingResult)
if (!(serchingCarResultObj?.result instanceof List)) {
    serchingCarResultObj.result = serchingCarResultObj.result.models
}
if (queryPlatformBodyByVinAndVehicleJson) {
    serchingCarResultObj = queryPlatformBodyByVinAndVehicleJson
}
String msg = (String) TbUtil.getFromJson(serchingCarResultObj, "message.code")
if ("success" != msg) {
    throw new InsReturnException("依照精友车型编码" + rbCode + "未查找到车型")
}
List<String> errCarList = autoTask.tempValues.errCarList
if (!autoTask.tempValues.carsResultList) {
    cars = serchingCarResultObj.result
} else {
    cars = autoTask.tempValues.carsResultList.clone()
    if (null == errCarList || errCarList.size() < 2) {
        cars.addAll(serchByName ? serchingCarResultObj.getJSONArray("result") : serchingCarResultObj.getJSONObject("result").getJSONArray("models"))
    }
}
autoTask.tempValues.put("carsResultList", cars)
//先搜索出全部车型数据，再从中挑选正确车型
autoTask.tempValues.serchingCarResultObjOld = cars
if (serchByName && serchingCarResultObj.getJSONArray("result").size() == 10) {
    //如果最后一页的size为10，则无论页数如何增加搜索结果都为最后一页的内容，因此以每页第一条数据的车型码为标记，不重复则继续翻页
    String mark = serchingCarResultObj.getJSONArray("result").getJSONObject(0).getString("moldCharacterCode")
    boolean turnPage = true

    if (null != autoTask.tempValues.jyCodeMark && serchingCarResultObj.toJSONString().contains(autoTask.tempValues.jyCodeMark))
        turnPage = false
    int getCarIndex = null == autoTask.tempValues.getCarIndex ? 2 : (Integer) autoTask.tempValues.getCarIndex + 1
    //最多取100条数据，避免因截取车型名称出错时，查车模板过多循环
    if (getCarIndex >= 11)
        turnPage = false
    if (turnPage) {
        autoTask.tempValues.jyCodeMark = mark
        JSONObject getCarInfoParam = JSON.parse(autoTask.tempValues.getCarInfoParamJson)
        getCarInfoParam.getJSONObject("meta").put("pageNo", getCarIndex)
        autoTask.tempValues.getCarInfoParamJson = getCarInfoParam.toJSONString()
        autoTask.tempValues.getCarIndex = getCarIndex
        throw new InsReturnException(InsReturnException.AllowRepeat, "即将翻页继续查找车型")
    }
}

int minPrice1 = 0//最低价
int minIndex1 = -1//最低价序号

int minPrice2 = 0//符合车型的最低价
int minIndex2 = -1//符合车型的最低价序号

int minPrice3 = 0//符合车型不符合时间的最低价
int minIndex3 = -1//符合车型不符合时间的最低价序号

int price

String productionDate = ""
String pd = ""
String rm = ""
String ed = ""

for (int i = 0; i < cars.size(); i++) {
    price = cars.getJSONObject(i).getInteger("purchaseValue")
    pd = cars.getJSONObject(i).getString("year")
    ed = StringUtil.isNoEmpty(pd) ? enrollDate.replace("-", "").substring(0, pd.length()) : enrollDate.replace("-", "")
    rm = cars.getJSONObject(i).getString("moldCharacterCode")
    //如果获取到的车型只有一条数据，则直接使用该数据
    if (1 == cars.size()) {
        minPrice2 = price
        minIndex2 = i
        break
    }
    if (ifpsvehicleFlag) {
        def resultList = queryPlatformBodyByVinAndVehicleJson?.result
        //只有一条上面写过了
        //如果获取到的车型列表有多条数据，则通过精友编码进行匹配，若匹配成功则使用匹配到的车型数据，然后进行【交管查车】，然后进行保费计算
        if (queryPlatformBodyByVinAndVehicleJson) {
            def queryPlatformBodyByVinAndVehicleResultJson = TbUtil.getResultByResultList(resultList, entity.order.carInfo.jyCode)
            if (queryPlatformBodyByVinAndVehicleResultJson) {
                firstIfpsvehicleCar = queryPlatformBodyByVinAndVehicleResultJson
                break
            } else {
                //如果获取到的车型列表有多条数据，通过精友编码进行匹配，未匹配成功则默认选择最便宜的车型，然后进行【交管查车】，然后进行保费计算，若价格相同，则选择第一个车型
                def result = resultList.sort {
                    it.purchaseValue
                }
                firstIfpsvehicleCar = result[0]
                break
            }
        }
    }


    if (!autoTask.tempValues.errCarDesc && !ifpsvehicleFlag) {
        if (rm.equals(rbCode)) {
            minPrice2 = price
            minIndex2 = i
            break
        }
    } else {
        if (errCarList.contains(rm)) {
            continue
        }
        String remark = cars.getJSONObject(i).getString("remark")
        if (StringUtil.isEmpty(remark))
            remark = ""
        String remarkWithoutSpace = remark.replace(" ", "")
        if (remark.contains(autoTask.tempValues.errCarDesc) || remarkWithoutSpace.contains(autoTask.tempValues.errCarDesc) || "".equals(autoTask.tempValues.errCarDesc)) {
            //循环选取价格最低者
            if (price < minPrice2 || minPrice2 == 0) {
                minPrice2 = price
                minIndex2 = i
            }
        } else if (price < minPrice3 || minPrice3 == 0) {
            minPrice3 = price
            minIndex3 = i
        }
    }
}
if (minIndex2 == -1 && minIndex3 != -1) {
    minIndex2 = minIndex3
}
if (minIndex2 == -1 && !ifpsvehicleFlag) {
    if (null == autoTask.tempValues.errCarDesc) {
        throw new InsReturnException("依照精友车型编码" + rbCode + "未查找到车型" + entity.order.carInfo.carModelName)
    } else if (cars.size() == 0 && autoTask.tempValues.carModelNamePT.toString().contains(" ")) {
        autoTask.tempValues.carModelNamePT = autoTask.tempValues.carModelNamePT.toString().split(" ")[0]
        JSONObject getCarInfoParam = autoTask.tempValues.getCarObj
        getCarInfoParam.getJSONObject("redata").put("name", autoTask.tempValues.carModelNamePT)
        autoTask.tempValues.put("getCarInfoParamJson", StringUtil.chinesetoUnicode(getCarInfoParam.toJSONString()))
        throw new InsReturnException(InsReturnException.AllowRepeat, "修改车型名称为" + autoTask.tempValues.carModelNamePT + "继续查找车型")
    } else {
        throw new InsReturnException("重选车型失败，依照提示 " + autoTask.tempValues.carModelNamePT + " " + autoTask.tempValues.errCarDesc + " 未查找到车型")
    }
} else {
    //minIndex2=0
    car = firstIfpsvehicleCar ?: cars.getJSONObject(minIndex2)
    autoTask.tempValues.moldCharacterCode = car.moldCharacterCode
}
if (autoTask.tempValues.hyVehicleCode) {
    car = autoTask.tempValues.serchingCarResultObjOld.find {
        it.hyVehicleCode == autoTask.tempValues.hyVehicleCode.toString()
    }
    //没查到车，自核需要返回异常，报价和暂存取新车购置价最便宜那辆
    if (!car) {
        if (autoTask.taskType.contains("autoinsure")) {
            throw new InsReturnException(InsReturnException.Others, "北京地区根据hyVehicleCode:" + autoTask.tempValues.hyVehicleCode + "未找到正确车型！无法提核")
        } else {
            car = autoTask.tempValues.serchingCarResultObjOld.sort {
                it.purchaseValue
            }[0]
        }
    }
}

if (("true" == autoTask.configs.needQueryPureriskAndVehicleInfo || bjhyVehicleCode) && !autoTask.tempValues.hyVehicleCode) {
    JSONObject jsonObject = (JSONObject) TbUtil.getQueryPureriskAndVehicleInfoParam("")
    jsonObject.getJSONObject("redata").put("plateNo", "新车未上牌".equals(entity.order.carInfo.plateNum) ? "LS" + entity.order.carInfo.engineNum.substring(0, 5) : entity.order.carInfo.plateNum)
    jsonObject.getJSONObject("redata").put("carVIN", entity.order.carInfo.vin)
    jsonObject.getJSONObject("redata").put("engineNo", entity.order.carInfo.engineNum)
    jsonObject.getJSONObject("redata").put("stRegisterDate", DateCalcUtil.getFormatDate(entity.order.carInfo.firstRegDate, "yyyy-MM-dd"))
    jsonObject.getJSONObject("redata").put("moldCharacterCode", car.get("moldCharacterCode"))
    jsonObject.getJSONObject("redata").put("modelCode", car.hyVehicleCode)
    if (["D6", "D8", "D12"].contains(car?.jyFuelType)) {
        //新能源
        jsonObject.getJSONObject("redata").put("usageType", "5")
        jsonObject.getJSONObject("redata").put("plateType", "52")
    }
    if (entity.order.carInfo.isNew)
        jsonObject.getJSONObject("redata").put("stInvoiceDate", DateCalcUtil.getFormatDate(entity.order.carInfo.firstRegDate, "yyyy-MM-dd"))

    def qpavResult = HttpSender.doPostWithRetry(
            5, autoTask?.httpClient as CloseableHttpClient,
            true,
            RobotConstant_2011.URL.queryPureriskAndVehicleInfoURL,
            Robot2011Util.genBody(autoTask.tempValues, jsonObject.toJSONString()),
            null,
            Robot2011Util.getDefaultHead(autoTask.tempValues, autoTask.configs),
            "UTF-8",
            null,
            "")
    qpavResult = Robot2011Util.decodeBody(autoTask.tempValues, qpavResult)
    JSONObject re = null
    if (qpavResult instanceof JSONObject) {
        re = (JSONObject) qpavResult
    } else {
        re = JSON.parseObject(qpavResult)
    }
    if ("failed".equals(TbUtil.getFromJson(re, "message.code"))) {
        throw new InsReturnException("查询纯风险保费失败：" + TbUtil.getFromJson(re, "message.message"))
    }

    JSONArray models = re.getJSONObject("result").getJSONArray("models")
    double minPrice = -1
    int index = -1
    for (int i = 0; i < models.size(); i++) {
        if (i == 0 || models.getJSONObject(i).getDoubleValue("purchaseValue") < minPrice) {
            minPrice = models.getJSONObject(i).getDoubleValue("purchaseValue")
            index = i
        }
    }
    if (index == -1)
        throw new InsReturnException("精灵查询纯风险保费出错")
    JSONObject model = models.getJSONObject(index).clone()
    if (!model.get("moldCharacterCode").equals(car.get("moldCharacterCode"))) {
        autoTask.tempValues.moldCharacterCode = model.get("moldCharacterCode")
        throw new InsReturnException(InsReturnException.AllowRepeat, "平台提示车型不一致，即将以车型码" + model.get("moldCharacterCode") + "重选车型")
    }
    if (model.hyVehicleCode != car.hyVehicleCode) {
        autoTask.tempValues.hyVehicleCode = model.hyVehicleCode
        throw new InsReturnException(InsReturnException.AllowRepeat, "自动纠正以平台返回车型:" + model.hyVehicleCode + "重选车型报价")
    }
    dealPlatformVo(model, autoTask)
}

autoTask.tempValues.put("finishSerchingCar", "true")

//以上为获取车辆信息,car

//平台信息返回 报价前
ptMsg(autoTask, car, entity)
//模板输出
output(autoTask, entity, car)

String reductionType = car.getString("taxMark")

//String quotationNo = requestNormalQuickSave(TbUtil, autoTask, car, enrollDate, reductionType, entity, logger)
//autoTask.tempValues.put("quotationNo", quotationNo)

//获取非车代理人参数
reqQueryMsbAgent(autoTask)
//暂存
reqQuickSave(autoTask, entity, logger)
//
JSONObject jj = JSON.parseObject("{\"meta\":{},\"redata\":{\"reductionType\":\"0\",\"stRegisterDate\":\"2016-12-08\"}}")
jj.getJSONObject("redata").put("reductionType", reductionType)
jj.getJSONObject("redata").put("stRegisterDate", enrollDate)
String queryTaxRuleParamJson = StringUtil.chinesetoUnicode(jj.toJSONString())
queryTaxRuleParamJson = Robot2011Util.genBody(autoTask.tempValues, queryTaxRuleParamJson)
String queryTaxResult = Robot2011Util.postWithRetry3Times(
        autoTask?.httpClient as CloseableHttpClient,
        RobotConstant_2011.URL.getTaxTypeURL,
        queryTaxRuleParamJson,
        autoTask.tempValues,
        autoTask.configs
)
queryTaxResult = Robot2011Util.decodeBody(autoTask.tempValues, queryTaxResult)
if (!queryTaxResult.startsWith("{")) {
    throw new InsReturnException("查找车船税类型失败")
}
boolean taxRestFulFlag = true
if (queryTaxResult) {
    def queryTaxResultJson = JSON.parseObject(queryTaxResult)
    if (queryTaxResultJson?.result?.taxType == 'C') {
        taxRestFulFlag = false
    }
}

boolean shanDongFlag = autoTask.configs.areaComCode == '山东' && taxRestFulFlag
if (queryTaxResult.contains("taxType") && queryTaxResult.contains("deductionDueCode") && shanDongFlag) {
    JSONObject result = JSON.parseObject(queryTaxResult)
    JSONObject taxTypeObj = result.getJSONObject("result")
    autoTask.tempValues.taxTypeObj = taxTypeObj
}

backPlatformVoInShanghai(autoTask, autoTask.tempValues.quotationNo as String, TbUtil)
autoTask.reqHeaders.put("Content-Type", "application/json;charset=utf-8")
autoTask.params.clear()

//需求9494 北京驾意险前置
//非车前置或者地区设置 都可进该流程
if (autoTask.tempValues['nonMotorCalculate']) {
} else {
    if ("1".equals(autoTask?.configs?.processControl) || ["110000", "110100", "441900"].contains(entity?.order?.insureArea?.city?.toString())) {
        //假如配置了 0-预报价不能走这种逻辑
        if (!"0".equals(autoTask?.configs?.processControl?.toString())) {
            def accidentNewInsuranceUtil = new robot_2011_new_accident_util()
            def accidentF = accidentNewInsuranceUtil.checkNewInitAccident(autoTask)
            if (accidentF) {
                //走了前置就不要走后置非车
                autoTask.tempValues.again = "true"
                //走了前置就不要走预报价
                autoTask.tempValues.doPreCalculateFlag = true
//                def robot_2011_special_util = new robot_2011_special_util()
//                robot_2011_special_util.doSaveInsureInfo(autoTask, entity)
                accidentNewInsuranceUtil.accident(autoTask, autoTask?.tempValues?.quotationNo?.toString(), null)
                autoTask.tempValues['nonMotorCalculate'] = true
            }
        }
    }
}

/**
 * 平台信息
 * @param autoTask
 * @param car
 * @param entity
 */
private void ptMsg(AutoTask autoTask, JSONObject car, Enquiry entity) {
    JSONObject PTMsg = null == autoTask.tempValues.get("PTMsg") ? new JSONObject() : (JSONObject) autoTask.tempValues.get("PTMsg")
    PTMsg.put("carBrandName", car.getString("name"))//车型
    PTMsg.put("price", car.getString("purchaseValue"))//新车购置价
    PTMsg.put("carModelDate", car.getString("year"))//上市年份
    PTMsg.put("seatCnt", entity.order.carInfo.seatCnt.toString())//核定载客
    PTMsg.put("modelLoad", StringUtil.isNoEmpty(car.getString("tonnage")) ? car.getString("tonnage") : "0")//核定载质量
    PTMsg.put("fullLoad", StringUtil.isNoEmpty(car.getString("fullWeight")) ? car.getString("fullWeight") : String.valueOf(entity.order.carInfo.fullLoad))
    //整备质量
    PTMsg.put("displacement", StringUtil.isNoEmpty(car.getString("displacement")) ? car.getString("displacement") : "0")
    //排气量
    PTMsg.put("modelCode", car.getString("moldCharacterCode"))//精友车型编码
    PTMsg.put("jyCode", car.hyVehicleCode)//行业车型编码
    //todo 需求 1979
    if (car.tpyRiskflagName) {
        PTMsg.tpyRiskflagName = car.tpyRiskflagName
        autoTask.tempValues.tpyRiskflagName = car.tpyRiskflagName
    }

    if (null != autoTask.tempValues.pureRiskPremium) {
        PTMsg.put(PlatformKey.quoteItems_ruleItem_basicRiskPremium, autoTask.tempValues.pureRiskPremium)
    }
    autoTask.tempValues.put("PTMsg", PTMsg)
}

/**
 * 构建快速保存参数
 * @param TbUtil
 * @param autoTask
 * @return
 */
private static JSONObject makeQuickSaveParam(common_2011 TbUtil, AutoTask autoTask) {
    JSONObject quickSaveParam = TbUtil.getQuickSaveParam(autoTask.configs.areaComCode as String)
    quickSaveParam?.redata?.fleetAgreementNo = ""
    if (autoTask?.configs?.busiInsuranceCode) {//8317
        def robot_2011_util = new robot_2011_util()
        quickSaveParam?.redata?.busiInsurance = autoTask?.tempValues?.insuranceCode ?: (autoTask.tempValues.insuranceCode = robot_2011_util.getCodeVBusiInsurance(autoTask))
    }
    quickSaveParam
}

private static String handleFuelType(AutoTask autoTask, String fuelType) {
    if ("北京" == autoTask.configs.areaComCode) {
        //北京地区能源类型编码比较特殊
        String regEx = '^-?[0-9]+$'
        Pattern pat = Pattern.compile(regEx)
        Matcher mat = pat.matcher(fuelType)
        if (mat.find()) {
            fuelType = "1" == fuelType ? "C" : "A"
        }
    }
    return fuelType
}

private static String handleVehicleRegisterAddress(AutoTask autoTask) {
    if (StringUtil.isNoEmpty((String) autoTask.configs.vehicleRegisterAddress)) {//福建需要填车籍地
        String vehicleRegisterAddress = "350000"
        String getVehicleRegisterAddressParam = "{\"meta\":{},\"redata\":{\"parentId\":\"681186\"}}"
        String url = "https://issue.cpic.com.cn/ecar/ecar/queryVehicleRegisterAddress"
        boolean firstSuccess = true
        def firstResult = HttpSender.doPostWithRetry(5, autoTask?.httpClient as CloseableHttpClient, true, url,
                Robot2011Util.genBody(autoTask.tempValues, getVehicleRegisterAddressParam),
                null,
                Robot2011Util.getDefaultHead(autoTask.tempValues, autoTask.configs),
                "UTF-8", null, "")
        firstResult = Robot2011Util.decodeBody(autoTask.tempValues, firstResult)
        JSONObject resultObj = firstResult instanceof JSONObject ? (JSONObject) firstResult : JSON.parse((String) firstResult)
        for (int i = 0; i < ((JSONArray) resultObj.result.codeViewList).size(); i++) {
            JSONObject j = resultObj.getJSONObject("result").getJSONArray("codeViewList").getJSONObject(i)
            if (j.getString("name").contains((String) autoTask.configs.vehicleRegisterAddress)) {
                getVehicleRegisterAddressParam = getVehicleRegisterAddressParam.replace("681186", j.getString("parentId"))
                vehicleRegisterAddress = vehicleRegisterAddress + "." + j.getString("code")
                break
            }
            if (i + 1 == resultObj.getJSONObject("result").getJSONArray("codeViewList").size() && getVehicleRegisterAddressParam.contains("681186")) {
                firstSuccess = false
            }
        }
        if (firstSuccess) {
            def secondResult = HttpSender.doPostWithRetry(5, autoTask?.httpClient as CloseableHttpClient, true, url,
                    Robot2011Util.genBody(autoTask.tempValues, getVehicleRegisterAddressParam),
                    null,
                    Robot2011Util.getDefaultHead(autoTask.tempValues, autoTask.configs),
                    "UTF-8", null, "")
            secondResult = Robot2011Util.decodeBody(autoTask.tempValues, secondResult)
            resultObj = firstResult instanceof JSONObject ? (JSONObject) secondResult : JSON.parse((String) secondResult)
            if (resultObj.getJSONObject("result").getJSONArray("codeViewList").size() > 0) {
                JSONObject j = resultObj.getJSONObject("result").getJSONArray("codeViewList").getJSONObject(0)
                if (StringUtil.isNoEmpty((String) j.get("code"))) {
                    vehicleRegisterAddress = vehicleRegisterAddress + "." + j.getString("code")
                    return vehicleRegisterAddress
                }
            }
        }
    }
    return null
}

private static JSONObject queryPlatformBodyByVinAndVehicle(Enquiry entity, common_2011 TbUtil, AutoTask autoTask) {
    def ifPsVehicleStr = '{"meta":{"pageNo":1},"redata":{"plateNo":"粤S9K95U","carVIN":"LGBH52E05JY908044","engineNo":"698264J","stRegisterDate":"2019-01-25","vehicleType":"01","usage":"101"}}'
    def ifPsVehicleJson = JSON.parseObject(ifPsVehicleStr)
    def useProps = entity.order.carInfo.useProps.toString()
    ifPsVehicleJson.redata.plateNo = entity.order.carInfo.plateNum
    ifPsVehicleJson.redata.engineNo = entity.order.carInfo.engineNum
    ifPsVehicleJson.redata.carVIN = entity.order.carInfo.vin
    ifPsVehicleJson.redata.stRegisterDate = Instant.ofEpochMilli(entity.order.carInfo.firstRegDate.getTime()).atZone(ZoneId.systemDefault()).toLocalDate()
    ifPsVehicleJson.redata.vehicleType = TbUtil.getCarVehicleType(useProps,
            entity.order.carInfo.modelLoad,
            entity.order.carInfo.seatCnt,
            entity.order.carInfo.plateNum,
            entity.order.carInfo.carModelName)
    ifPsVehicleJson.redata.usage = TbUtil.reMap(autoTask, "usageAttributeCode", useProps)

    def queryPlatformBodyByVinAndVehicleResult = HttpSender.doPostWithRetry(5, autoTask?.httpClient as CloseableHttpClient, true,
            RobotConstant_2011.URL.queryPlatformBodyByVinAndVehicle,
            Robot2011Util.genBody(autoTask.tempValues, ifPsVehicleJson.toJSONString()),
            null,
            Robot2011Util.getDefaultHead(autoTask.tempValues, autoTask.configs),
            "UTF-8", null, "")
    queryPlatformBodyByVinAndVehicleResult = Robot2011Util.decodeBody(autoTask.tempValues, queryPlatformBodyByVinAndVehicleResult)
    return JSON.parseObject(queryPlatformBodyByVinAndVehicleResult)
}

/**
 * 实际价值计算
 * @param entity
 * @param autoTask
 * @param car
 * @param TbUtil
 * @param vehiclePurpose
 * @param enrollDate
 * @return
 */
private static String actualValueCalc(Enquiry entity, AutoTask autoTask, JSONObject car, common_2011 TbUtil, String vehiclePurpose, String enrollDate) {
    String actualValue
    String start = StringUtil.isNoEmpty(entity.order?.suiteInfo?.bizSuiteInfo?.start) ? entity.order.suiteInfo.bizSuiteInfo.start : entity.order.suiteInfo.efcSuiteInfo.start
    start = start.substring(0, 10)
    try {
        //新能源实际价值
        if (autoTask?.tempValues?.NEFlag) {
            autoTask?.tempValues?.carPowerType = car.getString("carPowerType")
            Object res = TbUtil.makeInvocable().invokeFunction(
                    "getRateForNewEnergy",
                    TbUtil.reMap(autoTask, "usageAttributeCode", entity.order.carInfo.useProps.toString()) as String,
                    vehiclePurpose,
                    car.getDoubleValue("purchaseValue"),
                    autoTask?.tempValues?.carPowerType ?: "")
            actualValue = TbUtil.getActualAmount(enrollDate,
                    start,
                    Double.valueOf(res?.toString()),
                    car.getDoubleValue("purchaseValue"),
                    autoTask)
        } else {
            actualValue = TbUtil.getActualAmount(enrollDate,
                    start,
                    TbUtil.getDeprecateRate(TbUtil.reMap(autoTask, "usageAttributeCode", entity.order.carInfo.useProps.toString()) as String, vehiclePurpose, autoTask),
                    car.getDoubleValue("purchaseValue"),
                    autoTask)
        }
    } catch (Exception e) {
        throw new InsReturnException("计算车辆实际价值失败")
    }
    actualValue
}

private String getHolderTelephone(Enquiry entity, AutoTask autoTask, common_2011 TbUtil) {
    String holderTelphone = (String) entity.order.carOwnerInfo.mobile
    if (StringUtil.isEmpty(holderTelphone)) {
        holderTelphone = autoTask.configs.get("defaultPhone")
    }
    if (StringUtil.isEmpty(holderTelphone)) {
        holderTelphone = TbUtil.getMobile(autoTask)
    }
    holderTelphone
}

static String getUsageSubdivs(String usage, String uc) {
    String usageSubdivs = ""
    //企业非营业用车
    if ("301" == usage)
        usageSubdivs = "10" == uc ? "23" : "63"//使用性质细分 13
    //营业货车
    else if ("601" == usage)
        usageSubdivs = "73"//使用性质细分 13
    //党政机关用车
    else if ("201" == usage)
        usageSubdivs = "13"
    //公路客运
    else if ("502" == usage)
        usageSubdivs = "51"
    //公路客运
    else if ("502" == usage)
        usageSubdivs = "51"
    //预约出租车
    else if ("17" == uc)
        usageSubdivs = "34"
    //出租车
    else if ("2" == uc)
        usageSubdivs = "31"
    return usageSubdivs
}

private void putQuickSaveParam(
        JSONObject redata,
        JSONObject car,
        AutoTask autoTask,
        String enrollDate,
        String reductionType,
        Enquiry entity,
        common_2011 TbUtil) {
    //能源类型
    redata.put("fuelType", handleFuelType(autoTask, car.getString("fuelType")))
    redata.put("moldCharacterCode", car.get("moldCharacterCode"))//精友车型编码
    redata.put("power", car.get("power"))
    redata.put("producingArea", car.get("producingArea"))//产地
    redata.put("seatCount", entity.order.carInfo.seatCnt?.toString())
    redata.put("shortcutCode", null != autoTask.tempValues.shortcutCode ? autoTask.tempValues.shortcutCode : car.get("shortcutCode"))
    redata.put("purchasePrice", car.get("purchaseValue")?.toString())//车价
    redata.put("emptyWeight", entity.order.carInfo.fullLoad)//整备质量
    redata.put("carVIN", entity.order.carInfo.vin)//车架号
    redata.put("engineNo", entity.order.carInfo.engineNum)//发动机号

    //如进口车则指定车型名称
    String factoryType = car.getString("name")
    def registerModelNameFlag = StringUtil.isNoEmpty(entity.misc?.supplyParam?.registerModelName as String)
    if (registerModelNameFlag) {
        if (null != entity.order.suiteInfo.efcSuiteInfo) {
            factoryType = entity.misc?.supplyParam?.registerModelName
        }
        if (null != entity.order.suiteInfo.bizSuiteInfo) {
            autoTask.tempValues.put("needClause", "true")
        }
    }
    redata.put("factoryType", factoryType)//车型名称
    redata.put("modelType", car.get("name"))//车型名称
    if (car.containsKey("jyFuelType")) {
        redata.put("jyFuelType", car.get("jyFuelType"))//能源类型
    }
    //排量 如纯电动车则填0
    redata.put("engineCapacity", !"D6".equals(car.get("jyFuelType")) ? entity.order.carInfo.displacement : 0)
    redata.put("oriEngineCapacity", (0 == redata.getDoubleValue("engineCapacity") ? "" : redata.get("engineCapacity")).toString())
    if (redata.containsKey("vehiclePowerJY")) {
        redata.put("vehiclePowerJY", car.get("vehiclepowerjy"))
    }
    if (redata.containsKey("hfStartTime")) {
        redata.put("hfStartTime", car.get("hfStartTime"))
    }
    if (redata.containsKey("hfEndTime")) {
        redata.put("hfEndTime", car.get("hfEndTime"))
    }

    if (car.containsKey("power")) {
        redata.put("power", car.get("power"))//功率
    }
    if ("新车未上牌" == entity.order.carInfo.plateNum) {
        redata.put("plateNo", "LS" + entity.order.carInfo.vin.substring(entity.order.carInfo.vin.length() - 6))
        redata.put("plateless", true)
    } else {
        redata.put("plateNo", entity.order.carInfo.plateNum)
    }

    String plateType = null != autoTask.tempValues.plateTypeJg ? autoTask.tempValues.plateTypeJg : TbUtil.reMap(autoTask, "LicenseType", String.valueOf(entity.order.carInfo.plateType))

    redata.put("plateType", plateType)//号牌类型
    redata.put("plateColor", TbUtil.reMap(autoTask, "PlateColor", String.valueOf(entity.order.carInfo.plateType)))//号牌底色
    redata.put("stRegisterDate", enrollDate)//初登日期
    redata.put("stCertificationDate", enrollDate)//发证日期

    if (entity.order.carInfo.isTransfer) {
        redata.put("specialVehicleIden", "1")//过户车标识
        redata.put("stChangeRegisterDate", DateCalcUtil.getFormatDate(entity.order.carInfo.transferDate, "yyyy-MM-dd"))
        //转移登记日期
    }
    if (null != entity.order.carInfo.modelLoad) {
        redata.put("tonnage", String.valueOf(entity.order.carInfo.modelLoad / 1000))//吨位
    }
    redata.put("ownerName", entity.order.carOwnerInfo.name)
    redata.put("relationship", "1")


    String certType = TbUtil.reMap(autoTask, "Certificate", entity.order.carOwnerInfo.idCardType.toString())
    certType = TbUtil.getCardType(entity, entity.order.carOwnerInfo.idCardType.toString())
    if (StringUtil.isEmpty(certType))
        throw new InsReturnException("车主证件类型(" + certType + ")不合法")
    redata.put("certType", certType)
    //证件类型填营业执照时 需填写证件有效期
    if ("11".equals(redata.certType) && StringUtil.isNoEmpty((String) autoTask.configs.stCertificateValidity)) {
        redata.put("stCertificateValidity", autoTask.configs.stCertificateValidity)
    }
    redata.put("certNo", entity.order.carOwnerInfo.idCard)
    redata.put("ownerProp", TbUtil.reMap(autoTask, "BelongsNatureCode", entity.order.carInfo.carUserType.toString()))
    //车辆用户类型
    redata.put("usage", TbUtil.reMap(autoTask, "usageAttributeCode", entity.order.carInfo.useProps.toString()))

    //北京地区 bug 16952 车主为企业，被保人为个人 需要修改使用性质为 家庭自用车
    //2022年5月9日 bug 23782 注释掉
    //def beijingUseProps = (entity.order.carOwnerInfo.idCardType == 8) && (entity.order.insuredPersons[0].idCardType == 0) && autoTask.configs.areaComCode == "北京"
    //if (beijingUseProps) {
    //    redata.usage = "101"
    //}

    String uc = entity.order.carInfo.useProps.toString()//使用性质原始值
    //太保的使用性质编码
    String usage = TbUtil.reMap(autoTask, "usageAttributeCode", entity.order.carInfo.useProps.toString())
    redata.put("usageSubdivs", getUsageSubdivs(usage, uc))
    def robot_2011_special_util = new robot_2011_special_util()
    if ("15" == uc || "16" == uc) {
        String usageSubdivs = robot_2011_special_util.getSpecialCarTypeCode(entity.order.carInfo.carModelName)
        autoTask.tempValues.usageSubdivs = usageSubdivs
        redata.put("usageSubdivs", usageSubdivs)
    }

    //flag、车辆种类、车辆用途、车船税交管车辆类型（宁波）
    def (flag, vehiclePurpose, vehicleType, taxVehicleCode) = RobotDict_2011.getVehicle(autoTask, entity)
    //计算交强险原价
    String efcOrgCharge = TbUtil.getEfcOrgCharge(uc, flag)
    if (StringUtil.isNoEmpty(efcOrgCharge)) {
        autoTask.tempValues.efcOrgCharge = efcOrgCharge
    }
    redata.put("vehiclePurpose", vehiclePurpose)
    redata.put("vehicleType", vehicleType)
    if (StringUtil.isNoEmpty(taxVehicleCode)) {
        autoTask.tempValues.put("taxVehicleCode", taxVehicleCode)
    }


    if (null != autoTask.tempValues.get("vehicleStyle")) {
        redata.put("vehicleStyle", autoTask.tempValues.get("vehicleStyle"))
    }

    if (autoTask?.tempValues?.vehicleModel) {
        redata.put("pmVehicleModel", autoTask?.tempValues?.vehicleModel)
    }
    if (autoTask?.tempValues?.pmVehicleUsage) {
        redata.put("pmVehicleUsage", autoTask?.tempValues?.pmVehicleUsage)
    }
    if (autoTask?.tempValues?.isQueryJg) {
        redata.put("isQueryJg", autoTask?.tempValues?.isQueryJg)
    }
    if (autoTask?.tempValues?.illegalBehavior) {
        redata.put("tpyRiskflagName", autoTask?.tempValues?.illegalBehavior)
    }

    String actualValue = actualValueCalc(entity, autoTask, car, TbUtil, vehiclePurpose, enrollDate)
    autoTask.tempValues.actualValue = actualValue
    redata.put("actualValue", actualValue)//车辆实际价值
//carPriceType 车价选择 0-最低、1-最高、2-自定义
//协商实际价值
    redata.put("negotiatedValue", entity.order.carInfo.carPriceType == 2 ? entity.order.carInfo.definedCarPrice : actualValue)

    redata.put("reductionType", reductionType)//税收类型


    if (StringUtil.isNoEmpty((String) autoTask.configs.get("vehicleInspection"))) {
        //验车方式 01免验 02已验车合格 03待验车
        redata.put("vehicleInspection", autoTask.configs.get("vehicleInspection"))
    }
    if (StringUtil.isNoEmpty((String) autoTask.configs.get("globalType"))) {
        //综合类型信息
        redata.put("globalType", autoTask.configs.get("globalType"))
    }
    if ("true".equals(autoTask.configs.get("needHolderTelphone"))) {//报价时需要手机号码
        String holderTelphone = getHolderTelephone(entity, autoTask, TbUtil)
        redata.put("holderTelphone", holderTelphone)
    }

    if (StringUtil.isNoEmpty((String) autoTask.configs.actualSalesPerson)) {
        //福建需要填销售人员名称
        if ("true".equals(autoTask.configs.needSerchSaler) && StringUtil.isNoEmpty(entity.order.agentInfo.name)) {
            redata.put("actualSalesPerson", entity.order.agentInfo.name)
        } else {
            redata.put("actualSalesPerson", autoTask.configs.actualSalesPerson)
        }
    }
    String vehicleRegisterAddress = handleVehicleRegisterAddress(autoTask)
    if (vehicleRegisterAddress) {
        redata.put("vehicleRegisterAddress", vehicleRegisterAddress)
    }

//导入合作伙伴信息
    redata.put("cooperatorCode", '')
    redata.put("cooperatorName", '')
    if (null != autoTask.tempValues.cooperatorVo) {
        redata.put("cooperatorCode", autoTask.tempValues.cooperatorVo.cooperatorCode)
        redata.put("cooperatorName", autoTask.tempValues.cooperatorVo.cooperatorName)
    }

    if (null != autoTask.tempValues.get("sellerVos")) {//销售人员信息
        JSONArray sellerVos = (JSONArray) autoTask.tempValues.get("sellerVos")
        StringBuffer salesPerson = new StringBuffer()
        for (int i = 0; i < sellerVos.size(); i++) {
            String saleName = sellerVos.getJSONObject(i).getString("name")
            if (StringUtil.isNoEmpty(saleName))
                salesPerson.append(saleName + ",")
        }
        if (salesPerson.length() > 0)
            salesPerson.delete(salesPerson.length() - 1, salesPerson.length())

        redata.put("sellerVos", sellerVos)
        redata.put("salesPerson", salesPerson.toString())
    }

//上海太保特有纯风险保费节点
    if (null != autoTask.tempValues.platformVo) {
        redata.put("platformVo", autoTask.tempValues.platformVo)
    }
//上海新车要求填写 购车发票日期
    if (entity.order.carInfo.isNew && "上海".equals(autoTask.configs.areaComCode)) {
        redata.put("stInvoiceDate", DateCalcUtil.getFormatDate(entity.order.carInfo.firstRegDate, "yyyy-MM-dd"))
    }
//温州需填写车主地址 手机
    if (redata.containsKey("phoneNumber") || redata.containsKey("ownerAddress")) {
        redata.put("phoneNumber", StringUtil.isNoEmpty(entity.order.carOwnerInfo.mobile) ? entity.order.carOwnerInfo.mobile : autoTask.configs.get("defaultPhone"))
        redata.put("ownerAddress", StringUtil.isNoEmpty(entity.order.carOwnerInfo.address) ? entity.order.carOwnerInfo.address : autoTask.configs.get("defaultAddress"))
    }
//北京需要填写车主地址 前面模板已完成补充数据覆盖
    if ("北京".equals(autoTask.configs.areaComCode)) {
        redata.put("ownerAddress", StringUtil.isNoEmpty(entity.order.carOwnerInfo.address) ? entity.order.carOwnerInfo.address : autoTask.configs.get("defaultAddress"))
    }
//江苏、南京需要填写车主地址
    if (["江苏", "南京"].contains(autoTask.configs.areaComCode)) {
        redata.put("phoneNumber", StringUtil.isNoEmpty(entity.order.carOwnerInfo.mobile) ? entity.order.carOwnerInfo.mobile : autoTask.configs.get("defaultPhone"))
    }
//统保代码
    if (StringUtil.isNoEmpty((String) autoTask.configs.uniformInsurance)) {
        redata.put("uniformInsurance", autoTask.configs.uniformInsurance)
    }


    boolean usageSubdivs = autoTask.tempValues.usageSubdivs && robot_2011_special_util.specialCar().keySet().contains(autoTask.tempValues.usageSubdivs)
    if (usageSubdivs) {
        redata.usageType = "2"  //特种车 车辆种类
    }
    if (["D6", "D8", "D12"].contains(car?.jyFuelType)) {
        redata.usageType = "5"  //新能源
        redata.plateType = autoTask.tempValues.plateTypeJg ?: "52"
        redata.plateColor = "d"
    }
//第一步判断是不是新车 且广东机构不含（深圳）
    if (entity.order?.getCarInfo()?.getIsNew() && ["东莞", "广州", "广州市"].contains(autoTask?.configs?.areaComCode?.toString())) {
        //广东省银保监平台新车投保需要三项信息 先从supplyParam 这里没有从configMap
        if (entity?.getMisc()?.get("supplyParam")?.get("newCarSaleComName") || autoTask?.configs?.newCarSaleComName) {
            redata.put("saleCompany", entity?.getMisc()?.get("supplyParam")?.get("newCarSaleComName") ?: autoTask?.configs?.newCarSaleComName)
            redata.put("saleArea", entity?.getMisc()?.get("supplyParam")?.get("newCarSaleComCityCode") ?: autoTask?.configs?.newCarSaleComCityCode)
            redata.put("isSaleBy4S", entity?.getMisc()?.get("supplyParam")?.get("isSaleBy4S") ?: autoTask?.configs?.isSaleBy4S)
        }
    }

    redata['familyName'] = car['familyName']
    redata['tpyRiskflagCode'] = car['tpyRiskflagCode']
    redata['vehicleClassName'] = car['vehicleClassName']
    redata['tpyRiskflagName'] = car['tpyRiskflagName']
    redata['newFamilyGrade'] = car['newFamilyGrade']
    redata['vehiclePowerJY'] = car['vehiclePowerJY']
    redata['vehiclefgwCode'] = car['vehiclefgwCode']
    redata['quotationNo'] = autoTask.tempValues['quotationNo'] ?: ""

}


private void backPlatformVoInShanghai(AutoTask autoTask, String quotationNo, common_2011 TbUtil) {
    if ("上海" == autoTask.configs.areaComCode) {
        String paramStr = "{\"meta\":{},\"redata\":{\"quotationNo\":\"QSHH310Y1417F010636F\"}}"
        //填入查询号
        paramStr = paramStr.replace("QSHH310Y1417F010636F", quotationNo)
        paramStr = Robot2011Util.genBody(autoTask.tempValues, paramStr)
        def queryInsureInfoResult = Robot2011Util.postWithRetry3Times(
                autoTask?.httpClient as CloseableHttpClient,
                RobotConstant_2011.URL.queryInsureInfoURL,
                paramStr,
                autoTask.tempValues,
                autoTask.configs
        )
        queryInsureInfoResult = Robot2011Util.decodeBody(autoTask.tempValues, queryInsureInfoResult)
        JSONObject queryInsureInfoResultObj = null
        if (queryInsureInfoResult instanceof JSONObject)
            queryInsureInfoResultObj = (JSONObject) queryInsureInfoResult
        else
            queryInsureInfoResultObj = JSON.parseObject(queryInsureInfoResult)

        if (StringUtil.isNoEmpty(TbUtil.getFromJson(queryInsureInfoResultObj, "result.ecarvo.platformVo.benchmarkRiskPremium") as String)) {
            autoTask.tempValues.put("platformVo", queryInsureInfoResultObj.getJSONObject("result").getJSONObject("ecarvo").getJSONObject("platformVo"))
        }
        autoTask.tempValues.queryInsureInfoResult = queryInsureInfoResult
    }
}

private static void output(AutoTask autoTask, Enquiry entity, JSONObject car) {
    def TbUtil = new common_2011()
    def robot_2011_special_util = new robot_2011_special_util()
    //车辆信息
    autoTask.tempValues.special_car = car
    //是否是新能源
    if (["D6", "D8", "D12"].contains(car?.jyFuelType)) {
        //新能源标识
        autoTask?.tempValues?.NEFlag = '1'
    }
    if (StringUtil.isNoEmpty(entity.misc?.supplyParam?.registerModelName as String)) {
        if (null != entity.order.suiteInfo.bizSuiteInfo) {
            autoTask.tempValues.needClause = 'true'
        }
    }
    if (autoTask?.tempValues?.NEFlag) {
        autoTask?.tempValues?.carPowerType = car.getString('carPowerType') ?: ''
    }
    String uc = entity.order.carInfo.useProps.toString()//使用性质原始值
    //太保的使用性质编码
    String usage = RobotDict_2011.getValue("usageAttributeCode", uc)
    autoTask.tempValues.usageSubdivs = getUsageSubdivs(usage, uc)
    if ("15" == uc || "16" == uc) {
        String usageSubdivs = robot_2011_special_util.getSpecialCarTypeCode(entity.order.carInfo.carModelName)
        autoTask.tempValues.usageSubdivs = usageSubdivs
    }

    //flag、车辆种类、车辆用途、车船税交管车辆类型（宁波）
    def (flag, vehicleType, vehiclePurpose, taxVehicleCode) = RobotDict_2011.getVehicle(autoTask, entity)
    autoTask.tempValues.tb_car_info = [
            'vehiclePurpose': vehiclePurpose,
            "vehicleType"   : vehicleType,
            "taxVehicleCode": taxVehicleCode
    ]
    autoTask.tempValues.actualValue = RobotDict_2011.getActualValue(autoTask, entity)
    //计算交强险原价
    String efcOrgCharge = TbUtil.getEfcOrgCharge(uc, flag)
    if (StringUtil.isNoEmpty(efcOrgCharge)) {
        autoTask.tempValues.efcOrgCharge = efcOrgCharge
    }

}

private static void reqQueryMsbAgent(AutoTask autoTask) {
    def reqBody = RobotCar_2011.makeQueryMsbAgentReqBody(autoTask)
    String requestBody = JSON.toJSONString(reqBody)
    requestBody = Robot2011Util.genBody(autoTask.tempValues, requestBody)
    String response = Robot2011Util.postWithRetry3Times(autoTask.httpClient as CloseableHttpClient, RobotConstant_2011.URL.QUERY_MSB_AGENT, requestBody, autoTask.tempValues, autoTask.configs)
    response = Robot2011Util.decodeBody(autoTask.tempValues, response)
    RobotOutput_2011.handleQueryMsgAgent(autoTask, response)
}

private static void reqQuickSave(AutoTask autoTask, Enquiry entity, def logger) {
    def reqBody = RobotCar_2011.makeQuickSaveReqBody(autoTask, entity)
    autoTask.tempValues.saveParam = reqBody
    String requestBody = JSON.toJSONString(reqBody)
    logger.info("请求报文：{}", requestBody)
    requestBody = ScriptUtil.chineseToUnicode(requestBody)
    requestBody = Robot2011Util.genBody(autoTask.tempValues, requestBody)
    String response = Robot2011Util.postWithRetry3Times(autoTask.httpClient as CloseableHttpClient, RobotConstant_2011.URL.QUICK_SAVE, requestBody, autoTask.tempValues, autoTask.configs)
    response = Robot2011Util.decodeBody(autoTask.tempValues, response)
    RobotOutput_2011.handleQuickSave(autoTask, response)
}

static dealPlatformVo(model, autoTask) {
    JSONObject platformVo = JSON.parseObject("{\"modelCode\":\"BYQKMUUC0014\",\"brand\":\"大众FV7187TFATG\",\"brandCode\":\"YQK\",\"series\":\"迈腾\",\"seriesCode\":\"\",\"carName\":\"大众FV7187TFATG 舒适型\",\"noticeType\":\"\",\"configType\":\"UC\",\"benchmarkRiskPremium\":\"2213.449160\",\"pureRiskPremium\":\"2213.449160\",\"pureRiskPremiumFlag\":\"1\"}");
    platformVo.put("modelCode", model.get("hyVehicleCode"))
    platformVo.put("brand", model.get("modelType"))
    platformVo.put("brandCode", model.get("brandCode"))
    platformVo.put("series", model.get("series"))
    platformVo.put("carName", model.get("carName"))
    platformVo.put("configType", model.get("configType"))
    platformVo.put("benchmarkRiskPremium", model.get("pureRiskPremium"))
    platformVo.put("pureRiskPremium", model.get("pureRiskPremium"))
    platformVo.put("pureRiskPremiumFlag", model.get("pureRiskPremiumFlag"))
    autoTask.tempValues.put("pureRiskPremium", platformVo.get("pureRiskPremium"))
    autoTask.tempValues.put("shortcutCode", model.get("shortcutCode"))
    autoTask.tempValues.put("platformVo", platformVo)
}