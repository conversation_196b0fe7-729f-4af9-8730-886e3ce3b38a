package taipingyang.robot

import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.utils.dama.Dama2Web
import common.scripts.BaseScript_Http_Enq
import common.scripts.ScriptUtil
import groovy.transform.BaseScript
import org.apache.http.impl.client.CloseableHttpClient
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotConstant_2011
import taipingyang.robot.module.RobotDict_2011

/**
 * 交管平台查询，根据vin码查询
 * 目前北京、广州需查询，其他地区无需查询
 * <AUTHOR>
 */
@BaseScript BaseScript_Http_Enq _

skip('该地区无需启用交管平台查询，本模板将被跳过') {
    'true' != config?.needJgPtSerch
}

//是北京但不是京牌的跳过
skip('非京牌，跳过交管') {
    config.areaComCode == '北京' && !order.carInfo?.plateNum?.contains('京')
}


def params = RobotDict_2011.makeBaseReqBody {
    [
            'vin'     : order.carInfo.vin,
            'engineNo': order.carInfo.engineNum
    ]
}

def getJGcheckCodeResult = Robot2011Util.postWithRetry3Times(
        autoTask?.httpClient as CloseableHttpClient,
        RobotConstant_2011.URL.VEHICLE_QUERY_VALIDATION,
        Robot2011Util.genBody(tempValues, ScriptUtil.toJSONStringWithChinese(params)),
        tempValues,
        config
)
getJGcheckCodeResult = Robot2011Util.decodeBody(tempValues, getJGcheckCodeResult)

JSONObject getJGcheckCodeResultObj = ScriptUtil.initResp(getJGcheckCodeResult)
assertNotNull('获取验证码失败', getJGcheckCodeResultObj)

def msg = getJGcheckCodeResultObj.getJSONObject('message')
def code = msg.getString('code')
if ('success' != code) {
    fail('获取验证码信息失败，平台返回:' + code)
}

def result = getJGcheckCodeResultObj.getJSONObject('result')
String checkNo = result.getString('checkNo')
String checkCode = result.getString('checkCode')?.replace(' ', '')
String checkCodeStr = ''
try {
    checkCodeStr = Dama2Web.getAuth(checkCode, 1)
} catch (Exception e) {
    log.warn('解析交管平台验证码失败', e)
    throw new Exception('解析交管平台验证码失败')
}

head Robot2011Util.getDefaultHead(tempValues, config)

body Robot2011Util.serializer(tempValues), {
    RobotDict_2011.makeBaseReqBody {
        [
                'checkNo'  : checkNo,
                'checkCode': checkCodeStr
        ]
    }
}
