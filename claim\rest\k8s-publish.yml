server:
  port: ${profiles_port}
  undertow:
    io-threads: ${undertow_io_threads}
    worker-threads: ${undertow_worker_threads}
spring:
  data:
    mongodb:
      uri: ${mongo_uri}
    redis:
      host: ${redis_host}
      port: ${redis_port}
      password: ${redis_password}
      database: ${redis_database}

# scriptDir
router:
  script:
    dir: ${ins_router_script_dir}

zooKeeper:
  config:
    address: ${zookeeper_config_address}
    root: ${zookeeper_config_root}
    group: ${zookeeper_config_group}
    defaultHost: ${zookeeper_config_defaultHost}
mysql_username: ${mysql_username}
mysql_password: ${mysql_password}
mysql_host: ${mysql_host}
mysql_db: ${mysql_db}
