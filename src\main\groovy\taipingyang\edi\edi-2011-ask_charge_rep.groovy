package taipingyang.edi

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.utils.dama.Dama2Web
import groovy.transform.Field
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.regex.Matcher
import java.util.regex.Pattern

@Field private static Logger log = LoggerFactory.getLogger("edi-2011-ask_charge_rep")

def enquiry = enquiry as Map
def tempValues = tempValues as Map
def root = root as JSONObject

def status = root.getString('responseCode')
def edi_2011_ask_charge_util = new edi_2011_ask_charge_util()

def responseMsg = root.getString('responseMsg')

// 重试socketTimeoutException异常
if (responseMsg.contains("SocketTimeoutException")) {
    retrySocketTimeoutException(tempValues)
}

def carInfo = enquiry['carInfo'] as Map
//行业车型查询
resetCarModel(responseMsg, carInfo, tempValues, config)


def inquireRes = root.getJSONObject('inquireRes')

//回答问题
if (responseMsg.contains("回答问题")) {
    answer(inquireRes, tempValues)
}

if (responseMsg.contains("录入的校验码有误") && !tempValues.questionAnswerAskchargeFlag) {
    tempValues.questionAnswerAskchargeFlag = "1"
    tempValues.questionAnswerAskcharge = tempValues.questionAnswerAskcharge?.toString()?.replace("0", "o")
    throw new InsReturnException(InsReturnException.AllowRepeat, "商业险转保验证重新核保")
}


if (status != '0000') {
    //[保费计算]保费计算失败，错误信息为[报价引擎提示：com.cpic.p09.auto.pce.base.exception.CommonException: 报价引擎提示：[交强险]车牌号“”的保单发生重复投保，与其重复投保的其它公司的保单信息如下：车架号 L1NSPGHB0LA005629;发动机号 D3HG091611031001ED2;地区 440300。 (平台提示)]
    //[保费计算]保费计算失败，错误信息为[报价引擎提示：com.cpic.p09.auto.pce.base.exception.CommonException: 报价引擎提示：[交强险]号牌号码“京APNY8G”的保单发生重复投保，与其重复投保的其它公司的保单信息如下：投保确认码 02PICC110000C77BB9CF3B2760CE;保单号 PICC1C1764D7A0D08F88C254680D27A580FA;起保日期 2020-11-01 00:00;终保日期 2021-11-01 00:00;号牌号码 京APNY8G;号牌种类 02;车架号 LNN4GP4117SCN9AW4;发动机号 ENGXHASEHG6Z;地区 110000。 (平台提示)]
    if (responseMsg.contains("保单发生重复投保") && responseMsg.contains("车架号")) {
        repeatInsuredType1(responseMsg, carInfo)
    }

    //[保费计算]保费计算失败，错误信息为[报价引擎提示：com.cpic.p09.auto.pce.base.exception.CommonException: 报价引擎提示：[交强险]号牌号码“京APNY8G”的保单发生重复投保，与其重复投保的其它公司的保单信息如下：投保确认码 02PICC110000C77BB9CF3B2760CE;保单号 PICC1C1764D7A0D08F88C254680D27A580FA;起保日期 2020-11-01 00:00;终保日期 2021-11-01 00:00;号牌号码 京APNY8G;号牌种类 02;车架号 LNN4GP4117SCN9AW4;发动机号 ENGXHASEHG6Z;地区 110000。 (平台提示)]
    //[保费计算]保费计算失败，错误信息为[报价引擎提示：com.cpic.p09.auto.pce.base.exception.CommonException: 报价引擎提示：[交强险]号牌号码“粤EFC5883”的保单发生重复投保，与其重复投保的其它公司的保单信息如下：投保确认码 02TPIC440022060956223392428292-保单号 63003080120220021535-起保日期 2022-06-27 00:00-终保日期 2023-06-27 00:00-号牌号码 -号牌种类 -车架号 LW433B110N1058048-发动机号 226055122-CD-地区 广东。 (平台提示)]
    if (responseMsg.contains("保单发生重复投保") && responseMsg.contains("终保日期")) {
        repeatInsuredType2(edi_2011_ask_charge_util, responseMsg, tempValues)

    }
    //报价引擎提示：0101010128_重复投保[该车已在上海地区人保财险公司存在有效保单记录，保险期限是2021年06月15日 13:00－2022年06月15日 13:00[保险起期-保险止期]，请核对.] (平台提示)]
    if (responseMsg.contains("重复投保") && responseMsg.contains("保险期限是")) {
        repeatInsuredType3(edi_2011_ask_charge_util, responseMsg, tempValues)
    }

    throw new InsReturnException("太保保费计算接口出错：" + responseMsg)
}

def SQ = enquiry['SQ'] = enquiry['SQ'] ?: [:]

//if (status == "0000") {
    def inquireResMessage = inquireRes.getString('message')

    if (inquireResMessage && inquireResMessage.contains("商业险重复投保")) {
        repeatInsuredType4(SQ, inquireResMessage, edi_2011_ask_charge_util, tempValues)
    }
    //车损险自定义保额重试
    if (tempValues.definedCarPriceFlag && tempValues.definedCarPriceFlag < 2) {
        handleDefinedCarPrice(inquireRes, tempValues)
    }

    //宁波地区 旧车 投保车损险 使用折旧价格*0.75
    if ("330200" == enquiry?.insArea?.city?.toString() && !tempValues?.NingBoSumInsurd && !enquiry?.carInfo?.isNew) {
        if (tempValues?.NEFlag) {
            tempValues?.NingBoSumInsurd = inquireRes.newEnergyPolicy?.coverages?.find { it?.coverageCode?.toString()?.contains("DAMAGELOSSCOVERAGE") }?.sumInsurd
        } else {
            tempValues?.NingBoSumInsurd = inquireRes.businessPolicy?.coverages?.find { it?.coverageCode?.toString()?.contains("DAMAGELOSSCOVERAGE") }?.sumInsurd
        }
        if (tempValues?.NingBoSumInsurd) {
            throw new InsReturnException(InsReturnException.AllowRepeat, "宁波车损险保额重复报价")
        }
    }

    def definition = enquiry['definition'] = enquiry['definition'] as Map ?: [:]
    def totalCharge = BigDecimal.ZERO
    SQ['totalCharge'] = totalCharge
    def vehicle = inquireRes.getJSONObject('vehicle')
    tempValues.vehicle = vehicle
    //部分车辆信息回写
    carInfo.jyCode = vehicle.vehicleModelCode
    carInfo.carModelName = vehicle.trafficMakerModel
    carInfo.seatCnt = Integer.valueOf(vehicle.seatCount?.toString())
    carInfo.price = new BigDecimal(vehicle.purchasePrice?.toString())
    carInfo.fullLoad = vehicle.emptyWeight
    carInfo.displacement = vehicle.engineCapacity
    carInfo.modelLoad = vehicle.carryingCapacity
    carInfo.firstRegDate = LocalDateTime.parse(
            vehicle.registerDate as String, DateTimeFormatter.ofPattern('yyyyMMddHHmmss'))
            .format(DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss'))

    vehicle?.actualValue

    // （N：新保  R：续保  T：转保）
    if ("R".equals(vehicle.insuranceFlag)) {
        definition.put("application.loyalty", "1");
    } else if ("T".equals(vehicle.insuranceFlag)) {
        definition.put("application.loyalty ", "2");
    } else {
        definition.put("application.loyalty ", "0");
    }

    def newEnergyPolicy = inquireRes.getJSONObject('newEnergyPolicy')
    def businessPolicy = inquireRes.getJSONObject('businessPolicy')
    def isOverThreshold = (newEnergyPolicy ?: businessPolicy)?.getJSONObject('policyFloatingItem')?.getisOverThreshold
    def independentPriceRate = (newEnergyPolicy ?: businessPolicy)?.getJSONObject('premiumFloatingItem')?.getBigDecimal('independentPriceRate')
    if (independentPriceRate) {
        tempValues.premiumFloatingItem = independentPriceRate.setScale(6, BigDecimal.ROUND_HALF_UP)
    }

    tempValues.preminumCaculateQueryCode = (newEnergyPolicy ?: businessPolicy)?.preminumCaculateQueryCode
    //存在成本变动且非统一定价
    if (isOverThreshold && !"1".equals(config?.isTPreminum)) {
        tempValues.isOverThreshold = true
    }

    if (SQ['discountRates'] && !tempValues['inputPolicyDiscount']) {
        def discountRates = SQ['discountRates']
        def inputPolicyDiscount = discountRates['geniusItem.inputPolicyDiscount']
        if (inputPolicyDiscount) {
            tempValues.put('geniusItem.totalDiscount', inputPolicyDiscount)
            tempValues['inputPolicyDiscount'] = true
            throw new InsReturnException(InsReturnException.AllowRepeat, '修改折扣，重新报价')
        }
    }
    def trafficPolicy = inquireRes.getJSONObject('trafficPolicy')
    def totalCpicScore = ''
    def efcCpicScore = ''
    // 如果有交强险
    if (trafficPolicy) {
        def policyFloatingItem = trafficPolicy.getJSONObject('policyFloatingItem')
        if (policyFloatingItem) {
            efcCpicScore = policyFloatingItem.getString('cpicScore')
            // 交商合计太保分
            totalCpicScore = policyFloatingItem.getString('totalCpicScore')
        }
    }

    businessPolicy = businessPolicy ?: newEnergyPolicy
    def bizCpicScore = ''
    if (businessPolicy) {
        def policyFloatingItem = businessPolicy.getJSONObject('policyFloatingItem')
        if (policyFloatingItem) {
            bizCpicScore = policyFloatingItem.getString('cpicScore')
            totalCpicScore = totalCpicScore ?: policyFloatingItem.getString('totalCpicScore')
        }
    }
    def platformBack = tempValues[PlatformKey.platformBack] = tempValues[PlatformKey.platformBack] as Map ?: [:]
    def platformInfo = tempValues['platformInfo'] = tempValues['platformInfo'] ?: [:]
    platformInfo['definition'] = definition
    //写入交强险评分
    platformBack[PlatformKey.TRAFFIC_SCORE] = definition[PlatformKey.TRAFFIC_SCORE] = definition['CpicEfcScore'] = efcCpicScore
    //写入商业险评分
    platformBack[PlatformKey.bizScore] = definition[PlatformKey.bizScore] = definition['CpicBizScore'] = bizCpicScore
    //写入交商同保总分
    platformBack['application.totalScore'] = definition['application.totalScore'] = definition['CpicTotalScore'] = totalCpicScore

    edi_2011_ask_charge_util.ruleInfo(autoTask, tempValues, enquiry)

    // 如果有交强险
    if (trafficPolicy) {
        edi_2011_ask_charge_util.chargeEfcSuit(enquiry, trafficPolicy, totalCharge)
        tempValues.trafficPolicy = trafficPolicy
    }

    if (businessPolicy) {
        edi_2011_ask_charge_util.chargeBizSuit(enquiry, businessPolicy, tempValues)
        tempValues.businessPolicy = businessPolicy
    }
    def insuranceFlag = vehicle.getString('insuranceFlag') // 新转续标识: N: 新保; R: 续保; T: 转保;
    insuranceFlag = insuranceFlag == 'N' ? '0' : insuranceFlag == 'R' ? '1' : insuranceFlag == 'T' ? '2' : ''
    platformBack[PlatformKey.application_loyalty] = insuranceFlag
    definition['CpicInsType'] = insuranceFlag
    /* (Story#12907)[http://192.168.1.212/index.php?m=story&f=view&id=12907] end */
    if (SQ['nonMotor']) {
        def accidentPolicy = inquireRes.accidentPolicyList[0]
        edi_2011_ask_charge_util.chargeNonMotor(enquiry, accidentPolicy, totalCharge)
    }

    /* Story#12978[http://192.168.1.212/index.php?m=story&f=view&id=12978] start */
    def config = config as Map
    if (enquiry['advanceDeduction']) {
        if (!config.outerActivityCode) {
            throw new InsReturnException(InsReturnException.Others, '外部活动代码为空')
        }

        new edi_2011_advance_deduction_common().calculateAdvanceDeductionCharge(enquiry, inquireRes, tempValues, config)
    } else if (businessPolicy && (config['fixedPremium'] || config['fixeAdvanceDeductionPremium'])) {
        if (!config.outerActivityCode) {
            throw new InsReturnException(InsReturnException.Others, '外部活动代码为空')
        }

        // 第一开票人金额，实际保费
        def firstPresentPremium = 0
        // 第二开票人金额，垫资金额
        def secondPresentPremium = 0
        def efcCharge = SQ['efcCharge']?.toString()?.toBigDecimal() ?: 0
        def bizCharge = SQ['bizCharge']?.toString()?.toBigDecimal() ?: 0
        // 统一定价
        if (config['fixedPremium']) {
            def fixedPremium = config['fixedPremium'].toString().toBigDecimal()
            firstPresentPremium = fixedPremium?.subtract(efcCharge)?.setScale(2, BigDecimal.ROUND_HALF_UP)
            secondPresentPremium = efcCharge + bizCharge - fixedPremium
            if (secondPresentPremium < 0) {
                throw new InsReturnException("太保垫资业务异常：总金额小于建议金额")
            }
            businessPolicy.getJSONArray('coverages').each({ coverage ->
                if (coverage?.coverageCode?.toString()?.contains('DAMAGELOSSCOVERAGE')) {
                    coverage['presentFlag'] = '1'
                    coverage['presentPremium'] = bizCharge.subtract(firstPresentPremium)?.setScale(2, BigDecimal.ROUND_HALF_UP)
                }
            })
        }

        // 固定垫资金额
        if (config['fixeAdvanceDeductionPremium']) {
            def fixeAdvanceDeductionPremium =
                    config['fixeAdvanceDeductionPremium'].toString().toBigDecimal()
            if (fixeAdvanceDeductionPremium > bizCharge) {
                businessPolicy.getJSONArray('coverages').each({ coverage ->
                    def premium = (coverage as JSONObject).getBigDecimal('premium')
                    coverage['presentFlag'] = '1'
                    coverage['presentPremium'] = premium
                })

                if (trafficPolicy) {
                    firstPresentPremium = 0
                    secondPresentPremium = bizCharge
                } else {
                    firstPresentPremium = 1
                    secondPresentPremium = bizCharge - 1
                    // 固定垫资金额 >= 商业险保费时，实际垫资金额=商业险保费-1。商业险所有险别均垫资全额（其中一个险别除外：乘客>司机>三者>车损险，垫资=保费金额-1）
                    def coverageCodeList = ['DAMAGELOSSCOVERAGE', 'THIRDPARTYLIABILITYCOVERAGE',
                                            'INCARDRIVERLIABILITYCOVERAGE', 'INCARPASSENGERLIABILITYCOVERAGE']
                    for (def coverageCode in coverageCodeList.reverse()) {
                        def find = businessPolicy.getJSONArray('coverages').find({ coverage ->
                            coverage['coverageCode'].toString().contains(coverageCode)
                        })
                        if (find) {
                            find['presentPremium'] = find['presentPremium'] - 1
                            break
                        }
                    }
                }
            } else {
                secondPresentPremium = fixeAdvanceDeductionPremium
                businessPolicy.getJSONArray('coverages').each({ coverage ->
                    if (fixeAdvanceDeductionPremium && fixeAdvanceDeductionPremium > 0) {
                        def premium = (coverage as JSONObject).getBigDecimal('premium')
                        def presentPremium = fixeAdvanceDeductionPremium > premium ? premium : fixeAdvanceDeductionPremium
                        fixeAdvanceDeductionPremium = fixeAdvanceDeductionPremium - presentPremium
                        coverage['presentFlag'] = '1'
                        coverage['presentPremium'] = presentPremium
                    }
                })

                firstPresentPremium = bizCharge - secondPresentPremium
            }
        }

        def edi_2011_common = new edi_2011_common()
        def certificateType, certificateCode, customerType, name, ifSpecialTicket, isEInvoice, email, telephone
        def invoiceInfos = enquiry['invoiceInfos'] as JSONArray
        // invoiceType 发票类型: 1: 车险开票; 2: 垫资开票;
        if (invoiceInfos && invoiceInfos.find({ invoiceInfo -> invoiceInfo['invoiceType']?.toString() == '2' })) {
            def invoiceInfo = invoiceInfos.find({ invoiceInfo -> invoiceInfo['invoiceType']?.toString() == '2' })
            certificateType = edi_2011_common.getCardType(enquiry, invoiceInfo['idCardType'])
            certificateCode = invoiceInfo['idCard']
            customerType = invoiceInfo['customerType']
            name = invoiceInfo['name']
            ifSpecialTicket = invoiceInfo['ifSpecialTicket']
            isEInvoice = invoiceInfo['isEInvoice']
            email = invoiceInfo['email']
            telephone = invoiceInfo['mobile']
            customerKind = invoiceInfo['customerKind']
        } else {
            throw new InsReturnException(InsReturnException.Others, '太保垫资业务异常：未找到发票信息')
        }

        def presentPerson = [
                'firstPresentPremium' : firstPresentPremium,  // 第一开票人金额  BigDecimal  Y
                'secondPresentPremium': secondPresentPremium, // 第二开票人金额  BigDecimal  Y
                'certificateType'     : certificateType,      // 证件类型  String  Y
                'certificateCode'     : certificateCode,      // 证件号码  String  Y
                'customerType'        : customerType,         // 客户类型  String  Y
                'name'                : name,                 // 姓名  String  Y
                'ifSpecialTicket'     : ifSpecialTicket,      // 增值税发票类型  String  Y  0: 表示增值税普通发票; 1: 表示增值税专用发票;
                'isEInvoice'          : isEInvoice,           // 是否电子发票  String  Y
                'email'               : email,                // 电子邮件  String  E  如果是电子发票，必传其一
                'telephone'           : telephone,            // 联系电话  String
                'customerKind'        : '0'                   // 赠送部分开票人是否为客户  String  Y  指第二开票人是否为客户: 1: 是; 0: 不是;
        ]

        businessPolicy.presentPerson = presentPerson
        businessPolicy.outerActivityCode = config.outerActivityCode
        SQ['advanceDeductionCharge'] = secondPresentPremium
    }
    /* Story#12978[http://192.168.1.212/index.php?m=story&f=view&id=12978] end */
//}

private static void handleDefinedCarPrice(JSONObject inquireRes, Map tempValues) {
    def businessPolicy = inquireRes.newEnergyPolicy ?: inquireRes.businessPolicy
    if (businessPolicy) {
        //车损保额
        def sumInsurd = businessPolicy.coverages.find { it.coverageCode.toString().contains("DAMAGELOSSCOVERAGE") }?.sumInsurd as String
        if (sumInsurd) {
            def definedCarPrice = tempValues.definedCarPrice as BigDecimal
            def sumInsurdBigDecimal = new BigDecimal(sumInsurd)
            log.info("车损险保额:【保司返回：{}】，【自定义保额：{}】",  sumInsurdBigDecimal, definedCarPrice)
            if (definedCarPrice.compareTo(sumInsurdBigDecimal) != 0) {
                tempValues.definedCarPriceFlag = 2
                tempValues.preminumCaculateQueryCode = businessPolicy.preminumCaculateQueryCode
                throw new InsReturnException(InsReturnException.AllowRepeat, "商业车损险重复报价")
            }
        }
    }
}

private static void repeatInsuredType4(SQ, String inquireResMessage, edi_2011_ask_charge_util edi_2011_ask_charge_util, Map tempValues) {
//需求10603 针对提前报价的订单，需要返回提示语
    def message = SQ['message'] = SQ['message'] as Map ?: [:]
    message['BIZ_lastyear'] = inquireResMessage

    //def aaa = "1.商业险重复投保！</br>\\n2.重复投保的保险公司：中国太平洋财产保险股份有限公司</br>\\n3.投保确认码：</br>\\n4.保单号：AWUH02SY2021B000565N</br>\\n5.车牌号：</br>\\n6.号牌种类：</br>\\n7.车架号：LVTDB21B6LD292840</br>\\n8.发动机号：AHLM07154</br>\\n9.起保日期：2021年01月06日 15时00分</br>\\n10.终保日期：2022年01月07日 00时00分</br>\\n11.签单日期：2021年01月06日 00时00分</br>\\n12.已投保子险为：机动车第三者责任保险</br>机动车损失保险</br>代为送检服务特约条款</br>车辆安全检测特约条款</br>道路救援服务特约条款</br>代为驾驶服务特约条款</br></br></br></br>"
    def localDateTime = edi_2011_ask_charge_util.checkDate('终保日期：([0-9]+)年([0-9]+)月([0-9]+)日.?([0-9]+)时([0-9]+)分', inquireResMessage)
    if (localDateTime) {
        localDateTime = localDateTime as LocalDateTime
        if (localDateTime.getHour() == 23 && localDateTime.getMinute() == 59) {
            localDateTime = localDateTime.plusDays(1).withHour(0).withMinute(0)
        }

        def startDate = localDateTime
        def endDate = startDate.plusYears(1)
        if (startDate.toLocalDate().isLeapYear() && startDate.monthValue == 2 && startDate.dayOfMonth == 29) {
            endDate = startDate.plusYears(1).plusDays(1)
        }

        def dtf = DateTimeFormatter.ofPattern('yyyyMMddHHmmss')
        tempValues.repeatFlag = "1"
        tempValues.startDateStringTPYBiz = startDate.format(dtf)
        tempValues.endDateStringTPYBiz = endDate.format(dtf)
        throw new InsReturnException(InsReturnException.AllowRepeat, "重复投保修改日期")
    }
}
/**
 * SocketTimeoutException
 * @param responseMsg
 * @param tempValues
 */
private static void retrySocketTimeoutException(def tempValues) {
    //开启重试
    tempValues.retryTimes = tempValues.retryTimes ?: 0
    if (tempValues.retryTimes < 4) {
        tempValues.retryTimes++
        throw new InsReturnException(InsReturnException.AllowRepeat, "超时重试")
    }
}

private static void resetCarModel(String responseMsg, Map carInfo, Map tempValues, def config) {
    if (responseMsg.contains("精友行业车型编码与纯风险保费返回的行业车型编码不一致") && !carInfo['isNew']) {
        if (!tempValues.HYFlag
                && (Objects.isNull(config.requiresAutoCorrectCarModelCC) || config.requiresAutoCorrectCarModelCC?.toString() != 'false')) {
            if (['理想'].contains(carInfo.carBrandName)) {
                tempValues.carName = '理想'
            }
            tempValues.HYFlag = "1"
            def insReturn = new InsReturnException(InsReturnException.AllowRepeat, "不一致开启行业车型查询")
            insReturn.setStep(2)
            throw insReturn
        } else {
            throw new InsReturnException(InsReturnException.Others, responseMsg)
        }
    }
}

private static void answer(JSONObject inquireRes, Map tempValues) {
    def checkInfo = inquireRes.getJSONObject('checkInfo')
    def questionAnswer = Dama2Web.getAuth(checkInfo.getString('checkCode'), 1)
    tempValues.questionAnswerAskcharge = questionAnswer
    tempValues.isTrafficQuestionAskcharge = checkInfo.getString('isTrafficQuestion')
    throw new InsReturnException(InsReturnException.AllowRepeat, "商业险转保验证重新报价")
}

private void repeatInsuredType3(edi_2011_ask_charge_util edi_2011_ask_charge_util, String responseMsg, Map tempValues) {
    def localDateTime = edi_2011_ask_charge_util.checkDate('保险期限是([0-9]+)年([0-9]+)月([0-9]+)日.?([0-9]+):([0-9]+)', responseMsg)
    if (localDateTime) {
        //23:59
        if (localDateTime?.getHour()?.toString()?.equals("23") && localDateTime?.getMinute()?.toString()?.equals("59")) {
            localDateTime = localDateTime.plusMinutes(1)
        }
        //校验时间是否超出范围
        def checkPreQuoteFlag = edi_2011_ask_charge_util.checkPreQuoteDate(localDateTime, config)
        if (checkPreQuoteFlag) {
            def startDate = localDateTime as LocalDateTime
            def endDate = startDate.plusYears(1)
            if (startDate.toLocalDate().isLeapYear() && startDate.monthValue == 2 && startDate.dayOfMonth == 29) {
                endDate = startDate.plusYears(1).plusDays(1)
            }

            def dtf = DateTimeFormatter.ofPattern('yyyyMMddHHmmss')
            tempValues.startDateStringTPYEfc = startDate.format(dtf)
            tempValues.endDateStringTPYEfc = endDate.format(dtf)
            throw new InsReturnException(InsReturnException.AllowRepeat, "上海交强重复投保修改日期")
        }
    }
}

private static void repeatInsuredType2(edi_2011_ask_charge_util edi_2011_ask_charge_util, String responseMsg, Map tempValues) {
    def localDateTime = edi_2011_ask_charge_util.checkDate('终保日期 ([0-9]{4})-([0-9]{2})-([0-9]{2}) ([0-9]{2}):([0-9]{2})', responseMsg)
    if (localDateTime) {
        //23:59
        if (localDateTime?.getHour()?.toString()?.equals("23") && localDateTime?.getMinute()?.toString()?.equals("59")) {
            localDateTime = localDateTime.plusMinutes(1)
        }
    }

    def startDate = localDateTime as LocalDateTime
    def endDate = startDate.plusYears(1)
    if (startDate.toLocalDate().isLeapYear() && startDate.monthValue == 2 && startDate.dayOfMonth == 29) {
        endDate = startDate.plusYears(1).plusDays(1)
    }

    def dtf = DateTimeFormatter.ofPattern('yyyyMMddHHmmss')
    tempValues.startDateStringTPYEfc = startDate.format(dtf)
    tempValues.endDateStringTPYEfc = endDate.format(dtf)
    tempValues.repeatFlag = "1"
    throw new InsReturnException(InsReturnException.AllowRepeat, "重复投保修改日期")
}

private static void repeatInsuredType1(String responseMsg, Map carInfo) {
    Pattern p = Pattern.compile("车架号(\\s|：)([0-9a-zA-Z]+)(;|</br>|-)")
    Matcher m = p.matcher(responseMsg)
    def returnVin = ""
    while (m.find()) {
        returnVin = m.group(2)
    }
    if (!carInfo.vin?.toString()?.equals(returnVin)) {
        throw new InsReturnException("该车报价平台返回其他车辆信息，需上报平台处理")
    }
}