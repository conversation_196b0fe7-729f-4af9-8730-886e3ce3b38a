package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.utils.sender.HttpSender
import taipingyang.robot.module.Robot2011Util

AutoTask autoTask = autoTask
def TbUtil = new common_2011()


String deleteByQuotationNoURL = "https://issue.cpic.com.cn/ecar/quotationPolicy/deleteQuotationPolicy";

String query4quoteResult = (String) autoTask.backRoot;
query4quoteResult = Robot2011Util.decodeBody(autoTask.tempValues, query4quoteResult)
if (!query4quoteResult.startsWith("{")) {
    return;
}
JSONObject query4quoteResultObj = JSON.parseObject(query4quoteResult);
if (null == query4quoteResultObj.result) {
    return;
}
int size = query4quoteResultObj.getJSONArray("result").size();

boolean quoteDelete = false;
if (size < 1) {
} else {
    String status = TbUtil.getFromJson(query4quoteResultObj, "result.[0].quotationCode").toString();
    //暂存
    if ("1".equals(status)) {
        quoteDelete = true;
    }
}

if (quoteDelete) {
    def reqBody = Robot2011Util.genBody(autoTask.tempValues, autoTask.tempValues.deleteParam as String)
    def reqHeader = Robot2011Util.getDefaultHead(autoTask.tempValues, autoTask.configs)
    String deleteByQuotationNoResult = HttpSender.doPostWithRetry(5, autoTask?.httpClient, true, deleteByQuotationNoURL, reqBody, null, reqHeader, "UTF-8", null, "");
}
