package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.sender.HttpSender
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import org.apache.http.impl.client.CloseableHttpClient
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotConstant_2011

@BaseScript BaseScript_Http_Enq _

String policyno = (String) autoTask.tempValues.policyno?.bizPolicyCode
if (StringUtil.isEmpty(policyno))
    policyno = (String) autoTask.tempValues.policyno?.efcPolicyCode

if (StringUtil.isEmpty(policyno))
    throw new InsReturnException("查询用保单号为空")


JSONObject param = JSON.parseObject("{\"meta\":{\"pageSize\":8},\"redata\":{\"quotationNo\":\"\",\"insuredNo\":\"\",\"policyNo\":\"\",\"licensePlate\":\"\",\"policyHolder\":\"\",\"insurant\":\"\",\"productType\":\"\",\"quotationState\":\"\",\"dateType\":\"101\",\"startDate\":\"\",\"endDate\":\"\",\"isPrint\":\"\"}}");
param.getJSONObject("redata").put("policyNo", policyno);
def reqBody = Robot2011Util.genBody(tempValues, param.toJSONString())
def getQuotationNoResult = HttpSender.doPostWithRetry(5, autoTask?.httpClient as CloseableHttpClient, true,
        RobotConstant_2011.URL.QUERY_QUOTATION_POLICY,
        reqBody,
        null,
        Robot2011Util.getDefaultHead(tempValues, config),
        "UTF-8", null, "");
getQuotationNoResult = Robot2011Util.decodeBody(tempValues, getQuotationNoResult)
JSONObject getQuotationNoResultObj = JSON.parseObject((String) getQuotationNoResult)
if (null == getQuotationNoResultObj.result || getQuotationNoResultObj.getJSONArray("result").size() == 0)
    throw new InsReturnException("使用保单号" + policyno + "查无信息")
String quotationNo = getQuotationNoResultObj.getJSONArray("result").getJSONObject(0).getString("quotationNo")
param = JSON.parseObject("{\"meta\":{\"pageSize\":8},\"redata\":{\"quotationNo\":\"\",\"insuredNo\":\"\",\"policyNo\":\"\",\"licensePlate\":\"\",\"policyHolder\":\"\",\"insurant\":\"\",\"productType\":\"\",\"quotationState\":\"\",\"dateType\":\"101\",\"startDate\":\"\",\"endDate\":\"\",\"isPrint\":\"\"}}");
param.getJSONObject("redata").put("quotationNo", quotationNo);
String queryListParam = StringUtil.chinesetoUnicode(param.toJSONString())

head Robot2011Util.getDefaultHead(tempValues, config)

autoTask.params.clear()
Robot2011Util.genBody(tempValues, queryListParam)