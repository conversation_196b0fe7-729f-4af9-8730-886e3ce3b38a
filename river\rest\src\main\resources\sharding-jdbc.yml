mode:
  type: Standalone
  repository:
    type: JDBC
dataSources:
  ds_0:
    dataSourceClassName: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    username: $${mysql_username::river_zzb}
    password: $${mysql_password::Prfc65_fkzqU}
    url: jdbc:mysql://$${mysql_host::*************}:$${mysql_port::18635}/$${mysql_db::river_zzb}?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&useLegacyDatetimeCode=false&serverTimezone=GMT%2B8
    initial-size: 2
    max-active: 10
    min-idle: 1
    max-wait: 60000
    test-on-borrow: false
    test-on-return: false
    test-while-idle: true
    time-between-eviction-runs-millis: 30000
    min-evictable-idle-time-millis: 60000
    validation-query: SELECT 1
    filters: wall,stat,slf4j,config
    init-connection-sqls: set NAMES utf8mb4;
rules:
  - !SHARDING
    tables:
      auto_task:
        actualDataNodes: ds_0.auto_task_$->{(202504..202512).toList() + (202601..202612).toList()}
        # 表分片策略
        tableStrategy:
          complex:
            shardingColumns: autoTraceId,startTime
            shardingAlgorithmName: auto_task_monthly_sharding
    shardingAlgorithms:
      auto_task_monthly_sharding:
        type: CLASS_BASED
        props:
          strategy: COMPLEX
          algorithmClassName: com.cheche365.bc.sharding.MonthlyShardingAlgorithm
  - !SINGLE
    tables:
      - ds_0.*
    defaultDataSource: ds_0


props:
  sql-show: true
