package com.cheche365.bc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.dto.AutoQuoteCoreInfoSchema;
import com.cheche365.bc.dto.AutoTaskBiLogSchema;
import com.cheche365.bc.entity.AutoTask;
import com.cheche365.bc.hbase.AutoTaskLogRepository;
import com.cheche365.bc.mapper.AutoTaskMapper;
import com.cheche365.bc.message.TaskStatus;
import com.cheche365.bc.service.AutoTaskService;
import com.cheche365.bc.sharding.OldTableSwitch;
import com.cheche365.bc.sharding.ShardingContextHolder;
import com.cheche365.bc.sharding.HBaseDataSwitch;
import com.cheche365.bc.entity.hbase.AutoTaskLog;
import com.cheche365.bc.util.AutoQuoteCoreInfoBiLogUtil;
import com.cheche365.bc.util.AutoTaskBiLogUtil;
import com.cheche365.bi.logger.BiLoggerV2;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@Service
@Slf4j
@AllArgsConstructor
public class AutoTaskServiceImpl extends ServiceImpl<AutoTaskMapper, AutoTask> implements AutoTaskService {

    @Resource
    private BiLoggerV2 biLogger;

    @Resource
    private AutoTaskLogRepository autoTaskLogRepository;

    @Override
    @Transactional
    public boolean save(AutoTask entity) {

        try {
            ShardingContextHolder.setOpType("INSERT");
            boolean result;

            // 根据MonthlyShardingAlgorithm的isUseOldTable方法决定保存方式
            if (OldTableSwitch.isUseOldTable()) {
                result = saveWithDoubleWrite(entity);
            } else {
                // 正常模式，直接使用父类的save方法
                result = super.save(entity);
            }

            // HBase双写逻辑：如果启用双写且保存成功，则同时保存到HBase
            if (result && HBaseDataSwitch.isEnableDoubleWrite()) {
                saveToHBase(entity);
            }

            return result;
        } finally {
            ShardingContextHolder.clearOpType();
        }
    }

    /**
     * 实现双写逻辑：先移除分片键避免触发分片，然后分别插入到旧表和新表
     */
    @Transactional
    public boolean saveWithDoubleWrite(AutoTask entity) {
        try {
            ShardingContextHolder.setUseOldTable(true);
            super.save(entity);
            ShardingContextHolder.setUseOldTable(false);
            super.save(entity);
            return true;
        } catch (Exception e) {
            log.error("双写操作失败", e);
            throw new RuntimeException("双写操作失败", e);
        } finally {
            ShardingContextHolder.clearUseOldTable();
        }
    }

    /**
     * 根据实体的startTime确定分表名称
     */
    private String getShardedTableName(AutoTask entity) {
        if (entity.getStartTime() != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
            String yearMonth = entity.getStartTime().format(formatter);
            return "auto_task_" + yearMonth;
        }
        // 如果没有startTime，使用当前时间
        String yearMonth = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        return "auto_task_" + yearMonth;
    }

    @Override
    public List<AutoTask> getTaskByTaskIdComTaskType(String taskId, String com, String taskType) {
        return lambdaQuery().eq(AutoTask::getTraceKey, taskId)
            //
            .eq(AutoTask::getCompanyId, com)
            //
            .eq(AutoTask::getTaskType, taskType)
            //
            .orderByDesc(AutoTask::getStartTime)
            .list();
    }

    @Override
    public AutoTask getTaskByTaskTypeProposeNumOrOrderNo(String taskType, String no, int noType) {
        QueryWrapper<AutoTask> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(taskType), "taskType", taskType);
        if (noType == 1)
            wrapper.eq("biz_propose_no", no);
        else if (noType == 2)
            wrapper.eq("efc_propose_no", no);
        else
            wrapper.eq("biz_policy_no", no);
        return getOne(wrapper, false);
    }

    @Override
    public AutoTask getTaskByProposeNumOrOrderNo(String no) {
        return getOne(new QueryWrapper<AutoTask>().lambda()
            .and(
                wrapper -> wrapper.eq(AutoTask::getBizPolicyNo, no)
            ), false
        );
    }

    @Override
    public boolean updateById(AutoTask task) {
        // 处理生成 BI 日志
        try {
            com.cheche365.bc.task.AutoTask autoTaskRequest = (com.cheche365.bc.task.AutoTask) task;
            AutoTaskBiLogSchema schema = AutoTaskBiLogUtil.getAutoTaskBiLogSchema(autoTaskRequest);
            if (Objects.nonNull(schema)) {
                biLogger.info(schema);
                if (TaskStatus.QUOTE_SUCCESS.State().equals(task.getTaskStatus()) || TaskStatus.AUTO_INSURE_SUCCESS.State().equals(task.getTaskStatus())) {
                    AutoQuoteCoreInfoSchema autoQuoteCoreInfoSchema = AutoQuoteCoreInfoBiLogUtil.getAutoTaskBiLogSchema(autoTaskRequest, schema);
                    biLogger.info(autoQuoteCoreInfoSchema);
                }
            }
        } catch (Exception e) {
            log.error("BI 日志打印失败：{}", ExceptionUtils.getStackTrace(e));
        }
        ShardingContextHolder.setOpType("UPDATE");
        try {
            return super.updateById(task);
        } finally {
            ShardingContextHolder.clearOpType();
        }
    }

    @Override
    public AutoTask getOneTask(String taskId, String com, String[] taskType, Object bizProposeNo, Object efcProposeNo) {
        LambdaQueryWrapper<AutoTask> queryWrapper = new QueryWrapper<AutoTask>().lambda();
        queryWrapper.eq(StringUtils.isNotEmpty(taskId), AutoTask::getTraceKey, taskId);
        queryWrapper.eq(StringUtils.isNotEmpty(com), AutoTask::getCompanyId, com);
        if (null != bizProposeNo) {
            queryWrapper.eq(AutoTask::getBizProposeNo, bizProposeNo.toString());
        }
        if (null == bizProposeNo && null != efcProposeNo) {
            queryWrapper.eq(AutoTask::getEfcProposeNo, efcProposeNo.toString());
        }
        if (taskType.length > 1) {
            queryWrapper.in(AutoTask::getTaskType, Arrays.asList(taskType));
        } else {
            queryWrapper.eq(AutoTask::getTaskType, taskType[0]);
            queryWrapper.orderByDesc(AutoTask::getStartTime);
        }
        return baseMapper.selectOne(queryWrapper.last("limit 1"));
    }

    @Override
    public void updateClaimInfoByAutoTraceId(AutoTask autoTask) {
        UpdateWrapper<AutoTask> updateWrapper = new UpdateWrapper<>();
        ShardingContextHolder.setOpType("UPDATE");
        updateWrapper.lambda()
            .eq(AutoTask::getAutoTraceId, autoTask.getAutoTraceId())
            .set(AutoTask::getActionLogs, autoTask.getActionLogs())
            .set(AutoTask::getResultStr, autoTask.getResultStr())
            .set(AutoTask::getEndTime, autoTask.getEndTime())
            .set(AutoTask::getFeedbackJson, autoTask.getFeedbackJson());
        try {
            update(updateWrapper);

            // HBase双写逻辑：如果启用双写，则同时更新HBase
            if (HBaseDataSwitch.isEnableDoubleWrite()) {
                updateHBaseData(autoTask);
            }
        } catch (Exception e) {
            log.error("update error: {}", ExceptionUtils.getStackTrace(e));
        } finally {
            ShardingContextHolder.clearOpType();
        }

    }

    @Override
    public void updateClaimStatusByAutoTraceId(AutoTask autoTask) {
        UpdateWrapper<AutoTask> updateWrapper = new UpdateWrapper<>();
        ShardingContextHolder.setOpType("UPDATE");
        updateWrapper.lambda()
            .eq(AutoTask::getAutoTraceId, autoTask.getAutoTraceId())
            .set(AutoTask::getTaskStatus, autoTask.getTaskStatus())
            .set(AutoTask::getResultStr, autoTask.getResultStr());
        try {
            update(updateWrapper);

            // HBase双写逻辑：如果启用双写，则同时更新HBase
            if (HBaseDataSwitch.isEnableDoubleWrite()) {
                updateHBaseData(autoTask);
            }
        } finally {
            ShardingContextHolder.clearOpType();
        }
    }

    @Override
    public AutoTask getTaskBodyByAutoTraceId(String bodyType, String autoTraceId) {
        return getOne(new QueryWrapper<AutoTask>().select(bodyType).lambda().eq(AutoTask::getAutoTraceId, autoTraceId));
    }

    // ========== HBase数据转换和操作方法 ==========

    /**
     * 将AutoTask转换为AutoTaskLog
     * 只转换大文本字段：applyJson、resultStr、feedbackJson
     *
     * @param autoTask AutoTask实体
     * @return AutoTaskLog实体
     */
    private AutoTaskLog convertToAutoTaskLog(AutoTask autoTask) {
        if (autoTask == null) {
            return null;
        }

        return AutoTaskLog.builder()
            .autoTraceId(autoTask.getAutoTraceId())
            .traceKey(autoTask.getTraceKey())
            .applyJson(autoTask.getApplyJson())
            .resultStr(autoTask.getResultStr())
            .feedbackJson(autoTask.getFeedbackJson())
            .build();
    }

    /**
     * 将HBase中的AutoTaskLog数据合并到AutoTask中
     * 只合并大文本字段：applyJson、resultStr、feedbackJson
     *
     * @param autoTask    AutoTask实体
     * @param autoTaskLog HBase中的AutoTaskLog数据
     * @return 合并后的AutoTask实体
     */
    private AutoTask mergeHBaseData(AutoTask autoTask, AutoTaskLog autoTaskLog) {
        if (autoTask == null || autoTaskLog == null) {
            return autoTask;
        }

        autoTask.setApplyJson(autoTaskLog.getApplyJson());
        autoTask.setResultStr(autoTaskLog.getResultStr());
        autoTask.setFeedbackJson(autoTaskLog.getFeedbackJson());

        return autoTask;
    }

    /**
     * 保存数据到HBase
     * 异步保存，失败不影响主流程
     *
     * @param autoTask AutoTask实体
     */
    private void saveToHBase(AutoTask autoTask) {
        if (autoTask == null || autoTask.getAutoTraceId() == null) {
            return;
        }

        try {
            AutoTaskLog autoTaskLog = convertToAutoTaskLog(autoTask);
            autoTaskLogRepository.save(autoTaskLog);
            log.debug("成功保存数据到HBase，autoTraceId: {}", autoTask.getAutoTraceId());
        } catch (Exception e) {
            log.error("保存数据到HBase失败，autoTraceId: {}, 错误: {}",
                autoTask.getAutoTraceId(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 更新HBase中的数据
     * 失败不影响主流程
     *
     * @param autoTask AutoTask实体（包含要更新的字段）
     */
    private void updateHBaseData(AutoTask autoTask) {
        if (autoTask == null || autoTask.getAutoTraceId() == null) {
            return;
        }

        try {
            // 先查询现有数据
            AutoTaskLog existingLog = autoTaskLogRepository.findByAutoTraceId(autoTask.getAutoTraceId());

            if (existingLog != null) {
                // 更新现有数据
                if (autoTask.getApplyJson() != null) {
                    existingLog.setApplyJson(autoTask.getApplyJson());
                }
                if (autoTask.getResultStr() != null) {
                    existingLog.setResultStr(autoTask.getResultStr());
                }
                if (autoTask.getFeedbackJson() != null) {
                    existingLog.setFeedbackJson(autoTask.getFeedbackJson());
                }

                autoTaskLogRepository.save(existingLog);
                log.debug("成功更新HBase数据，autoTraceId: {}", autoTask.getAutoTraceId());
            } else {
                // 如果不存在，则创建新记录
                saveToHBase(autoTask);
            }
        } catch (Exception e) {
            log.error("更新HBase数据失败，autoTraceId: {}, 错误: {}",
                autoTask.getAutoTraceId(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 根据开关控制从数据库表或HBase查询大文本字段数据
     * 如果开关开启，从HBase查询；否则从数据库表查询
     *
     * @param autoTraceId 任务ID
     * @return 包含大文本字段的AutoTask对象，如果不存在返回null
     */
    public AutoTask getTaskWithLargeFields(String autoTraceId) {
        if (autoTraceId == null || autoTraceId.isEmpty()) {
            return null;
        }

        try {
            if (HBaseDataSwitch.isUseHBaseData()) {
                // 从HBase查询大文本字段
                return getTaskFromHBase(autoTraceId);
            } else {
                // 从数据库表查询
                return getTaskFromDatabase(autoTraceId);
            }
        } catch (Exception e) {
            log.error("查询任务数据失败，autoTraceId: {}, 错误: {}", autoTraceId, e.getMessage(), e);
            // 如果HBase查询失败，降级到数据库查询
            if (HBaseDataSwitch.isUseHBaseData()) {
                log.warn("HBase查询失败，降级到数据库查询，autoTraceId: {}", autoTraceId);
                return getTaskFromDatabase(autoTraceId);
            }
            return null;
        }
    }

    /**
     * 从数据库表查询任务数据（包含大文本字段）
     *
     * @param autoTraceId 任务ID
     * @return AutoTask对象
     */
    private AutoTask getTaskFromDatabase(String autoTraceId) {
        return getOne(new QueryWrapper<AutoTask>()
            .lambda()
            .eq(AutoTask::getAutoTraceId, autoTraceId));
    }

    /**
     * 从HBase查询大文本字段，并与数据库基本信息合并
     *
     * @param autoTraceId 任务ID
     * @return 合并后的AutoTask对象
     */
    private AutoTask getTaskFromHBase(String autoTraceId) {
        try {
            // 先从数据库查询基本信息（不包含大文本字段）
            AutoTask autoTask = getOne(new QueryWrapper<AutoTask>()
                .lambda()
                .eq(AutoTask::getAutoTraceId, autoTraceId)
                .select(AutoTask::getAutoTraceId, AutoTask::getTraceKey,
                    AutoTask::getCompanyId, AutoTask::getPlateNo,
                    AutoTask::getStartTime, AutoTask::getEndTime,
                    AutoTask::getTaskStatus, AutoTask::getTaskType,
                    AutoTask::getBizProposeNo, AutoTask::getEfcProposeNo,
                    AutoTask::getBizPolicyNo, AutoTask::getEfcPolicyNo,
                    AutoTask::getFailureCause, AutoTask::getFailureCauseCategory,
                    AutoTask::getInternetSales, AutoTask::getSourceKind,
                    AutoTask::getVin, AutoTask::getActionLogs));

            if (autoTask == null) {
                return null;
            }

            // 从HBase查询大文本字段
            AutoTaskLog autoTaskLog = autoTaskLogRepository.findByAutoTraceId(autoTraceId);

            // 合并数据
            return mergeHBaseData(autoTask, autoTaskLog);

        } catch (Exception e) {
            log.error("从HBase查询数据失败，autoTraceId: {}, 错误: {}", autoTraceId, e.getMessage(), e);
        }
        return null;
    }

    @Override
    public boolean updateSourceKindAndDataSourceLogIdById(String autoTraceId, Integer sourceKind, Integer dataSourceLogId) {
        ShardingContextHolder.setOpType("UPDATE");
        try {
            return lambdaUpdate()
                .eq(AutoTask::getAutoTraceId, autoTraceId)
                .set(AutoTask::getSourceKind, sourceKind)
                .set(AutoTask::getDataSourceLogId, dataSourceLogId)
                .update();
        } finally {
            ShardingContextHolder.clearOpType();
        }
    }

}
