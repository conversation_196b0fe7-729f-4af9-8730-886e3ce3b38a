package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.exception.TempSkipException
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.sender.HttpSender
import org.apache.http.impl.client.CloseableHttpClient
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotConstant_2011

Map entity = enquiry
Map tempValues = null != entity.get("tempValues") ? entity.get("tempValues") : tempValues
Map postParameters = postParameters
Map reqHeaders = reqHeaders
def TbUtil = new common_2011()

if (!tempValues?.needSM4Flag) {
    initConfig(httpClient as CloseableHttpClient, [
            "Content-Type": 'application/json;charset=utf-8'
    ], tempValues)
}
String getCarInfoParamJson = "";
Map header = new HashMap();
header.put("Content-Type", tempValues?.needSM4Flag ? 'text/plain' : "application/json;charset=utf-8")
if ("true" != entity?.configInfo?.configMap?.check4pay as String)
    throw new TempSkipException(1, "不启用支付前检测功能")
JSONObject param = new JSONObject()

String insuredNo = entity?.SQ?.bizProposeNum
if (StringUtil.isEmpty(insuredNo))
    insuredNo = entity?.SQ?.efcProposeNum;
if (StringUtil.isEmpty(insuredNo))
    throw new InsReturnException("查询用投保单号为空，请确认查询数据正确性")
param = JSON.parseObject("{\"meta\":{\"pageSize\":8},\"redata\":{\"quotationNo\":\"\",\"insuredNo\":\"ASHZF14CTP16F002169D\",\"policyNo\":\"\",\"licensePlate\":\"\",\"policyHolder\":\"\",\"insurant\":\"\",\"productType\":\"\",\"quotationState\":\"\",\"dateType\":\"101\",\"startDate\":\"\",\"endDate\":\"\",\"isPrint\":\"\"}}");
param.getJSONObject("redata").put("insuredNo", insuredNo)
def reqBody = param.toJSONString()
reqBody = Robot2011Util.genBody(tempValues, reqBody)
def queryStatusResult = HttpSender.doPostWithRetry(5,
        httpClient as CloseableHttpClient, true,
        RobotConstant_2011.URL.QUERY_QUOTATION_POLICY,
        reqBody,
        null,
        header,
        "UTF-8", null, "");
queryStatusResult = Robot2011Util.decodeBody(tempValues, queryStatusResult)
JSONObject queryStatusResultObj = null
if (queryStatusResult instanceof JSONObject)
    queryStatusResultObj = (JSONObject) queryStatusResult
else
    queryStatusResultObj = JSON.parseObject(queryStatusResult)


if (queryStatusResultObj.getJSONArray("result").size() == 0)
    throw new InsReturnException("按投保单号" + insuredNo + "查无记录")
String quotationNo = (String) queryStatusResultObj.getJSONArray("result").getJSONObject(0).get("quotationNo");
if (StringUtil.isEmpty(quotationNo))
    throw new InsReturnException("报价单号为空")

param = JSON.parseObject("{\"meta\":{\"pageSize\":8},\"redata\":{\"quotationNo\":\"QCHDA17Y1417F193427D\",\"policyNo\":\"\",\"partyName\":\"\",\"policyHolder\":\"\",\"plateNo\":\"\",\"paymentType\":\"\",\"payStatus\":\"\",\"payNo\":\"\",\"chequeNo\":\"\",\"dateType\":\"101\",\"beginDate\":\"\",\"endDate\":\"\"}}")
param.getJSONObject("redata").put("quotationNo", quotationNo)
reqBody = param.toJSONString()
reqBody = Robot2011Util.genBody(tempValues, reqBody)
def queryPayResult = HttpSender.doPostWithRetry(5,
        httpClient as CloseableHttpClient,
        true,
        RobotConstant_2011.URL.QUERY_PAYMENT_RECORD,
        reqBody,
        null,
        header,
        "UTF-8", null, "");
queryPayResult = Robot2011Util.decodeBody(tempValues, queryPayResult)

JSONObject queryPayResultObj = null
if (queryPayResult instanceof JSONObject)
    queryPayResultObj = (JSONObject) queryPayResult
else
    queryPayResultObj = JSON.parseObject(queryPayResult)

JSONArray resultList = queryPayResultObj.getJSONArray("result").clone()
if (resultList.size() == 0)
    throw new TempSkipException(1, "没有需要撤销的支付记录，跳过撤销操作")
Map payStatusMap = new HashMap();
payStatusMap.put("1", "初始");
payStatusMap.put("2", "待支付");
payStatusMap.put("3", "已支付");
payStatusMap.put("4", "作废");

Map paymentTypeMap = new HashMap();
paymentTypeMap.put("1", "支票");
paymentTypeMap.put("2", "划卡");
paymentTypeMap.put("chinapay", "银联电子支付");
paymentTypeMap.put("weixin", "微信支付");

Map statusMap = new HashMap();
statusMap.put("1", "暂存");
statusMap.put("10", "申诉中");
statusMap.put("2", "待核保");
statusMap.put("3", "拒保不可申诉");
statusMap.put("4", "退回修改");
statusMap.put("5", "核保通过");
statusMap.put("6", "拒保可申诉");
statusMap.put("7", "生效");
statusMap.put("8", "删除");
statusMap.put("9", "误核申请");

Map usageTypeMap = new HashMap();
usageTypeMap.put("11024400", "机动车综合保险2014版");
usageTypeMap.put("11022400", "交强险");

JSONArray payments = new JSONArray();
for (int i = 0; i < resultList.size(); i++) {
    JSONObject payment = (JSONObject) resultList.getJSONObject(i).clone();

    if ("待支付".equals(payStatusMap.get(payment.get("payStatus")))) {
        if (null != payment.get("payStatus") && null != payStatusMap.get(payment.get("payStatus"))) {
            //            if ("初始".equals(payStatusMap.get(payment.get("payStatus"))))
            //                throw new TempSkipException(1,"支付状态为初始，不需要撤销")
            payment.put("payStatus", payStatusMap.get(payment.get("payStatus")))
        }
        if (null != payment.get("paymentType") && null != paymentTypeMap.get(payment.get("paymentType")))
            payment.put("paymentType", paymentTypeMap.get(payment.get("paymentType")))
        if (null != payment.get("status") && null != statusMap.get(payment.get("status")))
            payment.put("status", statusMap.get(payment.get("status")))
        if (null != payment.get("usageType") && null != usageTypeMap.get(payment.get("usageType")))
            payment.put("usageType", usageTypeMap.get(payment.get("usageType")))
        payments.add(payment);
    } else if ("已支付".equals(payStatusMap.get(payment.get("payStatus")))) {
        throw new InsReturnException("投保单号 " + payment.get("insuredNo") + " 支付状态为已支付，中止支付申请流程")
    }
}
if (payments.size() == 0)
    throw new TempSkipException(1, "没有需要撤销的待支付支付记录，跳过撤销操作")

param = JSON.parseObject(TbUtil.getBaseParam());
param.getJSONObject("redata").put("payments", payments)
String nullifyJson = StringUtil.chinesetoUnicode(param.toJSONString())
reqHeaders.put("Content-Type", tempValues?.needSM4Flag ? 'text/plain' : "application/json;charset=utf-8")
postParameters.clear()
entity.put("tempValues", tempValues)
Robot2011Util.genBody(tempValues, nullifyJson)

private static JSONObject initConfig(CloseableHttpClient httpClient, LinkedHashMap<String, String> header, Map tempValues) {
    def param = [
            'meta'  : [:],
            'redata': [
                    'pageCode': 'partner_select'
            ]
    ]
    def resp = HttpSender.doPost(httpClient, true, RobotConstant_2011.URL.QUERY_CONFIGS, JSON.toJSONString(param), null, header, 'UTF-8', null, '')
    def queryConfigsResponse = JSON.parseObject(resp)
    def queryConfigsResult = queryConfigsResponse.getJSONObject('result')

    tempValues.needSM4Flag = queryConfigsResponse.containsKey('b')
    if (tempValues.needSM4Flag) {
        tempValues.put('sm4Key', Robot2011Util.rsaDecode(queryConfigsResponse.getString('b')))
    }
    return queryConfigsResult
}