package renbao.edi

import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.message.TaskStatus
import com.cheche365.bc.message.TaskType
import org.apache.commons.lang3.StringUtils
import static renbao.common.renbao_dict.*


    def script  = new edi_common_2005()
    def response = root
    script.commonDeal(response)
    if (response?.Header?.responsehead?.response_code == 1) {
        def prpCmainVo = response?.Body?.GETPOLICYDETAILCONDITIONRTN?.BIZ_ENTITY?.PrpCmainVo
        def underWriteFlag = prpCmainVo?.UnderWriteFlag?.toString()
        if (["1", "3", "4", "5"].contains(underWriteFlag)) {
            def efcSuiteInfo = enquiry?.baseSuiteInfo?.efcSuiteInfo
            if (tempValues['taskType'] != TaskType.AUTO_INSURE.code) {
                def prpCitemCarVo = prpCmainVo?.PrpCitemCarVos?.PrpCitemCarVo
                prpCitemCarVo.each {
                    useNatureCode = it.UseNatureCode.toString()
                    if (StringUtils.isNotBlank(useNatureCode)) {
                        def useProp = natureTocheche(useNatureCode)
                        if (useProp)
                            enquiry['carInfo']['useProps'] = useProp
                    }
                }
                def SQ = enquiry['SQ'] = enquiry['SQ'] ?: [:]
                //交强险信息
                efcSuiteInfo['amount'] = new BigDecimal(prpCmainVo?.SumAmount?.toString() ?: "0")
                efcSuiteInfo['orgCharge'] = new BigDecimal(prpCmainVo?.SumBenchPremium?.toString() ?: "0")
                efcSuiteInfo['discountCharge'] = new BigDecimal(prpCmainVo?.SumPremium?.toString() ?: "0")
                efcSuiteInfo['discountRate'] = new BigDecimal(prpCmainVo?.Discount?.toString() ?: "0")
                //车船税信息
                def taxSuiteInfo = enquiry?.baseSuiteInfo?.taxSuiteInfo
                taxSuiteInfo['charge'] = new BigDecimal(prpCmainVo?.SumPayTax?.toString() ?: "0")
                taxSuiteInfo['delayCharge'] = new BigDecimal(prpCmainVo?.DelayPayTax?.toString() ?: "0")

                //SQ信息
                def efcCharge = new BigDecimal(prpCmainVo?.SumPremium?.toString() ?: "0")
                def taxCharge = new BigDecimal(new BigDecimal(prpCmainVo?.SumPayTax?.toString() ?: "0"))
                //交强险总保费
                SQ['efcCharge'] = efcCharge
                //车船税金额
                SQ['taxCharge'] = taxCharge

                def bizCharge = new BigDecimal(SQ.get("bizCharge")?.toString()?:"0")
                def totalCharge = bizCharge.add(efcCharge).add(taxCharge)
                SQ['totalCharge'] = totalCharge
            }
            def effectiveTime = script.getEffectiveTimeFromIns(prpCmainVo)
            efcSuiteInfo['start'] = effectiveTime['startTime']
            efcSuiteInfo['end'] = effectiveTime['endTime']
        } else if (["0", "6", "7", "9"].contains(underWriteFlag)) {
            enquiry['taskStatus'] = TaskStatus.AUTO_INSURE_WAIT_QUERY.getState()
        } else {
            def errorInfo = [:]
            errorInfo.errorcode = "11"
            errorInfo.errordesc = underWriteFlag ? insureFlag(underWriteFlag) : "核保状态查询失败!"
            enquiry.errorInfo = errorInfo
            throw new InsReturnException(errorInfo.errordesc as String)
        }
    } else {
        def msg = response?.Header?.responsehead?.error_message
        throw new InsReturnException(msg.toString())
    }

