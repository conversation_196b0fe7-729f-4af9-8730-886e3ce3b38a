package renbao.robot

import com.cheche365.bc.exception.TempSkipException
import com.cheche365.bc.model.car.Enquiry
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript

import static renbao.robot.common_market.header
import static renbao.robot.common_market.hostPrefix


@BaseScript BaseScript_Http_Enq _

def enquiry = autoTask?.taskEntity as Enquiry
if (!enquiry.order?.imgAddress || enquiry.order?.imgAddress?.size() == 0){
    throw new TempSkipException(1, "任务数据未带影像信息，跳过影像上传流程")
}

head(header(autoTask))

def prepareQuery = hostPrefix(autoTask) + "/khyx/newFront/qth/proposalsearch/prepareQuery.do"
tempValues['prepareQuery'] = prepareQuery

