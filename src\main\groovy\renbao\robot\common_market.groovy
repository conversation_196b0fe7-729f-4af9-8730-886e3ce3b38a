package renbao.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.constants.Constants
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.utils.RedisUtil
import com.cheche365.bc.utils.encrypt.AesDesEncryption
import com.cheche365.bc.utils.encrypt.EncryptEnum
import com.cheche365.bc.utils.sender.HttpSender
import org.apache.commons.lang3.StringUtils
import org.apache.http.impl.client.CloseableHttpClient
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import renbao.common.renbao_tools

import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

import static common.common_all.checkEnergyType
import static common.common_all.getAge
import static common.common_all.getBirth
import static common.common_all.getMobile
import static common.common_all.getPersonSupply
import static common.common_all.getSex
import static common.common_all.nonLocalFlag
import static common.common_all.shareAmount
import static renbao.common.renbao_dict.formatPlateColor
import static renbao.common.renbao_dict.getPlateType
import static renbao.common.renbao_dict.identifyType
import static renbao.common.renbao_dict.kindCodeNew
import static renbao.common.renbao_dict.useNatureCode
import static renbao.common.renbao_dict.useProp
import static renbao.common.renbao_util.checkDateValid
import static renbao.common.renbao_util.getIDCardValidDate



static def header(autoTask, init = false) {
    def areaAbbreviation = autoTask?.configs['areaAbbreviation']
    def cookieStr = autoTask?.configs['cookieStr']
    if (!cookieStr) {
        throw new InsReturnException('未配置cookieStr，请先配置cookieStr')
    }
    def login = autoTask?.configs['login']
    if (init) {
        def cacheCookie = RedisUtil.get(Constants.COOKIE_STRING_CACHE_KEY_PREFIX + '2005' + ":" + login)
        if (!cacheCookie || cacheCookie != cookieStr ) {
            RedisUtil.set(Constants.COOKIE_STRING_CACHE_KEY_PREFIX + '2005' + ":" + login, cookieStr, 12 * 60 * 60)
        }
    }

    def token
    cookieStr.split("; ")?.each { item ->
        def split = item.split('=')
        if (split[0] == 'USER_TOKEN') {
            token = split[1]
        }
    }
    if (!token) {
        throw new InsReturnException('配置cookieStr格式不正确，请检查cookieStr')
    }
    if (init) {
        def cacheToken = RedisUtil.get(Constants.AUTHORIZATION_CACHE_KEY_PREFIX + '2005' + ":" + login)
        if (!cacheToken || cacheToken != token) {
            RedisUtil.set(Constants.AUTHORIZATION_CACHE_KEY_PREFIX + '2005' + ":" + login, token, 12 * 60 * 60)
        }
    }
    def header = [:]
    header << ['Accept' : "application/json, text/plain, */*"]
    header << ['User-Agent' : "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]
//    header << ['Connection' : "keep-alive"]
    header << ['authorization' : token]
    header << ['Accept-Encoding' : 'gzip, deflate']
    header << ['Accept-Language' : 'zh-CN,zh;q=0.9,en;q=0.8']
    header << ['Cookie' : cookieStr]
    return header
}

static def hostPrefix(autoTask) {
    return autoTask?.configs?.hostPrefix ?: ("http://www." + autoTask?.configs?.areaAbbreviation + ".yxgl-picc.cn:9000")
}

def dateTime2date (datetime) {
    def dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    def localDate = LocalDate.parse(datetime, dateTimeFormatter)
    localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
}
def getHour(datetime) {
    def dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    def dateTime = LocalDateTime.parse(datetime, dateTimeFormatter)
    return  datetime.contains("59:59") ? dateTime.hour + 1 : dateTime.hour

}
def getMin(datetime) {
    def dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    def dateTime = LocalDateTime.parse(datetime, dateTimeFormatter)
    return  datetime.contains("59:59") ? 0 : dateTime.minute
}
def getTime(date, pattern) {
    //2020-07-21T16:00:00.000Z
    def instant = date.toInstant()
    def datetime = instant.atZone(ZoneId.of("-00:00")).toLocalDateTime()
    return datetime.format(DateTimeFormatter.ofPattern(pattern))
}
def getBjTime(s) {
    if (s) {
        def df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS")
        def date = df.parse(s)
        df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        return df.format(date)
    } else {
        return ""
    }

}

def insuredFlag(autoTask) {
    def enquiry = autoTask.taskEntity as Enquiry
    def appCard = enquiry.order.insurePerson.idCard//投保人
    def insuredCard = enquiry.order.insuredPersons.get(0).idCard//被保人
    def ownerCard = enquiry.order.carOwnerInfo.idCard//车主
    def teamFlag = ([6, 8, 9, 10].contains(enquiry.order.insurePerson.idCardType) || [6, 8, 9, 10].contains(enquiry.order.insuredPersons.get(0).idCardType) || [6, 8, 9, 10].contains(enquiry.order.carOwnerInfo.idCardType))
    autoTask?.tempValues['teamFlag'] = teamFlag
    if (appCard == insuredCard && insuredCard == ownerCard) {
        //三者同一人
        autoTask?.tempValues['insuredFlag'] = '11100000'
        if (teamFlag) {
            autoTask?.tempValues['insuredFlag'] = '11100010'
        }
    } else if (appCard == insuredCard && insuredCard != ownerCard) {
        //投保人被保人一样，车主不一样
        autoTask?.tempValues['insuredFlag'] = '11000000'
    } else if (appCard != insuredCard && appCard == ownerCard) {
        //投保人车主一致，被保人不一样
        autoTask?.tempValues['insuredFlag'] = '10100000'
        if (teamFlag) {
            autoTask?.tempValues['insuredFlag'] = '10100010'
        }
    } else if (appCard != insuredCard && insuredCard == ownerCard) {
        //被保人车主一致，投保人不一样
        autoTask?.tempValues['insuredFlag'] = '01100000'
        if (teamFlag) {
            autoTask?.tempValues['insuredFlag'] = '01100010'
        }
    } else {
        //三者都不一样
        autoTask?.tempValues['insuredFlag'] = '00000000'
    }

}
def personInfo(autoTask) {
    insuredFlag(autoTask)
    def enquiry = autoTask.taskEntity as Enquiry
    def appInfo = autoTask?.tempValues['applicantInfo']
    def age = [6, 8, 9, 10].contains(enquiry.order.insurePerson.idCardType) ? '' : getAge(enquiry.order.insurePerson.idCard) + ''
    def trans = autoTask.tempValues['trans'] as String
    def insuredType = [6, 8, 9, 10].contains(enquiry.order.insurePerson.idCardType) ? 2 : 1
    def personInfo = [
            'khyxCinsured[0].acceptlicensedate' : '',
            'khyxCinsured[0].address' : '',
            'khyxCinsured[0].age' : age,
            'khyxCinsured[0].area' : appInfo['area'],
            'khyxCinsured[0].auditStatus' : appInfo['auditStatus'] ?: '2',
            'khyxCinsured[0].beneficiaryBlackType' : '',
            'khyxCinsured[0].birthday' : appInfo['birthday'],
            'khyxCinsured[0].bongdingSource' : '',
            'khyxCinsured[0].businessCode' : '',
            'khyxCinsured[0].businessCodeDateValid' : '',
            'khyxCinsured[0].businessKindCode' : '',
            'khyxCinsured[0].causetroubletimes' : '0',
            'khyxCinsured[0].city' : appInfo['city'],
            'khyxCinsured[0].conditionCode' : '',
            'khyxCinsured[0].confirmResult' : '',
            'khyxCinsured[0].contactPersonName' : appInfo['contactPersonName'],
            'khyxCinsured[0].creditDateValid' : appInfo['creditDateValid'],
            'khyxCinsured[0].creditDateValidStart' : appInfo['creditDateValidStart'],
            'khyxCinsured[0].currentGrowth' : '',
            'khyxCinsured[0].dateValid' : appInfo['dateValid'],
            'khyxCinsured[0].dateValidStart' : '',
            'khyxCinsured[0].drivingcartype' : '',
            'khyxCinsured[0].drivinglicenseno' : '',
            'khyxCinsured[0].drivingyears' : '0',
            'khyxCinsured[0].educationCode' : appInfo['educationCode'] ?: '20',
            'khyxCinsured[0].email' : appInfo['email'],
            'khyxCinsured[0].grade' : '',
            'khyxCinsured[0].gradeName' : '',
            'khyxCinsured[0].identifyNumber' : trans == 'trans' ? (insuredType == 2 ? appInfo['identifyNumber'] : enquiry.order.insurePerson.idCard) : (insuredType == 2 ? marketEncrypt(appInfo['identifyNumber']) : appInfo['identifyNumber']),
            'khyxCinsured[0].identifyNumberShow' : '',
            'khyxCinsured[0].identifyType' : insuredType == 2 ? '31' : appInfo['identifyType'],
            'khyxCinsured[0].institution' : '',
            'khyxCinsured[0].insuredAddress' : appInfo['insuredAddress'],
            'khyxCinsured[0].insuredAddressShow' : appInfo['insuredAddress'] ? ((appInfo['insuredAddress'] as String).length() > 2 ? (appInfo['insuredAddress'] as String).substring(0, 3) + '*******************' : appInfo['insuredAddress'] + '*******************') : '',
            'khyxCinsured[0].insuredCode' : appInfo['insuredCode'],
            'khyxCinsured[0].insuredEname' : '',
            'khyxCinsured[0].insuredName' : appInfo['insuredName'],
            'khyxCinsured[0].insuredType' : appInfo['insuredType'],
            'khyxCinsured[0].latestGrowth' : '',
            'khyxCinsured[0].livingAddress' : appInfo['livingAddress'],
            'khyxCinsured[0].marriage' : '',
            'khyxCinsured[0].mobile' : appInfo['mobile'],
            'khyxCinsured[0].modelEname' : '',
            'khyxCinsured[0].nation' : '',
            'khyxCinsured[0].nationality' : 'CHN',
            'khyxCinsured[0].occupationCode' : '40000',
            'khyxCinsured[0].oldAddress' : '',
            'khyxCinsured[0].oldMobile' : '',
            'khyxCinsured[0].oldPhonenum' : '',
            'khyxCinsured[0].orgDateValidStart' : '',
            'khyxCinsured[0].phonenumber' : appInfo['phonenumber'] ?: '',
            'khyxCinsured[0].postcode' : '',
            'khyxCinsured[0].province' : appInfo['province'],
            'khyxCinsured[0].queryInsuredFlag' : '',
            'khyxCinsured[0].registFund' : '0',
            'khyxCinsured[0].resident' : 'A',
            'khyxCinsured[0].revenueCode' : appInfo['revenueCode'] ?:'',
            'khyxCinsured[0].selfYearIncome' : '',
            'khyxCinsured[0].sex' : appInfo['sex'],
            'khyxCinsured[0].signCode' : appInfo?.signMessage?.signCode ?: (appInfo?.signCode ?: ''),
            'khyxCinsured[0].processDataPrimaryKey' : appInfo?.processDataPrimaryKey ?: appInfo?.signMessage?.processDataPrimaryKey ?: '',
            'khyxCinsured[0].startDateValid' : appInfo['startDateValid'],
            'khyxCinsured[0].unifiedSocialCreditCode' : appInfo['unifiedSocialCreditCode'],
            'khyxCinsured[0].unitType' : insuredType == 2 ? '300' : '',
            'khyxCinsured[0].versionNo' : appInfo['versionNo'] ?: '',
            'khyxCinsured[0].customerCodeAll' : appInfo['customerCodeAll'] ?: ''
    ]

    def ownerInfo = autoTask?.tempValues['ownerInfo']
    def insuredInfo = autoTask?.tempValues['insuredInfo']

    if ('11100000' == autoTask?.tempValues['insuredFlag'] || '11100010' == autoTask?.tempValues['insuredFlag']) {
        personInfo << [ 'khyxCinsured[0].insuredFlag' : autoTask?.tempValues['insuredFlag']]
    } else if ('11000000' == autoTask?.tempValues['insuredFlag']) {
        //投保人被保人一样，车主不一样
        personInfo << ['khyxCinsured[0].insuredFlag' : autoTask?.tempValues['insuredFlag']]
        age = [6, 8, 9, 10].contains(enquiry.order.carOwnerInfo.idCardType) ? '' : getAge(enquiry.order.carOwnerInfo.idCard) + ''
        insuredType = [6, 8, 9, 10].contains(enquiry.order.carOwnerInfo.idCardType) ? 2 : 1
        def owner = [
                'khyxCinsured[1].insuredFlag' : autoTask?.tempValues['teamFlag'] ? '00100010' : '00100000',
                'khyxCinsured[1].acceptlicensedate' : '',
                'khyxCinsured[1].address' : '',
                'khyxCinsured[1].age' : age,
                'khyxCinsured[1].area' : ownerInfo['area'],
                'khyxCinsured[1].auditStatus' : ownerInfo['auditStatus'] ?: '2',
                'khyxCinsured[1].beneficiaryBlackType' : '',
                'khyxCinsured[1].birthday' : ownerInfo['birthday'],
                'khyxCinsured[1].bongdingSource' : '',
                'khyxCinsured[1].businessCode' : '',
                'khyxCinsured[1].businessCodeDateValid' : '',
                'khyxCinsured[1].businessKindCode' : '',
                'khyxCinsured[1].causetroubletimes' : '',
                'khyxCinsured[1].city' : ownerInfo['city'],
                'khyxCinsured[1].conditionCode' : '',
                'khyxCinsured[1].confirmResult' : '',
                'khyxCinsured[1].contactPersonName' : ownerInfo['contactPersonName'],
                'khyxCinsured[1].creditDateValid' : ownerInfo['creditDateValid'],
                'khyxCinsured[1].creditDateValidStart' : ownerInfo['creditDateValidStart'],
                'khyxCinsured[1].currentGrowth' : '',
                'khyxCinsured[1].dateValid' : ownerInfo['dateValid'],
                'khyxCinsured[1].dateValidStart' : '',
                'khyxCinsured[1].drivingcartype' : '',
                'khyxCinsured[1].drivinglicenseno' : '',
                'khyxCinsured[1].drivingyears' : '',
                'khyxCinsured[1].educationCode' : ownerInfo['educationCode'] ?: '20',
                'khyxCinsured[1].email' : ownerInfo['email'],
                'khyxCinsured[1].grade' : '',
                'khyxCinsured[1].gradeName' : '',
                'khyxCinsured[1].identifyNumber' : trans == 'trans' ? (insuredType == 2 ? ownerInfo['identifyNumber'] : enquiry.order.carOwnerInfo.idCard) : (insuredType == 2 ? marketEncrypt(ownerInfo['identifyNumber']) : ownerInfo['identifyNumber']),
                'khyxCinsured[1].identifyNumberShow' : '',
                'khyxCinsured[1].identifyType' : insuredType == 2 ? '31' : ownerInfo['identifyType'],
                'khyxCinsured[1].institution' : '',
                'khyxCinsured[1].insuredAddress' : ownerInfo['insuredAddress'],
                'khyxCinsured[1].insuredAddressShow' : ownerInfo['insuredAddress'] ? ((ownerInfo['insuredAddress'] as String).length() > 2 ? (ownerInfo['insuredAddress'] as String).substring(0, 3) + '*******************' : (ownerInfo['insuredAddress'] as String) + '*******************') : '',
                'khyxCinsured[1].insuredCode' : ownerInfo['insuredCode'],
                'khyxCinsured[1].insuredEname' : '',
                'khyxCinsured[1].insuredName' : ownerInfo['insuredName'],
                'khyxCinsured[1].insuredType' : ownerInfo['insuredType'],
                'khyxCinsured[1].latestGrowth' : '',
                'khyxCinsured[1].livingAddress' : appInfo['livingAddress'],
                'khyxCinsured[1].marriage' : '',
                'khyxCinsured[1].mobile' : ownerInfo['mobile'],
                'khyxCinsured[1].modelEname' : '',
                'khyxCinsured[1].nation' : '',
                'khyxCinsured[1].nationality' : 'CHN',
                'khyxCinsured[1].occupationCode' : '40000',
                'khyxCinsured[1].oldAddress' : '',
                'khyxCinsured[1].oldMobile' : '',
                'khyxCinsured[1].oldPhonenum' : '',
                'khyxCinsured[1].orgDateValidStart' : '',
                'khyxCinsured[1].phonenumber' : ownerInfo['phonenumber'] ?: '',
                'khyxCinsured[1].postcode' : '',
                'khyxCinsured[1].province' : ownerInfo['province'],
                'khyxCinsured[1].queryInsuredFlag' : '',
                'khyxCinsured[1].registFund' : '',
                'khyxCinsured[1].resident' : 'A',
                'khyxCinsured[1].revenueCode' : ownerInfo['revenueCode'] ?: '',
                'khyxCinsured[1].selfYearIncome' : '',
                'khyxCinsured[1].sex' : ownerInfo['sex'],
                'khyxCinsured[1].signCode' : ownerInfo?.signMessage?.signCode ?: (ownerInfo?.signCode ?: ''),
                'khyxCinsured[1].processDataPrimaryKey' : ownerInfo?.processDataPrimaryKey ?: ownerInfo?.signMessage?.processDataPrimaryKey ?: '',
                'khyxCinsured[1].startDateValid' : ownerInfo['startDateValid'],
                'khyxCinsured[1].unifiedSocialCreditCode' : ownerInfo['unifiedSocialCreditCode'],
                'khyxCinsured[1].unitType' : insuredType == 2 ? '300' : '',
                'khyxCinsured[1].versionNo' : ownerInfo['versionNo'] ?: '',
                'khyxCinsured[1].customerCodeAll' : ownerInfo['customerCodeAll'] ?: ''
        ]
        personInfo += owner
    } else if (autoTask?.tempValues['insuredFlag'] == '10100000' || autoTask?.tempValues['insuredFlag'] == '10100010') {
        personInfo << ['khyxCinsured[0].insuredFlag' : autoTask?.tempValues['insuredFlag']]
        age = [6, 8, 9, 10].contains(enquiry.order.insuredPersons.get(0).idCardType) ? '' : getAge(enquiry.order.insuredPersons.get(0).idCard) + ''
        insuredType = [6, 8, 9, 10].contains(enquiry.order.insuredPersons.get(0).idCardType) ? 2 : 1
        def insured = [
                'khyxCinsured[1].insuredFlag' : '01000000',
                'khyxCinsured[1].acceptlicensedate' : '',
                'khyxCinsured[1].address' : '',
                'khyxCinsured[1].age' : age,
                'khyxCinsured[1].area' : insuredInfo['area'],
                'khyxCinsured[1].auditStatus' : insuredInfo['auditStatus'] ?: '2',
                'khyxCinsured[1].beneficiaryBlackType' : '',
                'khyxCinsured[1].birthday' : insuredInfo['birthday'],
                'khyxCinsured[1].bongdingSource' : '',
                'khyxCinsured[1].businessCode' : '',
                'khyxCinsured[1].businessCodeDateValid' : '',
                'khyxCinsured[1].businessKindCode' : '',
                'khyxCinsured[1].causetroubletimes' : '',
                'khyxCinsured[1].city' : insuredInfo['city'],
                'khyxCinsured[1].conditionCode' : '',
                'khyxCinsured[1].confirmResult' : '',
                'khyxCinsured[1].contactPersonName' : insuredInfo['contactPersonName'] ?: '',
                'khyxCinsured[1].creditDateValid' : insuredInfo['creditDateValid'] ?: '',
                'khyxCinsured[1].creditDateValidStart' : insuredInfo['creditDateValidStart'] ?: '',
                'khyxCinsured[1].currentGrowth' : '',
                'khyxCinsured[1].dateValid' : insuredInfo['dateValid'],
                'khyxCinsured[1].dateValidStart' : '',
                'khyxCinsured[1].drivingcartype' : '',
                'khyxCinsured[1].drivinglicenseno' : '',
                'khyxCinsured[1].drivingyears' : '',
                'khyxCinsured[1].educationCode' : insuredInfo['educationCode'] ?: '20',
                'khyxCinsured[1].email' : insuredInfo['email'],
                'khyxCinsured[1].grade' : '',
                'khyxCinsured[1].gradeName' : '',
                'khyxCinsured[1].identifyNumber' : trans == 'trans' ? (insuredType == 2 ? insuredInfo['identifyNumber'] : enquiry.order.insuredPersons[0].idCard) : (insuredType == 2 ? marketEncrypt(insuredInfo['identifyNumber']) : insuredInfo['identifyNumber']),
                'khyxCinsured[1].identifyNumberShow' : '',
                'khyxCinsured[1].identifyType' : insuredType == 2 ? '31' : insuredInfo['identifyType'],
                'khyxCinsured[1].institution' : '',
                'khyxCinsured[1].insuredAddress' : insuredInfo['insuredAddress'],
                'khyxCinsured[1].insuredAddressShow' : insuredInfo['insuredAddress'] ? ((insuredInfo['insuredAddress'] as String).length() > 2 ? (insuredInfo['insuredAddress'] as String).substring(0, 3) + '******' : (insuredInfo['insuredAddress'] as String) + '******') : '',
                'khyxCinsured[1].insuredCode' : insuredInfo['insuredCode'],
                'khyxCinsured[1].insuredEname' : '',
                'khyxCinsured[1].insuredName' : insuredInfo['insuredName'],
                'khyxCinsured[1].insuredType' : insuredInfo['insuredType'],
                'khyxCinsured[1].latestGrowth' : '',
                'khyxCinsured[1].livingAddress' : insuredInfo['livingAddress'],
                'khyxCinsured[1].marriage' : '',
                'khyxCinsured[1].mobile' : insuredInfo['mobile'],
                'khyxCinsured[1].modelEname' : '',
                'khyxCinsured[1].nation' : '',
                'khyxCinsured[1].nationality' : 'CHN',
                'khyxCinsured[1].occupationCode' : '40000',
                'khyxCinsured[1].oldAddress' : '',
                'khyxCinsured[1].oldMobile' : '',
                'khyxCinsured[1].oldPhonenum' : '',
                'khyxCinsured[1].orgDateValidStart' : '',
                'khyxCinsured[1].phonenumber' : insuredInfo['phonenumber'] ?: '',
                'khyxCinsured[1].postcode' : '',
                'khyxCinsured[1].province' : insuredInfo['province'],
                'khyxCinsured[1].queryInsuredFlag' : '',
                'khyxCinsured[1].registFund' : '',
                'khyxCinsured[1].resident' : 'A',
                'khyxCinsured[1].revenueCode' : insuredInfo['revenueCode'] ?: '',
                'khyxCinsured[1].selfYearIncome' : '',
                'khyxCinsured[1].sex' : insuredInfo['sex'],
                'khyxCinsured[1].signCode' : insuredInfo?.signMessage?.signCode ?: (insuredInfo?.signCode ?: ''),
                'khyxCinsured[1].processDataPrimaryKey' : insuredInfo?.processDataPrimaryKey ?: insuredInfo?.signMessage?.processDataPrimaryKey ?: '',
                'khyxCinsured[1].startDateValid' : insuredInfo['startDateValid'],
                'khyxCinsured[1].unifiedSocialCreditCode' : insuredInfo['unifiedSocialCreditCode'],
                'khyxCinsured[1].unitType' : insuredType == 2 ? '300' : '',
                'khyxCinsured[1].versionNo' : insuredInfo['versionNo'] ?: '',
                'khyxCinsured[1].customerCodeAll' : insuredInfo['customerCodeAll'] ?: ''
        ]
        personInfo += insured
    } else if (autoTask?.tempValues['insuredFlag'] == '01100000' || '01100010' == autoTask?.tempValues['insuredFlag']) {
        personInfo << ['khyxCinsured[0].insuredFlag' : '10000000']
        age = [6, 8, 9, 10].contains(enquiry.order.insuredPersons.get(0).idCardType) ? '' : getAge(enquiry.order.insuredPersons.get(0).idCard) + ''
        insuredType = [6, 8, 9, 10].contains(enquiry.order.insuredPersons.get(0).idCardType) ? 2 : 1
        def insuredOwner = [
                'khyxCinsured[1].insuredFlag' : autoTask?.tempValues['insuredFlag'],
                'khyxCinsured[1].acceptlicensedate' : '',
                'khyxCinsured[1].address' : '',
                'khyxCinsured[1].age' : age,
                'khyxCinsured[1].area' : insuredInfo['area'],
                'khyxCinsured[1].auditStatus' : insuredInfo['auditStatus'] ?: '2',
                'khyxCinsured[1].beneficiaryBlackType' : '',
                'khyxCinsured[1].birthday' : insuredInfo['birthday'],
                'khyxCinsured[1].bongdingSource' : '',
                'khyxCinsured[1].businessCode' : '',
                'khyxCinsured[1].businessCodeDateValid' : '',
                'khyxCinsured[1].businessKindCode' : '',
                'khyxCinsured[1].causetroubletimes' : '',
                'khyxCinsured[1].city' : insuredInfo['city'],
                'khyxCinsured[1].conditionCode' : '',
                'khyxCinsured[1].confirmResult' : '',
                'khyxCinsured[1].contactPersonName' : insuredInfo['contactPersonName'] ?: '',
                'khyxCinsured[1].creditDateValid' : insuredInfo['creditDateValid'] ?: '',
                'khyxCinsured[1].creditDateValidStart' : insuredInfo['creditDateValidStart'] ?: '',
                'khyxCinsured[1].currentGrowth' : '',
                'khyxCinsured[1].dateValid' : insuredInfo['dateValid'],
                'khyxCinsured[1].dateValidStart' : '',
                'khyxCinsured[1].drivingcartype' : '',
                'khyxCinsured[1].drivinglicenseno' : '',
                'khyxCinsured[1].drivingyears' : '',
                'khyxCinsured[1].educationCode' : insuredInfo['educationCode'] ?: '20',
                'khyxCinsured[1].email' : insuredInfo['email'],
                'khyxCinsured[1].grade' : '',
                'khyxCinsured[1].gradeName' : '',
                'khyxCinsured[1].identifyNumber' : trans == 'trans' ? (insuredType == 2 ? insuredInfo['identifyNumber'] : enquiry.order.insuredPersons[0].idCard) : (insuredType == 2 ? marketEncrypt(insuredInfo['identifyNumber']) : insuredInfo['identifyNumber']),
                'khyxCinsured[1].identifyNumberShow' : '',
                'khyxCinsured[1].identifyType' : insuredType == 2 ? '31' : insuredInfo['identifyType'],
                'khyxCinsured[1].institution' : '',
                'khyxCinsured[1].insuredAddress' : insuredInfo['insuredAddress'],
                'khyxCinsured[1].insuredAddressShow' : insuredInfo['insuredAddress'] ? ((insuredInfo['insuredAddress'] as String).length() > 2 ? (insuredInfo['insuredAddress'] as String).substring(0, 3) + '******' : (insuredInfo['insuredAddress'] as String) + '******') : '',
                'khyxCinsured[1].insuredCode' : insuredInfo['insuredCode'],
                'khyxCinsured[1].insuredEname' : '',
                'khyxCinsured[1].insuredName' : insuredInfo['insuredName'],
                'khyxCinsured[1].insuredType' : insuredInfo['insuredType'],
                'khyxCinsured[1].latestGrowth' : '',
                'khyxCinsured[1].livingAddress' : insuredInfo['livingAddress'],
                'khyxCinsured[1].marriage' : '',
                'khyxCinsured[1].mobile' : insuredInfo['mobile'],
                'khyxCinsured[1].modelEname' : '',
                'khyxCinsured[1].nation' : '',
                'khyxCinsured[1].nationality' : 'CHN',
                'khyxCinsured[1].occupationCode' : '40000',
                'khyxCinsured[1].oldAddress' : '',
                'khyxCinsured[1].oldMobile' : '',
                'khyxCinsured[1].oldPhonenum' : '',
                'khyxCinsured[1].orgDateValidStart' : '',
                'khyxCinsured[1].phonenumber' : insuredInfo['phonenumber'] ?: '',
                'khyxCinsured[1].postcode' : '',
                'khyxCinsured[1].province' : insuredInfo['province'],
                'khyxCinsured[1].queryInsuredFlag' : '',
                'khyxCinsured[1].registFund' : '',
                'khyxCinsured[1].resident' : 'A',
                'khyxCinsured[1].revenueCode' : insuredInfo['revenueCode'] ?: '',
                'khyxCinsured[1].selfYearIncome' : '',
                'khyxCinsured[1].sex' : insuredInfo['sex'],
                'khyxCinsured[1].signCode' : insuredInfo?.signMessage?.signCode ?: (insuredInfo?.signCode ?: ''),
                'khyxCinsured[1].processDataPrimaryKey' : insuredInfo?.processDataPrimaryKey ?: insuredInfo?.signMessage?.processDataPrimaryKey ?: '',
                'khyxCinsured[1].startDateValid' : insuredInfo['startDateValid'],
                'khyxCinsured[1].unifiedSocialCreditCode' : insuredInfo['unifiedSocialCreditCode'],
                'khyxCinsured[1].unitType' : insuredType == 2 ? '300' : '',
                'khyxCinsured[1].versionNo' : insuredInfo['versionNo'] ?: '',
                'khyxCinsured[1].customerCodeAll' : insuredInfo['customerCodeAll'] ?: ''
        ]
        personInfo += insuredOwner
    } else {
        personInfo << ['khyxCinsured[0].insuredFlag' : '10000000']
        insuredType = [6, 8, 9, 10].contains(enquiry.order.insuredPersons.get(0).idCardType) ? 2 : 1
        personInfo += [
                'khyxCinsured[1].insuredFlag' : '01000000',
                'khyxCinsured[1].acceptlicensedate' : '',
                'khyxCinsured[1].address' : '',
                'khyxCinsured[1].age' : [6, 8, 9, 10].contains(enquiry.order.insuredPersons.get(0).idCardType) ? '' : getAge(enquiry.order.insuredPersons.get(0).idCard) + '',
                'khyxCinsured[1].area' : insuredInfo['area'],
                'khyxCinsured[1].auditStatus' : insuredInfo['auditStatus'] ?: '2',
                'khyxCinsured[1].beneficiaryBlackType' : '',
                'khyxCinsured[1].birthday' : insuredInfo['birthday'],
                'khyxCinsured[1].bongdingSource' : '',
                'khyxCinsured[1].businessCode' : '',
                'khyxCinsured[1].businessCodeDateValid' : '',
                'khyxCinsured[1].businessKindCode' : '',
                'khyxCinsured[1].causetroubletimes' : '',
                'khyxCinsured[1].city' : insuredInfo['city'],
                'khyxCinsured[1].conditionCode' : '',
                'khyxCinsured[1].confirmResult' : '',
                'khyxCinsured[1].contactPersonName' : insuredInfo['contactPersonName'],
                'khyxCinsured[1].creditDateValid' : insuredInfo['creditDateValid'],
                'khyxCinsured[1].creditDateValidStart' : insuredInfo['creditDateValidStart'],
                'khyxCinsured[1].currentGrowth' : '',
                'khyxCinsured[1].dateValid' : insuredInfo['dateValid'],
                'khyxCinsured[1].dateValidStart' : '',
                'khyxCinsured[1].drivingcartype' : '',
                'khyxCinsured[1].drivinglicenseno' : '',
                'khyxCinsured[1].drivingyears' : '',
                'khyxCinsured[1].educationCode' : insuredInfo['educationCode'] ?: '20',
                'khyxCinsured[1].email' : insuredInfo['email'],
                'khyxCinsured[1].grade' : '',
                'khyxCinsured[1].gradeName' : '',
                'khyxCinsured[1].identifyNumber' :  trans == 'trans' ? (insuredType == 2 ? insuredInfo['identifyNumber'] : enquiry.order.insuredPersons[0].idCard) : (insuredType == 2 ? marketEncrypt(insuredInfo['identifyNumber']) : insuredInfo['identifyNumber']),
                'khyxCinsured[1].identifyNumberShow' : '',
                'khyxCinsured[1].identifyType' : insuredType == 2 ? '31' : insuredInfo['identifyType'],
                'khyxCinsured[1].institution' : '',
                'khyxCinsured[1].insuredAddress' : insuredInfo['insuredAddress'],
                'khyxCinsured[1].insuredAddressShow' : insuredInfo['insuredAddress'] ? ((insuredInfo['insuredAddress'] as String).length() > 2 ? (insuredInfo['insuredAddress'] as String).substring(0, 3) + '******' : (insuredInfo['insuredAddress'] as String) + '******') : '',
                'khyxCinsured[1].insuredCode' : insuredInfo['insuredCode'],
                'khyxCinsured[1].insuredEname' : '',
                'khyxCinsured[1].insuredName' : insuredInfo['insuredName'],
                'khyxCinsured[1].insuredType' : insuredInfo['insuredType'],
                'khyxCinsured[1].latestGrowth' : '',
                'khyxCinsured[1].livingAddress' : insuredInfo['livingAddress'],
                'khyxCinsured[1].marriage' : '',
                'khyxCinsured[1].mobile' : insuredInfo['mobile'],
                'khyxCinsured[1].modelEname' : '',
                'khyxCinsured[1].nation' : '',
                'khyxCinsured[1].nationality' : 'CHN',
                'khyxCinsured[1].occupationCode' : '40000',
                'khyxCinsured[1].oldAddress' : '',
                'khyxCinsured[1].oldMobile' : '',
                'khyxCinsured[1].oldPhonenum' : '',
                'khyxCinsured[1].orgDateValidStart' : '',
                'khyxCinsured[1].phonenumber' : insuredInfo['phonenumber'] ?: '',
                'khyxCinsured[1].postcode' : '',
                'khyxCinsured[1].province' : insuredInfo['province'],
                'khyxCinsured[1].queryInsuredFlag' : '',
                'khyxCinsured[1].registFund' : '',
                'khyxCinsured[1].resident' : 'A',
                'khyxCinsured[1].revenueCode' : insuredInfo['revenueCode'] ?: '',
                'khyxCinsured[1].selfYearIncome' : '',
                'khyxCinsured[1].sex' : insuredInfo['sex'],
                'khyxCinsured[1].signCode' : insuredInfo?.signMessage?.signCode ?: (insuredInfo?.signCode ?: ''),
                'khyxCinsured[1].processDataPrimaryKey' : insuredInfo?.processDataPrimaryKey ?: insuredInfo?.signMessage?.processDataPrimaryKey ?: '',
                'khyxCinsured[1].startDateValid' : insuredInfo['startDateValid'],
                'khyxCinsured[1].unifiedSocialCreditCode' : insuredInfo['unifiedSocialCreditCode'],
                'khyxCinsured[1].unitType' : insuredType == 2 ? '300' : '',
                'khyxCinsured[1].versionNo' : insuredInfo['versionNo'] ?: '',
                'khyxCinsured[1].customerCodeAll' : insuredInfo['customerCodeAll'] ?: ''
        ]
        insuredType = [6, 8, 9, 10].contains(enquiry.order.carOwnerInfo.idCardType) ? 2 : 1
        personInfo += [
                'khyxCinsured[2].insuredFlag' : autoTask?.tempValues['teamFlag'] ? '00100010' : '00100000',
                'khyxCinsured[2].acceptlicensedate' : '',
                'khyxCinsured[2].address' : '',
                'khyxCinsured[2].age' : [6, 8, 9, 10].contains(enquiry.order.carOwnerInfo.idCardType) ? '' : getAge(enquiry.order.carOwnerInfo.idCard) + '',
                'khyxCinsured[2].area' : ownerInfo['area'],
                'khyxCinsured[2].auditStatus' : ownerInfo['auditStatus'] ?: '2',
                'khyxCinsured[2].beneficiaryBlackType' : '',
                'khyxCinsured[2].birthday' : ownerInfo['birthday'],
                'khyxCinsured[2].bongdingSource' : '',
                'khyxCinsured[2].businessCode' : '',
                'khyxCinsured[2].businessCodeDateValid' : '',
                'khyxCinsured[2].businessKindCode' : '',
                'khyxCinsured[2].causetroubletimes' : '',
                'khyxCinsured[2].city' : ownerInfo['city'],
                'khyxCinsured[2].conditionCode' : '',
                'khyxCinsured[2].confirmResult' : '',
                'khyxCinsured[2].contactPersonName' : ownerInfo['contactPersonName'] ?: '',
                'khyxCinsured[2].creditDateValid' : ownerInfo['creditDateValid'] ?: '',
                'khyxCinsured[2].creditDateValidStart' : ownerInfo['creditDateValid'] ?: '',
                'khyxCinsured[2].currentGrowth' : '',
                'khyxCinsured[2].dateValid' : ownerInfo['dateValid'],
                'khyxCinsured[2].dateValidStart' : '',
                'khyxCinsured[2].drivingcartype' : '',
                'khyxCinsured[2].drivinglicenseno' : '',
                'khyxCinsured[2].drivingyears' : '',
                'khyxCinsured[2].educationCode' : ownerInfo['educationCode'] ?: '20',
                'khyxCinsured[2].email' : ownerInfo['email'],
                'khyxCinsured[2].grade' : '',
                'khyxCinsured[2].gradeName' : '',
                'khyxCinsured[2].identifyNumber' : trans == 'trans' ? (insuredType == 2 ? ownerInfo['identifyNumber'] : enquiry.order.carOwnerInfo.idCard) : (insuredType == 2 ? marketEncrypt(ownerInfo['identifyNumber']) : ownerInfo['identifyNumber']),
                'khyxCinsured[2].identifyNumberShow' : '',
                'khyxCinsured[2].identifyType' : insuredType == 2 ? '31' : ownerInfo['identifyType'],
                'khyxCinsured[2].institution' : '',
                'khyxCinsured[2].insuredAddress' : ownerInfo['insuredAddress'],
                'khyxCinsured[2].insuredAddressShow' : ownerInfo['insuredAddress'] ? ((ownerInfo['insuredAddress'] as String).length() > 2 ? (ownerInfo['insuredAddress'] as String).substring(0, 3) + '******' : (ownerInfo['insuredAddress'] as String) + '******') : '',
                'khyxCinsured[2].insuredCode' : ownerInfo['insuredCode'],
                'khyxCinsured[2].insuredEname' : '',
                'khyxCinsured[2].insuredName' : ownerInfo['insuredName'],
                'khyxCinsured[2].insuredType' : ownerInfo['insuredType'],
                'khyxCinsured[2].latestGrowth' : '',
                'khyxCinsured[2].livingAddress' : appInfo['livingAddress'],
                'khyxCinsured[2].marriage' : '',
                'khyxCinsured[2].mobile' : ownerInfo['mobile'],
                'khyxCinsured[2].modelEname' : '',
                'khyxCinsured[2].nation' : '',
                'khyxCinsured[2].nationality' : 'CHN',
                'khyxCinsured[2].occupationCode' : '40000',
                'khyxCinsured[2].oldAddress' : '',
                'khyxCinsured[2].oldMobile' : '',
                'khyxCinsured[2].oldPhonenum' : '',
                'khyxCinsured[2].orgDateValidStart' : '',
                'khyxCinsured[2].phonenumber' : ownerInfo['phonenumber'] ?: '',
                'khyxCinsured[2].postcode' : '',
                'khyxCinsured[2].province' : ownerInfo['province'],
                'khyxCinsured[2].queryInsuredFlag' : '',
                'khyxCinsured[2].registFund' : '',
                'khyxCinsured[2].resident' : 'A',
                'khyxCinsured[2].revenueCode' : ownerInfo['revenueCode'] ?: '',
                'khyxCinsured[2].selfYearIncome' : '',
                'khyxCinsured[2].sex' : ownerInfo['sex'],
                'khyxCinsured[2].signCode' : ownerInfo?.signMessage?.signCode ?: (ownerInfo?.signCode ?: ''),
                'khyxCinsured[2].processDataPrimaryKey' : ownerInfo?.processDataPrimaryKey ?: ownerInfo?.signMessage?.processDataPrimaryKey ?: '',
                'khyxCinsured[2].startDateValid' : ownerInfo['startDateValid'],
                'khyxCinsured[2].unifiedSocialCreditCode' : ownerInfo['unifiedSocialCreditCode'] ?: '',
                'khyxCinsured[2].unitType' : insuredType == 2 ? '300' : '',
                'khyxCinsured[2].versionNo' : ownerInfo['versionNo'] ?: '',
                'khyxCinsured[2].customerCodeAll' : ownerInfo['customerCodeAll'] ?: ''
        ]
    }
    return personInfo
}
def initKind(autoTask) {
    def kindInfos = [:]
    def enquiry = autoTask?.taskEntity as Enquiry
    int index = 0
    if (enquiry.order?.suiteInfo?.efcSuiteInfo?.start) {
        kindInfos += [
                'prpCitemKindVos[0].amount' : '200000',
                'prpCitemKindVos[0].kindCode' : '051074',
                'prpCitemKindVos[0].kindName' : '交强',
                'prpCitemKindVos[0].chooseFlag' : 'true'
        ]
        index = 1
    }
    if (enquiry.order?.suiteInfo?.bizSuiteInfo?.start) {
        def shareAmount = shareAmount(enquiry)
        def suites = enquiry.order?.suiteInfo?.bizSuiteInfo?.suites
        def vehicleInspection = false
        def nEChargerDamage = false
        def nEChargerDuty = false
        suites.eachWithIndex { key, value, i ->
            def kindCode = kindCodeNew(key)
            if (!kindCode) {
                throw new RuntimeException('存在不支持的险种：' + value.name)
            }
            def kindName = insCodeName(kindCode)
            def kindInfoKindCode = 'prpCitemKindVos['+ (i + index) + '].kindCode'
            def kindInfoKindName = 'prpCitemKindVos['+ (i + index) + '].kindName'
            def kindInfoKindChooseFlag = 'prpCitemKindVos['+ (i + index) + '].chooseFlag'
            def kindInfo = [
                    (kindInfoKindCode) : kindCode,
                    (kindInfoKindName) : kindName,
                    (kindInfoKindChooseFlag) : 'true'
            ]
            def amount = value.amount
            if (amount > 1) {
                amount = amount.intValue()
            }
            def kindInfoAmount = 'prpCitemKindVos['+ (i + index) + '].amount'
            def unitAmount = 0
            def quantity = 0

            switch (kindCode) {
                case  '051050' :
                    amount = autoTask.tempValues['actualVal'].toString().toBigDecimal()
                    break
                case '051051' :
                    amount = (amount/10000).intValue()
                    break
                case ['051065', '051066', '051067', '051068'].contains(kindCode) :
                    kindInfoAmount = 'prpCitemKindVos['+ (i + index) + '].deductibleRate'
                    break
                case ['051058'] :
                    amount = 300
                    quantity = 3
                    unitAmount = 100
                    break
                case ['051080', '051064', '051081', '051079'] :
                    if (kindCode == '051079')
                        vehicleInspection = true
                    quantity = Double.valueOf(amount).intValue()
                    amount = 0
                    break
                case ['051059'] :
                    amount = 0
                    break
                case ['051073'] :
                    quantity = enquiry.order.carInfo.seatCnt - 1
                    unitAmount = amount
                    amount = amount * quantity
                    def sharedAmountFlag = 'prpCitemKindVos['+ (i + index) + '].sharedAmountFlag'
                    if (value.share) {
                        def mainKey = key - 'NIHC'
                        unitAmount = shareAmount[(mainKey)].toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).intValue()
                        amount = unitAmount * quantity
                        kindInfo << [(sharedAmountFlag) : '1']
                    } else {
                        kindInfo << [(kindInfoAmount) : '0']
                    }
                    break
                case ['051063', '051072'] :
                    def sharedAmountFlag = 'prpCitemKindVos['+ (i + index) + '].sharedAmountFlag'
                    if (value.share) {
                        def mainKey = key - 'NIHC'
                        amount = shareAmount[(mainKey)].toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).intValue().toString()
                        kindInfo << [(sharedAmountFlag) : '1']
                    } else {
                        kindInfo << [(kindInfoAmount) : '0']
                    }
                    break
                case ['051086'] :
                    amount = 0
                    nEChargerDamage = true
                    def kindInfoUnitAmount = 'prpCitemKindVos['+ (i + index) + '].unitAmount'
                    kindInfo << [(kindInfoUnitAmount) :  '']
                    break
                case ['051087'] :
                    amount = 0
                    nEChargerDuty = true
                    def kindInfoUnitAmount = 'prpCitemKindVos['+ (i + index) + '].unitAmount'
                    kindInfo << [(kindInfoUnitAmount) : '']
                    break
            }
            if (amount) {
                kindInfo << [(kindInfoAmount) : amount + '']
            }
            if (unitAmount) {
                def kindInfoUnitAmount = 'prpCitemKindVos['+ (i + index) + '].unitAmount'
                kindInfo << [(kindInfoUnitAmount) : unitAmount + '']
            }
            if (quantity) {
                def kindInfoQuantity = 'prpCitemKindVos['+ (i + index) + '].quantity'
                kindInfo << [(kindInfoQuantity) : quantity + '']
            }
            kindInfos += kindInfo
        }
        if (vehicleInspection) {
            def vehicleInspectionInfo = [:]
            def tools = new renbao_tools()
            def defaultArray = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10']
            def clause = tools.getClauseCodeByAreaCode(enquiry?.order?.insureArea?.city) ?: tools.getClauseCodeByAreaCode(enquiry?.order?.insureArea?.province) ?: defaultArray
            def subArray = defaultArray - clause
            defaultArray.eachWithIndex { tag, clauseIndex ->
                def itemCode = 'safeCheckItemList[' + clauseIndex + '].itemCode'
                def itemName = 'safeCheckItemList[' + clauseIndex + '].itemName'
                def quantity = 'safeCheckItemList[' + clauseIndex + '].quantity'
                vehicleInspectionInfo << [(itemCode) : tag]
                vehicleInspectionInfo << [(itemName): tools.getClauseNameByCode(tag)]
                if (subArray && subArray.contains(tag)) {
                    vehicleInspectionInfo << [(quantity) : '']
                } else {
                    vehicleInspectionInfo << [(quantity) : '1']
                }

            }
            kindInfos += vehicleInspectionInfo
        }
        if (nEChargerDamage || nEChargerDuty) {
            def neChargerLocationType = enquiry?.misc?.supplyParam?.neChargerLocationType ?: autoTask.configs?.neChargerLocationType
            if (neChargerLocationType == '3') {
                neChargerLocationType = '2'
            }
            def neCharge = [
                    'chargingPileInfoList[0].seriNo' : '1',
                    'chargingPileInfoList[0].chargingPileModel' : enquiry?.misc?.supplyParam?.neChargerModel ?: autoTask.configs?.neChargerModel,
                    'chargingPileInfoList[0].chargingPileCode' : enquiry?.misc?.supplyParam?.neChargerNo ?: autoTask.configs?.neChargerNo,
                    'chargingPileInfoList[0].address' : enquiry?.misc?.supplyParam?.neChargerAddress ?: autoTask.configs?.neChargerAddress,
                    'chargingPileInfoList[0].chargingPileType' : enquiry?.misc?.supplyParam?.neChargerType ?: autoTask.configs?.neChargerType,
                    'chargingPileInfoList[0].addressType' : neChargerLocationType,
                    'chargingPileInfoList[0].chargingPileUseYears' : enquiry?.misc?.supplyParam?.neChargerWarranty ?: autoTask.configs?.neChargerWarranty
            ]
            if (nEChargerDamage) {
                neCharge << ['chargingPileInfoList[0].pileLossAmount' : enquiry.order?.suiteInfo?.bizSuiteInfo?.suites?.get("NEChargerDamage")?.amount?.intValue() + ""]
            }
            if (nEChargerDuty) {
                neCharge << ['chargingPileInfoList[0].pileLiabilityAmount' : enquiry.order?.suiteInfo?.bizSuiteInfo?.suites?.get("NEChargerDuty")?.amount?.intValue() + ""]
            }
            kindInfos += neCharge
        }
    }

    return kindInfos
}

def kindCode (key){
    def map = [
            "051050" : "VehicleDamage", //机动车损失保险
            "051051" : "ThirdParty", //第三者责任保险
            "051052" : "Driver", //车上人员责任险（司机）
            "051053" : "Passenger", //车上人员责任险（乘客）
            "051078" : "Theft", //盗抢险
            "051055" : "ExtraDevice", //附加新增加设备损失险
            "051054" : "Wheel", //附加车轮单独损失险
            "051056" : "Scratch", //附加车身划痕损失险
            "051058" : "CompensationDuringRepair", //附加修理期间费用补偿险
            "050253" : "SpecifyingPlant", //附加保险人指定修理厂特约险
            "051059" : "EngineDamageExclude", //发动机损坏除外特约条款
            "051060" : "GoodsOnVehicle", //车上货物责任险
            "050643" : "CompensationForMentalDistress", //精神损害抚慰金责任险
            "050451" : "NonInHealthCare", //附加医保外用药责任险
            "051064" : "RoadsideService", //道路救援服务特约条款
            "051079" : "VehicleInspection", //车辆安全检测特约条款
            "051080" : "DesignatedDriving", //代为驾驶服务特约条款
            "051081" : "SendForInspection", //代为送检服务特约条款
            "051063" : "NIHCThirdParty", //附加医保外用药责任险(三者险)
            "051072" : "NIHCDriver", //附加医保外用药责任险(司机险)
            "051073" : "NIHCPassenger", //附加医保外用药责任险(乘客险)
            "051061" : "CFMDThirdParty", //精神损害抚慰金责任险(三者险)
            "051083" : "CFMDDriver", //精神损害抚慰金责任险(司机险)
            "051071" : "CFMDPassenger", //精神损害抚慰金责任险(乘客)
            "051062" : "HolidayDouble", //附加法定节假日限额翻倍险
            "051065" : "ANCVehicleDamage", //附加绝对免赔率特约条款(机动车损失保险)
            "051066" : "ANCThirdParty", //附加绝对免赔率特约条款(机动车第三者责任保险)
            "051067" : "ANCDriver", //附加绝对免赔率特约条款(机动车车上人员责任保险(司机))
            "051068" : "ANCPassenger", //附加绝对免赔率特约条款(机动车车上人员责任保险(乘客))
            //不支持的险种
            "051082" : "unknown1",    //附加绝对免赔率特约险（盗抢险）
            "051069" : "unknown2",    //附加起重、装卸、挖掘车辆损失扩展条款
            "051070" : "unknown3",    //附加特种车辆固定设备、仪器损坏扩展条款
            "051074" : "unknown4",    //机动车交通事故责任强制
            "051085"               :  "NEGridBugDamage", //电网
            "051086"               :  "NEChargerDamage",//充电桩损失
            "051087"                 :  "NEChargerDuty" //充电桩责任
    ]
    return map.get(key);
}


def insCodeName(key) {
    def map = [
            "051050" : "机动车损失保险",
            "051051" : "第三者责任险",
            "051078" : "盗抢险",
            "051052" : "车上人员责任保险（司机）",
            "051053" : "车上人员责任保险（乘客）",
            "051056" : "车身划痕损失险",
            "051055" : "新增加设备损失险",
            "051060" : "附加车上货物责任险",
            "051061" : "附加精神损害抚慰金责任险（机动车第三者责任保险）",
            "051062" : "法定节假日限额翻倍险",
            "051054" : "车轮单独损失险",
            "051058" : "附加修理期间费用补偿险",
            "051064" : "机动车增值服务特约条款（道路救援服务）",
            "051079" : "机动车增值服务特约条款（车辆安全检测）",
            "051080" : "机动车增值服务特约条款（代为驾驶服务）",
            "051081" : "机动车增值服务特约条款（代为送检服务）",
            "051063" : "医保外医疗费用责任险（机动车第三者责任保险）",
            "051072" : "医保外医疗费用责任险（车上人员责任保险（司机））",
            "051073" : "医保外医疗费用责任险（车上人员责任保险（乘客））",
            "051065" : "附加绝对免赔率特约险（机动车损失保险）",
            "051066" : "附加绝对免赔率特约险（机动车第三者责任保险）",
            "051067" : "附加绝对免赔率特约险（车上人员责任保险（司机））",
            "051068" : "附加绝对免赔率特约险（车上人员责任保险（乘客））",
            "051082" : "附加绝对免赔率特约险（盗抢险）",
            "051069" : "附加起重、装卸、挖掘车辆损失扩展条款",
            "051070" : "附加特种车辆固定设备、仪器损坏扩展条款",
            "051059" : "发动机进水损坏除外特约条款",
            "051083" : "精神损害抚慰金责任险（车上人员责任保险（司机））",
            "051071" : "精神损害抚慰金责任险（车上人员责任保险（乘客））",
            "051085" : "附加外部电网故障损失险",
            "051086" : "附加自用充电桩损失保险",
            "051087" : "附加自用充电桩责任保险",
            "051074" : "机动车交通事故责任强制保险"
    ]
    return map.get(key)
}
def getUseYear (start, end) {
    def dt1 = LocalDateTime.parse(start, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
    def dt2 = LocalDateTime.parse(end, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
    def year = ChronoUnit.YEARS.between(dt1, dt2)
    def month = ChronoUnit.MONTHS.between(dt1, dt2)
    if (year == 0) {
        return month < 9 ? 0 : 1
    }
    return (month % 12) < 6 ? year : year + 1
}
def prpCmain(autoTask) {
    def enquiry = autoTask?.taskEntity as Enquiry
    def endDateBi = enquiry.order.suiteInfo?.bizSuiteInfo?.end ? dateTime2date(enquiry.order.suiteInfo?.bizSuiteInfo?.end) : LocalDate.now().plusDays(1).toString()
    def startDateBi = enquiry.order.suiteInfo?.bizSuiteInfo?.start ? dateTime2date(enquiry.order.suiteInfo?.bizSuiteInfo?.start) : LocalDate.now().plusDays(1).toString()
    def startHourBi = enquiry.order.suiteInfo?.bizSuiteInfo?.start ? getHour(enquiry.order.suiteInfo?.bizSuiteInfo?.start) + '' : '0'
    def startMinuteBi = enquiry.order.suiteInfo?.bizSuiteInfo?.start ? getMin(enquiry.order.suiteInfo?.bizSuiteInfo?.start) + '' : '0'
    def startDateCi = enquiry?.order?.suiteInfo?.efcSuiteInfo?.start ? dateTime2date(enquiry.order.suiteInfo?.efcSuiteInfo?.start) : ''
    def startHourCi = enquiry?.order?.suiteInfo?.efcSuiteInfo?.start ? getHour(enquiry.order.suiteInfo?.efcSuiteInfo?.start) + '' : ''
    def startMinuteCi = enquiry?.order?.suiteInfo?.efcSuiteInfo?.start ? getMin(enquiry.order.suiteInfo?.efcSuiteInfo?.start) + '' : ''
    def endDateCi = enquiry.order.suiteInfo?.efcSuiteInfo?.end ? dateTime2date(enquiry.order.suiteInfo?.efcSuiteInfo?.end) : ''
    def endHourCi = enquiry?.order?.suiteInfo?.efcSuiteInfo?.end ? getHour(enquiry.order.suiteInfo?.efcSuiteInfo?.end) + '' : ''
    def endMinCi = enquiry?.order?.suiteInfo?.efcSuiteInfo?.end ? getMin(enquiry.order.suiteInfo?.efcSuiteInfo?.end) + '' : ''
    [
        'prpCmain.agentCode' : '',
        'prpCmain.businesNature' : autoTask?.configs?.businessNature ?: '2',
        'prpCmain.comCode' : autoTask?.configs?.comCode ?: '',
        'prpCmain.custAuthorization' : '0',
        'prpCmain.dwhzTaskId' : '',
        'prpCmain.endDate' : endDateBi,
        'prpCmain.endDateCI' : endDateCi,
        'prpCmain.endhourbi' : '24',
        'prpCmain.endhourci' : endHourCi,
        'prpCmain.endminutebi' : '0',
        'prpCmain.endminuteci' : endMinCi,
        'prpCmain.handler1Code' : '',
        'prpCmain.handlerCode' : '',
        'prpCmain.lastAgentCode' : '',
        'prpCmain.operateCode' : '',
        'prpCmain.presaleCarFlag' : '0',
        'prpCmain.projectCode' : autoTask?.configs?.projectCode ?: '',
        'prpCmain.proposalNo' : '',
        'prpCmain.startDate' : startDateBi,
        'prpCmain.startDateCI' : startDateCi,
        'prpCmain.starthourbi' : startHourBi,
        'prpCmain.starthourci' : startHourCi,
        'prpCmain.startminutebi' : startMinuteBi,
        'prpCmain.startminuteci' : startHourCi,
        'prpCmain.vehicleModelCode' : autoTask?.tempValues?.carInfo['vehicleModelCode'] ?: '',
        'prpCmain.vehicleStyleUniqueId' : ''
    ]
}

def prpCitemCar(autoTask) {
    def enquiry = autoTask?.taskEntity as Enquiry
    def carInfo = autoTask?.tempValues?.carInfo
    def clauseType = useProp(enquiry?.order.carInfo.useProps)
    def fuelType = carInfo['fuel_type'] ? (carInfo['fuel_type'] as String).substring(0, 1) : 'A'
//    def energyType = commonAll.checkEnergyType(carInfo['vehicleName'], enquiry?.order?.carInfo?.plateNum)
//    fuelType = energyType == '0' ? 'A' : (energyType == '1' ? 'C' : 'D1')
    def plateColor = formatPlateColor(enquiry?.order?.carInfo?.plateColor)
    def licenseType = getPlateType(enquiry?.order?.carInfo?.plateType)
    if (plateColor == '52') {
        licenseType = '52'
    }
    def nonLocalFlag = nonLocalFlag(enquiry) ? "0" : "1"
    def carInsuredRelation = enquiry.order.carOwnerInfo.idCard == enquiry.order.insuredPersons[0].idCard ? '所有' : '使用'
    def useNatureCode = useNatureCode(enquiry?.order?.carInfo?.useProps)

    def firstReg = getTime(enquiry.order.carInfo.firstRegDate, "yyyy-MM-dd HH:mm:ss")
    def useYear = getUseYear(firstReg, enquiry.order.suiteInfo?.bizSuiteInfo?.start ?: enquiry.order.suiteInfo?.efcSuiteInfo?.start)
    def licenseNo = enquiry.order.carInfo.plateNum
    def energyTypePlat = checkEnergyType(enquiry.order.carInfo.carModelName, enquiry.order.carInfo.plateNum)
    if (licenseNo == '新车未上牌') {
        if ('510000' == enquiry.order.insureArea.province) {
            def engineNo = enquiry.order.carInfo.engineNum
            def tools = new renbao_tools()
            def sichuanLicensePrefix = tools.sichuanLicensePrefix(enquiry.order.insureArea.city)
            licenseNo = sichuanLicensePrefix + engineNo.substring(engineNo.length() - 5, engineNo.length())
        }
    }
    autoTask?.tempValues?.carInfo['licenseNo'] = licenseNo

    [
        'prpCitemCar.actualValue' : autoTask.tempValues['actualVal'],
        'prpCitemCar.brandId' : carInfo['brandId'],
        'prpCitemCar.brandIDNew' : carInfo['brandIDNew'],
        'prpCitemCar.brandName' : carInfo['vehicleName'],
        'prpCitemCar.carchecker' : autoTask?.configs?.carCheckerTranslate,
        'prpCitemCar.carInsuredRelation' : carInsuredRelation,
        'prpCitemCar.carKindCode' : carInfo['vehicleClassPicc'],
        'prpCitemCar.carLotEquQuality' : enquiry.order.carInfo.fullLoad > 30 ? enquiry.order.carInfo.fullLoad + '' : (enquiry.order.carInfo.fullLoad * 1000) + '',
        'prpCitemCar.certificateDate' : '',
        'prpCitemCar.clauseType' : clauseType,
        'prpCitemCar.countryNature' : '01',
        'prpCitemCar.cylindercount' : '',
        'prpCitemCar.energyType' : energyTypePlat,
        'prpCitemCar.engineNo' : enquiry.order.carInfo.engineNum,
        'prpCitemCar.enginePower' : carInfo['enginePower'],
        'prpCitemCar.enrollDate' : enquiry.order.carInfo.firstRegDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().toString(),
        'prpCitemCar.exhaustScale' : carInfo['vehicleExhaust'] + '',
        'prpCitemCar.familyId' : carInfo['familyIdNew'],
        'prpCitemCar.frameNo' : enquiry.order.carInfo.vin,
        'prpCitemCar.fuelType' : fuelType,
        'prpCitemCar.vehicleFuelType': carInfo['vehicleFuelType'],
        'prpCitemCar.IsCriterion' : '1',
        'prpCitemCar.isDangerousCar' : '',
        'prpCitemCar.isEnergyCar' : autoTask?.tempValues?.isEnergyCar,
        'prpCitemCar.lastBIPolicyNo' : '',
        'prpCitemCar.lastCIPolicyNo' : '',
        'prpCitemCar.lastEndDateBI' : '',
        'prpCitemCar.lastEndDateCI' : '',
        'prpCitemCar.lastUserclassificationCode' : '',
        'prpCitemCar.licenseColorCode' : plateColor,
        'prpCitemCar.licenseNo' : licenseNo,
        'prpCitemCar.licenseNo1' : '',
        'prpCitemCar.licenseNo2' : '',
        'prpCitemCar.licenseType' : licenseType,
        'prpCitemCar.loanName' : '',
        'prpCitemCar.loanVehicleFlag' : '0',
        'prpCitemCar.localLicense' : enquiry.order.carInfo.plateNum == '新车未上牌' ? '1': '',
        'prpCitemCar.localUse' : enquiry.order.carInfo.plateNum == '新车未上牌' ? '1': '',
        'prpCitemCar.modelCode' : carInfo['vehicleId'],
        'prpCitemCar.modelCodeAlias' : '',
        'prpCitemCar.modelDemandNo' : carInfo['vehicleId'],
        'prpCitemCar.netWeifaFlag' : '0',
        'prpCitemCar.Nodamageyears' : autoTask?.configs?.comCode ?: '',
        'prpCitemCar.nonlocalFlag' :  nonLocalFlag ? '0' : '1',
        'prpCitemCar.purchasePrice' : carInfo['priceP'] + '',
        'prpCitemCar.queryArea' : enquiry.order.insureArea.province,
        'prpCitemCar.referenceActualValue' : autoTask.tempValues['actualVal'],
        'prpCitemCar.runAreaCode' : '11',
        'prpCitemCar.runMiles' : '',
        'prpCitemCar.searchseqno' : carInfo['searchCode'],
        'prpCitemCar.seatCount' : enquiry.order.carInfo.seatCnt + '',
        'prpCitemCar.taxPayerType' : [6, 8, 9, 10].contains(enquiry.order.insuredPersons[0].idCardType) ? '10' : '01',
        'prpCitemCar.tonCount' : enquiry.order.carInfo.modelLoad ? enquiry.order.carInfo.modelLoad.intValue() + '' : '0',
        'prpCitemCar.useNatureCode' : useNatureCode,
        'prpCitemCar.useYears' : useYear + '',
        'prpCitemCar.vehicleFgwCode' : carInfo['vehicleFgwCode'],
        'prpCitemCar.vehicleMaker' : carInfo['vehicleMaker'],
        'prpCitemCar.vinNo' : enquiry.order.carInfo.vin,
        'prpCitemCar.lastCarChecker' : '',
        'prpCitemCar.issueDate' : ''
    ]
}

def generateRandomString(length) {
    def characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    def random = new Random()
    StringBuilder stringBuilder = new StringBuilder(length)
    for (int i = 0; i < length; i++) {
        int randomIndex = random.nextInt(characters.length())
        char randomChar = characters.charAt(randomIndex)
        stringBuilder.append(randomChar)
    }
    return stringBuilder.toString()
}
def queryPersonInfo(queryUrl, autoTask, header) {
    //营销系统客户信息修改和新增后不能立即查询，需要等待一段时间
    Thread.sleep(5000)
    def str = HttpSender.doGet(autoTask.httpClient as CloseableHttpClient, queryUrl as String, header as Map<String, String>, null, "UTF-8",null, false)
    Logger log = LoggerFactory.getLogger("营销系统客户信息查询")
    log.info("任务：{}，营销系统客户信息查询url：{}， 返回数据为：{}", autoTask.tempValues['businessId'], queryUrl, str)
    JSONObject.parseObject(str as String)
}

//官网js文件 app.*.js如app.d5595e49.js搜encrypt 可以找到key如下
//var t = "8F6B2AK33DZE20A05E74C231B47AC8F9"
//, n = r.a.enc.Utf8.parse(t)
//, a = r.a.enc.Utf8.parse(e)
//, o = r.a.AES.encrypt(a, n, {
//    mode: r.a.mode.ECB,
//    padding: r.a.pad.Pkcs7
//});
def marketEncrypt(str) {
    def key = '8F6B2AK33DZE20A05E74C231B47AC8F9'
    def build = AesDesEncryption.builder()
            .key(key)
            .keyFormat(EncryptEnum.KeyFormatEnum.UTF_8)
            .cipherAlgorithm(EncryptEnum.AlgorithmEnum.AES_ECB_PKCS5Padding)
            .build()
    return build.encrypt(str as String)
}
def marketDecrypt(str) {
    def key = '8F6B2AK33DZE20A05E74C231B47AC8F9'
    def build = AesDesEncryption.builder()
            .key(key)
            .keyFormat(EncryptEnum.KeyFormatEnum.UTF_8)
            .cipherAlgorithm(EncryptEnum.AlgorithmEnum.AES_ECB_PKCS5Padding)
            .build()
    return build.decrypt(str as String)
}


static def updateCustomer(autoTask, key, customerInfo) {
    def enquiry = autoTask.taskEntity as Enquiry
    def idCardType = getIdCardTypeByKey(key, autoTask)
    def idCard = getIdCardByKey(key, autoTask)
    def name = getNameByKey(key, autoTask)
    def supplyAddress = getPersonSupply(autoTask, key + 'Address')
    def supplyEmail = getPersonSupply(autoTask, key + 'Email')
    def supplyMobile = getPersonSupply(autoTask, key + 'Mobile')
    def insuredAddress = supplyAddress ? supplyAddress['detail'] : customerInfo['insuredAddress']
    if (StringUtils.isBlank(insuredAddress as String) || insuredAddress.length() < 5) {
        insuredAddress = autoTask?.configs['address']
    }
    def identifyType = identifyType(idCardType)
    def insuredMobile = (supplyMobile ?: customerInfo['mobile']) ?: getMobile()
    def insuredCode = customerInfo['insuredCode']
    def oldAddress = customerInfo['insuredAddress']
    def oldMobile = customerInfo['mobile']
    def email = supplyEmail ?: customerInfo['email']
    def emailEncryption = supplyEmail ? '' : customerInfo['emailEncryption']

    def dateValid = customerInfo['dateValid']
    def startDateValid = customerInfo['startDateValid']
    def checkDateValid = idCardType == 0 ? checkDateValid(customerInfo['startDateValid'], customerInfo['dateValid'], getBirth(idCard)) : false
    if (checkDateValid) {
        def idCardValidDate = getIDCardValidDate(autoTask, key)
        startDateValid = idCardValidDate['start']
        dateValid = idCardValidDate['end']
    }
    def newProvince = (supplyAddress && supplyAddress['province'] ? supplyAddress['province'] : customerInfo['province']) ?: enquiry?.order?.insureArea?.province
    def newCity = (supplyAddress && supplyAddress['city'] ? supplyAddress['city'] : customerInfo['city']) ?: enquiry?.order?.insureArea?.city
    def newArea = (supplyAddress && supplyAddress['district'] ? supplyAddress['district'] : customerInfo['area']) ?: (Integer.valueOf(enquiry?.order?.insureArea?.city) + 2) + ''
    def tools = new renbao_tools()
    newArea = tools.fixDistrict(newArea)
    def oldProvince = customerInfo['province']
    def oldCity = customerInfo['city']
    def oldArea = customerInfo['area']
    def professional = customerInfo['occupationCode']
    def orgDateValidStart = customerInfo['startDateValid'] ?: LocalDate.now().minusYears(10).toString()
    def birthDate = customerInfo['birthday']
    def gender = customerInfo['sex']
    def educationCode = customerInfo['educationCode'] ?: '20'
    def versionNo = customerInfo['versionNo'] ?: ''
    def contactPersonName = enquiry.misc?.supplyParam?.liaisonName ?: customerInfo['contactPersonName']
    def areaAbbreviation = autoTask?.configs['areaAbbreviation']
    def url = (autoTask?.configs?.hostPrefix ?: ("http://www." + areaAbbreviation + ".yxgl-picc.cn:9000")) + "/khyx/newFront/qth/price/InsuredUpdateToCif.do?insuredType=1&insuredName=${URLEncoder.encode(name, 'utf-8')}&insuredEname=&identifyNumber=${idCard}&insuredAddress=${URLEncoder.encode(insuredAddress as String, 'utf-8')}&identifyType=${identifyType}&insuredmobile=${URLEncoder.encode(insuredMobile as String, 'utf-8')}&phonenumber=&nationality=CHN&resident=A&insuredCode=${insuredCode}&oldAddress=${URLEncoder.encode(oldAddress as String, 'utf-8')}&oldPhonenum=&oldMobile=${URLEncoder.encode(oldMobile as String, 'utf-8')}&email=${URLEncoder.encode(email as String, 'utf-8')}&emailEncryption=${emailEncryption}&unifiedSocialCreditCode=&unifiedSocialCreditType=&dateValid=${dateValid}&birthDate=${birthDate}&newProvince=${newProvince}&newCity=${newCity}&newArea=${newArea}&gender=${gender}&oldProvince=${oldProvince}&oldCity=${oldCity}&oldArea=${oldArea}&creditDateValid=&creditDateValidStart=&professional=${professional}&revenueCode=&businessCode=&businessCodeDateValid=&marriage=&educationCode=${educationCode}&selfYearIncome=&contactPersonName=&unitType=&startDateValid=${startDateValid}&orgDateValidStart=${orgDateValidStart}&identifyNumberSeqNo=&insuredAddressSeqNo=1&mobileSeqNo=1&phonenumberSeqNo=&unifiedSocialCreditCodeSeqNo=&businessCodeSeqNo=&insuranceBusinessTypeFlag=0&isOcr=N&versionNo=${versionNo}" as String
    def insuredType = [6, 8, 9, 10].contains(idCardType) ? 2 : 1
    if (2 == insuredType) {
        url = (autoTask?.configs?.hostPrefix ?: ("http://www." + areaAbbreviation + ".yxgl-picc.cn:9000")) + "/khyx/newFront/qth/price/InsuredUpdateToCif.do?insuredType=2&insuredName=${URLEncoder.encode(name, 'utf-8')}&insuredEname=&identifyNumber=${customerInfo['identifyNumber']}&insuredAddress=${URLEncoder.encode(insuredAddress as String, 'utf-8')}&identifyType=31&insuredmobile=${URLEncoder.encode(insuredMobile as String, 'utf-8')}&phonenumber=&nationality=CHN&resident=A&insuredCode=${insuredCode}&oldAddress=${URLEncoder.encode(oldAddress as String, 'utf-8')}&oldPhonenum=&oldMobile=${URLEncoder.encode(oldMobile as String, 'utf-8')}&email=${URLEncoder.encode(email as String, 'utf-8')}&unifiedSocialCreditCode=${customerInfo['unifiedSocialCreditCode']}&dateValid=${dateValid}&birthDate=&newProvince=${newProvince}&newCity=${newCity}&newArea=${newArea}&gender=&oldProvince=${oldProvince}&oldCity=${oldCity}&oldArea=${oldArea}&creditDateValid=${customerInfo['creditDateValid'] ?: LocalDate.now().plusYears(10).toString()}&creditDateValidStart=${customerInfo['creditDateValidStart'] ?: LocalDate.now().minusYears(10).toString()}&professional=&revenueCode=&businessCode=&businessCodeDateValid=&marriage=&educationCode=${educationCode}&selfYearIncome=&contactPersonName=${URLEncoder.encode(contactPersonName as String, 'utf-8')}&unitType=300&startDateValid=${startDateValid}&orgDateValidStart=${orgDateValidStart}&identifyNumberSeqNo=2&insuredAddressSeqNo=1&mobileSeqNo=2&phonenumberSeqNo=&unifiedSocialCreditCodeSeqNo=1&businessCodeSeqNo=" as String
    }
    def str = HttpSender.doGet(autoTask.httpClient as CloseableHttpClient, url, header(autoTask) as Map<String, String>, null, "UTF-8",null, false)
    Logger log = LoggerFactory.getLogger("营销系统客户信息修改")
    log.info("任务：{}，营销系统客户信息修改url：{}， 返回数据为：{}", autoTask.tempValues['businessId'], url, str)
    def jsonObj = JSONObject.parseObject(str as String)
    if (insuredType == 1 && jsonObj['statusText'] == 'Fail') {
        throw new InsReturnException('更新关系人信息失败：' + str)
    }
    if (insuredType == 2 && (jsonObj['statusText'] == 'Fail' || !((jsonObj['data']['result'] as String).startsWith('处理成功|没有核心改动自动审批通过成功')))) {
        throw new InsReturnException('更新关系人信息失败：' + str)
    }
    if ((jsonObj['data']['result'] as String).contains('暂存成功')) {
        autoTask?.tempValues[(key + 'Info')] = jsonObj['data']
        if (StringUtils.isBlank(jsonObj['data']['versionNo'] as String) && StringUtils.isNotBlank(versionNo as String)) {
            autoTask?.tempValues[(key + 'Info')]['versionNo'] = customerInfo['versionNo']
        }
        autoTask?.tempValues[(key + 'Info')]['identifyNumber'] = customerInfo['identifyNumber']

    } else if (jsonObj['data']['result'] == "处理成功|") {
        customerInfo['dateValid'] = dateValid
        customerInfo['startDateValid'] = startDateValid
        if (StringUtils.isBlank(customerInfo['insuredAddress'] as String) || (customerInfo['insuredAddress'] as String).length() < 5) {
            customerInfo['insuredAddress'] = supplyAddress ? supplyAddress['detail'] : autoTask?.configs['address']
        }
        autoTask?.tempValues[(key + 'Info')] = customerInfo
    } else {
        throw new InsReturnException('更新关系人信息失败：' + str)
    }
}

static def addCustomer(autoTask, key) {
    def enquiry = autoTask.taskEntity as Enquiry
    def idCardType = getIdCardTypeByKey(key, autoTask)
    def idCard = getIdCardByKey(key, autoTask)
    def name = getNameByKey(key, autoTask)
    if (idCardType != 0) {
        throw new InsReturnException('当前关系人：' + name + '，证件号码：' + idCard + '，证件类型为：' + idCardType + '，无法新增该关系人')
    }
    def gender = getSex(idCard)
    def insuredType = 1
    def identifyType = identifyType(idCardType)
    def insuredAddress = autoTask.configs['address'] as String
    def insuredProvince = enquiry.order.insureArea.province
    def insuredCity = enquiry.order.insureArea.city

    def insuredArea = (Integer.valueOf(enquiry.order.insureArea.city) + 2) + ''
    def tools = new renbao_tools()
    insuredArea = tools.fixDistrict(insuredArea)

    def idCardValidDate = getIDCardValidDate(autoTask, key)
    def dateValid = idCardValidDate['end']
    def startDateValid = idCardValidDate['start']

    def insuredMobile = getMobile()
    def birthDate = getBirth(idCard)
    def age = getAge(idCard)
    def areaAbbreviation = autoTask?.configs['areaAbbreviation']
    def url = (autoTask?.configs?.hostPrefix ?: ("http://www." + areaAbbreviation + ".yxgl-picc.cn:9000")) + "/khyx/newFront/qth/price/addInsured.do?insuredType=${insuredType}&insuredName=${URLEncoder.encode(name, 'utf-8')}&insuredCode=&insuredEname=&identifyType=${identifyType}&identifyNumber=${idCard}&insuredAddress=${URLEncoder.encode(insuredAddress, 'utf-8')}&insuredprovince=${insuredProvince}&insuredcity=${insuredCity}&insuredarea=${insuredArea}&postcode=&dateValid=${dateValid}&startDateValid=${startDateValid}&insuredmobile=${insuredMobile}&phonenumber=&resident=A&nationality=CHN&gender=${gender}&birthDate=${birthDate}&email=&unifiedSocialCreditType=&unifiedSocialCreditCode=&revenueCode=&businessCode=&businessCodeDateValid=&creditDateValid=&occupationCode=&unitType=300&marriage=&age=${age}&educationCode=&selfYearIncome=&contactPersonName=&creditDateValidStart=&insuranceBusinessTypeFlag=0&scene=sale" as String
    def str = HttpSender.doGet(autoTask.httpClient as CloseableHttpClient, url, header(autoTask) as Map<String, String>, null, "UTF-8",null, false)
    Logger log = LoggerFactory.getLogger("营销系统客户信息新增")
    log.info("任务：{}，营销系统客户信息新增url：{}， 返回数据为：{}", autoTask.tempValues['businessId'], url, str)
    def jsonObj = JSONObject.parseObject(str as String)
    if (jsonObj['statusText'] != 'Success') {
        throw new InsReturnException('新建关系人信息失败：' + str)
    }
    if (jsonObj['data']['serviceMessage'] && jsonObj['data']['serviceMessage'].contains('已存在')) {
        throw new InsReturnException('新建团溪人信息失败：' + jsonObj['data']['serviceMessage'])
    }
    autoTask?.tempValues[(key + 'Info')] = jsonObj['data']
}

static def checkNeedUpdatePersonInfo(roleKey, autoTask, personInfo) {
    def idCardType = getIdCardTypeByKey(roleKey, autoTask)
    if (idCardType != 0) {
        return false
    }
    def supplyAddress = getPersonSupply(autoTask, roleKey + 'Address')
    def supplyEmail = getPersonSupply(autoTask, roleKey + 'Email') as String
    def supplyMobile = getPersonSupply(autoTask, roleKey + 'Mobile') as String
    def idCard = getIdCardByKey(roleKey, autoTask)
    def checkDateValid = checkDateValid(personInfo['startDateValid'], personInfo['dateValid'], getBirth(idCard))
    def tools = new renbao_tools()
    def checkArea = StringUtils.isBlank(personInfo['area'] as String) || (personInfo['area'] != tools.fixDistrict(personInfo['area']))
    def checkMobile = personInfo['mobile'] == ''
    def checkAddress = StringUtils.isBlank(personInfo['insuredAddress'] as String) || (personInfo['insuredAddress'] as String).length() < 5
    return StringUtils.isNoneBlank(supplyEmail, supplyMobile) || Objects.nonNull(supplyAddress) || checkDateValid || checkArea || checkMobile || checkAddress
}

static def getIdCardByKey(roleKey, autoTask) {
    def enquiry = autoTask?.taskEntity as Enquiry
    if ('applicant' == roleKey) {
        return enquiry.order.insurePerson.idCard
    } else if ('owner' == roleKey) {
        return enquiry.order.carOwnerInfo.idCard
    } else {
        return enquiry.order.insuredPersons[0].idCard
    }
}

static def getIdCardTypeByKey(roleKey, autoTask) {
    def enquiry = autoTask?.taskEntity as Enquiry
    if ('applicant' == roleKey) {
        return enquiry.order.insurePerson.idCardType
    } else if ('owner' == roleKey) {
        return enquiry.order.carOwnerInfo.idCardType
    } else {
        return enquiry.order.insuredPersons[0].idCardType
    }
}

static def getNameByKey(roleKey, autoTask) {
    def enquiry = autoTask?.taskEntity as Enquiry
    if ('applicant' == roleKey) {
        return enquiry.order.insurePerson.name
    } else if ('owner' == roleKey) {
        return enquiry.order.carOwnerInfo.name
    } else {
        return enquiry.order.insuredPersons[0].name
    }
}

def mapToUrlParams(Map params) {
    return params.collect { k, v ->
        if (v instanceof JSONObject || v instanceof JSONArray) {
            v = '[object Object]'
        }
        if (v == null) {
            v = ''
        }
        "${k}=${URLEncoder.encode(v as String, 'UTF-8')}"
    }.join('&')
}


static def personResponseMethod(autoTask, personRole) {
    def name = getNameByKey(personRole, autoTask)
    def idCardType = getIdCardTypeByKey(personRole, autoTask)
    def result = JSON.parseObject(autoTask?.backRoot?.toString())
    def roleName = personRole == 'applicant' ? '投保人' : (personRole == 'insured' ? '被保人' : '车主')
    if (result['statusText'] == 'Success') {
        if (result['data'] && result['data']['total'] > 1) {
            throw new InsReturnException("名称为${name}的${roleName}在保司系统存在多条！")
        }
        def insPersonInfo = result['data']['list'][0]
        def needUpdate = checkNeedUpdatePersonInfo(personRole, autoTask, insPersonInfo)
        if (needUpdate) {
            updateCustomer(autoTask, personRole, insPersonInfo)
        } else {
            // 没有补充数据修改客户信息
            def infoKey = (personRole + 'Info') as String
            autoTask?.tempValues[(infoKey)] = insPersonInfo
        }
    } else {
        def errorMsg = result['data']['errorMsg'] as String
        if (errorMsg.contains('已经是最后一页')) {
            // 新增
            if ([6, 8, 9, 10].contains(idCardType)) {
                throw new InsReturnException("${roleName}为团体类型，并且在人保系统中不存在，请手动新增该关系人")
            } else {
                addCustomer(autoTask, personRole)
            }
        } else {
            throw new InsReturnException("被保人信息查询失败：${errorMsg}")
        }
    }
}

static def personInfoQueryUrl(autoTask, personRole) {
    def name = getNameByKey(personRole, autoTask)
    def idCardType = getIdCardTypeByKey(personRole, autoTask)
    def identifyNumber = getIdCardByKey(personRole, autoTask)
    def insuredType = [6, 8, 9, 10].contains(idCardType) ? 2 : 1
    def identifyType = identifyType(idCardType)
    def personInfoQueryUrl = hostPrefix(autoTask) + "/khyx/newFront/qth/price/queryInsured.do?insuredType=${insuredType}&insuredName=${URLEncoder.encode(name, 'utf-8')}&identifyNumber=${URLEncoder.encode(identifyNumber, 'utf-8')}&insuredPage=1&identifyType=${identifyType}&unifiedSocialCreditCode=&insuredCode=&renewFlag=0" as String
    if (2 == insuredType) {
        personInfoQueryUrl = hostPrefix(autoTask) + "/khyx/newFront/qth/price/queryInsured.do?insuredType=${insuredType}&insuredName=${URLEncoder.encode(name, 'utf-8')}&identifyNumber=&insuredPage=1&identifyType=01&unifiedSocialCreditCode=${identifyNumber}&insuredCode=&renewFlag=0" as String
    }
    return personInfoQueryUrl
}
