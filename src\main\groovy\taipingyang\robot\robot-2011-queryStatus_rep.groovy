package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.tools.StringUtil
import taipingyang.robot.module.Robot2011Util

AutoTask autoTask = autoTask
def TbUtil = new common_2011()
Enquiry entity = (Enquiry) autoTask?.taskEntity;
JSONObject queryQuotationPolicyResultObj = Robot2011Util.initResp(autoTask.tempValues)(autoTask.backRoot)

int size = queryQuotationPolicyResultObj.getJSONArray("result").size();
String queryNo = autoTask.tempValues.get("queryNo");

if (size < 1) {
    throw new InsReturnException("查无此单，查询用投保单号为：" + queryNo + ",查询账号为：" + autoTask.configs.get("login"))
} else {
    String proposalNo = TbUtil.getFromJson(queryQuotationPolicyResultObj, "result.[0].insuredNo").toString();
    String policyNo = TbUtil.getFromJson(queryQuotationPolicyResultObj, "result.[0].policyNo").toString();
    String status = TbUtil.getFromJson(queryQuotationPolicyResultObj, "result.[0].quotationCode").toString();//

    boolean queryingBI = entity.bizProposeNum.equals(queryNo);

    String keyStr = queryingBI ? "prpCmain" : "prpCmainCI";
    keyStr = autoTask.tempValues.keyStr ?: keyStr

    Map statusMap = new HashMap();
    statusMap.put("1", "暂存");
    statusMap.put("10", "申诉中");
    statusMap.put("2", "待核保");
    statusMap.put("3", "拒保不可申诉");
    statusMap.put("4", "退回修改");
    statusMap.put("5", "核保通过");
    statusMap.put("6", "拒保可申诉");
    statusMap.put("7", "生效");
    statusMap.put("8", "删除");
    statusMap.put("9", "误核申请");



    String statusStr = statusMap.get(status) ?: status;
    String doNotThrowException6Area = ",河北,"
    boolean doNotThrowException6 = doNotThrowException6Area.contains("," + autoTask.configs.get("areaComCode") + ",");

    String type = autoTask.tempValues.taskType.split(/-/)[-1]
    if ("insurequery".equals(type) || "autoinsure".equals(type)) {//核保查询
        String sucessStr = ",5,7,有效,";
        String autoInsureMsg = "";
        if (StringUtil.isNoEmpty((String) autoTask.tempValues.get("autoInsureMsg")))
            autoInsureMsg = "提交核保时平台返回提示：" + (String) autoTask.tempValues.get("autoInsureMsg");
        if (sucessStr.contains("," + status + ",")) {
            if ("prpCmain".equals(keyStr))
                entity.bizProposeNum = proposalNo
            if ("prpCmainCI".equals(keyStr))
                entity.efcProposeNum = proposalNo
        } else if (!doNotThrowException6 && "2".equals(status)) {
//轮询状态 待核保 所有情况下待核保均抛6"true".equals(getBM("isAuto")) &&
            if (autoTask.tempValues.get("taskType").toString().contains("autoinsure"))
                autoTask.taskStatus = "34"
            throw new InsReturnException(6, "核保查询状态未通过，状态为" + statusStr + " " + autoInsureMsg)
        } else {
            throw new InsReturnException("核保查询状态未通过，状态为" + statusStr + " " + autoInsureMsg)
        }
    } else if (type.contains("approvedquery") || type.contains("policyDownload")) {//承保查询
        if (status == 8) {
            status = 7
        }
        if (",7,有效,".contains("," + status + ",") && policyNo) {
            if (autoTask.tempValues.'approvedquery_entity') {
                entity = autoTask.tempValues.'approvedquery_entity'
            }
            def policyType = queryQuotationPolicyResultObj?.result[0]?.policyType

            //写的什么？？？？
            //要根据接口返回的数据去判断 这是爬虫不是你想要什么就有什么？？？？？
            if (size == 1 && proposalNo && policyNo) {
                if (policyType == '0' || policyType == '5') {//商业险
                    entity.setBizProposeNum(proposalNo)
                    entity.setBizPolicyCode(policyNo)
                }
                if (policyType == '1') {//交强险
                    entity.setEfcPolicyCode(policyNo)
                    entity.setEfcProposeNum(proposalNo)
                }
            }

        } else {
            throw new InsReturnException("承保查询未通过，状态为" + statusStr + ",投保单号为:" + proposalNo);
        }


    }
    //模板往前执行的时候，较强险商业险都有
    autoTask.tempValues.'approvedquery_entity' = entity
    autoTask?.taskEntity = entity;
    //主要解决续保的时候回写数据问题
    if ("prpCmain" == keyStr && entity?.efcProposeNum && !autoTask.tempValues.keyStr) {//添加当时快速核保的时候不用校验
        autoTask.tempValues.keyStr = keyStr
        //就是把这个模板再跑一遍
        throw new InsReturnException(InsReturnException.AllowRepeat, "即将校验交强险投保单状态")
    }
    JSONObject queryPolicyParam = JSON.parse(TbUtil.getBaseParam());
    String quotationNo = TbUtil.getFromJson(queryQuotationPolicyResultObj, "result.[0].quotationNo").toString();
    queryPolicyParam.getJSONObject("redata").put("quotationNo", quotationNo);
    autoTask.tempValues.queryByProposeNumUrl = "https://issue.cpic.com.cn/ecar/quotationPreview/queryQuotationPreview";
    autoTask.tempValues.put("queryPolicyParam", StringUtil.chinesetoUnicode(queryPolicyParam.toJSONString()));
}
autoTask.tempValues.finishQueryStatus = "true"

