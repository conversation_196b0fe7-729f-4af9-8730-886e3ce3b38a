package taipingyang.robot


import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.tools.BigDecimalUtil
import com.cheche365.bc.tools.DateCalcUtil
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.sender.HttpSender
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotConstant_2011

@BaseScript BaseScript_Http_Enq _

def TbUtil = new common_2011()

//URL
String getCarIndexURL = "https://issue.cpic.com.cn/ecar/view/portal/page/car_insurance/carInfo.html"
String queryGDVehicleInfoURL = "https://issue.cpic.com.cn/ecar/ecar/queryGDVehicleInfo"

String getCarInfoParamJson = "";

//查找销售人员步骤
if (StringUtil.isNoEmpty((String) autoTask.configs.get("salerInfo")) && null == autoTask.tempValues.sellerVos) {
    TbUtil.getSellerVos(autoTask);
}

if (StringUtil.isEmpty((String) autoTask.tempValues.get("getCarInfoParamJson"))) {
    String plateNum = entity.order.carInfo.plateNum;
    //广东交管平台查询
    boolean queryGDVehicleInfo = "true".equals(autoTask.configs.queryGDVehicleInfo) && !"新车未上牌".equals(entity.order.carInfo.plateNum)
            && (plateNum.startsWith("粤"));
    if (queryGDVehicleInfo) {
        JSONObject queryGDVehicleInfoParam = JSON.parse(TbUtil.getBaseParam());
        //教练车去学字
        queryGDVehicleInfoParam.getJSONObject("redata").put("plateNo", entity.order.carInfo.plateNum.replace("学", ""));
        queryGDVehicleInfoParam.getJSONObject("redata").put("vin", entity.order.carInfo.vin);
        Map header = new HashMap();
        header.put("Content-Type", "application/json;charset=utf-8");
        String queryGDVehicleInfoResult = HttpSender.doPostWithRetry(5, autoTask?.httpClient, true, queryGDVehicleInfoURL, StringUtil.chinesetoUnicode(queryGDVehicleInfoParam.toJSONString()), null, header, "UTF-8", null, "");

        JSONObject queryGDVehicleInfoResultObj = JSON.parse(queryGDVehicleInfoResult);
        if ("failed".equals(TbUtil.getFromJson(queryGDVehicleInfoResultObj, "message.code"))) {
            throw new InsReturnException("广东交管平台查询失败，平台提示:" + TbUtil.getFromJson(queryGDVehicleInfoResultObj, "message.message"))
        }
        //交管车辆种类
        autoTask.tempValues.vehicleStyle = TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.coverField.vehicleStyle")
        //号牌类型
        autoTask.tempValues.plateTypeJg = TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.coverField.plateType")
        StringBuffer errMsg = new StringBuffer();
        String plateNumJg = TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.coverField.plateNo");
        if (StringUtil.isNoEmpty(plateNumJg)) {
            if (!entity.order.carInfo.plateNum.replace("学", "").equals(plateNumJg)) {
                if ("true".equals(autoTask.configs.overrideByGDJg)) {
                    entity.order.carInfo.plateNum = TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.coverField.plateNo");
                } else {
                    errMsg.append("车牌信息不匹配,投保值(" + entity.order.carInfo.plateNum + ")平台值(" + plateNumJg + ") ");
                }
            }
        }
        String engineNoJg = TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.coverField.engineNo");
        if (StringUtil.isNoEmpty(engineNoJg)) {
            if (!entity.order.carInfo.engineNum.equals(engineNoJg)) {
                if ("true".equals(autoTask.configs.overrideByGDJg)) {
                    entity.order.carInfo.engineNum = TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.coverField.engineNo");
                } else {
                    errMsg.append("发送机号信息不匹配,投保值(" + entity.order.carInfo.engineNum + ")平台值(" + engineNoJg + ") ");
                }
            }
        }
        String vinJg = TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.coverField.carVIN")
        if (StringUtil.isNoEmpty(vinJg)) {
            if (!entity.order.carInfo.vin.equals(vinJg)) {
                if ("true".equals(autoTask.configs.overrideByGDJg)) {
                    entity.order.carInfo.vin = TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.coverField.carVIN");
                } else {
                    errMsg.append("车架号信息不匹配,投保值(" + entity.order.carInfo.vin + ")平台值(" + vinJg + ") ");
                }
            }
        }
        String firstRegDateJg = TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.coverField.stRegisterDate")
        if (StringUtil.isNoEmpty(firstRegDateJg)) {
            if (!DateCalcUtil.getFormatDate(entity.order.carInfo.firstRegDate, "yyyy-MM-dd").equals(firstRegDateJg)) {
                if ("true".equals(autoTask.configs.overrideByGDJg)) {
                    entity.order.carInfo.firstRegDate = TbUtil.getDate(TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.coverField.stRegisterDate"), "yyyy-MM-dd");
                } else {
                    errMsg.append("初登信息不匹配,投保值(" + DateCalcUtil.getFormatDate(entity.order.carInfo.firstRegDate, "yyyy-MM-dd") + ")平台值(" + firstRegDateJg + ") ");
                }
            }
        }
        String fullLoadJg = TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.notNullCoverField.emptyWeight");
        if (StringUtil.isNoEmpty(fullLoadJg) && !"0".equals(fullLoadJg)) {
            if (!"0.000".equals(BigDecimalUtil.minus(String.valueOf(entity.order.carInfo.fullLoad), fullLoadJg, 3))) {
                if ("true".equals(autoTask.configs.overrideByGDJg) || !(6 == entity.order.carInfo.useProps || 12 == entity.order.carInfo.useProps)) {
                    entity.order.carInfo.fullLoad = new BigDecimal(fullLoadJg);
                } else {
                    errMsg.append("整备质量信息不匹配,投保值(" + String.valueOf(entity.order.carInfo.fullLoad) + ")平台值(" + fullLoadJg + ") ");
                }
            }
        }
        String displacementJg = TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.notNullCoverField.engineCapacity");
        if (StringUtil.isNoEmpty(displacementJg) && !"0".equals(displacementJg)) {
            if (!"0.000".equals(BigDecimalUtil.minus(String.valueOf(entity.order.carInfo.displacement), displacementJg, 3))) {
                if ("true".equals(autoTask.configs.overrideByGDJg) || (6 == entity.order.carInfo.useProps || 12 == entity.order.carInfo.useProps)) {
                    entity.order.carInfo.displacement = new BigDecimal(displacementJg);
                } else {
                    errMsg.append("排量信息不匹配,投保值(" + String.valueOf(entity.order.carInfo.displacement) + ")平台值(" + displacementJg + ") ");
                }
            }
        }
        String seatCntJg = TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.notNullCoverField.seatCount");
        if (StringUtil.isNoEmpty(seatCntJg) && !"0".equals(seatCntJg)) {
            if (!"0.000".equals(BigDecimalUtil.minus(String.valueOf(entity.order.carInfo.seatCnt), seatCntJg, 3))) {
                if ("true".equals(autoTask.configs.overrideByGDJg)) {
                    entity.order.carInfo.seatCnt = Integer.parseInt(TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.notNullCoverField.seatCount"));
                } else {
                    errMsg.append("座位数信息不匹配,投保值(" + String.valueOf(entity.order.carInfo.seatCnt) + ")平台值(" + seatCntJg + ") ");
                }
            }
        }
        //货车检验载重质量
        if (6 == entity.order.carInfo.useProps || 12 == entity.order.carInfo.useProps) {
            String jgTonnage = TbUtil.getFromJson(queryGDVehicleInfoResultObj, "result.notNullCoverField.tonnage");
            if (StringUtil.isNoEmpty(jgTonnage) && !"0".equals(jgTonnage)) {
                if (100 > Double.parseDouble(jgTonnage))
                    jgTonnage = BigDecimalUtil.multi(jgTonnage, "1000", 0);
                if (!"0.000".equals(BigDecimalUtil.minus(String.valueOf(entity.order.carInfo.modelLoad), jgTonnage, 3))) {
                    if ("true".equals(autoTask.configs.overrideByGDJg)) {
                        entity.order.carInfo.modelLoad = new BigDecimal(jgTonnage);
                    } else {
                        errMsg.append("载重质量信息不匹配,投保值(" + String.valueOf(entity.order.carInfo.modelLoad) + ")平台值(" + jgTonnage + ") ");
                    }
                }
            }
        }

        if (StringUtil.isNoEmpty(errMsg.toString())) {
            throw new InsReturnException("交管平台校验未通过:" + errMsg.toString());
        }
        tempValues.isQueryJg = "1";
    }
    //录单初始页
    JSONObject getCarInfoParam = JSON.parseObject("{\"meta\":{},\"redata\":{\"name\":\"大众汽车SVW7147BLD轿车\"}}");
    getCarInfoParam.getJSONObject("redata").put("name", entity.order.carInfo.carModelName);
    if (null != autoTask.tempValues.carModelNamePT)
        getCarInfoParam.getJSONObject("redata").put("name", autoTask.tempValues.carModelNamePT);
    getCarInfoParamJson = StringUtil.chinesetoUnicode(getCarInfoParam.toJSONString());
    autoTask.tempValues.getCarInfoParamJson = getCarInfoParamJson;
} else
    getCarInfoParamJson = autoTask.tempValues.getCarInfoParamJson;


if (autoTask.configs.userType && autoTask.configs.userType.toString() == '1') {
    def response = doPost {
        url = RobotConstant_2011.URL.FIND_THREE_PEOPLE
        headers = Robot2011Util.getDefaultHead(tempValues, config)
        body = Robot2011Util.genBody(tempValues, JSON.toJSONString([
                meta  : [:],
                redata: [:]
        ]))
    }
    response = Robot2011Util.decodeBody(tempValues, response)
    def resp = JSON.parseObject(response)
    if (resp?.message?.code == 'success') {
        autoTask.tempValues.put('threePeople', resp.result)
    }
}

autoTask.params.clear()

head Robot2011Util.getDefaultHead(tempValues, config)

Robot2011Util.genBody(tempValues, getCarInfoParamJson)
