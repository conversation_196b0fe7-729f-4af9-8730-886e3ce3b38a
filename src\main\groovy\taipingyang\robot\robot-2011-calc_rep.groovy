package taipingyang.robot

import cn.hutool.core.collection.CollUtil
import cn.hutool.core.convert.Convert
import cn.hutool.core.date.DatePattern
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.tools.StringUtil
import common.enums.SuiteEnum
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import taipingyang.robot.module.Robot2011Util

import java.math.RoundingMode
import java.text.SimpleDateFormat

@BaseScript BaseScript_Http_Enq _

def TbUtil = new common_2011()
def robot_2011_util = new robot_2011_util()
def ExistCI = tempValues['ExistCI'] = entity.order.isEfc()
def ExistBI = tempValues['ExistBI'] = entity.order.isBiz()


String calculateResult = (String) autoTask.backRoot
calculateResult = Robot2011Util.decodeBody(tempValues, calculateResult)
JSONObject calculateResultObj = JSON.parseObject(calculateResult);

log.info("保存计算保费响应：{}", calculateResultObj)
autoTask.tempValues.finishCalculate = "true"
autoTask.tempValues.newAccCalculateResultObj = calculateResultObj


check(TbUtil, calculateResultObj, robot_2011_util, ExistCI, ExistBI)

//保费回写
output(TbUtil, calculateResultObj, robot_2011_util, ExistBI, ExistCI)

private void check(common_2011 TbUtil, JSONObject calculateResultObj, robot_2011_util robot_2011_util, boolean ExistCI, boolean ExistBI) {
    String errMsg = (String) TbUtil.getFromJson(calculateResultObj, "message.message");
    robot_2011_util.errMsgException(errMsg, calculateResultObj, autoTask, ExistCI, entity)
//处理车型锁定问题
    String carModeMsg = (String) TbUtil.getFromJson(calculateResultObj, "result.checkInfoVo.questionAnswer");
    if ("2".equals(autoTask.configs.get("errorCarSolution")) && carModeMsg.contains("与当前所选车型不一致")) {
        robot_2011_util.dealError(ExistCI, ExistBI, calculateResultObj, autoTask, entity, carModeMsg, TbUtil)
    }

//处理转保验证码
    String checkCodeStr = (String) TbUtil.getFromJson(calculateResultObj, "result.checkInfoVo.checkCode");
    if (StringUtil.isNoEmpty(checkCodeStr) && !carModeMsg.contains("与当前所选车型不一致")) {
        robot_2011_util.dealCheckCodeStr(checkCodeStr, autoTask, calculateResultObj)
    }
    //判断是否需要重置起保日期
    checkNeedResetStartDate(calculateResultObj, TbUtil)
}

private void output(
        common_2011 TbUtil,
        JSONObject calculateResultObj,
        robot_2011_util robot_2011_util,
        boolean ExistBI, boolean ExistCI
) {
    autoTask.tempValues.finishCalculate = "true"
    autoTask.tempValues.quoteResult = calculateResultObj.getJSONObject('result')
    def accidentInsuranceUtil = new robot_2011_accident_util()
    def accidentNewInsuranceUtil = new robot_2011_new_accident_util()
//先初始化平台信息 规则 佣金回写对象
    if (null == entity.order.platformInfo)
        entity.order.setPlatformInfo(new HashMap());
    if (null == entity.order.platformInfo.ruleAttachedInfo)
        entity.order.platformInfo.put("ruleAttachedInfo", new HashMap());

    String ecompensationRate = TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.ecompensationRate");
//商业险预期赔付率
    String efcEcompensationRate = TbUtil.getFromJson(calculateResultObj, "result.compulsoryInsuransVo.ecompensationRate");
//交强险预期赔付率

    String quotationNo = TbUtil.getFromJson(calculateResultObj, "result.quotationNo").toString();
//保费回写
    robot_2011_util.backBaseSuiteInfo(ExistBI, ExistCI, calculateResultObj, autoTask, entity, ecompensationRate, efcEcompensationRate)
//保费回写补充
    robot_2011_util.backBaseSuiteInfoPlus(ExistBI, ExistCI, calculateResultObj, autoTask, entity)
    def accidentF = accidentNewInsuranceUtil.checkNewInitAccident(autoTask)
    //规则 重新报价无需重新添加非车险，太保是根据同一个单号来确定的
    if (accidentF) {
        if (!autoTask.tempValues.again) {
            //包含了暂存和回写 无论规则车型等重走的逻辑都不在走
            autoTask.tempValues.again = "true"
            accidentNewInsuranceUtil.accident(autoTask, quotationNo, calculateResultObj)
        } else {
            //规则无回写数据
            if (autoTask.tempValues.nonMotor) {
                if (!autoTask.taskType.contains("quote")) {
                    accidentNewInsuranceUtil.deleteMsbProduct(autoTask)
                }
                if ((autoTask.taskType.contains("quote")) || autoTask.taskType.endsWith("insure")) {
                    entity?.misc?.nonMotor = autoTask.tempValues.nonMotor
                }
            }
        }
    }

//    if (saveInsureResultAccidentJson?.result?.accident?.toString() == "true") {
    //驾意险 回写
//        accidentInsuranceUtil.quoteBackAccident(autoTask, calculateResultObj)
//    }

    def platformBack = autoTask.tempValues[PlatformKey.platformBack] = autoTask.tempValues[PlatformKey.platformBack] as Map ?: [:]
    def definition = platformBack['definition'] = platformBack['definition'] as Map ?: [:]
    if ("true".equals(autoTask.configs.needExtraInfo) || "东莞".contains(autoTask.configs.areaComCode)) {
        String bizScore = ExistBI ? TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.cpicScore") : "";
        def totalCpicScore = TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.totalCpicScore")
        //私家车车联网分档
        String bizRate = TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.carSpreading")
        //商业险含NCD标准预期赔付率
        String ncdEcompensationRate = ExistBI ? TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.ncdEcompensationRate") : ""
        //交商合计含NCD标准保费预期赔付率
        String ncdTotalEcompensationRate = ExistBI ? TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.ncdTotalEcompensationRate") : ""

        definition.put(PlatformKey.bizScore, bizScore)
        definition.put("application.bizRate", bizRate)//私家车车联网分档
        //商业险含NCD标准预期赔付率
        definition.put("application.cexpectClaimRate", ncdEcompensationRate)
        definition.put("cexpectClaimRate", ncdEcompensationRate)
        definition.put("cpicCexpectClaimRate", ncdEcompensationRate)
        //交商合计含NCD标准保费预期赔付率
        definition.put("application.compulsoryCexpectClaimRate", ncdTotalEcompensationRate)
        definition.put("compulsoryCexpectClaimRate", ncdTotalEcompensationRate)
        definition.put("cpicCompulsoryCexpectClaimRate", ncdTotalEcompensationRate)
        //根据地区来
        def areaList = ["山东", "宁波", "温州", "湖北"]
        if (areaList.contains(autoTask.configs.areaComCode.toString())) {
            if (ExistBI && ExistCI) {
                definition.put(PlatformKey.bizScore, totalCpicScore)
            }
        }

        String efcScore = ExistCI ? TbUtil.getFromJson(calculateResultObj, "result.compulsoryInsuransVo.compCpicScore") : "";
        if (efcScore) {
            platformBack.put(PlatformKey.TRAFFIC_SCORE, efcScore)
            definition.put(PlatformKey.TRAFFIC_SCORE, efcScore)
        }

        totalCpicScore = TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.totalCpicScore")
        def bizCpicScore = TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.cpicScore")
        def efcCpicScore = TbUtil.getFromJson(calculateResultObj, 'result.compulsoryInsuransVo.compCpicScore')
        if (efcCpicScore) {
            platformBack[PlatformKey.TRAFFIC_SCORE] = definition[PlatformKey.TRAFFIC_SCORE] = definition['CpicEfcScore'] = efcCpicScore
        }

        if (bizScore) {
            platformBack[PlatformKey.bizScore] = definition[PlatformKey.bizScore] = definition['CpicBizScore'] = bizCpicScore
        }

        if (entity.order.insureArea.province in ['370000', '420000'] || // 山东、湖北
                entity.order.insureArea.city in ['330200']) { // 宁波
            if (efcCpicScore && bizCpicScore) { // 混保业务
                platformBack[PlatformKey.bizScore] = definition[PlatformKey.bizScore] = definition['CpicTotalScore'] = totalCpicScore
            }
        }
    }
/* (Story#12907)[http://192..1.212/index.php?m=story&f=view&id=12907] start */
    def inType = calculateResultObj.getJSONObject('result').getString('inType') // N: 新保; R: 续保; T: 转保
    inType = inType == 'N' ? '0' : inType == 'R' ? '1' : inType == 'T' ? '2' : ''
    platformBack[PlatformKey.application_loyalty] = definition[PlatformKey.application_loyalty] = definition['CpicInsType'] = inType
/* (Story#12907)[http://192..1.212/index.php?m=story&f=view&id=12907] end */
//todo 需求2785
    String changeableFeeRate = ExistBI ? TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.changeableFeeRate") : "";
    if (changeableFeeRate) {
        platformBack.put(PlatformKey.bizBrokerageRate, changeableFeeRate)
        definition.put(PlatformKey.bizBrokerageRate, changeableFeeRate)
    }

//针对湖北，手续费修改
    if (entity.order.insureArea.province in ['420000']) {
        String poudage = ExistBI ? Convert.toStr(TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.poudage"), "") : ""
        if (poudage) {
            platformBack.put(PlatformKey.bizBrokerageRate, poudage)
            definition.put(PlatformKey.bizBrokerageRate, poudage)
        }
    }

//todo 需求 1979
    if (autoTask.configs.needRiskflag && autoTask.configs.needRiskflag == 'true') {
        JSONObject PTMsg = (JSONObject) autoTask.tempValues.get("PTMsg");
        def tpyRiskflagName = PTMsg.tpyRiskflagName
        if (tpyRiskflagName) {
            definition.tpyRiskflagName = tpyRiskflagName
        }
    }

    def applyJson = autoTask.applyJson
    def enquiry = JSON.parseObject(applyJson)
    def sq = enquiry['sq']
    def discountRates = sq['discountRates']
    if (sq['discountRates'] && !autoTask.tempValues['inputPolicyDiscount']) {
        def inputPolicyDiscount = discountRates['geniusItem.inputPolicyDiscount']
        if (inputPolicyDiscount) {
            def param = JSON.parseObject(autoTask.tempValues.calculateParamJson)
            def redata = param.getJSONObject('redata')
            redata.getJSONObject("commercialInsuransVo")
                    .put("premiumRatio", new BigDecimal(inputPolicyDiscount.toString())
                            .setScale(6, RoundingMode.HALF_UP).toString())
            autoTask.tempValues.put('calculateParamJson', param.toJSONString())
            autoTask.tempValues['inputPolicyDiscount'] = true
            throw new InsReturnException(InsReturnException.AllowRepeat, '修改折扣，重新报价')
        }
    }
//平台信息查询及回写
    robot_2011_util.backPlatInfo(autoTask, entity, calculateResultObj, ExistBI, ExistCI)
    def misc = autoTask.taskEntity.misc = autoTask.taskEntity.misc ?: [:]
    if (!misc['rootMap']) {
        misc['rootMap'] = [:]
    }
    def claimInformation = makeClaimInformation(autoTask)
    if (claimInformation) {
        misc['rootMap'] << [claimInformation: claimInformation]
    }
//BUG 27902 改流程影响巨大
    def independentPriceRate = TbUtil.getFromJson(calculateResultObj, "result.commercialInsuransVo.independentPriceRate")
    if (independentPriceRate) {
        entity?.order?.platformInfo?.definition?.put(PlatformKey.selfRate, independentPriceRate)
    }
//todo 加上 只有山东地区就可以了
    if (autoTask.taskType.contains("autoinsure") && ["山东"].contains(autoTask?.configs?.areaComCode as String) && !entity?.misc?.nonMotor) {
        //设置以及添加过了新非车险标签，再次报价核保就行
        accidentNewInsuranceUtil.onlySDQuote(autoTask)
    }
}


def makeClaimInformation(AutoTask autoTask) {
    def claimInformation = []
    def queryPlatformInfoResultObj = autoTask.tempValues?.queryPlatformInfoResultObj
    if (queryPlatformInfoResultObj && queryPlatformInfoResultObj.result) {
        queryPlatformInfoResultObj = queryPlatformInfoResultObj as JSONObject
        def result = queryPlatformInfoResultObj.getJSONObject("result")
        def items = [
                "claimInformationSoryVo": SuiteEnum.EFC,
                "claimInformationVo"    : SuiteEnum.BIZ
        ]
        items.each { key, suite ->
            def claimArray = result.getJSONArray(key)
            if (CollUtil.isNotEmpty(claimArray)) {
                makeClaimInformationItem(claimArray, suite) {
                    claim ->
                        claim['riskType'] = suite.id.toString()
                        claimInformation << claim
                }
            }
        }
    }
    return claimInformation
}

def makeClaimInformationItem(JSONArray claims, SuiteEnum suite, Closure closure) {
    def keys = makeKeys(suite)
    for (int i = 0; i < claims.size(); i++) {
        def claim = claims.getJSONObject(i)
        def item = [:]
        keys.each { key, handle ->
            item[key] = handle(claim)
        }
        closure(item)
    }
}

def makeKeys(SuiteEnum suiteEnum) {
    if (suiteEnum == SuiteEnum.BIZ) {
        return [
                'company'     : { JSONObject claim -> claim.getString("insuranceCompany") },
                'accidentTime': { JSONObject claim -> formatDate(claim.getString("insuranceTime")) },
                'closingTime' : { JSONObject claim -> formatDate(claim.getString("closingTime")) },
                'amount'      : { JSONObject claim -> formatAmount(claim.getString("insuranceMoney")) }
        ]
    } else if (suiteEnum == SuiteEnum.EFC) {
        return [
                'company'     : { JSONObject claim -> claim.getString("insuranceCompanySory") },
                'accidentTime': { JSONObject claim -> formatDate(claim.getString("insuranceTimeSory")) },
                'closingTime' : { JSONObject claim -> formatDate(claim.getString("closingTimeSory")) },
                'amount'      : { JSONObject claim -> formatAmount(claim.getString("insuranceMoneySory")) }
        ]
    }
    throw new InsReturnException("未知的险种")
}

def formatAmount(String amount) {
    return amount ? new BigDecimal(amount).setScale(2, RoundingMode.HALF_UP).toString() : null
}

def formatDate(String dateStr) {
    if (!dateStr) {
        return null
    }
    def dateformat = [
            new SimpleDateFormat('yyyy-MM-dd HH:mm:ss'),
            new SimpleDateFormat('yyyy-MM-dd HH:mm'),
            new SimpleDateFormat('yyyy-MM-dd'),
    ]
    def datetime = null
    for (format in dateformat) {
        try {
            format.setLenient(false)
            datetime = format.parse(dateStr)
            break
        } catch (e) {
        }
    }
    return datetime?.format(DatePattern.NORM_DATE_PATTERN)
}

void checkNeedResetStartDate(JSONObject respJson, common_2011 TbUtil) {
    if (!tempValues.needResetStartDateFlagFinish
            && tempValues.needResetStartDateFlag) {
        boolean needResetBiStartDate = false
        boolean needResetCiStartDate = false
        String prevBiEndDate = TbUtil.getFromJson(respJson, "result.commercialInsuransVo.terminationDate")?.toString();
        String prevCiEndDate = TbUtil.getFromJson(respJson, "result.compulsoryInsuransVo.terminationDate")?.toString();
        String biStartDate = tempValues.tempBiStartDate
        String ciStartDate = tempValues.tempCiStartDate
        if (prevBiEndDate && (prevBiEndDate != biStartDate)) {
            needResetBiStartDate = true
            def biSD = prevBiEndDate.split(' ')
            String startDate = biSD[0]
            String startTime = biSD.length > 1 ? " ${biSD[1]}" : ''
            TbUtil.startDateEditorWithStartTime(startDate, "BI", autoTask, startTime);
        }
        if (prevCiEndDate && (prevCiEndDate != ciStartDate)) {
            needResetCiStartDate = true
            def ciSD = prevCiEndDate.split(' ')
            String startDate = ciSD[0]
            String startTime = ciSD.length > 1 ? " ${ciSD[1]}" : ''
            TbUtil.startDateEditorWithStartTime(startDate, "CI", autoTask, startTime);
        }

        if (needResetBiStartDate || needResetCiStartDate) {
            tempValues.needResetStartDateFlagFinish = '1'
            String tip = ''
            if (needResetBiStartDate) {
                tip += '商业险起保时间调整为:' + prevBiEndDate + '\n'
            }
            if (needResetCiStartDate) {
                tip += '交强险起保时间调整为:' + prevCiEndDate + '\n'
            }
            log.info(tip)
            retry(tip)
        }
    }
}