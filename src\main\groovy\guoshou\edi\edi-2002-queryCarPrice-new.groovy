package guoshou.edi

import com.alibaba.fastjson.JSONObject
import groovy.transform.BaseScript
import guoshou.constants.GuoShouConstants
import guoshou.script.BaseScript_2002

import static guoshou.edi.edi_2002_common_new.*

@BaseScript BaseScript_2002 _

/**
 * 车辆实际价值获取接口
 */

def carKindCode = getCarKindCode(carInfo.useProps)
def request = [
        "commonData"  : setCommonDataForPost(config, tempValues.businessNumber, GuoShouConstants.CAR_PRICE_QUERY),
        "businessData": [
                "purchasePrice"    : carInfoQuery.purchasePrice,
                "enrollDate"       : tempValues.enrollDate,
                "startTime"        : getTimeByFormat(bizSuiteInfo?.start ?: efcSuiteInfo?.start),
                "carUserNatureCode": getUseProps(carInfo.useProps),
                "carKindCode"      : carKindCode,
                "passengersNumber" : carInfoBj?.passengersNumber ?: carInfoQuery.passengersNumber ?: carInfo.seatCnt,
                "energyTypesCode"  : getEnergyTypesCode(tempValues.energyType, enquiry),
                "productCode"      : getProductCode(bizSuiteInfo, tempValues.energyType)
        ],
] as JSONObject

// 货车，需传载量
if (carKindCode == "H0") {
    request.businessData.vehicleTonnage = carInfoBj?.vehicleTonnage ?: carInfoQuery.vehicleTonnage ?: carInfo.modelLoad
    request.businessData.vehicleQuality = carInfoBj?.wholeWeight ?: carInfoQuery.wholeWeight ?: carInfo.fullLoad
}

setRequestSignature(reqHeaders, request.toString())

request.toString()
