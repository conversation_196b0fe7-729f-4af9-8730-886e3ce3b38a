package renbao.robot


import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript

import static renbao.robot.common_market.header
import static renbao.robot.common_market.hostPrefix


@BaseScript BaseScript_Http_Enq _


head(header(autoTask, true))


def areaAbbreviation = config['areaAbbreviation']
assertNotNull('未配置地区简称，请检查（areaAbbreviation）配置项', areaAbbreviation)

def province = entity?.order?.insureArea?.province
def userCode = config['userName']
def getConfig = hostPrefix(autoTask) + "/khyx/newFront/qt/myinfo/operator/getOperateConfigByUserCode.do?comid=${province + '00'}&userCode=${userCode}" as String
tempValues['getConfig'] = getConfig

