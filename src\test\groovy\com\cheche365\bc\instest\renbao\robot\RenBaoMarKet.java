package com.cheche365.bc.instest.renbao.robot;

import com.cheche365.bc.SingleCompanyTest;
import com.cheche365.bc.message.TaskType;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.junit.Test;

/**
 * 人保营销系统测试
 */
public class RenBaoMarKet extends SingleCompanyTest {

    private static final CloseableHttpClient httpClient;

    static {
        httpClient = HttpSender.buildHttpClient();
    }

    private AutoTask t;

    @Override
    protected void init() {
        this.comId = "2005";
        this.cityCode = "440100";
        this.org = "1244000000";
    }

    private void initAutoTask(String taskType) throws Exception {
        t = this.getMockDataFromFile();
        t.setTaskType(taskType);
        t.setHttpClient(httpClient);
    }

    private void doQuote() throws Exception {
        this.singleInterface("robot-2005-market-getConfig",  "${getConfig}", t,"营销系统参数查询",HttpSender.HTTP_GET_METHOD);
        this.singleInterface("robot-2005-market-quoteRenew",  "${quoteRenew}", t,"营销系统续保查询",HttpSender.HTTP_GET_METHOD);
        this.singleInterface("robot-2005-market-vehicleQuery",  "${carModelQuery}", t,"营销系统车辆信息查询",HttpSender.HTTP_GET_METHOD);
        this.singleInterface("robot-2005-market-queryApplicant",  "${queryApplicant}", t,"投保人信息",HttpSender.HTTP_GET_METHOD);
        this.singleInterface("robot-2005-market-queryInsured",  "${queryInsured}", t,"被保人信息",HttpSender.HTTP_GET_METHOD);
        this.singleInterface("robot-2005-market-queryOwner",  "${queryOwner}", t,"被保人信息",HttpSender.HTTP_GET_METHOD);
        this.singleInterface("robot-2005-market-calActualVal",  "${calActualVal}", t,"营销车辆实际价值",HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2005-market-quote",  "${quoteUrl}", t,"营销算费",HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2005-market-nonMotorQuote",  "${jointSalesQuote}", t,"营销非车算费",HttpSender.HTTP_POST_METHOD);
    }
    @Test
    public void quote() throws Exception{
        initAutoTask(TaskType.QUOTE.code);
        doQuote();
    }

    private void doInsure() throws Exception {
        this.singleInterface("robot-2005-market-insure",  "${insureUrl}", t,"营销报价转投保",HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2005-market-img",  "${prepareQuery}", t,"营销影像上传",HttpSender.HTTP_GET_METHOD);
    }

    @Test
    public void insure() throws Exception{
        initAutoTask(TaskType.INSURE.code);
        doInsure();
    }

    private void doAutoInsure() throws Exception {
        this.singleInterface("robot-2005-market-submit",  "${submit}", t,"营销提核",HttpSender.HTTP_GET_METHOD);
    }

    @Test
    public void autoInsure() throws Exception {
        initAutoTask(TaskType.AUTO_INSURE.code);
        doInsure();
        doAutoInsure();
    }

    private void doPay() throws Exception {
        this.singleInterface("robot-2005-market-pay",  "${orderPay}", t,"营销获取支付图片",HttpSender.HTTP_GET_METHOD);

    }

    @Test
    public void pay() throws Exception {
        initAutoTask(TaskType.PAY.code);
        doPay();
    }

    private void doQuery() throws Exception {
        this.singleInterface("robot-2005-market-query",  "${queryUrl}", t,"营销投保单查询",HttpSender.HTTP_GET_METHOD);
    }

    @Test
    public void query() throws Exception {
        initAutoTask(TaskType.INSURE_QUERY.code);
        doQuery();
    }

    private void doDownload() throws Exception {
        this.singleInterface("robot-2005-market-policyDown",  "${downPrepare}", t,"营销保单下载",HttpSender.HTTP_GET_METHOD);
    }

    @Test
    public void download() throws Exception {
        initAutoTask("policyDownload");
        doDownload();
    }

    private void doRenewalQuery() throws Exception {
        this.singleInterface("robot-2005-market-getConfig",  "${getConfig}", t,"营销系统参数查询",HttpSender.HTTP_GET_METHOD);
        this.singleInterface("robot-2005-market-quoteRenew",  "${quoteRenew}", t,"营销续保查询",HttpSender.HTTP_GET_METHOD);
    }

    @Test
    public void renewalQuery() throws Exception {
        initAutoTask("renewalQuery");
        doRenewalQuery();
    }
}



