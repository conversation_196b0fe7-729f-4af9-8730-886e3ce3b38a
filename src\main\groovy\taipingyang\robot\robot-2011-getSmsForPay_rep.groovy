package taipingyang.robot


import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.tools.DateCalcUtil
import taipingyang.robot.module.Robot2011Util

Map entity = enquiry
Map tempValues = null != entity.get("tempValues") ? entity.get("tempValues") : tempValues
def TbUtil = new common_2011()
//URL
def result = (String) root;
result = Robot2011Util.decodeBody(tempValues, result)
JSONObject resultObj = JSON.parseObject(result);
if ("success" == TbUtil.getFromJson(resultObj, "message.code")) {
} else {
    throw new InsReturnException(11, "获取支付短信验证码失败,平台提示:" + TbUtil.getFromJson(resultObj, "message.message"))
}
//回写对象
JSONObject responseJson = new JSONObject();
responseJson.put("businessId", tempValues.businessId)
responseJson.put("taskType", tempValues.taskType)
responseJson.put("processType", entity.processType)
responseJson.put("date", DateCalcUtil.getFormatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
responseJson.put("result", "success");
entity.clear()
entity.putAll(responseJson)

