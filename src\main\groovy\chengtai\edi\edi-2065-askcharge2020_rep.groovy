package chengtai.edi

import cn.hutool.core.date.DateField
import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateTime
import cn.hutool.core.date.DateUtil
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.utils.PlatformUtil
import com.cheche365.bc.utils.RuleUtil

import java.text.SimpleDateFormat

if (enquiry?.SQ == null) enquiry.put("SQ", new HashMap<>())
if (enquiry?.SQ?.misc == null) enquiry?.SQ.put("misc", new HashMap<>())
def misc = enquiry?.SQ?.misc;
def script = new edi_2065_common()

def errorMessage = root?.Body?.countFeeResponse?.return?.policyFeeCountResponseBody?.Head?.errorMessage?.toString() ?: "报价失败!"

if (errorMessage.contains('该车型不是新能源车型')) {
    misc.energyFlag = ''
    throw new InsReturnException(InsReturnException.AllowRepeat, errorMessage, enquiry)
}

if (errorMessage?.contains('重复投保信息如下')) {
    enquiry.SQ << ['message': ['BIZ_lastyear': errorMessage]]
}

if (errorMessage.indexOf("商业险发生重复投保") != -1) {
    def (start, end) = calcDate(errorMessage)
    enquiry?.baseSuiteInfo?.bizSuiteInfo?.start = start ?: ""
    enquiry?.baseSuiteInfo?.bizSuiteInfo?.end = end ?: ""
    throw new InsReturnException(InsReturnException.AllowRepeat, errorMessage, enquiry)
}

if (errorMessage.indexOf("重复投保") != -1) {
    if (errorMessage.indexOf("终保日期") == -1) {
        throw new InsReturnException(InsReturnException.RepeatInsure, errorMessage, enquiry)
    }
    def (start, end) = calcDate(errorMessage)
    enquiry?.baseSuiteInfo?.efcSuiteInfo?.start = start ?: ""
    enquiry?.baseSuiteInfo?.efcSuiteInfo?.end = end ?: ""
    enquiry?.baseSuiteInfo?.taxSuiteInfo?.start = start ?: ""
    enquiry?.baseSuiteInfo?.taxSuiteInfo?.end = end ?: ""
    throw new InsReturnException(InsReturnException.AllowRepeat, errorMessage, enquiry)
}


if (root?.Body?.countFeeResponse?.return?.policyFeeCountResponseBody?.Head?.responseCode?.toString() == "0000") {
    def suites = enquiry?.baseSuiteInfo?.bizSuiteInfo?.suites
    def baseInfo = enquiry?.baseSuiteInfo?.efcSuiteInfo
    def CompRisk = root?.Body?.countFeeResponse?.return?.policyFeeCountResponseBody?.CompRisk
    def CommRisk = root?.Body?.countFeeResponse?.return?.policyFeeCountResponseBody?.CommRisk
    def efcquestion = CompRisk?.BasePart?.question?.toString() ?: ""
    def bizquestion = CommRisk?.BasePart?.question?.toString() ?: ""
    def taxCharge = new BigDecimal(0)
    def efcCharge = new BigDecimal(0)
    def bizCharge = new BigDecimal(0)
    enquiry?.SQ?.misc?.efcquestion = efcquestion
    enquiry?.SQ?.misc?.bizquestion = bizquestion

    if (efcquestion != "" || bizquestion != "") {
        enquiry?.SQ?.orderId = CompRisk?.Ftrno?.orderNumber?.toString() ?: CommRisk?.Ftrno?.orderNumber?.toString() ?: ""
        throw new InsReturnException(InsReturnException.AllowRepeat, "诚泰转保", enquiry)
    }

    if (CompRisk != null && CompRisk != "" && !efcquestion) {
        def item = CompRisk
        enquiry?.SQ?.orderId = item?.Ftrno?.orderNumber?.toString() ?: ""
        enquiry?.SQ?.misc?.orderId = item?.Ftrno?.orderNumber?.toString() ?: ""
        baseInfo?.discountCharge = item?.BasePart?.sumPremuim?.toString()
        baseInfo?.orgCharge = item?.BasePart?.benchmarkPremium?.toString() ?: ""
        baseInfo?.discountRate = new BigDecimal(item?.BasePart?.discount?.toDouble() / 100).setScale(4, BigDecimal.ROUND_HALF_UP)
        enquiry?.SQ?.efcCharge = new BigDecimal(String.valueOf(item?.BasePart?.sumPremuim))
        enquiry?.baseSuiteInfo?.taxSuiteInfo?.charge = item?.CarShipTax?.sumPayTax?.toString()
        enquiry?.SQ?.taxCharge = new BigDecimal(String.valueOf(item?.CarShipTax?.sumPayTax))
        taxCharge = new BigDecimal(String.valueOf(item?.CarShipTax?.sumPayTax))
        efcCharge = new BigDecimal(String.valueOf(item?.BasePart?.sumPremuim))
        def definition = enquiry.definition ?: [:]
        enquiry.definition = definition
        definition.put('application.trafficScore', item?.BasePart?.sunlightScore?.toString())
    }
    if (CommRisk != null && CommRisk != "" && !bizquestion) {
        def profitRate = CommRisk?.BasePart?.profitRate?.toString() //NCD系数
        def fixedPriceRate = CommRisk?.BasePart?.fixedPriceRate?.toString() //自主定价系数
        def definition = enquiry.definition ?: [:]
        enquiry.definition = definition
        definition.put(PlatformKey.selfRate, Double.valueOf(fixedPriceRate) / 100)
        definition.put(PlatformKey.noClaimDiscountCoefficient, Double.valueOf(profitRate) / 100)
        definition.put('application.bizScore', CommRisk?.BasePart?.sunlightScore?.toString())
        definition.put('bizContinuityInsureYears', CommRisk?.BasePart?.insureYears?.toString() ?: "")

        enquiry?.SQ?.orderId = CommRisk?.Ftrno?.orderNumber?.toString() ?: ""
        enquiry?.SQ?.misc?.orderId = CommRisk?.Ftrno?.orderNumber?.toString() ?: ""
        enquiry?.baseSuiteInfo?.bizSuiteInfo?.orgCharge = CommRisk?.BasePart?.benchmarkPremium?.toString() ?: ""
        enquiry?.baseSuiteInfo?.bizSuiteInfo?.discountRate = new BigDecimal(CommRisk?.BasePart?.discount?.toDouble() / 100).setScale(4, BigDecimal.ROUND_HALF_UP)
        enquiry?.baseSuiteInfo?.bizSuiteInfo?.discountCharge = CommRisk?.BasePart?.sumPremuim?.toString() ?: ""
        enquiry?.SQ?.bizCharge = new BigDecimal(String.valueOf(CommRisk?.BasePart?.sumPremuim))
        bizCharge = new BigDecimal(String.valueOf(CommRisk?.BasePart?.sumPremuim))
        def ThirdPartyAmount = suites.find { it.code.equals('ThirdParty') }?.amount
        CommRisk?.KindList?.Kind?.each { item ->
            suites.each {
                def kind = script.riskCodeMap().get(item?.kindCode.toString())
                if (it?.code == kind) {
                    it?.orgCharge = item?.benchmarkPremium?.toString()
                    it?.discountCharge = item?.grossPremium?.toString()
                    if (item?.discount?.toString()) {
                        it?.discountRate = new BigDecimal(item?.discount?.toDouble() / 100).setScale(4, BigDecimal.ROUND_HALF_UP)
                    }
                    if (it.code.equals('HolidayDouble'))
                        it.amount = ThirdPartyAmount
                }
            }
        }

        def platformInfo = tempValues[PlatformKey.platformBack] = tempValues[PlatformKey.platformBack] as Map ?: [:]
        platformInfo << [
                (PlatformKey.noClaimDiscountCoefficient): Double.valueOf(profitRate) / 100,
                (PlatformKey.selfRate)                  : Double.valueOf(fixedPriceRate) / 100,
                ('application.bizScore')                : definition['application.bizScore'],
                ('application.trafficScore')            : definition['application.trafficScore'],
                ('loyaltyReasons')                      : enquiry?.SQ?.loyaltyreasons ?: '',
                ('bizContinuityInsureYears')            : CommRisk?.BasePart?.insureYears?.toString() ?: "",
                ('bwCommercialClaimTimes')              : CommRisk?.BasePart?.claimsNum?.toString() ?: ''
        ]
        PlatformUtil.doBackPlatformInfo(autoTask, enquiry, tempValues)
        if (!tempValues?.ruleFlag) {
            //规则交互
            tempValues?.ruleFlag = true
            RuleUtil.doRuleInfo(autoTask, enquiry, tempValues, "", "", "")
            def ruleInfo = tempValues['ruleInfo']
            if (ruleInfo && ruleInfo['geniusItem.totalDiscount']) {
                tempValues?.hopeCoefficient = ruleInfo['geniusItem.totalDiscount'] as String
                throw new InsReturnException(InsReturnException.AllowRepeat, "根据规则传入系数：" + tempValues?.hopeCoefficient + "重新报价")
            }
            if (ruleInfo && ruleInfo['geniusItem.policyDiscount']) {
                tempValues?.expectDiscount = ruleInfo['geniusItem.policyDiscount'] as String
                throw new InsReturnException(InsReturnException.AllowRepeat, "根据规则传入折扣：" + tempValues?.expectDiscount + "重新报价")
            }
        }
    }
    def nonMotorCharge = new BigDecimal(enquiry?.SQ?.nonMotor?.discountCharge ?: '0')
    enquiry?.SQ?.totalCharge = bizCharge.add(taxCharge).add(efcCharge).add(nonMotorCharge)
} else {
    def Message = root?.Body?.countFeeResponse?.return?.policyFeeCountResponseBody?.Head?.errorMessage?.toString() ?: "报价失败!"
    if (Message.indexOf("录入的校验码有误") != -1) {
        if (enquiry?.SQ?.misc?.errorCode) {
            throw new InsReturnException(Message)
        } else {
            enquiry?.SQ?.misc?.errorCode = "1";
            throw new InsReturnException(InsReturnException.AllowRepeat, Message, enquiry)
        }
    } else {
        throw new InsReturnException(Message)
    }
}

/**
 * 获取返回的终保日期<p>
 * 返回的错误如下：<p>
 * 交强险平台返回：号牌号码“川QBS069”的保单发生重复投保，与其重复投保的本公司的保单信息如下：投保确认码 02CHAC510023060435765945827281;保单号 6020000080120230009835;起保日期 2023-06-26 12:00;终保日期 2024-06-26 12:00;号牌号码 川QBS069;号牌种类 02;车架号 LZWACAGA4FC058942;发动机号 F04057647;地区 四川<p>
 *     终保日期：2024-06-26 12:00<p>
 * 商业险发生重复投保，重复投保信息如下：&lt;br&gt;重复投保保单号:6020000082120230009591;&lt;br&gt;保险公司代码:CHAC;&lt;br&gt;险种信息:机动车第三者责任险,机动车车上人员责任险（司机）,机动车车上人员责任险（乘客）,医保外用药责任险（第三者责任保险）;&lt;br&gt;号牌号码:川QBS069;&lt;br&gt;号牌种类代码:02;&lt;br&gt;车辆识别代号:LZWACAGA4FC058942;&lt;br&gt;发动机号:F04057647;&lt;br&gt;起保日期:2023-06-27T00:00:00;&lt;br&gt;终保日期:2024-06-27T00:00:00;&lt;br&gt;签单日期:2023-06-03T00:00:00;&lt;br&gt;<p>
 *     终保日期：2024-06-27T00:00:00
 * @param errorMessage 错误提示
 * @return 返回java.util.Date
 */
Date getEndDateTime(String errorMessage) {
    def startPos = errorMessage.indexOf('终保日期')
    if (startPos == -1) {
        return null
    }
    startPos += 5
    def endPos = errorMessage.indexOf(';', startPos)
    if (endPos == -1) {
        throw new InsReturnException("终保日期格式错误")
    }
    def dateStr = errorMessage.substring(startPos, endPos)
    parseDate(dateStr)
}

def parseDate(String dateStr) {
    def dateformat = [
            new SimpleDateFormat('yyyy-MM-dd HH:mm'),
            new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    ]
    for (format in dateformat) {
        try {
            format.setLenient(false)
            return format.parse(dateStr)
        } catch (e) {
        }
    }
    return null
}

/**
 * 根据起保日期计算终保日期
 * 2024-02-29 00:00:00 ~ 2025-02-28 23:59:59<p>
 * 2024-02-29 10:00:00 ~ 2025-02-28 10:00:00<p>
 * 2024-02-21 00:00:00 ~ 2025-02-20 23:59:59<p>
 * 2024-02-21 10:00:00 ~ 2025-02-21 10:00:00<p>
 * 非闰年非即时起保的需要 -1天的end date
 * 闰年的非即时起保的需要 end date
 * @param startDate 起保日期
 * @return
 */
Date calcEndDate(Date startDate) {
    DateTime startDateTime = DateUtil.date(startDate)
    def endDate = DateUtil.offset(startDate, DateField.YEAR, 1)
    def isDayOfBegin = DateUtil.compare(startDateTime, DateUtil.beginOfDay(startDateTime)) == 0
    if (isDayOfBegin) {
        def isLeapYearAndTheEndOfMonth = startDateTime.isLeapYear() && (startDateTime.month() == 1 && startDateTime.dayOfMonth() == 29)
        def days = isLeapYearAndTheEndOfMonth ? 0 : -1
        endDate = DateUtil.endOfDay(endDate.offset(DateField.DAY_OF_YEAR, days))
    }
    endDate
}

def calcDate(String errorMessage) {
    def startDate = getEndDateTime errorMessage
    if (startDate == null) {
        throw new InsReturnException("终保日期格式错误")
    }
    def start = DateUtil.format(startDate, DatePattern.NORM_DATETIME_PATTERN)
    def endDate = calcEndDate(startDate)
    def end = DateUtil.format(endDate, DatePattern.NORM_DATETIME_PATTERN)
    return [start, end]
}
