package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.cheche365.bc.tools.StringUtil
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import taipingyang.robot.module.Robot2011Util

@BaseScript BaseScript_Http_Enq _

def taskType = autoTask.taskType
autoTask.tempValues.put("taskType", taskType)

String queryNoBI = entity.bizProposeNum
String queryNoCI = entity.efcProposeNum

def param = [
        'meta'  : ['pageSize': 8],
        'redata': [
                'dateType'      : '101',
                'ecarVin'       : '',
                'endDate'       : '',
                'inType'        : '',
                'insurant'      : '',
                'insuredNo'     : '',
                'isPrint'       : '',
                'isSaic'        : '0',
                'licensePlate'  : '',
                'policyHolder'  : '',
                'policyNo'      : '',
                'productType'   : '',
                'quotationNo'   : '',
                'quotationState': '',
                'startDate'     : ''
        ]
]

//快速续保和普通核保都相同
autoTask?.tempValues?.queryNo = queryNoBI ?: queryNoCI
autoTask?.tempValues?.ExistBI = true
if (taskType.split(/-/)[-1] == 'quotequery') {
    param['redata']['quotationNo'] = entity.efcProposeNum ?: entity.bizProposeNum
} else {
    param?.redata?.insuredNo = queryNoBI ?: queryNoCI
}

autoTask?.tempValues?.queryListParam = StringUtil.chinesetoUnicode(JSON.toJSONString(param))
if (autoTask.tempValues.keyStr && autoTask.taskType.contains("approvedquery")) {
    autoTask?.tempValues?.queryNo = queryNoCI
    autoTask?.tempValues?.ExistCI = true
    param?.redata?.insuredNo = queryNoCI
    autoTask?.tempValues?.queryListParam = StringUtil.chinesetoUnicode(JSON.toJSONString(param))
}

head Robot2011Util.getDefaultHead(tempValues, config)

body Robot2011Util.genBody(tempValues, tempValues?.queryListParam as String)
