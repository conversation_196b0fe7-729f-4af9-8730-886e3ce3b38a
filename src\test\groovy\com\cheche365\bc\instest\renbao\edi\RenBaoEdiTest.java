package com.cheche365.bc.instest.renbao.edi;

import com.cheche365.bc.SingleCompanyTest;
import com.cheche365.bc.message.TaskType;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.junit.Test;

/**
 * Created by Administrator on 2017/6/13.
 */
public class RenBaoEdiTest extends SingleCompanyTest {

    // 测试环境, 此地址为人保总公司车险5002系统，需要将<nshead:areacode>00000000</nshead:areacode> 切记 修改
    protected static final String TEST_URL = "http://114.247.181.203:9101/InterfaceProxy/cmp/ProxyService";
    // 生产环境
    protected static final String PROD_URL = "https://esb.epicc.com.cn/InterfaceProxy/cmp/ProxyService";

    private static final CloseableHttpClient httpClient;

    static {
        httpClient = HttpSender.buildHttpClient();
    }

    private AutoTask t;
    protected void init() {
        this.comId = "2005";
        this.cityCode = "440300";
        this.dataType = "map";
    }

    private void initAutoTask(String taskType) throws Exception {
        t = this.getMockDataFromFile();
        t.setTaskType(taskType);
        t.setHttpClient(httpClient);
        t.getReqHeaders().put("content-type", "text/xml;charset=UTF-8");
    }

    private void doQuote(String url) throws Exception {
        this.singleInterface("edi_2005_checkcar", url, t, "交管查车", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_checkcar_confirm", url, t, "交管查车确认", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_newcar", url, t, "新车备案", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_q39", url, t, "车型查询", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_ask_charge", url, t, "保费计算", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_noMotor_quote", "${nonMotorQuoteUrl}", t, "非车保费计算", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_noMotorLCO_quote", "${quoteUrl}", t, "LCO非车保费计算", HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void quote() throws Exception {
        initAutoTask(TaskType.QUOTE.code);
        doQuote(PROD_URL);
    }

    private void doInsure(String url) throws Exception {
        this.singleInterface("edi_2005_askInsure", url, t, "报价转投保", HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void insure() throws Exception {
        initAutoTask(TaskType.INSURE.code);
        doQuote(PROD_URL);
        doInsure(PROD_URL);
    }

    private void doAutoInsure(String url) throws Exception {
        this.singleInterface("edi_2005_fileupload", url, t, "上传影像", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_submitInsure", url, t, "提交核保", HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void autoInsure() throws Exception {
        initAutoTask(TaskType.AUTO_INSURE.code);
        doQuote(PROD_URL);
        doInsure(PROD_URL);
        doAutoInsure(PROD_URL);
    }

    private void doInsureQuery(String url) throws Exception {
        this.singleInterface("edi_2005_efc_insurequery", url, t, "交强险保单信息", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_biz_insurequery", url, t, "商业险保单信息", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_noMotor_insurequery", "${nonMotorQueryUrl}", t, "非车险保单信息", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_noMotorLCO_insurequery", "${queryUrl}", t, "保费计算", HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void insureQuery() throws Exception {
        initAutoTask(TaskType.INSURE_QUERY.code);
        doInsureQuery(PROD_URL);
    }

    private void doCancel(String url) throws Exception {
        this.singleInterface("edi_2005_cancel", url, t, "取消核保", HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void cancel() throws Exception {
        initAutoTask(TaskType.CANCEL_INSURE.code);
        doCancel(PROD_URL);
    }

    private void doApprovedQuery(String url) throws Exception {
        this.singleInterface("edi_2005_efc_policyinfo", url, t, "交强险保单信息", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_biz_policyinfo", url, t, "商业险保单信息", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_noMotor_policyquery", "${nonMotorQueryUrl}", t, "非车险保单信息", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_noMotorLCO_policyquery", "${queryUrl}", t, "保费计算", HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void approvedQuery() throws Exception {
        initAutoTask(TaskType.APPROVED_QUERY.code);
        doApprovedQuery(PROD_URL);
    }

    private void doIdentifyVerify(String url) throws Exception {
        this.singleInterface("edi_2005_idcard_collect", url, t, "北京身份采集", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_getSms", url, t, "获取短信", HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void identifyVerify() throws Exception {
        initAutoTask(TaskType.IDENTIFY_VERIFY.code);
        doIdentifyVerify(PROD_URL);
    }

    private void doPolicyDownload(String url) throws Exception {
        this.singleInterface("edi_2005_policyDown", url, t, "电子保单下载", HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void policyDownload() throws Exception {
        initAutoTask("policyDownload");
        doPolicyDownload(PROD_URL);
    }
}
