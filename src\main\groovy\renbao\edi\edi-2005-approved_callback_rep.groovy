package renbao.edi


import com.cheche365.bc.message.TaskStatus



def order = root.Package.Response.Order
def tBOrderId = order.TBOrderId.toString()
def sessionId = root?.Package?.Header?.SessionId?.toString()
def subOrders = order.SubOrderList.SubOrder
def policy = [:]
policy['enquiryId'] = sessionId
policy['status'] = TaskStatus.APPROVED_QUERY_SUCCESS.State()
def sq = [:]
sq['topOrderNo'] = tBOrderId
subOrders.each { sub ->
    if (sub.'@type' == 'biz') {
        sq['bizProposeNum'] = sub?.ProposalNo?.toString()
        sq['bizPolicyCode'] = sub?.PolicyNo?.toString()
        sq['bizCharge'] = new BigDecimal(sub?.Premium?.toString())
        sq['bizOrderNo'] = sub?.TBOrderId?.toString()
        sq['bizPolicyCode'] = sub?.PolicyNo?.toString()

    }
    if (sub.'@type' == 'force') {
        sq['efcProposeNum'] = sub?.ProposalNo?.toString()
        sq['efcPolicyCode'] = sub?.PolicyNo?.toString()
        sq['efcCharge'] = new BigDecimal(sub?.Premium?.toString())
        sq['efcOrderNo'] = sub?.TBOrderId?.toString()
        sq['efcPolicyCode'] = sub?.PolicyNo?.toString()
    }
}
enquiry['taskStatus'] = TaskStatus.APPROVED_QUERY_SUCCESS.State()
policy['sq'] = sq
enquiry?.SQ = sq
