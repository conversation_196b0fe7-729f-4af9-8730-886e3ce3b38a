package jintai.edi

import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.XmlUtil
import com.alibaba.fastjson.JSONArray
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.utils.PlatformUtil
import com.cheche365.bc.utils.RuleUtil
import com.cheche365.bc.utils.sender.HttpSender
import common.common_all
import org.apache.pdfbox.pdmodel.PDDocument
import org.slf4j.LoggerFactory

import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Map.Entry

/**
 * 将传入参数值按字母序排序后,执行MD5哈希,转化为Hex格式,得到签名串
 * @param appId
 * @param appKey
 * @param method
 * @param encrypt
 * @return 签名串
 */
static String makeSign(Map<String, String> signParams, String appKey) {

    //利用TreeMap对参数进行排序(字典序)
    TreeMap<String, String> sortedParams = new TreeMap<String, String>();
    sortedParams.putAll(signParams);

    String stringTemp = "";
    Iterator<Entry<String, String>> it = sortedParams.entrySet().iterator();
    boolean first = true;
    while (it.hasNext()) {
        Entry<String, String> e = it.next();
        String key = e.getKey();
        String val = e.getValue();

        //sign字段不参与签名, 值为空的字段不参与签名
        //其余字段,用key1=val1&key2=val2格式拼接起来
        if (!key.equals("sign") && val != null && !val.equals("")) {
            if (first) {
                first = false;
            } else {
                stringTemp += "&";
            }
            stringTemp += key + "=" + val;
        }
    }
    stringTemp += "&appKey=" + appKey;
    return md5hash(stringTemp)
}

static String md5hash(String src) {
    MessageDigest md = MessageDigest.getInstance("MD5");
    byte[] digest = md.digest(src.getBytes("UTF-8"));

    StringBuffer hexstr = new StringBuffer();

    String hex = "";
    for (int i = 0; i < digest.length; i++) {
        hex = Integer.toHexString(digest[i] & 0xFF);
        if (hex.length() < 2) {
            hexstr.append(0);
        }
        hexstr.append(hex);
    }
    return hexstr.toString();
}


static def parseIdCardType(code) {
    switch (code) {
        case 0: "01"; break
        case 4: "02"; break
        case 3: "03"; break
        default: "99"
    }
}

/**
 * 号牌种类 磐石 -》保司
 * @param plateType
 * @return
 */
def getPlateType(plateType) {
    switch (plateType) {
        case 0: "01"; break      // 大型汽车号牌
        case 1: "02"; break      // 小型汽车号牌
        case 2: "03"; break      // 使馆汽车号牌
        case 3: "04"; break      // 领馆汽车号牌
        case 4: "05"; break      // 境外汽车号牌
        case 5: "06"; break      // 外籍汽车号牌
        case 6: "07"; break      // 两轮三轮摩托车号牌
        case 7: "08"; break      // 轻便摩托车号牌
        case 8: "09"; break      // 使馆摩托车号牌
        case 9: "10"; break      // 领馆摩托车号牌
        case 10: "11"; break     // 境外摩托车号牌
        case 11: "12"; break     // 外籍摩托车
        case 12: "13"; break     // 农用运输车号牌
        case 13: "14"; break     // 拖拉机号牌
        case 14: "15"; break     // 挂车号牌
        case 15: "16"; break     // 教练汽车号牌
        case 17: "18"; break     // 试验汽车号牌
        case 16: "17"; break     // 教练摩托车号牌
        case 18: "19"; break     // 试验摩托车号牌
        case 19: "20"; break     // 临时入境汽车号牌
        case 20: "21"; break     // 临时入境摩托车号牌
        case 21: "22"; break     // 临时行驶车号牌
        case 22: "23"; break     // 警用汽车号牌
        case 23: "33"; break     // 警用摩托车号牌
        case 24: "31"; break     // 武警号牌
        case 25: "32"; break     // 军队号牌
        case 26: "25"; break     // 其他号牌
        case 52: "51"; break     // 大型新能源汽车
        case 51: "52"; break     // 小型新能源汽车
        default: "02"
    }
}

/**
 * 获取车辆使用性质
 * @param useProps
 * @return
 */
static def getUseProps(useProps) {
    [
            1 : "364001", // 非营业个人
            2 : "364005", // 营业出租租赁 ==》 出租客车
            3 : "364006", // 营业城市公交
            4 : "364007", // 营业公路客运
            6 : "364008", // 营业货车
            10: "364002", // 非营业企业
            11: "364003", // 非营业机关
            12: "364004", // 非营业货车
            13: "364011", // 兼用型拖拉机
            14: "364011", // 运输型拖拉机
            15: "364009", // 营业特种车
            16: "364009", // 非营业特种车
            17: "364016"  // 预约出租客运
    ].get(useProps) ?: "364001"
}

/**
 * 车辆种类
 */
static def getCarKindCode(mapKey) {
    [
            "KE": "365012",  // 三十六座及三十六座以上客车
            "NB": "365037",  // 三轮汽车
            "KD": "365011",  // 二十座至三十六座以下客车
            "HA": "365006",  // 二吨以下货车
            "GB": "365026",  // 二吨至五吨以下挂车
            "HB": "365007",  // 二吨至五吨以下货车
            "GC": "365027",  // 五吨至十吨以下挂车
            "HC": "365008",  // 五吨至十吨以下货车
            "NA": "365010",  // 低速货车
            "KA": "365001",  // 六座以下客车
            "KB": "365002",  // 六座至十座以下客车
            "JB": "365022",  // 兼用型拖拉机14.7KW以上及联合收割机
            "JA": "365019",  // 兼用型拖拉机14.7KW及以下
            "GD": "365028",  // 十吨以上挂车
            "HD": "365009",  // 十吨以上货车
            "KC": "365004",  // 十座至二十座以下客车
            "ZA": "365013",  // 特种车一
            "ZC": "365015",  // 特种车三
            "ZB": "365014",  // 特种车二
            "ZD": "365016",  // 特种车四
            "JD": "365024",  // 运输型拖拉机14.7KW以上
            "JC": "365023"   // 运输型拖拉机14.7KW及以下
    ].get(mapKey) ?: "365001"
}

static def getVehicleType(jgVehicleType) {
    [
            0 : "K33", // 轿车
            1 : "H31", // 轻型普通货车
            2 : "Q31", // 轻型半挂牵引车
            4 : "G21", // 中型普通全挂车
            5 : "G23", // 中型普通半挂车
            6 : "H26", // 中型集装厢车
            7 : "Z31", // 小型专项作业车
            8 : "T21", // 小型轮式拖拉机
            9 : "M21", // 普通二轮摩托车
            10: "M22", // 轻便二轮摩托车
            11: "M15", // 侧三轮摩托车
            12: "T22", // 手扶拖拉机
            13: "K33", // 轿车
            14: "K32", // 小型越野客车
            15: "B35", // 轻型自卸半挂车
            19: "K31", // 小型客车
            20: "K33", // 轿车
            21: "K43", // 微型轿车
            22: "M11", // 普通正三轮摩托车
            24: "J11", // 轮式装载机械
            25: "K33", // 轿车
            26: "K33", // 轿车
            27: "K33", // 轿车
            28: "K33", // 轿车
            29: "K11", // 大型普通客车
            31: "H32", // 轻型厢式货车
            32: "H22", // 中型厢式货车
            33: "H12", // 重型厢式货车
            99: "X99"  //其它
    ].get(jgVehicleType) ?: ""
}

/**
 * 关系人类型
 * @param idCardType
 * @return
 */
static def getCustomerType(idCardType) {
    switch (idCardType) {
        case [0, 1, 2, 3, 4, 5, 7]: "1"; break//个人
        case [6, 8, 9, 10]: "2"; break//企业
        default: "1"
    }
}

/**
 * 获取证件类型
 * @param idCardType
 */
def getCredentialCode(idCardType) {
    [
            0 : "01", // 身份证
            3 : "03", // 军人证件
            4 : "02", // 护照
            6 : "10", // 组织机构代码证
            8 : "13", // 社会信用代码证
            9 : "12", // 税务登记证
            10: "11", // 营业执照
    ].get(idCardType) ?: "99"
}

/**
 * 出生日期
 * @param idCard
 * @param idCardType
 * @return
 */
def getBirthday(carOwnerInfo) {
    def idCardType = carOwnerInfo?.idCardType
    def idCard = carOwnerInfo?.idCard
    if (idCardType != 0) {
        return [:]
    }
    if (idCard?.length() != 18)
        return [:]
    int year = idCard?.substring(6, 10) as int
    String birth = year + idCard.substring(10, 12) + idCard.substring(12, 14)
    def sex = carOwnerInfo?.sex == 0 ? 1 : 2
    return [birth: birth, age: Calendar.getInstance().get(Calendar.YEAR) - year, sex: sex]
}

/**
 * 险种代码对应的华农网销险种名称、代码
 */
def itemMap(code) {
    switch (code) {
        case "VehicleDamage": ["030006", "机动车损失保险"]; break
        case "ThirdParty": ["030018", "机动车第三者责任保险"]; break
        case "Driver": ["030001", "机动车车上人员责任保险(司机)"]; break
        case "Passenger": ["030009", "机动车车上人员责任保险(乘客)"]; break
        case "HolidayDouble": ["033024", "法定节假日限额翻倍险"]; break
        case "GoodsOnVehicle": ["030064", "车上货物责任险"]; break
        case "Scratch": ["032601", "车身划痕损失险"]; break
        case "CFMDThirdParty": ["033006", "精神损害抚慰金责任险(机动车第三者责任保险)"]; break
        case "CFMDDriver": ["033515", "精神损害抚慰金责任险(车上人员责任险(驾驶人))"]; break
        case "CFMDPassenger": ["033005", "精神损害抚慰金责任险(车上人员责任险(乘客))"]; break
        case "CompensationDuringRepair": ["033007", "修理期间费用补偿"]; break
        case "ExtraDevice": ["030021", "新增设备损失险"]; break
        case "Wheel": ["033501", "车轮单独损失险"]; break
        case "ANCVehicleDamage": ["033507", "机动车损失保险附加绝对免赔率特约"]; break
        case "ANCThirdParty": ["033508", "机动车第三者责任保险附加绝对免赔率特约"]; break
        case "ANCDriver": ["033509", "车上人员责任保险（驾驶员）附加绝对免赔率特约"]; break
        case "ANCPassenger": ["033510", "车上人员责任保险（乘客）附加绝对免赔率特约"]; break
        case "EngineDamageExclude": ["033502", "发动机进水损坏除外特约条款"]; break
        case "NIHCThirdParty": ["033512", "医保外医疗费用责任险(机动车第三者责任保险)"]; break
        case "NIHCDriver": ["033513", "医保外医疗费用责任险(车上人员责任险(驾驶人))"]; break
        case "NIHCPassenger": ["033514", "医保外医疗费用责任险(车上人员责任险(乘客))"]; break
        case "RoadsideService": ["033503", "道路救援服务"]; break
        case "VehicleInspection": ["033504", "车辆安全检测"]; break
        case "DesignatedDriving": ["033505", "代为驾驶服务"]; break
        case "SendForInspection": ["033506", "代为送检服务"]; break

        case "NEGridBugDamage": ["033516", "电网故障损失险"]; break
        case "NEChargerDamage": ["033517", "充电桩损失险"]; break
        case "NEChargerDuty": ["033518", "充电桩责任险"]; break
        default: [null, null, null]
    }
}

/**
 * 图片类型转换
 * @param code
 * @return
 */
def getImageType(code) {
    // 居民身份证
    def idCardList = ["0", "1", "16", "25", "26", "28", "29", "43", "44", "107", "108"]

    def imageMapList = [
            ['thirdCode': "01", 'desc': "居民身份证", 'dataList': idCardList],
            ['thirdCode': "11", 'desc': "驾驶证", 'dataList': ["21", "22"]]
    ]
    def result
    for (def temp : imageMapList) {
        def dataList = temp.dataList
        if (dataList == null) {
            continue
        }
        if (dataList.contains(code)) {
            result = temp.thirdCode
            break
        }
    }
    if (!result) {
        return "99"
    }
    return result
}

/**
 * 交管车辆类型
 */
def vehicleType(def mapKey) {
    def vehicleTypeMap = [
            13: "K33",
            19: "K31",
            18: "K21",
            17: "K11",
            21: "K41",
            31: "H31",
            32: "H11",
            33: "H21"
    ]
    return vehicleTypeMap.get(mapKey) ?: "K33"
}

static kindMapNew(code) {
    switch (code) {
        case "030006": ["机动车损失险", "VehicleDamage"]; break
        case "030018": ["机动车第三者责任保险", "ThirdParty"]; break
        case "030001": ["司机责任险", "Driver"]; break
        case "030009": ["乘客责任险", "Passenger"]; break
        case "033024": ["附加法定节假日限额翻倍险", "HolidayDouble"]; break
        case "030064": ["车上货物责任险", "GoodsOnVehicle"]; break
        case "032601": ["附加车身划痕损失险", "Scratch"]; break
        case "033006": ["精神损害抚慰金责任险(三者险)", "CFMDThirdParty"]; break
        case "033515": ["精神损害抚慰金责任险(司机险)", "CFMDDriver"]; break
        case "033005": ["精神损害抚慰金责任险(乘客险)", "CFMDPassenger"]; break
        case "033007": ["附加修理期间费用补偿险", "CompensationDuringRepair"]; break
        case "030021": ["附加新增加设备损失险", "ExtraDevice"]; break
        case "033501": ["车轮单独损失险", "Wheel"]; break
        case "033507": ["附加绝对免赔率特约条款(机动车损失保险)", "ANCVehicleDamage"]; break
        case "033508": ["附加绝对免赔率特约条款(机动车第三者责任保险)", "ANCThirdParty"]; break
        case "033509": ["附加绝对免赔率特约条款(机动车车上人员责任保险(司机))", "ANCDriver"]; break
        case "033510": ["附加绝对免赔率特约条款(机动车车上人员责任保险(乘客))", "ANCPassenger"]; break
        case "033502": ["发动机进水损坏除外特约条款", "EngineDamageExclude"]; break
        case "033512": ["医保外医疗费用责任险(机动车第三者责任保险)", "NIHCThirdParty"]; break
        case "033513": ["医保外医疗费用责任险(车上人员责任险(驾驶人))", "NIHCDriver"]; break
        case "033514": ["医保外医疗费用责任险(车上人员责任险(乘客))", "NIHCPassenger"]; break
        case "033503": ["道路救援服务", "RoadsideService"]; break
        case "033504": ["车辆安全检测", "VehicleInspection"]; break
        case "033505": ["代为驾驶服务", "DesignatedDriving"]; break
        case "033506": ["代为送检服务", "SendForInspection"]; break
        case "033516": ["电网故障损失险", "NEGridBugDamage"]; break
        case "033517": ["充电桩损失险", "NEChargerDamage"]; break
        case "033518": ["充电桩责任险", "NEChargerDuty"]; break
        default: [null, null]
    }
}

/**
 * 服务档次
 * @param designatedDrivingMile
 * @return
 */
static getDrivingServiceType(designatedDrivingMile) {
    def DrivingServiceType = 10
    if (designatedDrivingMile > 10 && designatedDrivingMile <= 20) {
        DrivingServiceType = 20
    } else if (designatedDrivingMile > 20) {
        DrivingServiceType = 30
    }
    return DrivingServiceType
}

/**
 * 通过密码 获取字节流
 * @param secret
 * @return
 */
static getPolicyByte(secret, idCard) {
    def log = LoggerFactory.getLogger("edi-2050-getPolicyByte")
    def passwordList = [
            idCard?.substring(idCard.length() - 4) as String,
            idCard?.substring(idCard.length() - 6) as String,
            idCard?.substring(idCard.length() - 8) as String
    ]
    // 由于锦泰edi电子保单打开需要密码，但是密码不固定，可能是证件后4位、后6位、后8位。问保司有什么规律没有，他说他也不知道....
    def out = new ByteArrayOutputStream()
    def secretInputStream = new ByteArrayInputStream(secret as byte[])
    for (password in passwordList) {
        try {
            def load = PDDocument.load(secretInputStream, password)
            load.setAllSecurityToBeRemoved(true)
            load.save(out)
            return out.toByteArray()
        } catch (Exception ignored) {
            log.info("锦泰edi#pdf密码错误，替换密码重试，当前密码：{}", password)
        } finally {
            secretInputStream.reset()
        }
    }

    return out.toByteArray()
}

/**
 *
 * @param code
 * @return
 */
def getInsureError(code) {
    [
            "21": "自动核保通过",
            "22": "自动核保不通过",
            "31": "转人工核保异常",
            "32": "转人工核保成功",
            "91": "不满足核保条件",
    ].get(code) ?: "核保失败"
}


/**
 * 根据起保时间计算终保时间
 */
def getEndDateNew(String startDate) throws Exception {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date date = sdf.parse(startDate)
    Calendar cal = Calendar.getInstance()
    cal.setTime(date)
    // 将日期加一年减1天
    cal.add(Calendar.YEAR, 1)
    cal.add(Calendar.DAY_OF_MONTH, -1)
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    String time = dateFormat.format(cal.getTime())
    return time + " 23:59:59"
}

/**
 * 根据起保时间计算终保时间
 */
def getStartDateNew(String startDate) throws Exception {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    Date date = sdf.parse(startDate)
    Calendar cal = Calendar.getInstance()
    cal.setTime(date)
    if (startDate.contains("23:59")) {
        cal.add(Calendar.DAY_OF_MONTH, 1)
    }
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    String time = dateFormat.format(cal.getTime())
    return time + " 00:00:00"
}

/**
 * 信息回写
 * @param bizContent
 */
def write(autoTask, bizContent, enquiry, tempValues, step) {
    def platformInfo = tempValues?.platformInfo ?: [:]   //回写磐石definition，同时配置平台信息码表会回写到otherInfo
    def platformBack = tempValues?.platformBack ?: [:]     //回写规则引擎
    def definition = platformInfo?.definition ?: [:]

    def basePart = bizContent?.Packet?.BasePart
    // 报价成功
    enquiry?.SQ?.totalCharge = 0
    def efcSuiteInfo = enquiry?.baseSuiteInfo?.efcSuiteInfo
    def bizSuiteInfo = enquiry?.baseSuiteInfo?.bizSuiteInfo
    if (efcSuiteInfo) {
        def ApplicationTCI = bizContent?.Packet?.ApplicationTCI
        // 报价单号
        enquiry?.efcProposeNum = basePart?.QtnId
        enquiry?.SQ?.efcTempId = basePart?.QtnId
        // 折扣率
        efcSuiteInfo?.discountRate = new BigDecimal("1")
        // 折后保费
        efcSuiteInfo?.discountCharge = ApplicationTCI?.Premium?.toBigDecimal()
        // 原始保费
        efcSuiteInfo?.orgCharge = ApplicationTCI?.Premium?.toBigDecimal()
        enquiry?.SQ?.efcCharge = ApplicationTCI?.Premium?.toBigDecimal()
        enquiry?.SQ?.totalCharge += ApplicationTCI?.Premium?.toBigDecimal()

        //回写车船税保费
        def carShipTax = ApplicationTCI?.VehicleTax
        def taxCharge = carShipTax?.SumTax?.toBigDecimal()?.setScale(2, BigDecimal.ROUND_HALF_UP)
        enquiry?.baseSuiteInfo?.taxSuiteInfo ?: [:]
        enquiry?.baseSuiteInfo?.taxSuiteInfo?.charge = taxCharge
        enquiry?.SQ?.taxCharge = taxCharge

        enquiry?.SQ?.totalCharge += carShipTax?.SumTax?.toBigDecimal()

        platformBack << [
                (PlatformKey.TRAFFIC_SCORE)                     : basePart?.PolicyRiskScore,    //交强险评分
                (PlatformKey.application_expectTrafficLossRatio): basePart?.UnderwritingScore,   //承保分
        ]
        definition << [
                (PlatformKey.TRAFFIC_SCORE)                     : basePart?.PolicyRiskScore,    //交强险评分
                (PlatformKey.application_expectTrafficLossRatio): basePart?.UnderwritingScore,   //承保分
        ]
        platformBack.definition = definition
        enquiry.definition = definition
        tempValues?.platformBack = platformBack
    }

    if (bizSuiteInfo) {
        // 报价单号
        enquiry?.bizProposeNum = basePart?.QtnId
        enquiry?.SQ?.bizTempId = basePart?.QtnId

        def ApplicationVCI = bizContent?.Packet?.ApplicationVCI
        def suitesList = []
        // 折扣率 商业险折扣系数 = NCD系数 * 自主定价系数 * 交通违法系数 1
        def discountRate = new BigDecimal(ApplicationVCI?.ClaimAdjustValue?.toString()).multiply(new BigDecimal(ApplicationVCI?.SelfPricingCoef?.toString()))?.setScale(2, BigDecimal.ROUND_HALF_UP)
        def CoverageInfo = JSONArray.parse(ApplicationVCI?.CoverageList?.CoverageItem?.toString()) ?: []
        def CoverageList = new JSONArray()
        if (CoverageInfo instanceof JSONArray) {
            CoverageList = CoverageInfo
        } else {
            CoverageList.push(CoverageInfo)
        }
        for (int i = 0; i < CoverageList.size(); i++) {
            def it = CoverageList[i]
            def suiteDef = [:]
            def kindInfo = kindMapNew(it?.CoverageCode)
            suiteDef.put("code", kindInfo[1])
            suiteDef.put("name", kindInfo[0])
            if (["033514", "030009"].contains(it?.CoverageCode)) {
                suiteDef.amount = it.CoverageAmount.toBigDecimal() / (enquiry?.carInfo?.seatCnt - 1)
            } else if (["033506"].contains(it?.CoverageCode)) {
                def suite = bizSuiteInfo.suites.find { suite ->
                    suite?.get("code")?.toString()?.equals(kindInfo[1])
                }
                if (suite?.amount) {
                    suiteDef.put("amount", new BigDecimal(suite?.amount?.toString() ?: "0"))
                }
                suiteDef.put("sendForInspectionLevel", it?.ServiceItem?.toString())
            } else if (["033502", "033503", "033504", "033505", "033506"].contains(it.CoverageCode)) {
                if ("033505" == it.CoverageCode) {
                    suiteDef['designatedDrivingMile'] = it?.DrivingService?.DrivingServiceType
                }
                suiteDef.amount = bizSuiteInfo.suites.find { suite ->
                    suite.code == kindInfo[1]
                }.amount
            } else {
                suiteDef.put("amount", new BigDecimal(it?.CoverageAmount?.toString()) ?: "0")
            }
            def discountCharge = it?.CoveragePremium?.toBigDecimal()?.setScale(2, BigDecimal.ROUND_HALF_UP)
            suiteDef.put("discountCharge", discountCharge)
            suiteDef.put("discountRate", discountRate)
            def orgCharge = discountCharge?.divide(discountRate, 2)
            suiteDef.put("orgCharge", orgCharge)
            suitesList.add(suiteDef)
        }
        bizSuiteInfo.suites = suitesList
        // 折后保费
        def Premium = ApplicationVCI?.Premium?.toBigDecimal()?.setScale(2, BigDecimal.ROUND_HALF_UP)
        bizSuiteInfo?.discountCharge = Premium
        bizSuiteInfo?.discountRate = discountRate
        // 折前保费 折后保费/折扣率
        def orgCharge = Premium.divide(discountRate, 2)
        bizSuiteInfo?.orgCharge = orgCharge ?: new BigDecimal("0")
        enquiry?.SQ?.bizCharge = ApplicationVCI?.Premium?.toBigDecimal()
        enquiry?.SQ?.totalCharge += ApplicationVCI?.Premium?.toBigDecimal()

        platformBack << [
                (PlatformKey.selfRate)                   : ApplicationVCI?.SelfPricingCoef, //自主定价系数
                (PlatformKey.noClaimDiscountCoefficient) : ApplicationVCI?.ClaimAdjustValue, //NCD无赔款优待系数
                (PlatformKey.bizScore)                   : basePart?.PolicyRiskScore,   //商业险评分
                (PlatformKey.application_expectLossRatio): basePart?.UnderwritingScore,   //承保分
                "csxForecastScore"                       : basePart?.UBITranche // UBI评分
        ]
        definition << [
                (PlatformKey.selfRate)                   : ApplicationVCI?.SelfPricingCoef, //自主定价系数
                (PlatformKey.noClaimDiscountCoefficient) : ApplicationVCI?.ClaimAdjustValue, //NCD无赔款优待系数
                (PlatformKey.bizScore)                   : basePart?.PolicyRiskScore,   //商业险评分
                (PlatformKey.application_expectLossRatio): basePart?.UnderwritingScore,   //承保分
                "csxForecastScore"                       : basePart?.UBITranche // UBI评分
        ]
        platformBack.definition = definition
        enquiry.definition = definition
        tempValues?.platformBack = platformBack
    }

    if (enquiry?.SQ?.nonMotor) {
        enquiry?.SQ?.nonMotor?.discountCharge = bizContent.Packet?.map?.ApplicationACC?.Premium?.toBigDecimal() ?:
                bizContent.Packet?.ApplicationPROP?.Premium?.toBigDecimal()
        enquiry?.SQ?.totalCharge += bizContent.Packet?.map?.ApplicationACC?.Premium?.toBigDecimal() ?:
                bizContent.Packet?.ApplicationPROP?.Premium?.toBigDecimal()
    }
    PlatformUtil.doBackPlatformInfo(autoTask as AutoTask, enquiry as Map, tempValues as Map)

    //调用规则
    RuleUtil.doRuleInfo(autoTask, enquiry, tempValues, '', '', '')
    def ruleInfo = tempValues['ruleInfo']
    if (ruleInfo['geniusItem.policyDiscount'] && !tempValues['geniusItem.policyDiscount']) {
        tempValues['geniusItem.totalDiscount'] = ruleInfo['geniusItem.totalDiscount']
        tempValues['geniusItem.policyDiscount'] = ruleInfo['geniusItem.policyDiscount']
        def ex = new InsReturnException(InsReturnException.AllowRepeat, '调用规则修改折扣，重新报价')
        ex.step = step
        throw ex
    }
}

/**
 * 将传入的时间字符串转换成指定格式
 * @param time 时间字符串
 * @param format 当前时间字符串的格式
 * @param targetFormat 指定的格式
 * @return
 */
static def parseTime(time, format, targetFormat) {
    if (!time) {
        return ""
    }

    DateUtil.parse(time as String, format as String).toString(targetFormat as String)
}

/**
 *
 * @param method
 * @param config
 * @param timestamp
 * @param bizContent
 * @return
 */
static def sign(method, config, timestamp, bizContent) {
    def signInfo = [
            "appId"     : config?.appId,
            "method"    : method,
            "timestamp" : timestamp,
            "bizContent": compress(bizContent)

    ]

    makeSign(signInfo as Map, config.appKey as String)
}

/**
 * 压缩 xml
 * @param str
 * @return
 */
static def compress(str) {
    def parsedXml = XmlUtil.parseXml(str)
    def cleanedXmlString = XmlUtil.toStr(parsedXml)
    return cleanedXmlString.replaceAll(/>\s+</, '><').trim()
}

/**
 * 判断非车是否匹配
 * @param response
 * @param productCode
 * @param tempValues
 * @return
 */
static def isFindNonMotor(response, productCode, tempValues) {
    def prefix = productCode?.toString()?.split("-")[0]
    def code = productCode?.toString()?.split("-")[1]

    def nonMotorInfo = null
    if (prefix == "PROP") {
        def plan = productCode?.toString()?.split("-")[2]
        nonMotorInfo = response.Packet.ProjectPROPList.ProjectItem.myArrayList.find { it ->
            (it?.map?.ProjectCode == code) && (it?.map?.ProjectPlan = plan)
        }
    }

    if (prefix == "ACC") {
        nonMotorInfo = response.Packet.ProjectACCList.ProjectItem.myArrayList.find { it -> it.map?.ProjectCode == code }
    }

    if (!nonMotorInfo) {
        throw new InsReturnException("非车查询列表异常, 未查询到该款非车产品。")
    }

    tempValues.nonMotorAmount = nonMotorInfo?.PlanAmount
    tempValues.nonMotorPremium = nonMotorInfo?.PlanPremium
    tempValues.nonMotorPlan = nonMotorInfo?.PlanPremium
}

/**
 *
 * @param enquiry
 * @param url
 * @param type
 * @param policyNo
 * @return
 */
static def downloadPolicy(enquiry, url, type, policyNo) {
    def commonAll = new common_all()
    def idCard = enquiry?.insurePerson?.idCard?.toString()
    def urlList = url?.toString()?.split("\\|")
    if (!urlList) {
        throw new InsReturnException("电子保单下载url为空")
    }

    if (urlList?.size() == 2) {
        def efcPolicyBytes = getPolicyByte(HttpSender.doGet(null, urlList[0], null, 'UTF-8', null, true), idCard)

        def efcPolicyMarkBytes = getPolicyByte(HttpSender.doGet(null, urlList[1], null, 'UTF-8', null, true), idCard)

        def mergePolicy = commonAll.mergePDF(efcPolicyBytes, efcPolicyMarkBytes)
        commonAll.uploadOBS(enquiry, type, mergePolicy, "2050", policyNo)
        return
    }

    def policyBytes = getPolicyByte(HttpSender.doGet(null, urlList[0] as String, null, 'UTF-8', null, true), idCard)
    // 解密获取文件流
    def decryptBytes = getPolicyByte(policyBytes, idCard)
    commonAll.uploadOBS(enquiry, type, decryptBytes, "2050", policyNo)
}

/**
 * 获取充电桩安装地点类型
 * @param autoTask
 * @param config
 * @return
 */
static def getChargingPostAddressType(autoTask, config) {
    def commonAll = new common_all()
    def chargerLocationType = commonAll.getPersonSupply(autoTask, "neChargerLocationType")
    if (chargerLocationType) {
        return chargerLocationType == "1" ? "3A2001" : "3A2002"
    }

    return config?.NEChargerLocationType ?: ""
}

/**
 * 获取充电桩种类
 * @param autoTask
 * @param config
 * @return
 */
static def getChargingPostKind(autoTask, config) {
    def commonAll = new common_all()
    def chargerLocationType = commonAll.getPersonSupply(autoTask, "neChargerType")
    if (chargerLocationType) {
        return chargerLocationType == "1" ? "3A3001" : (chargerLocationType == "2" ? "3A3002" : "3A3003")
    }

    return config?.NEChargerType ?: ""
}

/**
 * 商业险重复投保
 * @param enquiry
 * @param bizEndDate
 * @param isTransfer
 * @return
 */
def checkBizRepeat(enquiry, bizEndDate, isTransfer) {
    if (!bizEndDate) {
        throw new InsReturnException("商业险重复投保")
    }

    def endTempDate = LocalDateTime.parse(bizEndDate as String, DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
    if (bizEndDate.contains("235900")) {
        endTempDate.plusMinutes(1)
    }

    if (bizEndDate.contains("235959")) {
        endTempDate.plusSeconds(1)
    }

    def endDate = getEndDateNew(endTempDate.format("yyyy-MM-dd HH:ss:mm"))
    enquiry.baseSuiteInfo?.bizSuiteInfo?.start = endTempDate.format("yyyy-MM-dd HH:ss:mm")
    enquiry.baseSuiteInfo?.bizSuiteInfo?.end = endDate

    if (isTransfer) {
        def ex = new InsReturnException(InsReturnException.AllowRepeat, "锦泰商业险重复投保，重新报价")
        ex.step = 2
        throw ex
    }

    throw new InsReturnException(InsReturnException.AllowRepeat, "锦泰商业险重复投保，重新报价")
}

/**
 * 交强险重复投保
 * @param enquiry
 * @param efcEndDate
 * @param isTransfer
 * @return
 */
def checkEfcRepeat(enquiry, efcEndDate, isTransfer) {
    if (!efcEndDate) {
        throw new InsReturnException("交强险险重复投保")
    }

    def startDate = LocalDateTime.parse(efcEndDate as String, DateTimeFormatter.ofPattern("yyyyMMddHHmmss")).
            format(DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss'))

    def endDate = getEndDateNew(startDate)
    enquiry.baseSuiteInfo?.efcSuiteInfo?.start = startDate
    enquiry.baseSuiteInfo?.efcSuiteInfo?.end = endDate

    if (isTransfer) {
        def ex = new InsReturnException(InsReturnException.AllowRepeat, "锦泰交强险重复投保，重新报价")
        ex.step = 2
        throw ex
    }

    throw new InsReturnException(InsReturnException.AllowRepeat, "锦泰交强险重复投保，重新报价")
}

/**
 *
 * @param interfaceMsg
 * @param type
 * @param tempValues
 */
static def checkCarModelCode(bizContent, type, tempValues) {
    def interfaceMsg = bizContent?.Packet?.InterfaceMsg

    if (interfaceMsg?.ErrorCode == 6001 && bizContent?.Packet?.BasePart?.ModelCode) {
        def modelCode = bizContent?.Packet?.BasePart?.ModelCode?.toString()?.split(",")?.first()
        tempValues.modelCode = modelCode

        def exception = new InsReturnException(InsReturnException.AllowRepeat, "替换车型编码重新报价")
        if ("askCharge" == type) {
            exception.step = 3
        } else {
            exception.step = 4
        }
        throw exception
    }
}
