package taipingyang.robot


import cn.hutool.core.convert.Convert
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.car.CarInfo
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.model.car.SpecialClause
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.tools.BigDecimalUtil
import com.cheche365.bc.tools.DateCalcUtil
import com.cheche365.bc.tools.FileUtil
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.PlatformUtil
import com.cheche365.bc.utils.dama.Dama2Web
import com.cheche365.bc.utils.sender.HttpSender
import org.apache.http.impl.client.CloseableHttpClient
import org.springframework.util.CollectionUtils
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotDict_2011

import javax.script.Invocable
import javax.script.ScriptEngine
import javax.script.ScriptEngineManager
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.Period
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.util.regex.Matcher
import java.util.regex.Pattern

import static com.cheche365.bc.exception.InsReturnException.Others
import static com.cheche365.bc.exception.InsReturnException.RepeatInsure
import static java.lang.Integer.parseInt
import static java.lang.Integer.toHexString

String encrypt(String strSrc, String encName) {
    MessageDigest md = null;
    String strDes = null;

    byte[] bt = strSrc.getBytes();
    try {
        if (encName == null || encName.equals("")) {
            encName = "SHA-256";
        }
        md = MessageDigest.getInstance(encName);
        md.update(bt);
        strDes = bytes2Hex(md.digest()); // to HexString
    } catch (NoSuchAlgorithmException e) {
        return null;
    }
    return strDes;
}

String bytes2Hex(byte[] bts) {
    String des = "";
    String tmp = null;
    for (int i = 0; i < bts.length; i++) {
        tmp = (toHexString(bts[i] & 0xFF));
        if (tmp.length() == 1) {
            des += "0";
        }
        des += tmp;
    }
    return des;
}

String chinesetoUnicode(String str) {
    StringBuffer sb = new StringBuffer();
    char[] charArr = str.toCharArray();
    for (char ch : charArr) {
        if (ch > 127) {
            sb.append("\\u" + toHexString(ch));
        } else {
            sb.append(ch);
        }
    }
    return sb.toString();

//    return URLEncoder.encode(str,"UTF-8");
}

Object getFromJson(Object json, String path) throws Exception {
    Object result = null;
    try {
        String[] paths = path.split("\\.");
        List<String> list = new ArrayList<String>();
        for (String s : paths) {
            list.add(s);
        }
        result = getFromJson(json, list);
        if (null == result)
            result = "";//20161214
        if (!(result instanceof String))
            result = String.valueOf(result);
    } catch (Exception e) {
        e.print("从json中提取数据出错" + path);
        e.printStackTrace();
    }
    return result;
}

Object getFromJson(Object json, List<String> paths) throws Exception {
    if (null == json) {
        return null;
    }
    if (paths.size() == 1) {
        JSONObject obj = (JSONObject) json;
        return obj.get(paths.get(0));
    } else {
        if (json instanceof JSONObject) {
            JSONObject obj = (JSONObject) json;
            if (paths.get(1).contains("[")) {
                JSONArray array = obj.getJSONArray(paths.get(0));
                paths.remove(0);
                return getFromJson(array, paths);
            } else {
                JSONObject obj1 = obj.getJSONObject(paths.get(0));
                paths.remove(0);
                return getFromJson(obj1, paths);
            }
        }
        if ((json instanceof JSONArray) && ((JSONArray) json).size() > 0) {
            JSONArray arr = (JSONArray) json;
            if (paths.get(0).contains("[")) {
                String s = paths.get(0);
                JSONObject obj = arr.getJSONObject(parseInt(s.substring(1, s.length() - 1)));
                paths.remove(0);
                return getFromJson(obj, paths);
            }
            return null;
        }
    }
    return null;
}

String getBaseParam() {
    return "{\"meta\":{},\"redata\":{}}";
}

JSONObject getQuickSaveParam(String areaComCode) {
    JSONObject j = JSON.parseObject("{\"meta\":{},\"redata\":{\"plateNo\":\"鄂A92P57\",\"plateType\":\"02\",\"plateColor\":\"1\",\"carVIN\":\"LJ16AA3C9A7044019\",\"engineNo\":\"A4030237\",\"stRegisterDate\":\"2010-09-19\",\"usage\":\"101\",\"vehicleType\":\"02\",\"vehiclePurpose\":\"01\",\"isCarInsure\":\"\",\"modelType\":\"江淮HFC6500KA2C8T轻型客车\",\"moldCharacterCode\":\"JHAAID0082\",\"producingArea\":\"0\",\"factoryType\":\"江淮HFC6500KA2C8T轻型客车\",\"negotiatedValue\":\"63140.00\",\"loan\":\"0\",\"specialVehicleIden\":\"\",\"stChangeRegisterDate\":\"\",\"relationship\":\"1\",\"fuelType\":\"D2\",\"ownerName\":\"张慧春\",\"ownerProp\":\"1\",\"certType\":\"1\",\"certNo\":\"420124197205122738\",\"trafficType\":\"\",\"transitType\":\"\",\"engineCapacity\":\"2.771\",\"power\":\"80\",\"seatCount\":\"9\",\"tonnage\":\"\",\"emptyWeight\":\"2000\",\"shortcutCode\":\"JH-HFC6500KA2C8T\",\"reductionType\":\"0\",\"purchasePrice\":\"110000\",\"actualValue\":\"63140.00\",\"plateless\":false}}");
    if ("湖南".equals(areaComCode))
        j = JSON.parseObject("{\"meta\":{},\"redata\":{\"plateNo\":\"湘A9444G\",\"plateType\":\"02\",\"plateColor\":\"1\",\"carVIN\":\"LFV3A24GXC3081128\",\"engineNo\":\"286887\",\"stRegisterDate\":\"2012-12-26\",\"usage\":\"101\",\"vehicleType\":\"01\",\"vehiclePurpose\":\"01\",\"isCarInsure\":\"\",\"modelType\":\"奥迪FV7181FADBG轿车\",\"moldCharacterCode\":\"ADAAFD0201\",\"producingArea\":\"2\",\"factoryType\":\"奥迪FV7181FADBG轿车\",\"negotiatedValue\":\"234720\",\"loan\":\"0\",\"specialVehicleIden\":\"\",\"relationship\":\"1\",\"fuelType\":\"0\",\"tpyRiskflagCode\":\"003\",\"stChangeRegisterDate\":\"\",\"ownerName\":\"湖南先导电子陶瓷科技产业园发展有限公司\",\"ownerProp\":\"3\",\"certType\":\"11\",\"certNo\":\"9143012456174160X8\",\"holderTelphone\":\"\",\"stCertificationDate\":\"\",\"stCertificateValidity\":\"9999-12-31\",\"trafficType\":\"\",\"transitType\":\"\",\"engineCapacity\":\"1.798\",\"power\":\"140\",\"seatCount\":\"5\",\"tonnage\":\"\",\"emptyWeight\":\"1750\",\"shortcutCode\":\"AD-FV7181FADBG\",\"uniformInsurance\":\"\",\"payWay\":\"\",\"reductionType\":\"0\",\"vehiclePowerJY\":\"140\",\"oriEngineCapacity\":\"1.798\",\"address\":\"\",\"jyFuelType\":\"D1\",\"purchasePrice\":\"360000\",\"actualValue\":\"234720.00\",\"tpyRiskflagName\":\"C\",\"carModelRiskLevel\":\"\",\"plateless\":false}}");
    else if ("北京".equals(areaComCode))
        j = JSON.parseObject("{\"meta\":{},\"redata\":{\"plateNo\":\"京AY2240\",\"plateType\":\"02\",\"plateColor\":\"1\",\"carVIN\":\"LVSHFFAL9HS102929\",\"engineNo\":\"GA532500\",\"stRegisterDate\":\"2017-01-18\",\"usage\":\"101\",\"vehicleType\":\"01\",\"vehiclePurpose\":\"01\",\"isCarInsure\":\"\",\"modelType\":\"福特CAF7154A5轿车\",\"moldCharacterCode\":\"FTBBID0004\",\"producingArea\":\"2\",\"factoryType\":\"福特CAF7154A5轿车\",\"negotiatedValue\":\"90411\",\"loan\":\"0\",\"specialVehicleIden\":\"\",\"relationship\":\"1\",\"fuelType\":\"A\",\"tpyRiskflagCode\":\"003\",\"stChangeRegisterDate\":\"\",\"ownerName\":\"周宗良\",\"ownerProp\":\"1\",\"certType\":\"1\",\"certNo\":\"110228198004192919\",\"holderTelphone\":\"\",\"stCertificationDate\":\"\",\"haulage\":\"\",\"zeroTonnage\":\"\",\"trafficType\":\"\",\"transitType\":\"\",\"engineCapacity\":\"1.498\",\"power\":\"83\",\"seatCount\":\"5\",\"tonnage\":\"\",\"emptyWeight\":\"1300\",\"shortcutCode\":\"FT-CAF7154A5\",\"uniformInsurance\":\"\",\"payWay\":\"\",\"reductionType\":\"0\",\"vehiclePowerJY\":\"83\",\"oriEngineCapacity\":\"1.498\",\"address\":\"\",\"jyFuelType\":\"D1\",\"purchasePrice\":\"96800\",\"actualValue\":\"90411.00\",\"tpyRiskflagName\":\"C\",\"carModelRiskLevel\":\"\",\"plateless\":false,\"platformVo\":{\"modelCode\":\"BCAAFAUC0002\",\"brand\":\"福特CAF7154A5轿车\",\"brandCode\":\"CAA\",\"series\":\"福睿斯\",\"seriesCode\":\"\",\"carName\":\"福特CAF7154A5 时尚型\",\"noticeType\":\"\",\"configType\":\"UC\",\"benchmarkRiskPremium\":\"1367.020720\",\"pureRiskPremium\":\"1367.020720\",\"pureRiskPremiumFlag\":\"1\"}}}");
    else if ("温州".equals(areaComCode) || "浙江".equals(areaComCode))
        j = JSON.parseObject("{\"meta\":{},\"redata\":{\"fleetAgreementNo\":\"\",\"plateNo\":\"浙CC2N77\",\"plateType\":\"02\",\"plateColor\":\"1\",\"carVIN\":\"LGBF1CE085R116777\",\"engineNo\":\"VQ23 089777\",\"stRegisterDate\":\"2017-03-17\",\"usage\":\"101\",\"vehicleType\":\"01\",\"vehiclePurpose\":\"01\",\"isCarInsure\":\"\",\"modelType\":\"天籁EQ7230AA轿车\",\"moldCharacterCode\":\"RCAAKD0014\",\"producingArea\":\"2\",\"factoryType\":\"天籁EQ7230AA轿车\",\"negotiatedValue\":\"158189\",\"loan\":\"0\",\"specialVehicleIden\":\"\",\"relationship\":\"1\",\"fuelType\":\"0\",\"tpyRiskflagCode\":\"003\",\"stChangeRegisterDate\":\"\",\"ownerName\":\"张汉钦\",\"ownerProp\":\"1\",\"certType\":\"1\",\"certNo\":\"330325197702215914\",\"ownerAddress\":\"温州市龙湾区龙海路香雅园4幢107-109室\",\"phoneNumber\":\"13957757012\",\"holderTelphone\":\"13957757012\",\"stCertificationDate\":\"\",\"noDamageYears\":\"\",\"trafficType\":\"\",\"transitType\":\"\",\"engineCapacity\":\"2.349\",\"power\":\"127\",\"seatCount\":\"5\",\"tonnage\":\"\",\"emptyWeight\":\"1560\",\"shortcutCode\":\"TL-EQ7230AA\",\"uniformInsurance\":\"\",\"cooperatorCode\":\"\",\"cooperatorName\":\"\",\"payWay\":\"\",\"reductionType\":\"0\",\"hfStartTime\":\"\",\"hfEndTime\":\"\",\"showDefaultTaxType\":\"0\",\"vehiclePowerJY\":\"\",\"oriEngineCapacity\":\"\",\"address\":\"\",\"jyFuelType\":\"D1\",\"newFamilyGrade\":\"1\",\"purchasePrice\":\"184800\",\"actualValue\":\"158189.00\",\"tpyRiskflagName\":\"C\",\"carModelRiskLevel\":\"\",\"plateless\":false,\"quotationNo\":\"\",\"usageType\":\"0\",\"fixedTelephone\":\"\"}}");
    else if ("江苏".equals(areaComCode))
        j = JSON.parseObject("{\"meta\":{},\"redata\":{\"fleetAgreementNo\":\"\",\"plateNo\":\"苏AF05556\",\"plateType\":\"52\",\"plateColor\":\"1\",\"carVIN\":\"LC0CD4C31H1011583\",\"engineNo\":\"A17006947\",\"stRegisterDate\":\"2017-05-04\",\"usage\":\"101\",\"vehicleType\":\"02\",\"vehiclePurpose\":\"01\",\"isCarInsure\":\"\",\"modelType\":\"比亚迪BYD6480STHEV3插电式混合动力多用途乘用车\",\"moldCharacterCode\":\"BYAAVD0004\",\"producingArea\":\"0\",\"factoryType\":\"比亚迪BYD6480STHEV3插电式混合动力多用途乘用车\",\"negotiatedValue\":\"245584\",\"loan\":\"0\",\"specialVehicleIden\":\"\",\"relationship\":\"1\",\"fuelType\":\"3\",\"tpyRiskflagCode\":\"003\",\"stChangeRegisterDate\":\"\",\"ownerName\":\"汪敏\",\"ownerProp\":\"1\",\"certType\":\"1\",\"certNo\":\"320925198605110024\",\"holderTelphone\":\"\",\"stCertificationDate\":\"\",\"noDamageYears\":\"\",\"salesPerson\":\"王庆霞\",\"vehicleStyle\":\"K32\",\"trafficType\":\"\",\"transitType\":\"\",\"engineCapacity\":\"1.999\",\"power\":\"151\",\"seatCount\":\"7\",\"tonnage\":\"\",\"emptyWeight\":\"2390\",\"shortcutCode\":\"BYD-BYD6480STHEV3\",\"uniformInsurance\":\"\",\"cooperatorCode\":\"\",\"cooperatorName\":\"\",\"payWay\":\"\",\"reductionType\":\"1\",\"hfStartTime\":\"2018-09-18\",\"hfEndTime\":\"2099-12-31\",\"showDefaultTaxType\":\"1\",\"vehiclePowerJY\":\"151\",\"oriEngineCapacity\":\"1.999\",\"address\":\"\",\"jyFuelType\":\"D12\",\"newFamilyGrade\":\"1\",\"pmVehicleUsage\":\"\",\"purchasePrice\":\"284900\",\"actualValue\":\"245584.00\",\"tpyRiskflagName\":\"C\",\"carModelRiskLevel\":\"\",\"plateless\":false,\"usageType\":\"0\",\"newCarFlag\":\"\",\"secretCertNo\":2,\"secretHolderTelphone\":2}}");
    else if ('重庆'.equals(areaComCode)) {
        j = JSON.parseObject('{"meta":{},"redata":{"fleetAgreementNo":"","holderName":"","plateNo":"","plateType":"","plateColor":"","carVIN":"","engineNo":"","stRegisterDate":"","usage":"","vehicleType":"","vehiclePurpose":"","isCarInsure":"","modelType":"","moldCharacterCode":"","producingArea":"","factoryType":"","negotiatedValue":"","loan":"0","specialVehicleIden":"","relationship":"","fuelType":"","stChangeRegisterDate":"","ownerName":"","ownerProp":"","certType":"","certNo":"","tpyRiskflagCode":"","holderTelphone":"","stCertificationDate":"","stCertificateValidity":"","noDamageYears":"","vehicleLoanBank":"","premiumLoanBank":"","vehicleLoanCode":"","premiumLoanCode":"","ocrName":"","pmVehicleModel":"","transferCompanyName":"","busTransferCompanyName":"","threeOrgName":"","fourOrgName":"","carTranche":"","trafficAndThirdSpreading":"","threeDamageTranche":"","twoGuestCrisisJudge":"","vehicleTotal":"","agentPerson":"","specialVehicleCode":"","specialRiskCode":"","rechargeMileage":"","totalMotorPower":"","qualityType":"","qualityName":"","branchQualityType":"","branchQualityName":"","levelScene":"","familyName":"","trafficType":"","transitType":"","engineCapacity":"","power":"","seatCount":"","tonnage":"","emptyWeight":"","shortcutCode":"","uniformInsurance":"","cooperatorCode":"","cooperatorName":"","payWay":"","payWaySubset":"","busiInsurance":"","lifeInsurancePerphone":"","reductionType":"","hfStartTime":"","hfEndTime":"","showDefaultTaxType":"","vehiclePowerJY":"","oriEngineCapacity":"","address":"","jyFuelType":"","newFamilyGrade":"","pmVehicleUsage":"","ownerAddress":"","fixedTelephone":"","phoneNumber":"","infosameInsured":"","caliberOfNewChannels":"","taxTypeWz":"","siteUserId":"","underwritingCode":"","agreementType":"","vehiclefgwCode":"","vehicleClassName":"","lastMoldType":"","ythTaskId":"","purchasePrice":"","actualValue":"","tpyRiskflagName":"","carModelRiskLevel":"","renewScore":"","plateless":false,"sellerVos":[],"usageType":"","newCarFlag":"","secretCertNo":2,"secretHolderTelphone":2,"isRenew":0,"isShowAccidentPlan":false,"accidentPlan":"","lastBusinessPolicyNo":"","lastAccPolicyNo":"","oldCustomerFlag":"","ocrCustomerFlag":"0","isLastLocality":"","carPowerType":"","modelCarloadTranche":"","registerDateIsPass":"0","oldVehicleLSPlateAppeal":"0","isQueryJg":"0"}}')
    }

    return j;
}

JSONObject getSalerInfoParam(String areaComCode) {
    JSONObject j = JSON.parseObject("{\"meta\":{\"pageNo\":1,\"pageSize\":20},\"redata\":{\"certificateNo\":\"\",\"salerName\":\"\\u9093\\u79c9\\u5065\"}}");

    return j;
}

JSONObject getQueryQuickOfferParam(String areaComCode) {
    return JSON.parseObject("{\"meta\":{},\"redata\":{\"ecarvo\":{\"plateNo\":\"\\u6e58ANV596\",\"carVIN\":\"\",\"plateColor\":\"1\"}}}");
}

JSONObject getSaveInsureParam(String areaComCode) {
    if ("宁波".contains(areaComCode))
        return JSON.parseObject('{"meta":{},"redata":{"quotationNo":"","commercial":true,"compulsory":true,"compulsoryInsuransVo":{"countingPoolsRemainQuota":"","originalPoudage":0.07,"policyCostRateVar":"0.5263","lastActualAddedservicerate":"","premiumRatio":0.594,"lastLocalDiscuteRate":"0.594000","totalPolicyCostRateVar":"0.5263","damageSpreading":"","originalSubjectRate":"|||||||0.0575||","blackGrade":"","trafficTransgressRate":1,"businessFeeVar":"0.0575","totalEcompensationRate":"0.3798","lastPremiumCalculationScheme":"C","lastActualPoudage":"","subjectRate":"|||||||0.0575||","stMinPremRatio":"","inferedDiscuteRate":0.594,"ncdComeFromBusses":"","netPremium":"975.24","carSpreading":"N2","inferedChangeableFeeRateVar":"0.1465","totalNetPremium":"","standardPremium":2039.04,"telSalePerson":"","stMaxSuggestRatio":0.594,"policyCostRate":0.52625004,"oriChangeableFeeRateInVar":"0.0000","originalAddedservicerate":"0.0","deferSubjectAcount":"","businesspoudage":"0.07","stStartDate":"2024-09-0100:00","stMaxPremRatio":"","casePriority":"","serviceBagCode":"","premium":1211.19,"lastBusinessFee":"0","totalNetEcompensationRate":"","headIndependentPriceRate":"","twoGuestFlag":"","lastActualPerformance":"","nonClaimDiscountRate":"","totalCpicScore":"","serviceValueAddRate":"0.0","vehicleRiskLevel":"Z","inferedChangeableFeeRate":0.1465,"subjectAcount":"","bNonAutoTotalPolicyCostRate":"0.52625004","stSchemeId":"","personTotalEcompensationRate":"","lastchangeableFeeRate":"","commercialAgreementVos":[],"caseNo":"","performanceVar":"","policyCostRateExcMFR":"","underwritingRate":"","deferSubjectRate":"","oriChangeableFeeRateVar":"","productCode":"","blackDivisor":"","originalDeferSubjectRcount":"","firstPlateNo":"","stMinSuggestRatio":0.594,"thirdAndDamageSpreading":"","annualPremium":1211.19,"totalPolicyCostRateExcMFR":"0.5263","checkConsistency":"6","premiumCalculationScheme":"C","lasttrafficsubsidyflag":"","profitPoolsRemainQuota":"","vehicleClaimType":"","discuteAdjustProportion":"0.000000","cpicScore":"2","originalBusinessFee":0.0575,"insuranceQueryCode":"V0101CPIC330224080104296281577","caseName":"","lastActualBusinessFee":"","lastPerformance":"0","localDiscuteRate":0.594,"wyCarType":"-1","oriAddedserviceRateVar":"0.0","lastAdviceDiscuteMinRate":"0","stEndDate":"2025-09-0100:00","originalPerFormance":0.019,"lastTargetPolicyCostRate":"","shareRuleNo":"","microTagging":"THC_APPLET_FLG|THC_USER_FLG|OTO_WECHAT_FLG|FGSGW_USER_FLG","poudage":0.07,"channelCityNum":"","changeableFeeRate":0.1465,"aidingFundProportion":"","independentPriceRate":"0.990000","whetherServiceBag":"","claimTimes":"0","oriBusinessFeeVar":"0.0575","aidingFund":"","businessfee":"0.0575","lastPremiumRatio":0.594,"targetPolicyCostRate":0,"delayBusinessRate":"0","insureYears":"3","isLinkage":"0","feemanagematchflag":"","oriPerformanceVar":"0.019","reconcileCode":"","newCarFlag":"0","localPureRiskPremium":433.91,"flexibleScore":"","premiumChange":"0","firstIndependentPriceRate":"","servicePolicyCostRate":"","specialCarFlag":"","lastPageChangeableFeeRate":"0","originalDeferSubjectRate":"","channelRate":"","originalChangeableFeeRate":0.1465,"originalSubjectAcount":"","commContinuedInsuredYears":"0","lastLocalPureriskPremium":"433.91","ncdTotalEcompensationRate":"0.3760","chdCarRecord":"","thirdPartySpreading":"","insuranceFlag":"T","lastPoudage":"0","ncdEcompensationRate":"0.3760","netEcompensationRate":"0.4449","oriPoudageVar":"0.07","poudageVar":"0.0700","performance":"0.019","isAdjustDiscuteRate":"0","tBNonAutoTotalPolicyCostRate":"0.5205","oriPoudageInVar":"0.0000","profitChangeValue":"","ruleNo":"100003897530","addedserviceRateVar":"0.0000","riskCore":"1","thirdLoginName":""},"commercialInsuransVo":{"insuranceQueryCode":"V0101CPIC330224080104313507829","aidingFundProportion":"","aidingFund":"","vehicleRiskLevel":"Z","telSalePerson":"","localPureRiskPremium":577.18,"lastLocalPureriskPremium":"577.18","caseNo":"","caseName":"","casePriority":"","productCode":"","netPremium":"1183.72","totalNetPremium":"2079.95","microTagging":"OTO_WECHAT_FLG|ZGSGW_USER_FLG|FGSGW_USER_FLG","blackGrade":"","firstPlateNo":"","ncdComeFromBusses":"","insureYears":"3","claimTimes":"1","thirdLoginName":"","whetherServiceBag":"","serviceBagCode":"","stStartDate":"2024-09-18 00:00","stEndDate":"2025-09-18 00:00","totalCpicScore":"25","premiumRatio":"0.693000","poudage":"0.07","businessfee":"0.0575","performance":"0.019","serviceValueAddRate":"0.0","poudageVar":"0.0700","businessFeeVar":"0.0575","performanceVar":"0.0190","addedserviceRateVar":"0.0000","isAdjustDiscuteRate":"0","discuteAdjustProportion":"0.000000","profitChangeValue":"","profitPoolsRemainQuota":"","countingPoolsRemainQuota":"","thirdPartySpreading":"","damageSpreading":"","thirdAndDamageSpreading":"","chdCarRecord":"","carSpreading":"4","channelCityNum":"","twoGuestFlag":"","wyCarType":"-1","commContinuedInsuredYears":"0","netEcompensationRate":"0.4876","totalNetEcompensationRate":"0.7006","ncdEcompensationRate":"0.4120","specialCarFlag":"","ncdTotalEcompensationRate":"0.6344","blackDivisor":"","tBNonAutoTotalPolicyCostRate":"0.7273","personTotalEcompensationRate":"0.6383","bNonAutoTotalPolicyCostRate":"0.56266798","riskCore":"2","flexibleScore":"","ecompensationRate":"0.4162","totalEcompensationRate":"0.6383","espcompensationRate":"0.2884","cpicScore":"3","stMaxPremRatio":"","stMinPremRatio":"","stMaxSuggestRatio":"","stMinSuggestRatio":"","isLinkage":"0","lastActualAddedservicerate":"","lastActualPoudage":"","lastActualBusinessFee":"","lastActualPerformance":"","servicePolicyCostRate":"","firstIndependentPriceRate":"","delayBusinessRate":"0","stSchemeId":"","changeableFeeRate":0.1465,"originalPoudage":0.07,"originalBusinessFee":0.0575,"originalPerFormance":0.019,"originalAddedservicerate":"0.0","originalChangeableFeeRate":0.1465,"inferedChangeableFeeRate":0.1465,"localDiscuteRate":0.693,"inferedDiscuteRate":0.693,"targetPolicyCostRate":0,"premiumCalculationScheme":"C","policyCostRate":0.56266798,"policyCostRateVar":"0.5627","policyCostRateExcMFR":"0.5627","totalPolicyCostRateVar":"0.7273","totalPolicyCostRateExcMFR":"0.7273","lastPremiumCalculationScheme":"C","lastTargetPolicyCostRate":"","originalSubjectRate":"|||||||0.0575||","originalSubjectAcount":"","originalDeferSubjectRate":"","originalDeferSubjectRcount":"","subjectRate":"|||||||0.0575||","subjectAcount":"","deferSubjectRate":"","deferSubjectAcount":"","lastPerformance":"0","lastBusinessFee":"0","lastPoudage":"0","lastLocalDiscuteRate":"0.693000","lastAdviceDiscuteMinRate":"0","lastPageChangeableFeeRate":"0","businesspoudage":"0.07","ruleNo":"100003897530","shareRuleNo":"","annualPremium":1470.11,"lasttrafficsubsidyflag":"","feemanagematchflag":"","newCarFlag":"0","lastPremiumRatio":0.693,"lastchangeableFeeRate":0.1465,"oriPoudageVar":"0.07","oriPerformanceVar":"0.019","oriBusinessFeeVar":"0.0575","oriAddedserviceRateVar":"0.0","oriChangeableFeeRateVar":"0.1465","oriPoudageInVar":"0.0000","oriChangeableFeeRateInVar":"0.0000","inferedChangeableFeeRateVar":"0.1465","nonClaimDiscountRate":"0.7","trafficTransgressRate":"1","underwritingRate":"","channelRate":"","independentPriceRate":"0.990000","headIndependentPriceRate":"","standardPremium":2121.37,"premium":1470.11,"vehicleClaimType":"","insuranceFlag":"T","checkConsistency":null,"commercialAgreementVos":[],"reconcileCode":"","premiumChange":"0"},"quoteInsuranceVos":[{"amount":"217168","insuranceType":"1","insuranceCode":"DAMAGELOSSCOVERAGE","nonDeductible":"291.04","standardPremium":5072.61,"premium":1940.27},{"insuranceCode":"DAMAGELOSSEXEMPTDEDUCTIBLESPECIALCLAUSE","nonDeductible":"291.04","standardPremium":"760.89"},{"amount":"10000","insuranceType":"1","insuranceCode":"INCARDRIVERLIABILITYCOVERAGE","nonDeductible":"2.41","standardPremium":42,"premium":16.07},{"insuranceCode":"INCARDRIVERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE","nonDeductible":"2.41","standardPremium":"6.30"},{"amount":"10000","insuranceType":"1","insuranceCode":"INCARPASSENGERLIABILITYCOVERAGE","nonDeductible":"6.20","factorVos":[{"factorKey":"seat","factorValue":"4"}],"standardPremium":108,"premium":41.31},{"insuranceCode":"INCARPASSENGERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE","nonDeductible":"6.20","standardPremium":"16.20"}],"isHolidaysDouble":0,"ifAllroundClause":0,"schemeId":"","inType":"T","ecarvo":{"actualValue":"217168","carVIN":"LE4WG4AB8GL179386","certNo":"330227198901180795","certType":"1","emptyWeight":"1596","engineCapacity":"1.595","engineNo":"10421917","factoryType":"梅赛德斯-奔驰BJ7164GL轿车","fuelType":"0","holderTelphone":"13712346544","id":79785830,"jyFuelType":"D1","loan":"0","modelType":"梅赛德斯-奔驰BJ7164GL轿车","moldCharacterCode":"MSBAHD0050","negotiatedValue":"217168","newFamilyGrade":"1.8","ownerName":"朱蒙子","ownerProp":"1","plateColor":"1","plateNo":"浙BL32Z2","plateType":"02","plateless":false,"power":"115","prevMoldChrCode":"MSBAHD0050","prevNegoValue":"217168","producingArea":"2","purchasePrice":"277000","reductionType":"0","registerDate":1471536000000,"relationship":"1","seatCount":"5","secretCertNo":"2","secretHolderTelphone":"2","shortcutCode":"MSDSBC-BJ7164GL","showDefaultTaxType":"1","stRegisterDate":"2016-08-19","taxCustomerType":"1","taxMark":"0","taxTypeWz":"0","tpyRiskflagCode":"005","tpyRiskflagName":"E","usage":"101","usageType":"0","vehiclePurpose":"01","vehicleType":"01","carModelRiskLevel":"17","inType":"T","lastyearPurchaseprice":"","lastyearModeltype":"","lastyearModelcode":"","transferCompanyName":"","newCarFlag":0},"insuredVo":{"hqCustomerAccTimes":"","hqCustomerGrouping":"","hqCustomerValue":""}}}')
    if ("温州".equals(areaComCode) || "浙江".equals(areaComCode))
        return JSON.parseObject("{\"meta\":{},\"redata\":{\"quotationNo\":\"\",\"commercial\":true,\"compulsory\":true,\"compulsoryInsuransVo\":{\"insuranceQueryCode\":\"01CPIC330019001553075142980825\",\"stBackStartDate\":\"\",\"stBackEndDate\":\"\",\"aidingFundProportion\":\"0.02\",\"aidingFund\":\"19.0\",\"stStartDate\":\"2019-03-21 00:00\",\"stEndDate\":\"2020-03-21 00:00\",\"ecompensationRate\":\"0.7858\",\"compCpicScore\":66,\"trafficSpreading\":\"\",\"taxType\":\"3\",\"standardPremium\":\"950\",\"taxBureauName\":\"\",\"stTaxStartDate\":\"2019-01-01\",\"stTaxEndDate\":\"2019-12-31\",\"taxpayerName\":\"张汉钦\",\"taxpayerType\":\"1\",\"taxpayerNo\":\"330325197702215914\",\"cipremium\":950,\"taxAmount\":660,\"stPayableAmount\":\"660.00\",\"stLateFee\":\"0.00\",\"stBackAmount\":\"0.00\",\"vehicleClaimType\":\"\",\"annualPremium\":950,\"changeablefeeRate\":0,\"localPureRiskPremium\":704.27,\"trafficpoudage\":0,\"businessfee\":0,\"performance\":0,\"policycostRate\":0.7858,\"jqNewCarFlag\":\"0\"},\"commercialInsuransVo\":{\"insuranceQueryCode\":\"V0101CPIC330019001553075142881\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"vehicleRiskLevel\":\"Z\",\"stStartDate\":\"2019-03-21 00:00\",\"stEndDate\":\"2020-03-21 00:00\",\"totalCpicScore\":\"59\",\"premiumRatio\":\"0.637500\",\"ecompensationRate\":\"0.4227\",\"totalEcompensationRate\":\"0.5759\",\"cpicScore\":\"32\",\"thirdPartySpreading\":\"\",\"damageSpreading\":\"\",\"thirdAndDamageSpreading\":\"\",\"stMaxPremRatio\":\"1.3225\",\"stMinPremRatio\":\"0.6375\",\"stMaxSuggestRatio\":\"0.637500\",\"stMinSuggestRatio\":\"0.637500\",\"stSchemeId\":\"\",\"nonClaimDiscountRate\":\"1\",\"trafficTransgressRate\":\"1\",\"underwritingRate\":\"0.85\",\"channelRate\":\"0.75\",\"standardPremium\":2042.4,\"premium\":1302.03,\"poudage\":\"0\",\"vehicleClaimType\":\"3\",\"checkConsistency\":\"\"},\"quoteInsuranceVos\":[{\"amount\":\"500000\",\"insuranceType\":\"1\",\"insuranceCode\":\"THIRDPARTYLIABILITYCOVERAGE\",\"nonDeductible\":\"155.96\",\"standardPremium\":1631,\"premium\":1039.76},{\"insuranceCode\":\"THIRDPARTYLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":\"155.96\",\"standardPremium\":\"244.65\"},{\"amount\":\"10000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARDRIVERLIABILITYCOVERAGE\",\"nonDeductible\":\"3.92\",\"standardPremium\":41,\"premium\":26.14},{\"insuranceCode\":\"INCARDRIVERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":\"3.92\",\"standardPremium\":\"6.15\"},{\"amount\":\"10000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARPASSENGERLIABILITYCOVERAGE\",\"nonDeductible\":\"9.95\",\"factorVos\":[{\"factorKey\":\"seat\",\"factorValue\":\"4\"}],\"standardPremium\":104,\"premium\":66.3},{\"insuranceCode\":\"INCARPASSENGERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":\"9.95\",\"standardPremium\":\"15.60\"}],\"accident\":false,\"isHolidaysDouble\":0,\"ifAllroundClause\":0,\"schemeId\":\"\",\"inType\":\"T\",\"ecarvo\":{\"actualValue\":\"158189.00\",\"carVIN\":\"LGBF1CE085R116777\",\"certNo\":\"330325197702215914\",\"certType\":\"1\",\"emptyWeight\":\"1560\",\"engineCapacity\":\"2.349\",\"engineNo\":\"VQ23 089777\",\"factoryType\":\"天籁EQ7230AA轿车\",\"fuelType\":\"0\",\"holderTelphone\":\"13957757012\",\"id\":64502988,\"jyFuelType\":\"D1\",\"loan\":\"0\",\"modelType\":\"天籁EQ7230AA轿车\",\"moldCharacterCode\":\"RCAAKD0014\",\"negotiatedValue\":\"158189\",\"newFamilyGrade\":\"1\",\"ownerAddress\":\"温州市龙湾区龙海路香雅园4幢107-109室\",\"ownerName\":\"张汉钦\",\"ownerProp\":\"1\",\"phoneNumber\":\"13957757012\",\"plateColor\":\"1\",\"plateNo\":\"浙CC2N77\",\"plateType\":\"02\",\"plateless\":false,\"power\":\"127\",\"prevMoldChrCode\":\"RCAAKD0014\",\"prevNegoValue\":\"158189\",\"producingArea\":\"2\",\"purchasePrice\":\"184800\",\"reductionType\":\"0\",\"registerDate\":1489680000000,\"relationship\":\"1\",\"seatCount\":\"5\",\"shortcutCode\":\"TL-EQ7230AA\",\"showDefaultTaxType\":\"0\",\"stRegisterDate\":\"2017-03-17\",\"taxCustomerType\":\"1\",\"taxMark\":\"0\",\"tpyRiskflagCode\":\"003\",\"tpyRiskflagName\":\"C\",\"usage\":\"101\",\"usageType\":\"0\",\"vehiclePurpose\":\"01\",\"vehicleType\":\"01\",\"carModelRiskLevel\":\"-1\",\"inType\":\"T\",\"lastyearPurchaseprice\":\"\",\"lastyearModeltype\":\"\",\"lastyearModelcode\":\"\",\"transferCompanyName\":\"\",\"newCarFlag\":0}}}");
    if ("上海".contains(areaComCode))
        return JSON.parseObject("{\"meta\":{},\"redata\":{\"quotationNo\":\"\",\"commercial\":true,\"compulsory\":true,\"compulsoryInsuransVo\":{\"insuranceQueryCode\":\"01CPIC01170000000001250787642A\",\"stBackStartDate\":\"2016-12-31\",\"stBackEndDate\":\"2016-12-31\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"stStartDate\":\"2017-11-28 00:00\",\"stEndDate\":\"2018-11-28 00:00\",\"ecompensationRate\":\"0.6930\",\"taxType\":\"N\",\"standardPremium\":\"950\",\"exceedDaysCount\":\"0\",\"reductionAmount\":\"\",\"taxPaidNo\":\"纳税\",\"stTaxStartDate\":\"2017-01-01\",\"stTaxEndDate\":\"2017-12-31\",\"payableAmount\":\"360\",\"backAmount\":\"0\",\"stLateFeeStartDate\":\"2017-11-07\",\"stLateFeeEndDate\":\"2017-11-06\",\"lateFee\":\"0\",\"taxpayerName\":\"谭敏仪\",\"taxpayerType\":\"1\",\"taxpayerNo\":\"440107199009090325\",\"totalWeight\":\"\",\"taxBureauName\":\"\",\"taxVehicleType\":\"K02\",\"taxpayerSubstRecno\":\"\",\"cipremium\":855,\"taxAmount\":360,\"stPayableAmount\":\"360.0\",\"stLateFee\":\"0.0\",\"stBackAmount\":\"0.0\",\"vehicleClaimType\":\"1\"},\"commercialInsuransVo\":{\"insuranceQueryCode\":\"01CPIC01170000000001250787635C\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"vehicleRiskLevel\":\"A\",\"stStartDate\":\"2017-11-28 00:00\",\"stEndDate\":\"2018-11-28 00:00\",\"premiumRatio\":\"0.490875\",\"ecompensationRate\":\"0.4965\",\"totalEcompensationRate\":\"0.5818\",\"stMaxPremRatio\":\"1.018325\",\"stMinPremRatio\":\"0.490875\",\"stMaxSuggestRatio\":\"0.490875\",\"stMinSuggestRatio\":\"0.490875\",\"stSchemeId\":\"\",\"nonClaimDiscountRate\":\"0.7\",\"trafficTransgressRate\":\"1.1\",\"underwritingRate\":\"0.85\",\"channelRate\":\"0.75\",\"standardPremium\":2272.4,\"premium\":1115.47,\"poudage\":\"0\",\"vehicleClaimType\":\"1\"},\"quoteInsuranceVos\":[{\"amount\":\"1000000\",\"insuranceType\":\"1\",\"insuranceCode\":\"THIRDPARTYLIABILITYCOVERAGE\",\"nonDeductible\":\"145.50\",\"standardPremium\":1976,\"premium\":969.97},{\"insuranceCode\":\"THIRDPARTYLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":\"145.50\",\"standardPremium\":\"296.40\"}],\"commercialAgreementVos\":[{\"customAgreement\":\"本保单保险车辆行驶证车主为：丁仲晖。\"}],\"platformVo\":{\"benchmarkRiskPremium\":\"1060.975565\",\"brand\":\"荣威CSA7150MCT轿车\",\"brandCode\":\"RWA\",\"carName\":\"荣威CSA7150MCT 讯驰版\",\"configType\":\"UB\",\"id\":1485717,\"modelCode\":\"BRWASXUB0010\",\"pureRiskPremium\":\"1060.975565\",\"series\":\"荣威350\"},\"inType\":\"R\",\"ecarvo\":{\"actualValue\":\"46528\",\"carVIN\":\"LSJA16E3XCG053192\",\"certNo\":\"440107199009090325\",\"certType\":\"1\",\"emptyWeight\":\"1265.00\",\"engineCapacity\":\"1.498\",\"engineNo\":\"A8GC7090453\",\"factoryType\":\"荣威CSA7150MCT轿车\",\"id\":12311714,\"jyFuelType\":\"D1\",\"loan\":\"0\",\"modelType\":\"荣威CSA7150MCT轿车\",\"moldCharacterCode\":\"RWAACD0016\",\"negotiatedValue\":\"46528\",\"ownerName\":\"谭敏仪\",\"ownerProp\":\"1\",\"plateColor\":\"1\",\"plateNo\":\"沪CDZ606\",\"plateType\":\"02\",\"plateless\":false,\"platformVo\":{\"benchmarkRiskPremium\":\"1060.975565\",\"brand\":\"荣威CSA7150MCT轿车\",\"brandCode\":\"RWA\",\"carName\":\"荣威CSA7150MCT 讯驰版\",\"configType\":\"UB\",\"id\":1485717,\"modelCode\":\"BRWASXUB0010\",\"pureRiskPremium\":\"1060.975565\",\"series\":\"荣威350\"},\"power\":\"80\",\"prevMoldChrCode\":\"RWAACD0016\",\"prevNegoValue\":\"46528\",\"producingArea\":\"02\",\"purchasePrice\":\"72700\",\"reductionType\":\"0\",\"registerDate\":1354032000000,\"relationship\":\"1\",\"seatCount\":\"5\",\"shortcutCode\":\"39CPIC01170000000001250754049F\",\"stRegisterDate\":\"2012-11-28\",\"taxCustomerType\":\"1\",\"taxMark\":\"0\",\"tpyRiskflagCode\":\"003\",\"tpyRiskflagName\":\"C\",\"usage\":\"101\",\"usageType\":\"0\",\"vehiclePurpose\":\"01\",\"vehicleType\":\"01\",\"carModelRiskLevel\":\"-5\",\"inType\":\"R\",\"lastyearPurchaseprice\":\"\",\"lastyearModeltype\":\"\",\"lastyearModelcode\":\"\",\"transferCompanyName\":\"\"}}}");
    if ("云南".contains(areaComCode))
        return JSON.parseObject("{\"meta\":{},\"redata\":{\"quotationNo\":\"\",\"commercial\":true,\"compulsory\":true,\"compulsoryInsuransVo\":{\"insuranceQueryCode\":\"01CPIC530017001514171694592226\",\"stBackStartDate\":\"\",\"stBackEndDate\":\"\",\"aidingFundProportion\":\"0.02\",\"aidingFund\":\"19.8\",\"stStartDate\":\"2018-01-24 00:00\",\"stEndDate\":\"2019-01-24 00:00\",\"ecompensationRate\":\"0.5057\",\"taxType\":\"3\",\"standardPremium\":\"1100\",\"localCalTax\":\"0\",\"taxBureau\":\"\",\"stTaxStartDate\":\"2017-01-01\",\"stTaxEndDate\":\"2017-12-31\",\"taxBureauName\":\"\",\"taxpayerSex\":\"1\",\"taxpayerName\":\"吴学兵\",\"taxpayerType\":\"1\",\"taxpayerNo\":\"53212219950313081X\",\"cipremium\":990,\"taxAmount\":300,\"stPayableAmount\":\"300.0\",\"stLateFee\":\"0.0\",\"stBackAmount\":\"0.0\",\"vehicleClaimType\":\"5\"},\"commercialInsuransVo\":{\"insuranceQueryCode\":\"V0101CPIC530017001514171689631\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"vehicleRiskLevel\":\"A\",\"stStartDate\":\"2018-01-24 00:00\",\"stEndDate\":\"2019-01-24 00:00\",\"premiumRatio\":\"0.637500\",\"ecompensationRate\":\"0.2227\",\"totalEcompensationRate\":\"0.3321\",\"stMaxPremRatio\":\"1.3225\",\"stMinPremRatio\":\"0.6375\",\"stMaxSuggestRatio\":\"0.637500\",\"stMinSuggestRatio\":\"0.637500\",\"stSchemeId\":\"\",\"nonClaimDiscountRate\":\"1\",\"trafficTransgressRate\":\"1\",\"underwritingRate\":\"0.85\",\"channelRate\":\"0.75\",\"standardPremium\":2463.3,\"premium\":1570.36,\"poudage\":\"0\",\"vehicleClaimType\":\"1\"},\"quoteInsuranceVos\":[{\"amount\":\"1000000\",\"insuranceType\":\"1\",\"insuranceCode\":\"THIRDPARTYLIABILITYCOVERAGE\",\"nonDeductible\":\"204.83\",\"standardPremium\":2142,\"premium\":1365.53},{\"insuranceCode\":\"THIRDPARTYLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":\"204.83\",\"standardPremium\":\"321.30\"}],\"accident\":false,\"inType\":\"T\",\"ecarvo\":{\"actualValue\":\"45730\",\"carVIN\":\"LZWADAGAXF4195715\",\"certNo\":\"53212219950313081X\",\"certType\":\"1\",\"emptyWeight\":\"1305\",\"engineCapacity\":\"1.485\",\"engineNo\":\"18FB0410833\",\"factoryType\":\"五菱LZW6442JF多用途乘用车\",\"fuelType\":\"0\",\"id\":17299993,\"jyFuelType\":\"D1\",\"loan\":\"0\",\"modelType\":\"五菱LZW6442JF多用途乘用车\",\"moldCharacterCode\":\"TYBBED0015\",\"negotiatedValue\":\"45730\",\"ownerName\":\"吴学兵\",\"ownerProp\":\"1\",\"plateColor\":\"1\",\"plateNo\":\"云A085WB\",\"plateType\":\"02\",\"plateless\":false,\"power\":\"82\",\"prevMoldChrCode\":\"TYBBED0015\",\"prevNegoValue\":\"45730\",\"producingArea\":\"0\",\"purchasePrice\":\"53800\",\"reductionType\":\"0\",\"registerDate\":1450108800000,\"relationship\":\"1\",\"seatCount\":\"7\",\"shortcutCode\":\"WL-LZW6442JF\",\"stRegisterDate\":\"2015-12-15\",\"taxCustomerType\":\"1\",\"taxMark\":\"0\",\"tpyRiskflagCode\":\"003\",\"tpyRiskflagName\":\"C\",\"usage\":\"101\",\"usageType\":\"0\",\"vehiclePurpose\":\"01\",\"vehicleType\":\"02\",\"carModelRiskLevel\":\"-3\",\"inType\":\"T\",\"lastyearPurchaseprice\":\"\",\"lastyearModeltype\":\"\",\"lastyearModelcode\":\"\",\"transferCompanyName\":\"\"}}}");
    if ("陕西".contains(areaComCode))
        return JSON.parseObject("{\"meta\":{},\"redata\":{\"quotationNo\":\"\",\"commercial\":true,\"compulsory\":true,\"compulsoryInsuransVo\":{\"insuranceQueryCode\":\"01CPIC610018001539245035678315\",\"stBackStartDate\":\"\",\"stBackEndDate\":\"\",\"aidingFundProportion\":\"0.02\",\"aidingFund\":\"13.3\",\"stStartDate\":\"2018-10-25 00:00\",\"stEndDate\":\"2019-10-25 00:00\",\"ecompensationRate\":\"0.4477\",\"compCpicScore\":43,\"taxType\":\"T\",\"standardPremium\":\"950\",\"taxBureau\":\"\",\"stTaxStartDate\":\"2018-01-01\",\"stTaxEndDate\":\"2018-12-31\",\"taxBureauName\":\"\",\"registryNumber\":\"91610103566043026\",\"taxpayerName\":\"史婵媛\",\"taxpayerType\":\"1\",\"taxpayerNo\":\"610502198312220062\",\"taxpayerRecno\":\"610502198312220062\",\"cipremium\":665,\"taxAmount\":480,\"stPayableAmount\":\"480.0\",\"stLateFee\":\"0.0\",\"stBackAmount\":\"0.0\",\"vehicleClaimType\":\"\",\"annualPremium\":950,\"changeablefeeRate\":0.04,\"localPureRiskPremium\":280.84,\"trafficpoudage\":0.04,\"businessfee\":0,\"performance\":0,\"policycostRate\":0.4877,\"jqNewCarFlag\":\"0\"},\"commercialInsuransVo\":{\"insuranceQueryCode\":\"V0101CPIC610018001539245035650\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"vehicleRiskLevel\":\"A\",\"stStartDate\":\"2018-10-25 00:00\",\"stEndDate\":\"2019-10-25 00:00\",\"totalCpicScore\":\"92\",\"premiumRatio\":\"0.445430\",\"ecompensationRate\":\"0.7200\",\"totalEcompensationRate\":\"0.6644\",\"cpicScore\":\"93\",\"stMaxPremRatio\":\"2.55\",\"stMinPremRatio\":\"0.255\",\"stMaxSuggestRatio\":\"\",\"stMinSuggestRatio\":\"\",\"stSchemeId\":\"\",\"changeableFeeRate\":0.2,\"originalPoudage\":0.2,\"originalBusinessFee\":0,\"originalPerFormance\":0,\"originalChangeableFeeRate\":0.2,\"inferedChangeableFeeRate\":0.2,\"localDiscuteRate\":0.44543,\"inferedDiscuteRate\":0.44543,\"targetPolicyCostRate\":0.92,\"premiumCalculationScheme\":\"A\",\"poudage\":\"0.2\",\"businessfee\":\"0.0\",\"performance\":\"0.0\",\"localPureRiskPremium\":1759.63,\"policyCostRate\":0.92,\"lastPremiumCalculationScheme\":\"A\",\"lastTargetPolicyCostRate\":0.92,\"originalSubjectRate\":\"|||||||||\",\"originalSubjectAcount\":\"|||||||||\",\"originalDeferSubjectRate\":\"|||||||||\",\"originalDeferSubjectRcount\":\"|||||||||\",\"subjectRate\":\"|||||||||\",\"subjectAcount\":\"|||||||||\",\"deferSubjectRate\":\"|||||||||\",\"deferSubjectAcount\":\"|||||||||\",\"lastPerformance\":\"0.0\",\"lastBusinessFee\":\"0.0\",\"lastPoudage\":\"0.2\",\"lastLocalDiscuteRate\":\"0.44543\",\"lastLocalPureriskPremium\":\"1759.63\",\"lastAdviceDiscuteMinRate\":\"2.550000\",\"lastPageChangeableFeeRate\":\"0.2000\",\"businesspoudage\":\"0.2\",\"ruleNo\":\"100002208877\",\"shareRuleNo\":\"\",\"annualPremium\":2590.58,\"lasttrafficsubsidyflag\":\"0\",\"feemanagematchflag\":\"S\",\"newCarFlag\":\"0\",\"lastPremiumRatio\":0.44543,\"lastchangeableFeeRate\":0.2,\"nonClaimDiscountRate\":\"0.85\",\"trafficTransgressRate\":\"1\",\"underwritingRate\":\"0.551616\",\"channelRate\":\"0.95\",\"standardPremium\":5815.88,\"premium\":2590.58,\"vehicleClaimType\":\"\"},\"quoteInsuranceVos\":[{\"amount\":\"133053\",\"insuranceType\":\"1\",\"insuranceCode\":\"DAMAGELOSSCOVERAGE\",\"nonDeductible\":\"234.63\",\"standardPremium\":3511.69,\"premium\":1564.21},{\"insuranceCode\":\"DAMAGELOSSEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":\"234.63\",\"standardPremium\":\"526.75\"},{\"amount\":\"1000000\",\"insuranceType\":\"1\",\"insuranceCode\":\"THIRDPARTYLIABILITYCOVERAGE\",\"nonDeductible\":\"69.90\",\"standardPremium\":1046.11,\"premium\":465.97},{\"insuranceCode\":\"THIRDPARTYLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":\"69.90\",\"standardPremium\":\"156.92\"},{\"amount\":\"10000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARDRIVERLIABILITYCOVERAGE\",\"nonDeductible\":\"1.10\",\"standardPremium\":16.4,\"premium\":7.31},{\"insuranceCode\":\"INCARDRIVERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":\"1.10\",\"standardPremium\":\"2.46\"},{\"amount\":\"10000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARPASSENGERLIABILITYCOVERAGE\",\"nonDeductible\":\"2.78\",\"factorVos\":[{\"factorKey\":\"seat\",\"factorValue\":\"4\"}],\"standardPremium\":41.6,\"premium\":18.53},{\"insuranceCode\":\"INCARPASSENGERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":\"2.78\",\"standardPremium\":\"6.24\"},{\"amount\":\"133053\",\"insuranceType\":\"1\",\"insuranceCode\":\"THEFTCOVERAGE\",\"nonDeductible\":\"24.19\",\"standardPremium\":271.53,\"premium\":120.95},{\"insuranceCode\":\"THEFTCOVERAGEEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":\"24.19\",\"standardPremium\":\"54.31\"},{\"amount\":0,\"insuranceType\":\"1\",\"insuranceCode\":\"GLASSBROKENCOVERAGE\",\"nonDeductible\":null,\"factorVos\":[{\"factorKey\":\"producingArea\",\"factorValue\":\"0\"}],\"standardPremium\":181.87,\"premium\":81.01}],\"accident\":false,\"isHolidaysDouble\":0,\"ifAllroundClause\":0,\"schemeId\":\"\",\"inType\":\"R\",\"ecarvo\":{\"actualValue\":133053,\"carVIN\":\"LFV3A23C3C3822928\",\"certNo\":\"610502198312220062\",\"certType\":\"1\",\"emptyWeight\":\"1535\",\"engineCapacity\":\"1.798\",\"engineNo\":\"193405\",\"factoryType\":\"大众FV7187TRATG轿车\",\"fuelType\":\"0\",\"id\":45058188,\"jyFuelType\":\"D1\",\"loan\":\"0\",\"modelType\":\"大众FV7187TRATG轿车\",\"moldCharacterCode\":\"DZAAAD0097\",\"negotiatedValue\":133053,\"ownerName\":\"史婵媛\",\"ownerProp\":\"1\",\"plateColor\":\"1\",\"plateNo\":\"陕AU129C\",\"plateType\":\"02\",\"plateless\":false,\"power\":\"118\",\"prevMoldChrCode\":\"DZAAAD0097\",\"prevNegoValue\":133053,\"producingArea\":\"2\",\"purchasePrice\":\"231800\",\"reductionType\":\"0\",\"registerDate\":1351180800000,\"relationship\":\"1\",\"seatCount\":\"5\",\"shortcutCode\":\"DZ-FV7187TRATG\",\"stRegisterDate\":\"2012-10-26\",\"taxCustomerType\":\"1\",\"taxMark\":\"0\",\"tpyRiskflagCode\":\"004\",\"tpyRiskflagName\":\"D\",\"usage\":\"101\",\"usageType\":\"0\",\"vehiclePurpose\":\"01\",\"vehicleType\":\"01\",\"carModelRiskLevel\":\"-1\",\"inType\":\"R\",\"lastyearPurchaseprice\":\"\",\"lastyearModeltype\":\"\",\"lastyearModelcode\":\"\",\"transferCompanyName\":\"\",\"newCarFlag\":0}}}");
    if ("山东".contains(areaComCode))
        return JSON.parseObject('{\n' +
                '  "meta": {\n' +
                '  },\n' +
                '  "redata": {\n' +
                '    "quotationNo": "QJIN00RY2023F003W66N",\n' +
                '    "inquireQID": "BJIN26202303131852504159570201215731001",\n' +
                '    "busiInsurance": "",\n' +
                '    "commercial": true,\n' +
                '    "compulsory": true,\n' +
                '    "compulsoryInsuransVo": {\n' +
                '      "insuranceQueryCode": "",\n' +
                '      "stBackStartDate": "",\n' +
                '      "stBackEndDate": "",\n' +
                '      "aidingFundProportion": "",\n' +
                '      "aidingFund": "",\n' +
                '      "telSalePerson": "",\n' +
                '      "localPureRiskPremium": 396.62,\n' +
                '      "lastLocalPureriskPremium": "",\n' +
                '      "netPremium": "463.87",\n' +
                '      "totalNetPremium": "750.56",\n' +
                '      "microTagging": "",\n' +
                '      "blackGrade": "",\n' +
                '      "firstIndependentPriceRate": "",\n' +
                '      "firstPlateNo": "鲁AL98C0",\n' +
                '      "stStartDate": "2023-03-15 00:00",\n' +
                '      "stEndDate": "2024-03-15 00:00",\n' +
                '      "ecompensationRate": "0.6322",\n' +
                '      "compCpicScore": 44,\n' +
                '      "trafficSpreading": "",\n' +
                '      "compContinuedInsuredYears": "0",\n' +
                '      "channelCityNum": "",\n' +
                '      "twoGuestFlag": "",\n' +
                '      "netEcompensationRate": "0.8550",\n' +
                '      "specialCarFlag": "",\n' +
                '      "ncdEcompensationRate": "0.6322",\n' +
                '      "blackDivisor": "",\n' +
                '      "riskCore": "2",\n' +
                '      "taxType": "T",\n' +
                '      "standardPremium": "950",\n' +
                '      "taxBureau": "",\n' +
                '      "stTaxStartDate": "2023-01-01",\n' +
                '      "stTaxEndDate": "2023-12-31",\n' +
                '      "taxBureauName": "山东省税务局",\n' +
                '      "registryNumber": "913701263071559552",\n' +
                '      "stTaxBackAmount": "",\n' +
                '      "taxpayerName": "时光",\n' +
                '      "taxpayerType": "1",\n' +
                '      "taxpayerNo": "37010519810908412X",\n' +
                '      "taxpayerRecno": "37010519810908412X",\n' +
                '      "cipremium": 665,\n' +
                '      "taxAmount": 360,\n' +
                '      "stPayableAmount": "360.0",\n' +
                '      "stLateFee": "0.0",\n' +
                '      "stBackAmount": "0.0",\n' +
                '      "vehicleClaimType": "",\n' +
                '      "annualPremium": 665,\n' +
                '      "changeablefeeRate": 0.2606,\n' +
                '      "trafficpoudage": 0.04,\n' +
                '      "businessfee": 0.2,\n' +
                '      "performance": 0.0187,\n' +
                '      "policycostRate": 0.8928,\n' +
                '      "jqNewCarFlag": "0",\n' +
                '      "secretTaxpayerNo": 2,\n' +
                '      "secretTaxpayerRecno": 2,\n' +
                '      "compulsoryAgreementVos": [\n' +
                '      ],\n' +
                '      "specialAgreement": "",\n' +
                '      "reconcileCode": ""\n' +
                '    },\n' +
                '    "commercialInsuransVo": {\n' +
                '      "insuranceQueryCode": "",\n' +
                '      "aidingFundProportion": "",\n' +
                '      "aidingFund": "",\n' +
                '      "vehicleRiskLevel": "Z",\n' +
                '      "telSalePerson": "",\n' +
                '      "localPureRiskPremium": 174.9,\n' +
                '      "lastLocalPureriskPremium": "174.90",\n' +
                '      "caseNo": "",\n' +
                '      "caseName": "",\n' +
                '      "casePriority": "",\n' +
                '      "productCode": "",\n' +
                '      "netPremium": "286.69",\n' +
                '      "totalNetPremium": "750.56",\n' +
                '      "microTagging": "",\n' +
                '      "blackGrade": "",\n' +
                '      "firstPlateNo": "",\n' +
                '      "stStartDate": "2023-03-15 00:00",\n' +
                '      "stEndDate": "2024-03-15 00:00",\n' +
                '      "totalCpicScore": "24",\n' +
                '      "premiumRatio": "0.445000",\n' +
                '      "poudage": "0.12",\n' +
                '      "businessfee": "0.12",\n' +
                '      "performance": "0.0187",\n' +
                '      "serviceValueAddRate": "0.0019",\n' +
                '      "poudageVar": "0.1200",\n' +
                '      "businessFeeVar": "0.1200",\n' +
                '      "performanceVar": "0.0187",\n' +
                '      "addedserviceRateVar": "0.0019",\n' +
                '      "changeableFeeRateVar": "0.2606",\n' +
                '      "inferedChangeableFeeRateVar": "0.2606",\n' +
                '      "isAdjustDiscuteRate": "0",\n' +
                '      "discuteAdjustProportion": "0.000",\n' +
                '      "ecompensationRate": "0.4511",\n' +
                '      "totalEcompensationRate": "0.5630",\n' +
                '      "profitChangeValue": "",\n' +
                '      "profitPoolsRemainQuota": "",\n' +
                '      "countingPoolsRemainQuota": "",\n' +
                '      "cpicScore": "6",\n' +
                '      "thirdPartySpreading": "",\n' +
                '      "damageSpreading": "",\n' +
                '      "thirdAndDamageSpreading": "",\n' +
                '      "chdCarRecord": "",\n' +
                '      "carSpreading": "18",\n' +
                '      "channelCityNum": "",\n' +
                '      "twoGuestFlag": "",\n' +
                '      "wyCarType": "-1",\n' +
                '      "commContinuedInsuredYears": "0",\n' +
                '      "netEcompensationRate": "0.6101",\n' +
                '      "totalNetEcompensationRate": "0.7615",\n' +
                '      "ncdEcompensationRate": "0.4014",\n' +
                '      "specialCarFlag": "",\n' +
                '      "quotationMode": "2",\n' +
                '      "ncdTotalEcompensationRate": "0.5376",\n' +
                '      "blackDivisor": "",\n' +
                '      "riskCore": "1",\n' +
                '      "stMaxPremRatio": "1.124125",\n' +
                '      "stMinPremRatio": "0.541875",\n' +
                '      "stMaxSuggestRatio": "0.445",\n' +
                '      "stMinSuggestRatio": "0.445",\n' +
                '      "isLinkage": "0",\n' +
                '      "lastActualAddedservicerate": "",\n' +
                '      "lastActualPoudage": "",\n' +
                '      "lastActualBusinessFee": "",\n' +
                '      "lastActualPerformance": "",\n' +
                '      "servicePolicyCostRate": "",\n' +
                '      "firstIndependentPriceRate": "",\n' +
                '      "delayBusinessRate": "0",\n' +
                '      "stSchemeId": "",\n' +
                '      "changeableFeeRate": 0.2606,\n' +
                '      "originalPoudage": 0.12,\n' +
                '      "originalBusinessFee": 0.12,\n' +
                '      "originalPerFormance": 0.0187,\n' +
                '      "originalAddedservicerate": "0.0019",\n' +
                '      "originalChangeableFeeRate": 0.2606,\n' +
                '      "inferedChangeableFeeRate": 0.2606,\n' +
                '      "localDiscuteRate": 0.445,\n' +
                '      "inferedDiscuteRate": 0.445,\n' +
                '      "targetPolicyCostRate": 0.9047,\n' +
                '      "premiumCalculationScheme": "A2",\n' +
                '      "policyCostRate": 0.7117,\n' +
                '      "lastPremiumCalculationScheme": "A2",\n' +
                '      "lastTargetPolicyCostRate": "",\n' +
                '      "originalSubjectRate": "||||||0.10|0.02||",\n' +
                '      "originalSubjectAcount": "",\n' +
                '      "originalDeferSubjectRate": "",\n' +
                '      "originalDeferSubjectRcount": "",\n' +
                '      "subjectRate": "||||||0.10|0.02||",\n' +
                '      "subjectAcount": "",\n' +
                '      "deferSubjectRate": "",\n' +
                '      "deferSubjectAcount": "",\n' +
                '      "lastPerformance": "0.0187",\n' +
                '      "lastBusinessFee": "0.12",\n' +
                '      "lastPoudage": "0.12",\n' +
                '      "lastLocalDiscuteRate": "0.445",\n' +
                '      "lastAdviceDiscuteMinRate": "0",\n' +
                '      "lastPageChangeableFeeRate": "0",\n' +
                '      "businesspoudage": "0.12",\n' +
                '      "ruleNo": "100008400798",\n' +
                '      "shareRuleNo": "",\n' +
                '      "annualPremium": 411,\n' +
                '      "lasttrafficsubsidyflag": "",\n' +
                '      "feemanagematchflag": "S",\n' +
                '      "newCarFlag": "0",\n' +
                '      "lastPremiumRatio": 0.445,\n' +
                '      "lastchangeableFeeRate": 0.2606,\n' +
                '      "oriPoudageVar": "0.12",\n' +
                '      "oriPerformanceVar": "0.0187",\n' +
                '      "oriBusinessFeeVar": "0.12",\n' +
                '      "oriAddedserviceRateVar": "0.0019",\n' +
                '      "oriChangeableFeeRateVar": "0.2606",\n' +
                '      "oriPoudageInVar": "0.0000",\n' +
                '      "oriChangeableFeeRateInVar": "0.0000",\n' +
                '      "nonClaimDiscountRate": "0.5",\n' +
                '      "trafficTransgressRate": "1",\n' +
                '      "underwritingRate": "",\n' +
                '      "channelRate": "",\n' +
                '      "independentPriceRate": "0.890000",\n' +
                '      "headIndependentPriceRate": "",\n' +
                '      "standardPremium": 923.59,\n' +
                '      "premium": 411,\n' +
                '      "vehicleClaimType": "",\n' +
                '      "checkConsistency": "",\n' +
                '      "commercialAgreementVos": [\n' +
                '      ],\n' +
                '      "reconcileCode": "",\n' +
                '      "premiumChange": "0"\n' +
                '    },\n' +
                '    "quoteInsuranceVos": [\n' +
                '      {\n' +
                '        "amount": "2000000",\n' +
                '        "insuranceType": "1",\n' +
                '        "insuranceCode": "THIRDPARTYLIABILITYCOVERAGE",\n' +
                '        "nonDeductible": null,\n' +
                '        "standardPremium": 820.09,\n' +
                '        "premium": 364.94\n' +
                '      },\n' +
                '      {\n' +
                '        "amount": "10000",\n' +
                '        "insuranceType": "1",\n' +
                '        "insuranceCode": "INCARDRIVERLIABILITYCOVERAGE",\n' +
                '        "nonDeductible": null,\n' +
                '        "standardPremium": 28.99,\n' +
                '        "premium": 12.9\n' +
                '      },\n' +
                '      {\n' +
                '        "amount": "10000",\n' +
                '        "insuranceType": "1",\n' +
                '        "insuranceCode": "INCARPASSENGERLIABILITYCOVERAGE",\n' +
                '        "nonDeductible": null,\n' +
                '        "factorVos": [\n' +
                '          {\n' +
                '            "factorKey": "seat",\n' +
                '            "factorValue": "4"\n' +
                '          }\n' +
                '        ],\n' +
                '        "standardPremium": 74.51,\n' +
                '        "premium": 33.16\n' +
                '      }\n' +
                '    ],\n' +
                '    "isHolidaysDouble": 0,\n' +
                '    "ifAllroundClause": 0,\n' +
                '    "schemeId": "",\n' +
                '    "inType": "T",\n' +
                '    "ecarvo": {\n' +
                '      "actualValue": "65763.00",\n' +
                '      "caliberOfNewChannels": "B06",\n' +
                '      "carVIN": "LSJW74U67JZ024903",\n' +
                '      "certNo": "37010519810908412X",\n' +
                '      "certType": "1",\n' +
                '      "emptyWeight": "1265",\n' +
                '      "engineCapacity": "1.598",\n' +
                '      "engineNo": "TGGJ2280715",\n' +
                '      "factoryType": "荣威CSA7162UDAA轿车",\n' +
                '      "familyName": "荣威RX3",\n' +
                '      "fuelType": "0",\n' +
                '      "holderTelphone": "15972681095",\n' +
                '      "id": 365283210,\n' +
                '      "jyFuelType": "D1",\n' +
                '      "loan": "0",\n' +
                '      "modelType": "荣威CSA7162UDAA轿车",\n' +
                '      "moldCharacterCode": "RWAAJD0004",\n' +
                '      "negotiatedValue": "65763",\n' +
                '      "newCarFlag": 0,\n' +
                '      "newFamilyGrade": "1",\n' +
                '      "ocrCustomerFlag": "0",\n' +
                '      "ownerName": "时光",\n' +
                '      "ownerProp": "1",\n' +
                '      "plateColor": "1",\n' +
                '      "plateNo": "鲁AL98C0",\n' +
                '      "plateType": "02",\n' +
                '      "plateless": false,\n' +
                '      "pmVehicleUsage": "A",\n' +
                '      "power": "92",\n' +
                '      "prevMoldChrCode": "RWAAJD0004",\n' +
                '      "prevNegoValue": "65763",\n' +
                '      "producingArea": "0",\n' +
                '      "purchasePrice": "101800",\n' +
                '      "reductionType": "0",\n' +
                '      "registerDate": "2018-03-19",\n' +
                '      "registerDateIsPass": "0",\n' +
                '      "relationship": "1",\n' +
                '      "seatCount": "5",\n' +
                '      "secretCertNo": "2",\n' +
                '      "secretHolderTelphone": "1",\n' +
                '      "shortcutCode": "RW-CSA7162UDAA",\n' +
                '      "showDefaultTaxType": "0",\n' +
                '      "stRegisterDate": "2018-03-19",\n' +
                '      "taxCustomerType": "1",\n' +
                '      "taxMark": "0",\n' +
                '      "taxTypeWz": "0",\n' +
                '      "tonnage": "0",\n' +
                '      "tpyRiskflagCode": "003",\n' +
                '      "tpyRiskflagName": "C",\n' +
                '      "usage": "101",\n' +
                '      "vehiclePurpose": "01",\n' +
                '      "vehicleStyle": "K33",\n' +
                '      "vehicleType": "01",\n' +
                '      "vehiclefgwCode": "CSA7162UDAA",\n' +
                '      "inType": "T",\n' +
                '      "carModelRiskLevel": "",\n' +
                '      "lastyearPurchaseprice": "",\n' +
                '      "lastyearModeltype": "",\n' +
                '      "lastyearModelcode": "",\n' +
                '      "transferCompanyName": "",\n' +
                '      "oldCustomerFlag": "0",\n' +
                '      "isLastLocality": "",\n' +
                '      "g7Score": "",\n' +
                '      "g7CoustomerSign": "",\n' +
                '      "qualityType": "1",\n' +
                '      "qualityName": "家用车旧车蓝海",\n' +
                '      "busiInsurance": ""\n' +
                '    },\n' +
                '    "insuredVo": {\n' +
                '      "hqCustomerAccTimes": "",\n' +
                '      "hqCustomerGrouping": "",\n' +
                '      "hqCustomerValue": "",\n' +
                '      "cifCustLevel": ""\n' +
                '    },\n' +
                '    "isClickQuotation": 1,\n' +
                '    "isShowAccidentPlan": false,\n' +
                '    "isPurchaseNoncar": "0"\n' +
                '  }\n' +
                '}\n')
    //JSON.parseObject('{"meta":{},"redata":{"quotationNo":"QGUZTPFY2024F00287ML","inquireQID":"BGUZ26202404071050207782951101216231007","commercial":true,"compulsory":false,"commercialInsuransVo":{"insuranceQueryCode":"","aidingFundProportion":"","aidingFund":"","vehicleRiskLevel":"","telSalePerson":"","localPureRiskPremium":1173.08,"lastLocalPureriskPremium":"1173.08","caseNo":"","caseName":"","casePriority":"","productCode":"","netPremium":"2622.92","totalNetPremium":"2461.89","microTagging":"IFPOLICY_ACTIVE|IFSERVICE_ACTIVE|THC_APPLET_FLG|THC_USER_FLG|OTO_WECHAT_FLG|FGSGW_USER_FLG","blackGrade":"","firstPlateNo":"","ncdComeFromBusses":"4","insureYears":"0","claimTimes":"0","stStartDate":"2024-04-08 00:00","stEndDate":"2025-04-08 00:00","totalCpicScore":"","premiumRatio":"0.950000","poudage":"0.1","businessfee":"0.0","performance":"0.015","serviceValueAddRate":"0.0","poudageVar":"0.1000","businessFeeVar":"0.0000","performanceVar":"0.0000","addedserviceRateVar":"0.0000","isAdjustDiscuteRate":"","discuteAdjustProportion":"","ecompensationRate":"0.3958","totalEcompensationRate":"0.3958","profitChangeValue":"","profitPoolsRemainQuota":"","countingPoolsRemainQuota":"","cpicScore":"13","thirdPartySpreading":"","damageSpreading":"","thirdAndDamageSpreading":"","chdCarRecord":"","carSpreading":"","channelCityNum":"","twoGuestFlag":"","wyCarType":"-1","commContinuedInsuredYears":"0","netEcompensationRate":"0.4472","totalNetEcompensationRate":"","ncdEcompensationRate":"0.3760","specialCarFlag":"","ncdTotalEcompensationRate":"0.3760","blackDivisor":"","riskCore":"1","flexibleScore":"","stMaxPremRatio":"","stMinPremRatio":"","stMaxSuggestRatio":"","stMinSuggestRatio":"","isLinkage":"1","lastActualAddedservicerate":"","lastActualPoudage":"","lastActualBusinessFee":"","lastActualPerformance":"","servicePolicyCostRate":"","firstIndependentPriceRate":"","delayBusinessRate":"0","stSchemeId":"","changeableFeeRate":0.115,"originalPoudage":0.1,"originalBusinessFee":0,"originalPerFormance":0.015,"originalAddedservicerate":"0.0","originalChangeableFeeRate":0.115,"inferedChangeableFeeRate":0.115,"localDiscuteRate":0.95,"inferedDiscuteRate":0.95,"targetPolicyCostRate":0,"premiumCalculationScheme":"C","policyCostRate":0.5108098,"policyCostRateVar":"0.4958","policyCostRateExcMFR":"0.5108","totalPolicyCostRateVar":"0.4958","totalPolicyCostRateExcMFR":"0.5108","lastPremiumCalculationScheme":"C","lastTargetPolicyCostRate":"","originalSubjectRate":"|||||||||","originalSubjectAcount":"","originalDeferSubjectRate":"","originalDeferSubjectRcount":"","subjectRate":"|||||||||","subjectAcount":"","deferSubjectRate":"","deferSubjectAcount":"","lastPerformance":"0","lastBusinessFee":"0","lastPoudage":"0","lastLocalDiscuteRate":"0.950000","lastAdviceDiscuteMinRate":"0","lastPageChangeableFeeRate":"0","businesspoudage":"0.1","ruleNo":"100010378364","shareRuleNo":"","annualPremium":3141.58,"lasttrafficsubsidyflag":"","feemanagematchflag":"","newCarFlag":"1","lastPremiumRatio":0.95,"lastchangeableFeeRate":0.115,"oriPoudageVar":"0.1","oriPerformanceVar":"0.0","oriBusinessFeeVar":"0.0","oriAddedserviceRateVar":"0.0","oriChangeableFeeRateVar":"0.1000","oriPoudageInVar":"0.0000","oriChangeableFeeRateInVar":"0.0150","inferedChangeableFeeRateVar":"0.1000","nonClaimDiscountRate":"1","trafficTransgressRate":"1","underwritingRate":"","channelRate":"","independentPriceRate":"0.950000","headIndependentPriceRate":"","standardPremium":3306.92,"premium":3141.58,"vehicleClaimType":"","checkConsistency":"","commercialAgreementVos":[],"reconcileCode":"","premiumChange":"0"},"quoteInsuranceVos":[{"amount":"114050","insuranceType":"1","insuranceCode":"DAMAGELOSSCOVERAGE","nonDeductible":null,"standardPremium":2025.27,"premium":1924.01},{"amount":"3000000","insuranceType":"1","insuranceCode":"THIRDPARTYLIABILITYCOVERAGE","nonDeductible":null,"standardPremium":1074.67,"premium":1020.94},{"amount":"20000","insuranceType":"1","insuranceCode":"INCARDRIVERLIABILITYCOVERAGE","nonDeductible":null,"standardPremium":57.97,"premium":55.07},{"amount":"20000","insuranceType":"1","insuranceCode":"INCARPASSENGERLIABILITYCOVERAGE","nonDeductible":null,"factorVos":[{"factorKey":"seat","factorValue":"4"}],"standardPremium":149.01,"premium":141.56}],"isHolidaysDouble":0,"ifAllroundClause":0,"schemeId":"","inType":"N","ecarvo":{"actualValue":"114050","caliberOfNewChannels":"B06","carVIN":"L6T7622Z1PF019217","certNo":"******************","certType":"1","emptyWeight":"1465","engineCapacity":"1.499","engineNo":"P3G00013395","factoryType":"领克MR6432D03多用途乘用车","familyName":"领克06","fuelType":"0","holderTelphone":"18242353247","id":481373249,"isSaleBy4S":"1","jyFuelType":"D1","lastMoldType":"0","levelScene":"合作代理-C003-1","loan":"0","modelType":"领克MR6432D03多用途乘用车","moldCharacterCode":"LKKAED0016","negotiatedValue":"114050","newCarFlag":1,"newFamilyGrade":"1","ocrCustomerFlag":"0","oldCustomerFlag":"1","oldVehicleLSPlateAppeal":"0","ownerName":"余肖如","ownerProp":"1","plateColor":"1","plateNo":"粤S35K2J","plateType":"02","plateless":false,"power":"133","prevMoldChrCode":"LKKAED0021","prevNegoValue":"114050","producingArea":"0","purchasePrice":"119800","qualityName":"家用车旧车蓝海","qualityType":"1","reductionType":"0","registerDate":"2023-07-19","registerDateIsPass":"0","relationship":"1","saleArea":"441900","saleCompany":"北京车车科技有限公司","seatCount":"5","secretCertNo":"2","secretHolderTelphone":"2","shortcutCode":"LK-MR6432D03","showDefaultTaxType":"1","siteUserId":"PFXB072404070953469690758","stRegisterDate":"2023-07-19","taxCustomerType":"1","taxMark":"0","taxTypeWz":"0","tonnage":"0.0000","tpyRiskflagCode":"003","tpyRiskflagName":"C","usage":"101","vehicleClassName":"越野车类","vehicleInspection":"01","vehiclePurpose":"01","vehicleType":"01","vehiclefgwCode":"MR6432D03","inType":"N","carModelRiskLevel":"","lastyearPurchaseprice":"","lastyearModeltype":"","lastyearModelcode":"","transferCompanyName":"","busTransferCompanyName":"","isLastLocality":"","g7Score":"","g7CoustomerSign":""},"insuredVo":{"hqCustomerAccTimes":"","hqCustomerGrouping":"","hqCustomerValue":"","cifCustLevel":""},"isClickQuotation":1,"isShowAccidentPlan":false,"isPurchaseNoncar":"0"}}')
    return JSON.parseObject('{"meta":{},"redata":{"busiInsurance":"","commercial":true,"commercialInsuransVo":{"addedserviceRateVar":"0.0000","aidingFund":"","aidingFundProportion":"","annualPremium":2069.11,"blackDivisor":"","blackGrade":"","businessFeeVar":"0.0000","businessfee":"0.0","businesspoudage":"0.15","carSpreading":"","caseName":"","caseNo":"","casePriority":"","changeableFeeRate":0.152,"channelCityNum":"","channelRate":"","chdCarRecord":"","checkConsistency":"","claimTimes":"0","commContinuedInsuredYears":"3","commercialAgreementVos":[],"countingPoolsRemainQuota":"","cpicScore":"93","damageSpreading":"","deferSubjectAcount":"","deferSubjectRate":"","delayBusinessRate":"0","discuteAdjustProportion":"","ecompensationRate":"0.5309","feemanagematchflag":"S","firstIndependentPriceRate":"","firstPlateNo":"","flexibleScore":"","headIndependentPriceRate":"","independentPriceRate":"1.200000","inferedChangeableFeeRate":0.152,"inferedChangeableFeeRateVar":"0.1520","inferedDiscuteRate":0.72,"insuranceQueryCode":"","insureYears":"3","isAdjustDiscuteRate":"","isLinkage":"1","lastActualAddedservicerate":"","lastActualBusinessFee":"","lastActualPerformance":"","lastActualPoudage":"","lastAdviceDiscuteMinRate":"0","lastBusinessFee":"0.0","lastLocalDiscuteRate":"0.72","lastLocalPureriskPremium":"1036.39","lastPageChangeableFeeRate":"0","lastPerformance":"0.002","lastPoudage":"0.15","lastPremiumCalculationScheme":"A2","lastPremiumRatio":0.72,"lastTargetPolicyCostRate":"","lastchangeableFeeRate":0.152,"lasttrafficsubsidyflag":"","localDiscuteRate":0.72,"localPureRiskPremium":1036.39,"microTagging":"IFPOLICY_ACTIVE|IFSERVICE_ACTIVE|THC_APPLET_FLG|THC_USER_FLG|OTO_WECHAT_FLG|FGSGW_USER_FLG","ncdComeFromBusses":"","ncdEcompensationRate":"0.6372","ncdTotalEcompensationRate":"0.7364","netEcompensationRate":"0.6261","netPremium":"1655.29","newCarFlag":"0","nonClaimDiscountRate":"0.6","oriAddedserviceRateVar":"0.0","oriBusinessFeeVar":"0.0","oriChangeableFeeRateInVar":"0.0000","oriChangeableFeeRateVar":"0.1520","oriPerformanceVar":"0.002","oriPoudageInVar":"0.0000","oriPoudageVar":"0.15","originalAddedservicerate":"0.0","originalBusinessFee":0,"originalChangeableFeeRate":0.152,"originalDeferSubjectRate":"","originalDeferSubjectRcount":"","originalPerFormance":0.002,"originalPoudage":0.15,"originalSubjectAcount":"","originalSubjectRate":"|||||||||","performance":"0.002","performanceVar":"0.0020","policyCostRate":0.74294139,"policyCostRateExcMFR":"0.6829","policyCostRateVar":"0.6829","poudage":"0.15","poudageVar":"0.1500","premium":2069.11,"premiumCalculationScheme":"A2","premiumChange":"0","premiumRatio":"0.720000","productCode":"","profitChangeValue":"","profitPoolsRemainQuota":"","quotationMode":"2","reconcileCode":"","riskCore":"2","ruleNo":"100010862194","servicePolicyCostRate":"","serviceValueAddRate":"0.0","shareRuleNo":"","specialCarFlag":"","stEndDate":"2025-04-13 00:00","stMaxPremRatio":"","stMaxSuggestRatio":"","stMinPremRatio":"","stMinSuggestRatio":"","stSchemeId":"","stStartDate":"2024-04-13 00:00","standardPremium":2873.77,"subjectAcount":"","subjectRate":"|||||||||","targetPolicyCostRate":0.94,"telSalePerson":"","thirdAndDamageSpreading":"","thirdPartySpreading":"","totalCpicScore":"93","totalEcompensationRate":"0.6496","totalNetEcompensationRate":"0.7279","totalNetPremium":"2461.89","totalPolicyCostRateExcMFR":"0.7572","totalPolicyCostRateVar":"0.7572","trafficTransgressRate":"1","twoGuestFlag":"","underwritingRate":"","vehicleClaimType":"","vehicleRiskLevel":"R","wyCarType":"-1"},"compulsory":true,"compulsoryInsuransVo":{"aidingFund":"","aidingFundProportion":"","annualPremium":855,"blackDivisor":"","blackGrade":"","businessfee":0,"changeablefeeRate":0,"channelCityNum":"","cipremium":855,"claimTimes":"0","compContinuedInsuredYears":"3","compCpicScore":81,"compulsoryAgreementVos":[],"ecompensationRate":"0.9367","firstIndependentPriceRate":"","firstPlateNo":"鄂AW635M","insuranceQueryCode":"","jqNewCarFlag":"0","lastLocalPureriskPremium":"0.00","localPureRiskPremium":755.52,"microTagging":"","ncdComeFromTraffic":"2","ncdEcompensationRate":"0.9367","netEcompensationRate":"0.9367","netPremium":"806.60","performance":0,"policycostRate":1.00166922,"reconcileCode":"","registryNumber":"9142010268234976X9","riskCore":"7","secretTaxpayerNo":2,"secretTaxpayerRecno":2,"specialAgreement":"","specialCarFlag":"","stBackAmount":"0.00","stBackEndDate":"2023-12-31","stBackStartDate":"2023-12-31","stEndDate":"2025-04-13 00:00","stLateFee":"0.00","stPayableAmount":"420.00","stStartDate":"2024-04-13 00:00","stTaxEndDate":"2024-12-31","stTaxStartDate":"2024-01-01","standardPremium":"950","sumRefundTax":"","taxAmount":420,"taxBureau":"","taxBureauName":"湖北省地方税务局","taxType":"T","taxpayerName":"杨爽","taxpayerNo":"420101199208297036","taxpayerRecno":"420101199208297036","taxpayerType":"1","telSalePerson":"","totalNetPremium":"2461.89","trafficSpreading":"","trafficpoudage":0,"twoGuestFlag":"","vehicleClaimType":""},"ecarvo":{"actualValue":127901,"busTransferCompanyName":"","busiInsurance":"","caliberOfNewChannels":"B06","carModelRiskLevel":"","carVIN":"LS4ASE2A0MA704640","certNo":"420101199208297036","certType":"1","emptyWeight":"1815.0000","engineCapacity":"1.998","engineNo":"M9EJ004274","factoryType":"长安SC6493ABA6多用途乘用车","familyName":"长安UNI-K","fuelType":"0","g7CoustomerSign":"","g7Score":"","holderTelphone":"","id":*********,"isSaleBy4S":"","inType":"R","isLastLocality":"","jyFuelType":"D1","lastMoldType":"0","lastyearModelcode":"","lastyearModeltype":"","lastyearPurchaseprice":"","levelScene":"合作代理-C003-1","loan":"0","modelType":"长安SC6493ABA6多用途乘用车","moldCharacterCode":"CAAFJD0002","negotiatedValue":127901,"newCarFlag":0,"newFamilyGrade":"1","ocrCustomerFlag":"0","oldCustomerFlag":"0","oldVehicleLSPlateAppeal":"0","ownerName":"杨爽","ownerProp":"1","plateColor":"1","plateNo":"鄂AW635M","plateType":"02","plateless":false,"power":"171","prevMoldChrCode":"CAAFJD0002","prevNegoValue":127901,"producingArea":"0","purchasePrice":"161900","qualityName":"家用车旧车蓝海","qualityType":"1","reductionType":"0","registerDate":"2021-04-14","registerDateIsPass":"0","relationship":"1","saleArea":"","saleCompany":"","renewScore":"5","seatCount":"5","secretCertNo":"2","secretHolderTelphone":"2","shortcutCode":"CA-SC6493ABA6","showDefaultTaxType":"0","siteUserId":"","stRegisterDate":"2021-04-14","taxCustomerType":"1","taxMark":"0","taxTypeWz":"0","tonnage":"0.0000","tpyRiskflagCode":"003","tpyRiskflagName":"C","transferCompanyName":"","usage":"101","vehicleClassName":"越野车类","vehicleInspection":"01","vehiclePurpose":"01","vehicleType":"01","vehiclefgwCode":"SC6493ABA6"},"ifAllroundClause":0,"inType":"R","inquireQID":"BWUH26202404011512001237911901216231009","insuredVo":{"cifCustLevel":"","hqCustomerAccTimes":"","hqCustomerGrouping":"","hqCustomerValue":""},"isClickQuotation":1,"isHolidaysDouble":0,"isPurchaseNoncar":0,"isShowAccidentPlan":false,"quotationNo":"QWUHC03Y2024F0019MVT","quoteInsuranceVos":[{"amount":"127901","insuranceCode":"DAMAGELOSSCOVERAGE","insuranceType":"1","nonDeductible":null,"premium":1217.32,"standardPremium":1690.72},{"amount":"1000000","insuranceCode":"THIRDPARTYLIABILITYCOVERAGE","insuranceType":"1","nonDeductible":null,"premium":628.25,"standardPremium":872.57},{"amount":"30000","insuranceCode":"INCARDRIVERLIABILITYCOVERAGE","insuranceType":"1","nonDeductible":null,"premium":62.61,"standardPremium":86.96},{"amount":"30000","factorVos":[{"factorKey":"seat","factorValue":"4"}],"insuranceCode":"INCARPASSENGERLIABILITYCOVERAGE","insuranceType":"1","nonDeductible":null,"premium":160.93,"standardPremium":223.52}],"schemeId":""}}');
}

//获取折旧率
double getDeprecateRate(String useNatureCode, String vehiclePurpose, AutoTask autoTask) {
    double deprecateRate = 0;
    if ("101".equals(useNatureCode)) {
        if ("01".equals(vehiclePurpose))
            deprecateRate = 0.006;
        if ("02".equals(vehiclePurpose))
            deprecateRate = 0.009;

    } else if ("201".equals(useNatureCode) || "202".equals(useNatureCode) || "301".equals(useNatureCode)) {
        if ("01".equals(vehiclePurpose))
            deprecateRate = 0.006;
        if ("02".equals(vehiclePurpose))
            deprecateRate = 0.009;
        if ("03".equals(vehiclePurpose))
            deprecateRate = 0.009;
        if ("04".equals(vehiclePurpose))
            deprecateRate = 0.009;
        if ("05".equals(vehiclePurpose))
            deprecateRate = 0.011;
        if ("07".equals(vehiclePurpose))
            deprecateRate = 0.009;

    } else if ("701".equals(useNatureCode) || "702".equals(useNatureCode)) {
        if (autoTask.tempValues.usageSubdivs || !"07".equals(vehiclePurpose)) {//特种车
            deprecateRate = 0.009;
        } else {
            deprecateRate = 0.011;
        }
    } else if ("401".equals(useNatureCode) || "402".equals(useNatureCode)) {
        if ("01".equals(vehiclePurpose))
            deprecateRate = 0.011;
        if ("02".equals(vehiclePurpose))
            deprecateRate = 0.011;

    } else if ("501".equals(useNatureCode) || "502".equals(useNatureCode)) {
        if ("01".equals(vehiclePurpose))
            deprecateRate = 0.009;
        if ("02".equals(vehiclePurpose))
            deprecateRate = 0.009;

    } else if ("601".equals(useNatureCode)) {
        if ("03".equals(vehiclePurpose))
            deprecateRate = 0.011;
        if ("04".equals(vehiclePurpose))
            deprecateRate = 0.011;
        if ("05".equals(vehiclePurpose))
            deprecateRate = 0.014;
        if ("07".equals(vehiclePurpose))
            deprecateRate = 0.009;

    }
    return deprecateRate;
}

//计算折旧价
String getActualAmount(String beginDate, String endDate, double perMonthRate, double baseAmount, AutoTask autoTask) throws Exception {
    double actualAmount = 0;
    int monthCount = 0;
    double dblSumDepreciationRate = 0;

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    Date begin = sdf.parse(beginDate);
    Date end = sdf.parse(endDate);
//  现使用太保错误逻辑 日数不影响折旧月数
    monthCount = getMonths(begin, end);
    dblSumDepreciationRate = monthCount * perMonthRate;
    //总折旧率不能超过80%
    if (dblSumDepreciationRate > 0.8) {
        dblSumDepreciationRate = 0.8;
    }
    actualAmount = baseAmount * (1 - dblSumDepreciationRate);
    //太保要整数
    String actualAmountStr = BigDecimalUtil.add(String.valueOf(actualAmount), "0", 0);
    autoTask.tempValues.monthCount = monthCount;
    autoTask.tempValues.actualAmountStr = actualAmountStr;
    return actualAmountStr;
}

def reMap(AutoTask autoTask, String reMapKey, String name) {
    return RobotDict_2011.getValue(reMapKey, name)
}

//各地区差异参数及逻辑判断
boolean isNumberTaxType(AutoTask autoTask) {
    String numberTaxTypeArea = ",温州,北京,云南,";//纳税类型代码为数字的地区 河北,
    boolean isNumberTaxType = numberTaxTypeArea.contains("," + autoTask.configs.get("areaComCode") + ",") || "true".equals(autoTask.configs.get("isNumberTaxType"));
    return isNumberTaxType
}

JSONObject getCalculateParam(String areaComCode) {
    if ("宁波".equals(areaComCode)) {
        return JSON.parseObject("{\"meta\":{},\"redata\":{\"quotationNo\":\"QNIB250Y1419F040569H\",\"commercial\":true,\"compulsory\":true,\"compulsoryInsuransVo\":{\"insuranceQueryCode\":\"\",\"stBackStartDate\":\"\",\"stBackEndDate\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"telSalePerson\":\"\",\"stStartDate\":\"2019-08-02 00:00\",\"stEndDate\":\"2020-08-02 00:00\",\"ecompensationRate\":\"\",\"compCpicScore\":\"\",\"trafficSpreading\":\"\",\"taxType\":\"T\",\"taxBureau\":\"\",\"stTaxStartDate\":\"2019-01-01\",\"stTaxEndDate\":\"2019-12-31\",\"taxBureauName\":\"\",\"registryNumber\":\"\",\"taxpayerName\":\"朱蒙子\",\"taxpayerType\":\"1\",\"taxpayerNo\":\"330227198901180795\",\"taxpayerRecno\":\"330227198901180795\",\"cipremium\":null,\"taxAmount\":null,\"vehicleClaimType\":\"\",\"annualPremium\":\"\",\"changeablefeeRate\":\"\",\"localPureRiskPremium\":\"\",\"trafficpoudage\":\"\",\"businessfee\":\"\",\"performance\":\"\",\"policycostRate\":\"\",\"jqNewCarFlag\":\"\"},\"commercialInsuransVo\":{\"insuranceQueryCode\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"vehicleRiskLevel\":\"\",\"telSalePerson\":\"\",\"stStartDate\":\"2019-08-02 00:00\",\"stEndDate\":\"2020-08-02 00:00\",\"totalCpicScore\":\"\",\"premiumRatio\":null,\"thirdPartySpreading\":\"\",\"damageSpreading\":\"\",\"thirdAndDamageSpreading\":\"\",\"chdCarRecord\":\"\",\"ecompensationRate\":\"\",\"totalEcompensationRate\":\"\",\"espcompensationRate\":\"\",\"cpicScore\":\"\",\"stMaxPremRatio\":\"\",\"stMinPremRatio\":\"\",\"stMaxSuggestRatio\":\"\",\"stMinSuggestRatio\":\"\",\"stSchemeId\":\"\",\"nonClaimDiscountRate\":\"\",\"trafficTransgressRate\":\"\",\"underwritingRate\":\"\",\"channelRate\":\"\",\"standardPremium\":null,\"premium\":null,\"poudage\":null,\"vehicleClaimType\":\"\",\"checkConsistency\":\"\"},\"quoteInsuranceVos\":[{\"amount\":\"218830\",\"insuranceType\":\"1\",\"insuranceCode\":\"DAMAGELOSSCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"DAMAGELOSSEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"10000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARDRIVERLIABILITYCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"INCARDRIVERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"10000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARPASSENGERLIABILITYCOVERAGE\",\"nonDeductible\":null,\"factorVos\":[{\"factorKey\":\"seat\",\"factorValue\":\"4\"}],\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"INCARPASSENGERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null}],\"accident\":false,\"isHolidaysDouble\":0,\"ifAllroundClause\":0,\"schemeId\":\"\",\"productFlag\":\"0\",\"isRenew\":0}}")
    }
    if ("上海".contains(areaComCode))
        return JSON.parseObject("{\"meta\":{},\"redata\":{\"quotationNo\":\"\",\"commercial\":true,\"compulsory\":true,\"compulsoryInsuransVo\":{\"insuranceQueryCode\":\"\",\"stBackStartDate\":\"2016-12-31\",\"stBackEndDate\":\"2016-12-31\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"stStartDate\":\"2017-11-07 00:00\",\"stEndDate\":\"2018-11-07 00:00\",\"ecompensationRate\":\"\",\"taxType\":\"N\",\"exceedDaysCount\":\"\",\"reductionAmount\":\"\",\"taxPaidNo\":\"\",\"stTaxStartDate\":\"2017-01-01\",\"stTaxEndDate\":\"2017-12-31\",\"payableAmount\":\"\",\"backAmount\":\"\",\"stLateFeeStartDate\":\"2017-11-06\",\"stLateFeeEndDate\":\"2017-11-06\",\"lateFee\":\"\",\"taxpayerName\":\"谭敏仪\",\"taxpayerType\":\"1\",\"taxpayerNo\":\"440107199009090325\",\"totalWeight\":\"\",\"taxBureauName\":\"\",\"taxVehicleType\":\"K02\",\"taxpayerSubstRecno\":\"\",\"cipremium\":null,\"taxAmount\":null,\"vehicleClaimType\":\"\"},\"commercialInsuransVo\":{\"insuranceQueryCode\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"vehicleRiskLevel\":\"\",\"stStartDate\":\"2017-11-28 00:00\",\"stEndDate\":\"2018-11-28 00:00\",\"premiumRatio\":null,\"ecompensationRate\":\"\",\"totalEcompensationRate\":\"\",\"stMaxPremRatio\":\"\",\"stMinPremRatio\":\"\",\"stMaxSuggestRatio\":\"\",\"stMinSuggestRatio\":\"\",\"stSchemeId\":\"\",\"nonClaimDiscountRate\":\"\",\"trafficTransgressRate\":\"\",\"underwritingRate\":\"\",\"channelRate\":\"\",\"standardPremium\":null,\"premium\":null,\"poudage\":null,\"vehicleClaimType\":\"\"},\"quoteInsuranceVos\":[{\"amount\":\"1000000\",\"insuranceType\":\"1\",\"insuranceCode\":\"THIRDPARTYLIABILITYCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"THIRDPARTYLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null}],\"platformVo\":{\"benchmarkRiskPremium\":\"1060.975565\",\"brand\":\"荣威CSA7150MCT轿车\",\"brandCode\":\"RWA\",\"carName\":\"荣威CSA7150MCT 讯驰版\",\"configType\":\"UB\",\"id\":1485717,\"modelCode\":\"BRWASXUB0010\",\"pureRiskPremium\":\"1060.975565\",\"series\":\"荣威350\"}}}");
    if ("湖南".equals(areaComCode))
        return JSON.parseObject("{\"meta\":{},\"redata\":{\"quotationNo\":\"\",\"commercial\":true,\"compulsory\":true,\"compulsoryInsuransVo\":{\"insuranceQueryCode\":\"\",\"stBackStartDate\":\"2015-12-31\",\"stBackEndDate\":\"2015-12-31\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"stStartDate\":\"2017-07-19 00:00\",\"stEndDate\":\"2018-07-19 00:00\",\"taxType\":\"T\",\"taxBureau\":\"\",\"stTaxStartDate\":\"2017-01-01\",\"stTaxEndDate\":\"2017-12-31\",\"taxBureauName\":\"\",\"taxpayerName\":\"郑州福蒙特置业有限公司\",\"taxpayerRecno\":\"66090583-3\",\"cipremium\":1342,\"taxAmount\":480,\"stPayableAmount\":\"480.00\",\"stLateFee\":\"0\",\"stBackAmount\":\"0.00\"},\"commercialInsuransVo\":{\"insuranceQueryCode\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"vehicleRiskLevel\":\"\",\"stStartDate\":\"2017-07-19 00:00\",\"stEndDate\":\"2018-07-19 00:00\",\"premiumRatio\":null,\"stMaxPremRatio\":\"\",\"stMinPremRatio\":\"\",\"stMaxSuggestRatio\":\"\",\"stMinSuggestRatio\":\"\",\"stSchemeId\":\"\",\"nonClaimDiscountRate\":\"1\",\"trafficTransgressRate\":\"1\",\"underwritingRate\":\"0.85\",\"channelRate\":\"0.85\",\"standardPremium\":4354.42,\"premium\":3146.07,\"poudage\":null},\"quoteInsuranceVos\":[{\"amount\":\"78792.00\",\"insuranceType\":\"1\",\"insuranceCode\":\"DAMAGELOSSCOVERAGE\",\"nonDeductible\":\"218.75\",\"standardPremium\":null,\"premium\":1458.33},{\"insuranceCode\":\"DAMAGELOSSEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":\"218.75\",\"standardPremium\":null},{\"amount\":\"200000\",\"insuranceType\":\"1\",\"insuranceCode\":\"THIRDPARTYLIABILITYCOVERAGE\",\"nonDeductible\":\"147.50\",\"standardPremium\":null,\"premium\":983.32},{\"insuranceCode\":\"THIRDPARTYLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":\"147.50\",\"standardPremium\":null},{\"amount\":\"10000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARDRIVERLIABILITYCOVERAGE\",\"nonDeductible\":\"4.23\",\"standardPremium\":null,\"premium\":28.18},{\"insuranceCode\":\"INCARDRIVERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":\"4.23\",\"standardPremium\":null},{\"amount\":\"10000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARPASSENGERLIABILITYCOVERAGE\",\"nonDeductible\":\"39.88\",\"factorVos\":[{\"factorKey\":\"seat\",\"factorValue\":\"16\"}],\"standardPremium\":null,\"premium\":265.88},{\"insuranceCode\":\"INCARPASSENGERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":\"39.88\",\"standardPremium\":null}],\"holderVo\":{\"address\":\"河南省郑州市郑东新区商都路\",\"certificateCode\":\"66090583-3\",\"certificateType\":\"6\",\"name\":\"郑州福蒙特置业有限公司\",\"relationship\":\"9\",\"telephone\":\"13603456936\"},\"insuredVo\":{\"address\":\"河南省郑州市郑东新区商都路\",\"certificateCode\":\"66090583-3\",\"certificateType\":\"6\",\"name\":\"郑州福蒙特置业有限公司\",\"otherInfo\":\"郑州福蒙特置业有限公司\",\"relationship\":\"9\",\"telephone\":\"13603456936\"}}}")
    if ("北京".equals(areaComCode))
        return JSON.parseObject("{\"meta\":{},\"redata\":{\"quotationNo\":\"\",\"commercial\":true,\"compulsory\":true,\"compulsoryInsuransVo\":{\"insuranceQueryCode\":\"\",\"stBackStartDate\":\"2016-12-31\",\"stBackEndDate\":\"2016-12-31\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"stStartDate\":\"2017-12-20 00:00\",\"stEndDate\":\"2018-12-20 00:00\",\"ecompensationRate\":\"\",\"taxType\":\"3\",\"exceedDaysCount\":\"\",\"reductionAmount\":\"\",\"taxPaidNo\":\"\",\"stTaxStartDate\":\"2017-01-18\",\"stTaxEndDate\":\"2017-12-31\",\"payableAmount\":\"\",\"backAmount\":\"\",\"stLateFeeStartDate\":\"2017-12-19\",\"stLateFeeEndDate\":\"2017-12-19\",\"lateFee\":\"\",\"taxpayerName\":\"周宗良\",\"taxpayerType\":\"1\",\"taxpayerNo\":\"110228198004192919\",\"totalWeight\":\"\",\"taxBureauName\":\"\",\"taxVehicleType\":\"K11\",\"stVehicleLicensingDate\":\"2017-01-18\",\"taxNonlocalBureau\":\"\",\"cipremium\":null,\"taxAmount\":null,\"vehicleClaimType\":\"\"},\"commercialInsuransVo\":{\"insuranceQueryCode\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"vehicleRiskLevel\":\"\",\"stStartDate\":\"2017-12-20 00:00\",\"stEndDate\":\"2018-12-20 00:00\",\"premiumRatio\":null,\"ecompensationRate\":\"\",\"totalEcompensationRate\":\"\",\"stMaxPremRatio\":\"\",\"stMinPremRatio\":\"\",\"stMaxSuggestRatio\":\"\",\"stMinSuggestRatio\":\"\",\"stSchemeId\":\"\",\"nonClaimDiscountRate\":\"\",\"trafficTransgressRate\":\"\",\"underwritingRate\":\"\",\"channelRate\":\"\",\"standardPremium\":null,\"premium\":null,\"poudage\":null,\"vehicleClaimType\":\"\"},\"quoteInsuranceVos\":[{\"amount\":\"90411\",\"insuranceType\":\"1\",\"insuranceCode\":\"DAMAGELOSSCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"DAMAGELOSSEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"500000\",\"insuranceType\":\"1\",\"insuranceCode\":\"THIRDPARTYLIABILITYCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"THIRDPARTYLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"10000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARDRIVERLIABILITYCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"INCARDRIVERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"10000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARPASSENGERLIABILITYCOVERAGE\",\"nonDeductible\":null,\"factorVos\":[{\"factorKey\":\"seat\",\"factorValue\":\"4\"}],\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"INCARPASSENGERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"90411\",\"insuranceType\":\"1\",\"insuranceCode\":\"THEFTCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"THEFTCOVERAGEEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null}],\"accident\":false,\"platformVo\":{\"benchmarkRiskPremium\":\"1367.020720\",\"brand\":\"福特CAF7154A5轿车\",\"brandCode\":\"CAA\",\"carName\":\"福特CAF7154A5 时尚型\",\"configType\":\"UC\",\"id\":2451630,\"modelCode\":\"BCAAFAUC0002\",\"pureRiskPremium\":\"1367.020720\",\"series\":\"福睿斯\"}}}")
    if ("云南".equals(areaComCode))
        return JSON.parseObject("{\"meta\":{},\"redata\":{\"quotationNo\":\"\",\"commercial\":true,\"compulsory\":true,\"compulsoryInsuransVo\":{\"insuranceQueryCode\":\"\",\"stBackStartDate\":\"\",\"stBackEndDate\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"stStartDate\":\"2018-08-29 00:00\",\"stEndDate\":\"2019-08-29 00:00\",\"ecompensationRate\":\"\",\"compCpicScore\":\"\",\"taxType\":\"3\",\"localCalTax\":\"1\",\"taxBureau\":\"\",\"stTaxStartDate\":\"2018-01-01\",\"stTaxEndDate\":\"2018-12-31\",\"taxBureauName\":\"\",\"taxpayerSex\":\"\",\"taxpayerName\":\"马立辉\",\"taxpayerType\":\"1\",\"taxpayerNo\":\"530125198312150415\",\"cipremium\":null,\"taxAmount\":null,\"vehicleClaimType\":\"\",\"annualPremium\":\"\",\"changeablefeeRate\":\"\",\"localPureRiskPremium\":\"\",\"trafficpoudage\":\"\",\"businessfee\":\"\",\"performance\":\"\",\"policycostRate\":\"\",\"jqNewCarFlag\":\"\"},\"commercialInsuransVo\":{\"insuranceQueryCode\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"vehicleRiskLevel\":\"\",\"stStartDate\":\"2018-08-29 00:00\",\"stEndDate\":\"2019-08-29 00:00\",\"totalCpicScore\":\"\",\"premiumRatio\":null,\"ecompensationRate\":\"\",\"totalEcompensationRate\":\"\",\"cpicScore\":\"\",\"stMaxPremRatio\":\"\",\"stMinPremRatio\":\"\",\"stMaxSuggestRatio\":\"\",\"stMinSuggestRatio\":\"\",\"stSchemeId\":\"\",\"nonClaimDiscountRate\":\"\",\"trafficTransgressRate\":\"\",\"underwritingRate\":\"\",\"channelRate\":\"\",\"standardPremium\":null,\"premium\":null,\"poudage\":null,\"vehicleClaimType\":\"\"},\"quoteInsuranceVos\":[{\"amount\":\"88888\",\"insuranceType\":\"1\",\"insuranceCode\":\"DAMAGELOSSCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"DAMAGELOSSEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"1000000\",\"insuranceType\":\"1\",\"insuranceCode\":\"THIRDPARTYLIABILITYCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"THIRDPARTYLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"10000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARDRIVERLIABILITYCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"INCARDRIVERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"10000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARPASSENGERLIABILITYCOVERAGE\",\"nonDeductible\":null,\"factorVos\":[{\"factorKey\":\"seat\",\"factorValue\":\"4\"}],\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"INCARPASSENGERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null}],\"accident\":false,\"isHolidaysDouble\":0,\"ifAllroundClause\":0,\"schemeId\":\"\",\"productFlag\":\"0\"}}")
    if ("陕西".equals(areaComCode))
        return JSON.parseObject("{\"meta\":{},\"redata\":{\"quotationNo\":\"\",\"commercial\":true,\"compulsory\":true,\"compulsoryInsuransVo\":{\"insuranceQueryCode\":\"\",\"stBackStartDate\":\"\",\"stBackEndDate\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"stStartDate\":\"2018-10-12 00:00\",\"stEndDate\":\"2019-10-12 00:00\",\"ecompensationRate\":\"\",\"compCpicScore\":\"\",\"taxType\":\"T\",\"taxBureau\":\"\",\"stTaxStartDate\":\"2018-01-01\",\"stTaxEndDate\":\"2018-12-31\",\"taxBureauName\":\"\",\"registryNumber\":\"\",\"taxpayerName\":\"史婵媛\",\"taxpayerType\":\"1\",\"taxpayerNo\":\"610502198312220062\",\"taxpayerRecno\":\"610502198312220062\",\"cipremium\":null,\"taxAmount\":null,\"vehicleClaimType\":\"\",\"annualPremium\":\"\",\"changeablefeeRate\":\"\",\"localPureRiskPremium\":\"\",\"trafficpoudage\":\"\",\"businessfee\":\"\",\"performance\":\"\",\"policycostRate\":\"\",\"jqNewCarFlag\":\"\"},\"commercialInsuransVo\":{\"insuranceQueryCode\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"vehicleRiskLevel\":\"\",\"stStartDate\":\"2018-10-12 00:00\",\"stEndDate\":\"2019-10-12 00:00\",\"totalCpicScore\":\"\",\"premiumRatio\":null,\"ecompensationRate\":\"\",\"totalEcompensationRate\":\"\",\"cpicScore\":\"\",\"stMaxPremRatio\":\"\",\"stMinPremRatio\":\"\",\"stMaxSuggestRatio\":\"\",\"stMinSuggestRatio\":\"\",\"stSchemeId\":\"\",\"nonClaimDiscountRate\":\"\",\"trafficTransgressRate\":\"\",\"underwritingRate\":\"\",\"channelRate\":\"\",\"standardPremium\":null,\"premium\":null,\"poudage\":null,\"vehicleClaimType\":\"\"},\"quoteInsuranceVos\":[{\"amount\":\"133053\",\"insuranceType\":\"1\",\"insuranceCode\":\"DAMAGELOSSCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"DAMAGELOSSEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"1000000\",\"insuranceType\":\"1\",\"insuranceCode\":\"THIRDPARTYLIABILITYCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"THIRDPARTYLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"10000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARDRIVERLIABILITYCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"INCARDRIVERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"10000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARPASSENGERLIABILITYCOVERAGE\",\"nonDeductible\":null,\"factorVos\":[{\"factorKey\":\"seat\",\"factorValue\":\"4\"}],\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"INCARPASSENGERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"133053\",\"insuranceType\":\"1\",\"insuranceCode\":\"THEFTCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"THEFTCOVERAGEEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":0,\"insuranceType\":\"1\",\"insuranceCode\":\"GLASSBROKENCOVERAGE\",\"nonDeductible\":null,\"factorVos\":[{\"factorKey\":\"producingArea\",\"factorValue\":\"0\"}],\"standardPremium\":null,\"premium\":null}],\"accident\":false,\"isHolidaysDouble\":0,\"ifAllroundClause\":0,\"schemeId\":\"\",\"productFlag\":\"0\"}}")
    if ("深圳".equals(areaComCode))
        return JSON.parseObject("{\"meta\":{},\"redata\":{\"quotationNo\":\"\",\"commercial\":true,\"compulsory\":true,\"compulsoryInsuransVo\":{\"insuranceQueryCode\":\"\",\"stBackStartDate\":\"\",\"stBackEndDate\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"stStartDate\":\"2019-01-03 00:00\",\"stEndDate\":\"2020-01-03 00:00\",\"ecompensationRate\":\"\",\"compCpicScore\":\"\",\"trafficSpreading\":\"\",\"taxType\":\"T\",\"taxBureau\":\"\",\"stTaxStartDate\":\"2019-01-01\",\"stTaxEndDate\":\"2019-12-31\",\"taxBureauName\":\"\",\"registryNumber\":\"\",\"taxpayerName\":\"深圳市坪山新区名雅幼儿园\",\"taxpayerType\":\"11\",\"taxpayerNo\":\"5244030035918546XY\",\"taxpayerRecno\":\"5244030035918546XY\",\"cipremium\":null,\"taxAmount\":null,\"vehicleClaimType\":\"\",\"annualPremium\":\"\",\"changeablefeeRate\":\"\",\"localPureRiskPremium\":\"\",\"trafficpoudage\":\"\",\"businessfee\":\"\",\"performance\":\"\",\"policycostRate\":\"\",\"jqNewCarFlag\":\"\"},\"commercialInsuransVo\":{\"insuranceQueryCode\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"vehicleRiskLevel\":\"\",\"stStartDate\":\"2019-01-03 00:00\",\"stEndDate\":\"2020-01-03 00:00\",\"totalCpicScore\":\"\",\"premiumRatio\":null,\"ecompensationRate\":\"\",\"totalEcompensationRate\":\"\",\"cpicScore\":\"\",\"thirdPartySpreading\":\"\",\"damageSpreading\":\"\",\"thirdAndDamageSpreading\":\"\",\"stMaxPremRatio\":\"\",\"stMinPremRatio\":\"\",\"stMaxSuggestRatio\":\"\",\"stMinSuggestRatio\":\"\",\"stSchemeId\":\"\",\"nonClaimDiscountRate\":\"\",\"trafficTransgressRate\":\"\",\"underwritingRate\":\"\",\"channelRate\":\"\",\"standardPremium\":null,\"premium\":null,\"poudage\":null,\"vehicleClaimType\":\"\",\"checkConsistency\":\"\"},\"quoteInsuranceVos\":[{\"amount\":\"67146\",\"insuranceType\":\"1\",\"insuranceCode\":\"DAMAGELOSSCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"DAMAGELOSSEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"1000000\",\"insuranceType\":\"1\",\"insuranceCode\":\"THIRDPARTYLIABILITYCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"THIRDPARTYLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"100000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARDRIVERLIABILITYCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"INCARDRIVERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":\"50000\",\"insuranceType\":\"1\",\"insuranceCode\":\"INCARPASSENGERLIABILITYCOVERAGE\",\"nonDeductible\":null,\"factorVos\":[{\"factorKey\":\"seat\",\"factorValue\":\"18\"}],\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"INCARPASSENGERLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"amount\":0,\"insuranceType\":\"1\",\"insuranceCode\":\"GLASSBROKENCOVERAGE\",\"nonDeductible\":null,\"factorVos\":[{\"factorKey\":\"producingArea\",\"factorValue\":\"0\"}],\"standardPremium\":null,\"premium\":null},{\"insuranceType\":\"1\",\"insuranceCode\":\"PADDLEDAMAGECOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"PADDLEDAMAGEEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null},{\"insuranceType\":\"1\",\"insuranceCode\":\"DAMAGELOSSCANNOTFINDTHIRDSPECIALCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null}],\"isHolidaysDouble\":0,\"ifAllroundClause\":0,\"schemeId\":\"\",\"productFlag\":\"0\"}}")
    if ("天津".equals(areaComCode)) {
        return JSON.parseObject("{\"meta\":{},\"redata\":{\"quotationNo\":\"QTIJ120Y1419F028069W\",\"commercial\":true,\"compulsory\":true,\"compulsoryInsuransVo\":{\"insuranceQueryCode\":\"\",\"stBackStartDate\":\"\",\"stBackEndDate\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"telSalePerson\":\"\",\"stStartDate\":\"2019-07-25 00:00\",\"stEndDate\":\"2020-07-25 00:00\",\"ecompensationRate\":\"\",\"compCpicScore\":\"\",\"trafficSpreading\":\"\",\"taxType\":\"0\",\"stTaxStartDate\":\"2019-01-01\",\"stTaxEndDate\":\"2019-12-31\",\"taxVehicleType\":\"\",\"taxCustomerType\":\"1\",\"taxpayerName\":\"陶传胜\",\"taxpayerType\":\"201\",\"taxpayerNo\":\"412828198107093011\",\"taxpayerRecno\":\"412828198107093011\",\"taxpayerPhoneNo\":\"\",\"taxpayerContactAddr\":\"\",\"taxpayerZipCode\":\"\",\"taxpayerNational\":\"\",\"taxpayerHomeAddr\":\"\",\"taxPlateType\":\"\",\"vehicleSettledAddr\":\"\",\"taxTicketType\":\"\",\"taxTicketNo\":\"\",\"taxTimePeriod\":\"\",\"taxFuelType\":\"\",\"taxVehicleProp\":\"\",\"cipremium\":null,\"taxAmount\":null,\"vehicleClaimType\":\"\",\"annualPremium\":\"\",\"changeablefeeRate\":\"\",\"localPureRiskPremium\":\"\",\"trafficpoudage\":\"\",\"businessfee\":\"\",\"performance\":\"\",\"policycostRate\":\"\",\"jqNewCarFlag\":\"\",\"taxBureauName\":\"\"},\"commercialInsuransVo\":{\"insuranceQueryCode\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"vehicleRiskLevel\":\"\",\"telSalePerson\":\"\",\"stStartDate\":\"2019-07-25 00:00\",\"stEndDate\":\"2020-07-25 00:00\",\"totalCpicScore\":\"\",\"premiumRatio\":null,\"ecompensationRate\":\"\",\"totalEcompensationRate\":\"\",\"cpicScore\":\"\",\"thirdPartySpreading\":\"\",\"damageSpreading\":\"\",\"thirdAndDamageSpreading\":\"\",\"chdCarRecord\":\"\",\"stMaxPremRatio\":\"\",\"stMinPremRatio\":\"\",\"stMaxSuggestRatio\":\"\",\"stMinSuggestRatio\":\"\",\"stSchemeId\":\"\",\"nonClaimDiscountRate\":\"\",\"trafficTransgressRate\":\"\",\"underwritingRate\":\"\",\"channelRate\":\"\",\"standardPremium\":null,\"premium\":null,\"poudage\":null,\"vehicleClaimType\":\"\",\"checkConsistency\":\"\"},\"quoteInsuranceVos\":[{\"amount\":\"500000\",\"insuranceType\":\"1\",\"insuranceCode\":\"THIRDPARTYLIABILITYCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"THIRDPARTYLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null}],\"accident\":false,\"isHolidaysDouble\":0,\"ifAllroundClause\":0,\"schemeId\":\"\",\"productFlag\":\"0\",\"isRenew\":0}}")
    }
    if ("浙江".equals(areaComCode)) {
        return JSON.parseObject("{\"meta\":{},\"redata\":{\"quotationNo\":\"QHAZ01VY1419F026086K\",\"commercial\":true,\"compulsory\":true,\"compulsoryInsuransVo\":{\"insuranceQueryCode\":\"\",\"stBackStartDate\":\"\",\"stBackEndDate\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"telSalePerson\":\"\",\"stStartDate\":\"2019-06-29 00:00\",\"stEndDate\":\"2020-06-29 00:00\",\"ecompensationRate\":\"\",\"compCpicScore\":\"\",\"trafficSpreading\":\"\",\"taxType\":\"T\",\"taxBureau\":\"\",\"stTaxStartDate\":\"2019-01-01\",\"stTaxEndDate\":\"2019-12-31\",\"taxBureauName\":\"\",\"registryNumber\":\"\",\"taxpayerName\":\"马雷\",\"taxpayerType\":\"1\",\"taxpayerNo\":\"130434199209050814\",\"taxpayerRecno\":\"130434199209050814\",\"cipremium\":null,\"taxAmount\":null,\"vehicleClaimType\":\"\",\"annualPremium\":\"\",\"changeablefeeRate\":\"\",\"localPureRiskPremium\":\"\",\"trafficpoudage\":\"\",\"businessfee\":\"\",\"performance\":\"\",\"policycostRate\":\"\",\"jqNewCarFlag\":\"\"},\"commercialInsuransVo\":{\"insuranceQueryCode\":\"\",\"aidingFundProportion\":\"\",\"aidingFund\":\"\",\"vehicleRiskLevel\":\"\",\"telSalePerson\":\"\",\"stStartDate\":\"2019-06-29 00:00\",\"stEndDate\":\"2020-06-29 00:00\",\"totalCpicScore\":\"\",\"premiumRatio\":null,\"ecompensationRate\":\"\",\"totalEcompensationRate\":\"\",\"cpicScore\":\"\",\"thirdPartySpreading\":\"\",\"damageSpreading\":\"\",\"thirdAndDamageSpreading\":\"\",\"chdCarRecord\":\"\",\"stMaxPremRatio\":\"\",\"stMinPremRatio\":\"\",\"stMaxSuggestRatio\":\"\",\"stMinSuggestRatio\":\"\",\"stSchemeId\":\"\",\"nonClaimDiscountRate\":\"\",\"trafficTransgressRate\":\"\",\"underwritingRate\":\"\",\"channelRate\":\"\",\"standardPremium\":null,\"premium\":null,\"poudage\":null,\"vehicleClaimType\":\"\",\"checkConsistency\":\"\"},\"quoteInsuranceVos\":[{\"amount\":\"1000000\",\"insuranceType\":\"1\",\"insuranceCode\":\"THIRDPARTYLIABILITYCOVERAGE\",\"nonDeductible\":null,\"standardPremium\":null,\"premium\":null},{\"insuranceCode\":\"THIRDPARTYLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE\",\"nonDeductible\":null,\"standardPremium\":null}],\"accident\":false,\"isHolidaysDouble\":0,\"ifAllroundClause\":0,\"schemeId\":\"\",\"productFlag\":\"0\",\"isRenew\":0}}")
    }
    if ('山东'.equals(areaComCode)) {
        return JSON.parseObject('{"meta":{},"redata":{"quotationNo":"","commercial":true,"compulsory":true,"compulsoryInsuransVo":{"insuranceQueryCode":"","stBackStartDate":"","stBackEndDate":"","stStartDate":"2016-10-13 00:00","stEndDate":"2017-10-13 00:00","taxType":"T","taxBureau":"","stTaxStartDate":"2016-01-01","stTaxEndDate":"2016-12-31","taxBureauName":"","cipremium":null,"taxAmount":null},"commercialInsuransVo":{"insuranceQueryCode":"","stStartDate":"2016-10-13 00:00","stEndDate":"2017-10-13 00:00","premiumRatio":null,"stMaxPremRatio":"","stMinPremRatio":"","stMaxSuggestRatio":"","stMinSuggestRatio":"","stSchemeId":"","nonClaimDiscountRate":"","trafficTransgressRate":"","underwritingRate":"","channelRate":"","standardPremium":null,"premium":null},"quoteInsuranceVos":[{"amount":"31752","insuranceType":"1","insuranceCode":"DAMAGELOSSCOVERAGE","nonDeductible":null,"standardPremium":null,"premium":null},{"insuranceCode":"DAMAGELOSSEXEMPTDEDUCTIBLESPECIALCLAUSE","nonDeductible":null},{"amount":"500000","insuranceType":"1","insuranceCode":"THIRDPARTYLIABILITYCOVERAGE","nonDeductible":null,"standardPremium":null,"premium":null},{"insuranceCode":"THIRDPARTYLIABILITYEXEMPTDEDUCTIBLESPECIALCLAUSE","nonDeductible":null}]}}')
    }

    if ('重庆' == areaComCode) {
        return JSON.parseObject('{"meta":{},"redata":{"quotationNo":"","inquireQID":"","commercial":true,"compulsory":true,"compulsoryInsuransVo":{"insuranceQueryCode":"","stBackStartDate":"","stBackEndDate":"","aidingFundProportion":"","aidingFund":"","telSalePerson":"","localPureRiskPremium":"","lastLocalPureriskPremium":"","netPremium":"","totalNetPremium":"","microTagging":"","blackGrade":"","firstIndependentPriceRate":"","firstPlateNo":"","ncdComeFromTraffic":"","stStartDate":"","stEndDate":"","ecompensationRate":"","compCpicScore":"","trafficSpreading":"","compContinuedInsuredYears":"","channelCityNum":"","twoGuestFlag":"","netEcompensationRate":"","specialCarFlag":"","ncdEcompensationRate":"","blackDivisor":"","riskCore":"","taxType":"T","taxBureau":"","stTaxStartDate":"","stTaxEndDate":"","taxBureauName":"","registryNumber":"","sumRefundTax":"","taxpayerName":"","taxpayerType":"","taxpayerNo":"","taxpayerRecno":"","cipremium":null,"taxAmount":null,"vehicleClaimType":"","annualPremium":"","changeablefeeRate":"","trafficpoudage":"","businessfee":"","performance":"","policycostRate":"","jqNewCarFlag":"","secretTaxpayerNo":2},"commercialInsuransVo":{"insuranceQueryCode":"","aidingFundProportion":"","aidingFund":"","vehicleRiskLevel":"","telSalePerson":"","localPureRiskPremium":"","lastLocalPureriskPremium":"","caseNo":"","caseName":"","casePriority":"","productCode":"","netPremium":"","totalNetPremium":"","microTagging":"","blackGrade":"","firstPlateNo":"","ncdComeFromBusses":"","stStartDate":"","stEndDate":"","totalCpicScore":"","premiumRatio":null,"poudage":"","businessfee":"","performance":"","serviceValueAddRate":"","poudageVar":"","businessFeeVar":"","performanceVar":"","addedserviceRateVar":"","isAdjustDiscuteRate":"","discuteAdjustProportion":"","ecompensationRate":"","totalEcompensationRate":"","profitChangeValue":"","profitPoolsRemainQuota":"","countingPoolsRemainQuota":"","cpicScore":"","thirdPartySpreading":"","damageSpreading":"","thirdAndDamageSpreading":"","chdCarRecord":"","carSpreading":"","isLocalInsurance":"","vehicleEarthly":"","channelCityNum":"","twoGuestFlag":"","wyCarType":"","commContinuedInsuredYears":"","netEcompensationRate":"","totalNetEcompensationRate":"","ncdEcompensationRate":"","specialCarFlag":"","ncdTotalEcompensationRate":"","blackDivisor":"","riskCore":"","flexibleScore":"","stMaxPremRatio":"","stMinPremRatio":"","stMaxSuggestRatio":"","stMinSuggestRatio":"","isLinkage":"","lastActualAddedservicerate":"","lastActualPoudage":"","lastActualBusinessFee":"","lastActualPerformance":"","servicePolicyCostRate":"","firstIndependentPriceRate":"","delayBusinessRate":"","stSchemeId":"","nonClaimDiscountRate":"","trafficTransgressRate":"","underwritingRate":"","channelRate":"","standardPremium":null,"premium":null,"vehicleClaimType":"","checkConsistency":""},"quoteInsuranceVos":[],"isHolidaysDouble":0,"ifAllroundClause":0,"schemeId":"","taskId":"","productFlag":"0","isRenew":"0"}}')
    }

    return JSON.parseObject('{"meta":{},"redata":{"commercial":true,"compulsory":true,"quoteInsuranceVos":[{"insuranceCode":"DAMAGELOSSCOVERAGE","amount":85442,"premium":1073.13,"standardPremium":2063.72,"insuranceType":"1"},{"insuranceCode":"THIRDPARTYLIABILITYCOVERAGE","amount":2000000.00,"premium":479.48,"standardPremium":922.08,"insuranceType":"1"}],"compulsoryInsuransVo":{"stEndDate":"2021-11-24 00:00","stBackStartDate":"","taxBureau":"","cipremium":855.0,"stStartDate":"2020-11-24 00:00","taxpayerRecno":"432522195903162896","stTaxEndDate":"2020-12-31","taxpayerName":"贺长庚","insuranceQueryCode":"01CPIC440020001603715835811956","stTaxStartDate":"2020-01-01","taxAmount":300.0,"taxType":"T","stBackEndDate":"","taxBureauName":"广州市地方税务局越秀分局"},"commercialInsuransVo":{"insuranceQueryCode":"V0101CPIC440020001603935035006","aidingFundProportion":"","aidingFund":"","vehicleRiskLevel":"Z","telSalePerson":"","localPureRiskPremium":268.08,"lastLocalPureriskPremium":"268.08","stStartDate":"2020-11-28 00:00","stEndDate":"2021-11-28 00:00","totalCpicScore":"9","isAdjustDiscuteRate":"","discuteAdjustProportion":"","ecompensationRate":"0.5920","totalEcompensationRate":"0.5422","cpicScore":"10","thirdPartySpreading":"","damageSpreading":"","thirdAndDamageSpreading":"","chdCarRecord":"","carSpreading":"6","wyCarType":"-1","commContinuedInsuredYears":"0","stMaxPremRatio":"","stMinPremRatio":"","stMaxSuggestRatio":"","stMinSuggestRatio":"","isLinkage":"0","stSchemeId":"","changeableFeeRate":0,"originalPoudage":0,"originalBusinessFee":0,"originalPerFormance":0,"originalChangeableFeeRate":0,"inferedChangeableFeeRate":"","inferedDiscuteRate":"","targetPolicyCostRate":0,"premiumCalculationScheme":"C","poudage":"0","businessfee":"0.0","performance":"0.0","policyCostRate":0.592,"lastPremiumCalculationScheme":"C","lastTargetPolicyCostRate":"","originalSubjectRate":"","originalSubjectAcount":"","originalDeferSubjectRate":"","originalDeferSubjectRcount":"","subjectRate":"","subjectAcount":"","deferSubjectRate":"","deferSubjectAcount":"","lastPerformance":"0","lastBusinessFee":"0","lastPoudage":"0","lastAdviceDiscuteMinRate":"0","lastPageChangeableFeeRate":"0","businesspoudage":"0.0","ruleNo":"100003938778","shareRuleNo":"","annualPremium":480.04,"lasttrafficsubsidyflag":"0","feemanagematchflag":"","newCarFlag":"0","lastchangeableFeeRate":0,"nonClaimDiscountRate":"0.5","trafficTransgressRate":"1","underwritingRate":"","channelRate":"","independentPriceRate":"0.880000","standardPremium":1090.99,"premium":480.04,"vehicleClaimType":"","checkConsistency":""},"quotationNo":"QGUZ2L0Y2020F028540F"}}');
}


//查找销售人员
void getSellerVos(AutoTask autoTask) {
    Enquiry entity = (Enquiry) autoTask.getTaskEntity();
    Map header = new HashMap();
    header.put("Content-Type", "application/json;charset=utf-8")
    String getSalerInfoURL = "https://issue.cpic.com.cn/ecar/salesInfo/querySalesInfo"
    String agentPersonName = entity.order.agentInfo.name;
    //当以代理人名称查找失败时 是否启用备用销售人员信息（带-为渠道，渠道不受此参数影像）
    boolean doNotUseDefaultSaler = "true".equals(autoTask.configs.needSerchSaler) && "true".equals(autoTask.configs.doNotUseDefaultSaler) && !agentPersonName.contains("-")
    List<String> salerList = new ArrayList<String>();
    List<String> salerCodeList = new ArrayList<>();
    //todo 1498 【百川】中介销售人员职业证号取值优化
    if (autoTask.configs.orgId.toString().startsWith("1244") && ("true".equals(autoTask.configs.needSerchSaler) || entity.order?.agentInfo?.certNumber?.startsWith("20211"))) {
        if (StringUtil.isNoEmpty(entity.order.agentInfo.certNumber)) {
            salerList.add(agentPersonName)
            salerCodeList.add(entity.order.agentInfo.certNumber)
        } else if (doNotUseDefaultSaler) {
            throw new InsReturnException("代理人执业证号为空")
        } else {
        }
    }
    int successSize = autoTask.configs.salerInfo.split(",").size();
    for (int i = 0; i < autoTask.configs.salerInfo.split(",").size(); i++) {
        salerList.add(autoTask.configs.salerInfo.split(",")[i])
        if (StringUtil.isNoEmpty(autoTask.configs.salerCodes) && autoTask.configs.salerCodes.toString().split(",").size() > i) {
            salerCodeList.add(autoTask.configs.salerCodes.toString().split(",")[i])
        } else {
            salerCodeList.add("");
        }
    }
    def getSalerInfoResult = "";
    JSONArray sellerVos = new JSONArray();
    for (int i = 0; i < salerList.size(); i++) {
        boolean isAgent = (salerList.get(i).equals(agentPersonName) && "true".equals(autoTask.configs.needSerchSaler)) || entity.order?.agentInfo?.certNumber?.startsWith("20211")
        JSONObject getSalerInfoParam = getSalerInfoParam("");
        getSalerInfoParam.getJSONObject("redata").put("salerName", salerList.get(i));
        //添加执业证号以精确查找
        if (StringUtil.isNoEmpty(salerCodeList.get(i))) {
            getSalerInfoParam.getJSONObject("redata").put("certificateNo", salerCodeList.get(i));
        }
        autoTask.tempValues.put("getSalerInfoParam", StringUtil.chinesetoUnicode(getSalerInfoParam.toJSONString()));
        def reqBody = Robot2011Util.genBody(autoTask.tempValues, autoTask.tempValues.getSalerInfoParam as String)
        def reqHeader = Robot2011Util.getDefaultHead(autoTask.tempValues, autoTask.configs);
        getSalerInfoResult = HttpSender.doPostWithRetry(5, autoTask?.httpClient as CloseableHttpClient, true, getSalerInfoURL,
                reqBody, null,
                reqHeader,
                "UTF-8", null, "");
        getSalerInfoResult = Robot2011Util.decodeBody(autoTask.tempValues, getSalerInfoResult)
        if (!getSalerInfoResult.startsWith("{"))
            throw new InsReturnException("查找销售人员信息失败" + autoTask.tempValues.get("getSalerInfoParam"));

        JSONObject result = JSON.parseObject(getSalerInfoResult);
        String errMsg = "";
        if ("success" != (String) getFromJson(result, "message.code")) {
            if (isAgent) {
                continue;
            } else {
                throw new InsReturnException("查找销售人员失败,平台返回提示:" + errMsg);
            }
        }

        JSONArray resultArray = result.getJSONArray("result");
        if (resultArray.size() == 0) {
            if (isAgent) {
                if (doNotUseDefaultSaler) {
                    throw new InsReturnException("按代理人名称 " + agentPersonName + " 及执业证号查无销售人员记录")
                } else {
                    continue;
                }
            } else {
                throw new InsReturnException("按配置销售人员 " + salerList.get(i) + " 及执业证号查无销售人员记录")
            }
        }
        JSONObject saler = null;
        for (int j = 0; j < resultArray.size(); j++) {
            JSONObject jsonObject = resultArray.getJSONObject(j);
            if (salerList.get(i).equals(jsonObject.get("salerName"))) {
                if ("东莞".contains((String) autoTask.configs.get("areaComCode")) && jsonObject.getString("certificateNo").startsWith("01")) {
                    continue;
                } else {
                    saler = jsonObject.clone();
                }
            }
        }
        if (null == saler) {
            if (isAgent) {
                continue;
            } else {
                throw new InsReturnException("按配置销售人员 " + salerList.get(i) + " 及执业证号" + salerCodeList.get(i) + "查找的结果不通过")
            }
        }

        JSONObject sellerVosObj = new JSONObject();
        sellerVosObj.put("branchCode", saler.get("branchCode"));
        sellerVosObj.put("sellerId", saler.get("entityId"));
        sellerVosObj.put("name", saler.get("salerName"));
        sellerVosObj.put("certNo", saler.get("certificateNo"));

        sellerVos.add(sellerVosObj);
        if (sellerVos.size() == successSize) {
            break
        }
    }
    autoTask.tempValues.put("sellerVos", sellerVos);
}

//修正起保日期
String checkStartDate(String startDate, AutoTask autoTask, String type) throws Exception {
    int dif = getTimeDifference(DateCalcUtil.getFormatDate(new Date(), "yyyy-MM-dd"), startDate);
    if (dif < 1) {
        startDate = LocalDate.now().plusDays(1).toString()
    }
    int maxDif = StringUtil.isNoEmpty(autoTask.configs.maxDif) ? Integer.parseInt(autoTask.configs.maxDif) : 90;
    if (StringUtil.isNoEmpty(autoTask.configs.maxDifCI) && "CI".equals(type))
        maxDif = Integer.parseInt(autoTask.configs.maxDifCI);
    if (dif > maxDif)
        throw new InsReturnException(RepeatInsure, "起保日期超出限制,限制提前投保日为" + maxDif + "当前投保日期为" + startDate);
    return startDate;
}

String getEndDate(String startDate) throws Exception {

    Date date = null;
    try {
        date = dateOperator("Y", "+", "1", getDate(startDate, "yyyy-MM-dd"));
    } catch (Exception e) {
        e.printStackTrace();
        throw new Exception("终保日期转换错误");
    }

    return DateCalcUtil.getFormatDate(date, "yyyy-MM-dd") + " 00:00";
}

String getEndDateWithStartTime(String startDate, String startTime) throws Exception {

    Date date = null;
    try {
        date = dateOperator("Y", "+", "1", getDate(startDate, "yyyy-MM-dd"));
    } catch (Exception e) {
        e.printStackTrace();
        throw new Exception("终保日期转换错误");
    }

    return DateCalcUtil.getFormatDate(date, "yyyy-MM-dd") + (startTime ?: " 00:00");
}
//以J2结构为模板把J1数据复制至J2
JSONObject j1InfoToJ2(JSONObject j1, JSONObject j2) {
    Iterator it = j2.keySet().iterator();
    while (it.hasNext()) {
        String key = it.next().toString();
        if (j1.containsKey(key))
            j2.put(key, j1.get(key));
    }
    return j2;
}

//根据初次报价险种集合换算出二次报价/暂存时作为参数的险种集合
private JSONArray getInsureQuoteInsuranceVos(JSONArray quoteInsuranceVos, AutoTask autoTask) {
    JSONObject indexObj = new JSONObject();
    String riskWithoutNcf = "GlassIns,VehicleDemageMissedThirdPartyCla,SpecifyingPlantCla";
    String riskWithFactorVos = ['GlassIns', 'PassengerIns', 'SpecifyingPlantCla', 'RoadsideService', 'SendForInspection', 'CompensationDuringRepair', 'ANCVehicleDamage', 'ANCThirdParty', 'ANCDriver', 'ANCPassenger'];
    for (int i = 0; i < quoteInsuranceVos.size(); i++) {
        JSONObject j = quoteInsuranceVos.getJSONObject(i);
        def robot_2011_special_util = new robot_2011_special_util()
        String code = ""
        if (autoTask?.tempValues?.NEFlag) {
            code = reMap(autoTask, "codeReturn", j.getString("insuranceCode").replace("NEWENERGY", ""))
        } else {
            code = reMap(autoTask, "codeReturn", j.getString("insuranceCode")) ?: robot_2011_special_util.p2codeMap().get(j.getString("insuranceCode"))
        }
        if (!code.startsWith("Ncf")) {
            quoteInsuranceVos.getJSONObject(i).remove("stAmount");
            quoteInsuranceVos.getJSONObject(i).remove("stPremium");
            quoteInsuranceVos.getJSONObject(i).remove("stStandardPremium");
            if (!j.containsKey("insuranceType"))
                quoteInsuranceVos.getJSONObject(i).put("insuranceType", "1")
            if (j.containsKey("factorVos") && !riskWithFactorVos.contains(code))
                quoteInsuranceVos.getJSONObject(i).remove("factorVos");
            String ncfKey = "Ncf" + code;
            if (indexObj.containsKey(ncfKey)) {
                quoteInsuranceVos.getJSONObject(i).put("nonDeductible", quoteInsuranceVos.getJSONObject(indexObj.getIntValue(ncfKey)).getString("nonDeductible"));
            } else {
                if (!riskWithoutNcf.contains(code))
                    indexObj.put(code, i);
                else
                    quoteInsuranceVos.getJSONObject(i).put("nonDeductible", null);
            }
        } else {
            quoteInsuranceVos.getJSONObject(i).remove("stNonDeductible");
            if (indexObj.containsKey(code.substring(3))) {
                String nonDeductible = j.getString("nonDeductible");
                quoteInsuranceVos.getJSONObject(indexObj.getIntValue(code.substring(3))).put("nonDeductible", nonDeductible);
            } else {
                indexObj.put(code, i);
            }
        }
    }

    return quoteInsuranceVos;
}

/**
 * @param amount ：eg：'"10.00"'
 * @return amount integer string format
 */
String amountToserviceTimes(String amount) {
    amount.find(/\d+\.\d+/).toDouble().intValue().toString()
}


JSONObject getRiskObj(String code, String amount, boolean isNcfRisk, AutoTask autoTask) {
    Enquiry entity = (Enquiry) autoTask?.taskEntity;
    JSONObject j = new JSONObject();
    j.put("insuranceCode", code);//险种代码
    j.put("nonDeductible", null);
    if (!isNcfRisk) {
        j.put("amount", amount);//保额
        j.put("insuranceType", "1");
        j.put("standardPremium", null);
        j.put("premium", null);
        JSONArray jsonArray = new JSONArray();
        JSONObject obj = new JSONObject();
        //车身划痕损失险
        if ("CARBODYPAINTCOVERAGE".equals(code)) {
            if (amount.contains(".")) {
                j.put("amount", amount.split("\\.")[0]);//保额
            }

        }
        //三者险
        if ("THIRDPARTYLIABILITYCOVERAGE".equals(code))
            autoTask.tempValues.put("3rdAmount", amount)
        else if ("INCARPASSENGERLIABILITYCOVERAGE".equals(code)) {//车上人员责任险
            int seatCnt = autoTask.tempValues.seatCnt;
            obj.put("factorKey", "seat");
            obj.put("factorValue", (seatCnt.toString().toInteger() - 1).toString());
            jsonArray.add(obj);
            j.put("factorVos", jsonArray);
        } else if ("GLASSBROKENCOVERAGE".equals(code)) {//玻璃破碎险
            obj.put("factorKey", "producingArea");
            obj.put("factorValue", "0.00".equals(BigDecimalUtil.minus(amount, "0")) ? "0" : "1");
            jsonArray.add(obj);
            j.put("factorVos", jsonArray);
            j.put("amount", "0");//保额
        } else if ("THIRDPARTYLEGALHOLIDAYLIMITCLAUSE".equals(code)) {//三者假日翻倍险
            if (null != autoTask.tempValues.get("3rdAmount"))
                j.put("amount", autoTask.tempValues.get("3rdAmount"));
            else
                j.put("amount", "3rdAmount");
        } else if ("THIRDPARTYMEDICARELIABILITYCOVERAGE".equals(code)) {
            if (Convert.toBigDecimal(amount, java.math.BigDecimal.ZERO) < java.math.BigDecimal.ZERO) {
                if (null != autoTask.tempValues.get("3rdAmount")) {
                    j.put("amount", autoTask.tempValues.get("3rdAmount"));
                } else {
                    j.put("amount", "3rdAmount");
                }
            }
        } else if ("APPOINTEDREPAIRFACTORYSPECIALCLAUSE".equals(code)) {//指定修理厂险
            obj.put("factorKey", "repairFactorRate");
            String repairFactorRate = (String) autoTask.configs.repairFactorRate;
            repairFactorRate = StringUtil.isNoEmpty(repairFactorRate) ? repairFactorRate : "0.15";//默认费率0.15
            if (1 == entity.order.carInfo.glassType && StringUtil.isNoEmpty((String) autoTask.configs.get("repairFactorRate2")))
                repairFactorRate = autoTask.configs.repairFactorRate2;
            obj.put("factorValue", repairFactorRate);

            autoTask.tempValues.repairRate = repairFactorRate;
            jsonArray.add(obj);
            j.put("factorVos", jsonArray);
        } else if ("PADDLEDAMAGECOVERAGE".equals(code) || "DAMAGELOSSCANNOTFINDTHIRDSPECIALCOVERAGE".equals(code)) {
//发动机涉水损失险 机动车损失保险无法找到第三方特约险
            j.remove("amount");
        } else if ('SUBSTITUTEDRIVINGSPECIALCLAUSE' == code) {
            def suite = findSuiteFromApplyJson(autoTask.applyJson, 'DesignatedDriving') as JSONObject
            def designatedDrivingMile = suite.getString('designatedDrivingMile')
            def map = [
                    //修改为30 需求2965
                    numOrGrade      : designatedDrivingMile ? designatedDrivingMile.toBigDecimal().intValue().toString() : '10',
                    serviceType     : 'sd',
                    serviceTimes    : amountToserviceTimes(amount),
                    freeServiceTimes: ''
            ]
            def robot_2011_util = new robot_2011_util()
            j.putIfAbsent('substituteDrivingVos', [robot_2011_util.treMap(autoTask, map, "SUBSTITUTEDRIVINGSPECIALCLAUSE")])
        } else if ('ROADRESCUESPECIALCLAUSE' == code) {
            def serviceTimes = amountToserviceTimes(amount)
            j.amount = serviceTimes
            j.putIfAbsent('factorVos', [
                    [
                            factorKey  : 'serviceTimes',
                            factorValue: serviceTimes
                    ]
            ])
        } else if ('VEHICLESAFETYDETECTIONSPECIALCLAUSE' == code) {
            def suite = findSuiteFromApplyJson(autoTask.applyJson, 'VehicleInspection') as JSONObject
            def vehicleInspectionLevel = suite.getString('vehicleInspectionLevel')
            def map = [
                    //修改为'C' 需求2965
                    "numOrGrade"  : vehicleInspectionLevel ?: "A",
                    "serviceType" : "vsd",
                    "serviceTimes": amountToserviceTimes(amount)
            ]
            def robot_2011_util = new robot_2011_util()
            j.putIfAbsent('vehicleSafetyDetectionVos', [robot_2011_util.treMap(autoTask, map, "VEHICLESAFETYDETECTIONSPECIALCLAUSE")])
        } else if ('SUBSTITUTESENDDETECTIONSPECIALCLAUSE' == code) {
            def suite = findSuiteFromApplyJson(autoTask.applyJson, 'SendForInspection') as JSONObject
            def sendForInspectionLevel = suite.getString('sendForInspectionLevel')
            /**
             * 根据不同情况计算出年差
             * 1-非四川地区  车座>=7 || 年差>=6 C  否则 A
             *    当前时间和初登日期年差，
             * 2-四川地区  车座>5 || 年差>=5 C    否则 A
             *    第一次报价的起始时间(初次投保时间)和初登日期的年差
             *    报价更改后的起始时间和初登日期的年差
             */
            LocalDate firstRegYear = entity.order.carInfo.firstRegDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            def currentYear = LocalDate.now()
            Period period = Period.between(firstRegYear, currentYear);
            //todo bug  14939
            boolean selectWhat = (entity?.order?.carInfo?.seatCnt >= 7) || (period.years >= 6)
            if (autoTask.configs.areaComCode.toString().contains("四川")) {
                String start = entity.order.suiteInfo.bizSuiteInfo.start ?: entity.order.suiteInfo.efcSuiteInfo.start
                def originYear = LocalDate.parse(start[0..9])
                period = Period.between(firstRegYear, originYear);
                selectWhat = entity?.order?.carInfo?.seatCnt > 6 || (period.years >= 5)
            }
            autoTask.tempValues.selectWhat = selectWhat
            j.putIfAbsent('factorVos', [
                    [
                            "factorKey"  : "serviceTimes",
                            "factorValue": amountToserviceTimes(amount)
                    ], [
                            "factorKey"  : "serviceGrade",
                            "factorValue": sendForInspectionLevel ?: (autoTask.tempValues.selectWhat ?: selectWhat) ? "C" : "A"
                    ]
            ])
        } else if ('REPAIRPERIODCOMPENSATIONSPECIALCLAUSE' == code) {
            j.amount = '100'
            j.putIfAbsent('factorVos', [
                    [
                            "factorKey"  : "maxClaimDays",
                            "factorValue": "3"
                    ]
            ])
        } else if (code in [
                'DAMAGECARDEDUCTIBLESPECIALCLAUSE',
                'THIRDPARTYLIABILITYDEDUCTIBLESPECIALCLAUSE',
                'INCARDRIVERLIABILITYDEDUCTIBLESPECIALCLAUSE',
                'INCARPASSENGERLIABILITYDEDUCTIBLESPECIALCLAUSE',
        ]) {
            j.putIfAbsent('factorVos', [
                    [
                            "factorKey"  : "deductibleRate",
                            "factorValue": amount
                    ]
            ])
        } else if ("EXTERNALPOWERFAULTCOVERAGE".equals(code)) {
            j.remove("amount")
        }
        //假如是新能源  险种增加前缀
        if (autoTask?.tempValues?.NEFlag) {
            j.put("insuranceCode", "NEWENERGY" + code)
        }
    }

    return j;
}

String getDateRegex() {
    return "[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}";
}

String getDateWithHourRegex() {
    return "[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}";
}

String getDateWithMinRegex() {
    return "[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}";
}

String getDateWithSecRegex() {
    return "[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}";
}

String getSubStr(String content, String uniqueStr, String beginStr, String endStr, String regex) {
    String returnStr = "";
    if (!content.contains(uniqueStr) || !content.contains(beginStr) || !content.contains(endStr)) {
        return "";
    }
    int begin = content.indexOf(beginStr, content.indexOf(uniqueStr)) + 1;
    int end = content.indexOf(endStr, begin);
    if (end < begin) {
        return "";
    }
    returnStr = content.substring(begin, end)
    if (StringUtil.areNotEmpty(returnStr, regex)) {
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(returnStr);
        List<String> list = new ArrayList<>();
        while (m.find()) {
            list.add(m.group());
        }
        if (!CollectionUtils.isEmpty(list)) {
            String only = list.get(0);
            if (list.size() > 1) {
                for (String s : list) {
                    if (s.length() > only.length()) {
                        only = s;
                    }
                }
            }
            returnStr = only;
        }
    }
    return returnStr;
}

String getSubStr(String content, String uniqueStr, String beginStr, String endStr) {
    return getSubStr(content, uniqueStr, beginStr, endStr, "");
}


String getSubStr(String content, String uniqueStr, int uniqueLength, String endStr) {
    if (!content.contains(uniqueStr) || !content.contains(endStr)) {
        return "";
    }
    int begin = content.indexOf(uniqueStr) + uniqueLength;
    int end = content.indexOf(endStr, begin);
    if (end < begin)
        return "";
    return content.substring(begin, end);
}

void startDateEditor(String newStart, String type, AutoTask autoTask) throws Exception {
    Enquiry enquiry = (Enquiry) autoTask.taskEntity;
    JSONObject jsonObject = JSON.parseObject(autoTask.tempValues.calculateParamJson);
    JSONObject redata = jsonObject.getJSONObject("redata");
    //交强险
    if ("CI".equals(type) || (autoTask.tempValues.cibi && autoTask.configs.areaComCode != '山东')) {
        newStart = checkStartDate(newStart, autoTask, "CI");
        JSONObject compulsoryInsuransVo = redata.getJSONObject("compulsoryInsuransVo");
        compulsoryInsuransVo.put("stStartDate", newStart + " 00:00");
        compulsoryInsuransVo.put("stEndDate", getEndDate(newStart));
        //车船税跨年
        def areaCodeList = ['河北', '长沙', '湖南', '山东', '东莞', '四川', '广州市', '河南', '重庆', '佛山', '湖北', '宁波', '陕西']
        //取消河北，山东的车船税跨年
        if (!newStart.substring(0, 4).equals(compulsoryInsuransVo.getString("stTaxStartDate").substring(0, 4))) {
            String year = newStart.substring(0, 4);
            if (!areaCodeList.contains(autoTask?.configs?.areaComCode)) {
                compulsoryInsuransVo.put("stTaxStartDate", year + "-01-01")
                compulsoryInsuransVo.put("stTaxEndDate", year + "-12-31")
            }
        }
        //深圳车船税起止日期跟投保日期不跟交强险起保日期
        redata.put("compulsoryInsuransVo", compulsoryInsuransVo);
        doProductCodeDate(autoTask, redata)
        jsonObject.put("redata", redata);
        autoTask.tempValues.calculateParamJson = jsonObject.toJSONString()

        return;
    }

    if ("BI".equals(type) && (!autoTask.tempValues.cibi || autoTask.configs.areaComCode == '山东')) {
        newStart = checkStartDate(newStart, autoTask, "BI");
        JSONObject commercialInsuransVo = redata.getJSONObject("commercialInsuransVo")
        commercialInsuransVo.put("stStartDate", newStart + " 00:00");
        commercialInsuransVo.put("stEndDate", getEndDate(newStart));
        redata.put("commercialInsuransVo", commercialInsuransVo);
        String actualValue = RobotDict_2011.getActualValue(autoTask, enquiry, newStart)
        if (!actualValue.equals(autoTask.tempValues.actualValue)) {
            autoTask.tempValues.actualValue = actualValue
            if (enquiry.order.carInfo.carPriceType != 2) {
                JSONArray jsonArray = redata.getJSONArray("quoteInsuranceVos");
                String codes = ",DAMAGELOSSCOVERAGE,THEFTCOVERAGE,SELFIGNITECOVERAGE,SPECIALCARDAMAGELOSSCOVERAGE,NEWENERGYDAMAGELOSSCOVERAGE,";
//车辆损失险、全车抢盗险、自燃损失险
                int k = 0;
                for (int i = 0; i < jsonArray.size(); i++) {
                    String insuranceCode = jsonArray.getJSONObject(i).getString("insuranceCode");
                    if (codes.contains("," + insuranceCode + ",")) {
                        jsonArray.getJSONObject(i).put("amount", actualValue);
                        k++;
                    }
                    if (k == 4)
                        break;
                }
                redata.put("quoteInsuranceVos", jsonArray);
                redata.getJSONObject("ecarvo").put("negotiatedValue", actualValue);//车辆协商价值
            } else {
                redata.getJSONObject("ecarvo").put("negotiatedValue", enquiry.order.carInfo.definedCarPrice);//车辆协商价值
            }
            redata.getJSONObject("ecarvo").put("actualValue", actualValue);//车辆实际价值
        }
        //发证日期
        if (null != autoTask.tempValues.stVehicleLicensingDate)
            redata.getJSONObject("compulsoryInsuransVo").put("stVehicleLicensingDate", autoTask.tempValues.stVehicleLicensingDate)
        doProductCodeDate(autoTask, redata)
        jsonObject.put("redata", redata);
        autoTask.tempValues.calculateParamJson = jsonObject.toJSONString()
        return;
    }

}

void startDateEditorWithStartTime(String newStart, String type, AutoTask autoTask, String startTime) throws Exception {
    Enquiry enquiry = (Enquiry) autoTask.taskEntity;
    JSONObject jsonObject = JSON.parseObject(autoTask.tempValues.calculateParamJson);
    JSONObject redata = jsonObject.getJSONObject("redata");
    //交强险
    if ("CI".equals(type) || (autoTask.tempValues.cibi && autoTask.configs.areaComCode != '山东')) {
        newStart = checkStartDate(newStart, autoTask, "CI");
        JSONObject compulsoryInsuransVo = redata.getJSONObject("compulsoryInsuransVo");
        compulsoryInsuransVo.put("stStartDate", newStart + (startTime ?: " 00:00"));
        compulsoryInsuransVo.put("stEndDate", getEndDateWithStartTime(newStart, startTime));
        //车船税跨年
        def areaCodeList = ['河北', '长沙', '湖南', '山东', '东莞', '四川', '广州市', '河南', '重庆', '佛山', '湖北', '宁波', '陕西']
        //取消河北，山东的车船税跨年
        if (!newStart.substring(0, 4).equals(compulsoryInsuransVo.getString("stTaxStartDate").substring(0, 4))) {
            String year = newStart.substring(0, 4);
            if (!areaCodeList.contains(autoTask?.configs?.areaComCode)) {
                compulsoryInsuransVo.put("stTaxStartDate", year + "-01-01")
                compulsoryInsuransVo.put("stTaxEndDate", year + "-12-31")
            }
        }
        //深圳车船税起止日期跟投保日期不跟交强险起保日期
        redata.put("compulsoryInsuransVo", compulsoryInsuransVo);
        doProductCodeDate(autoTask, redata)
        jsonObject.put("redata", redata);
        autoTask.tempValues.calculateParamJson = jsonObject.toJSONString()

        return;
    }

    if ("BI".equals(type) && (!autoTask.tempValues.cibi || autoTask.configs.areaComCode == '山东')) {
        newStart = checkStartDate(newStart, autoTask, "BI");
        JSONObject commercialInsuransVo = redata.getJSONObject("commercialInsuransVo")
        commercialInsuransVo.put("stStartDate", newStart + (startTime ?: " 00:00"));
        commercialInsuransVo.put("stEndDate", getEndDateWithStartTime(newStart, startTime));
        redata.put("commercialInsuransVo", commercialInsuransVo);
        String actualValue = RobotDict_2011.getActualValue(autoTask, enquiry, newStart)
        if (!actualValue.equals(autoTask.tempValues.actualValue)) {
            autoTask.tempValues.actualValue = actualValue
            if (enquiry.order.carInfo.carPriceType != 2) {
                JSONArray jsonArray = redata.getJSONArray("quoteInsuranceVos");
                String codes = ",DAMAGELOSSCOVERAGE,THEFTCOVERAGE,SELFIGNITECOVERAGE,SPECIALCARDAMAGELOSSCOVERAGE,NEWENERGYDAMAGELOSSCOVERAGE,";
//车辆损失险、全车抢盗险、自燃损失险
                int k = 0;
                for (int i = 0; i < jsonArray.size(); i++) {
                    String insuranceCode = jsonArray.getJSONObject(i).getString("insuranceCode");
                    if (codes.contains("," + insuranceCode + ",")) {
                        jsonArray.getJSONObject(i).put("amount", actualValue);
                        k++;
                    }
                    if (k == 4)
                        break;
                }
                redata.put("quoteInsuranceVos", jsonArray);
                redata.getJSONObject("ecarvo").put("negotiatedValue", actualValue);//车辆协商价值
            } else {
                redata.getJSONObject("ecarvo").put("negotiatedValue", enquiry.order.carInfo.definedCarPrice);//车辆协商价值
            }
            redata.getJSONObject("ecarvo").put("actualValue", actualValue);//车辆实际价值
        }
        //发证日期
        if (null != autoTask.tempValues.stVehicleLicensingDate)
            redata.getJSONObject("compulsoryInsuransVo").put("stVehicleLicensingDate", autoTask.tempValues.stVehicleLicensingDate)
        doProductCodeDate(autoTask, redata)
        jsonObject.put("redata", redata);
        autoTask.tempValues.calculateParamJson = jsonObject.toJSONString()
        return;
    }

}

def static doProductCodeDate(autoTask, JSONObject redata) {
    def accidentInsuransVo = redata.accidentInsuransVo
    def commercialInsuransVo = redata.commercialInsuransVo
    def compulsoryInsuransVo = redata.compulsoryInsuransVo
    if (accidentInsuransVo) {
        redata.accidentInsuransVo.stStartDate = commercialInsuransVo?.stStartDate ?: compulsoryInsuransVo?.stStartDate
        redata.accidentInsuransVo.stEndDate = commercialInsuransVo?.stEndDate ?: compulsoryInsuransVo?.stEndDate
    }
    //处理 代为送检服务特约条款
    if (autoTask.configs.areaComCode.toString().contains("四川")) {
        dealFactorValue(autoTask, redata, commercialInsuransVo?.stStartDate ?: compulsoryInsuransVo?.stStartDate)
    }
}
//处理 代为送检服务特约条款
def static dealFactorValue(autoTask, JSONObject redata, String timeStr) {
    def flag = false
    redata.quoteInsuranceVos.each {
        if (it?.insuranceCode == "SUBSTITUTESENDDETECTIONSPECIALCLAUSE") {
            flag = true
        }
    }
    if (flag) {
        Enquiry entity = (Enquiry) autoTask.getTaskEntity()
        //和初登日期进行比较
        LocalDate firstRegYear = entity.order.carInfo.firstRegDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
        def updateYear = LocalDate.parse(timeStr[0..9])
        Period period = Period.between(firstRegYear, updateYear);
        def selectWhat = entity?.order?.carInfo?.seatCnt > 5 || (period.years >= 5)
        if (autoTask.configs.areaComCode.toString().contains("四川")) {
            selectWhat = entity?.order?.carInfo?.seatCnt > 6 || (period.years >= 5)
        }
        redata.quoteInsuranceVos.each {
            if (it?.insuranceCode == "SUBSTITUTESENDDETECTIONSPECIALCLAUSE") {
                it.factorVos.each {
                    if (it.factorKey == "serviceGrade") {
                        it.factorValue = selectWhat ? "C" : "A"
                    }
                }
            }
        }
    }
}
//处理关于重复投保提示的平台信息
void savePoliciesInfo(String msg, String type, AutoTask autoTask) {
    JSONObject PTMsg = (JSONObject) autoTask.tempValues.get("PTMsg");
    JSONArray policiesArray = new JSONArray();
    JSONObject policies = new JSONObject();
    if ("CI".equals(type)) {
        if (msg.contains("终保日期")) {
            policies.put("policyId", getSubStr(msg, "保单号 ", " ", ";"));
            policies.put("policyStartTime", getSubStr(msg, "起保日期 ", " ", ";", getDateWithHourRegex()) + ":00:00");
            String policyEndTime = getSubStr(msg, "终保日期 ", " ", ";", getDateWithHourRegex()) + ":59:59"
            if (policyEndTime.contains("00:59:59")) {
                policyEndTime = policyEndTime.replace("00:59:59", "00:00:00")
            }
            policies.put("policyEndTime", policyEndTime);
            policiesArray.add(policies);
            PTMsg.put("efcPolicies", policiesArray);
            autoTask.tempValues.put("PTMsg", PTMsg);
        } else if (msg.contains("-保险止期")) {
            String start = getSubStr(msg, "保险期限是", "是", "－")
            if (StringUtil.isNoEmpty(start))
                start = start.replace("年", "-").replace("月", "-").replace("日", "").substring(0, 13)
            policies.put("policyId", getSubStr(msg, "保单号 ", " ", ";"));
            policies.put("policyStartTime", start + ":00:00");
            String policyEndTime = dateOperator4String("Y", "+", "1", start, "yyyy-MM-dd hh") + ":59:59"
            if (policyEndTime.contains("00:59:59")) {
                policyEndTime = policyEndTime.replace("00:59:59", "00:00:00")
            }
            policies.put("policyEndTime", policyEndTime);
            String company = getSubStr(msg, "[该车已在", "在", "存在")
            if (StringUtil.isNoEmpty(company))
                policies.put("insCorpName", company);
            policiesArray.add(policies);
            PTMsg.put("efcPolicies", policiesArray);
            autoTask.tempValues.put("PTMsg", PTMsg);
        } else if (msg.contains("保险期限是[")) {
            msg = getSubStr(msg, "保险期限是[", "[", "]");
            String start = msg.split("-")[0];
            String end = msg.split("-").length > 1 ? msg.split("-")[1] : "";
            if (StringUtil.isNoEmpty(start) && start.length() > 7) {
                start = start.substring(0, 4) + "-" + start.substring(4, 6) + "-" + start.substring(6, 8)
                policies.put("policyStartTime", start + " 00:00:00");
                policies.put("policyEndTime", dateOperator4String("Y", "+", "1", start, "yyyy-MM-dd") + " 23:59:59");
            } else if (StringUtil.isNoEmpty(end) && end.length() > 7) {
                end = end.substring(0, 4) + "-" + end.substring(4, 6) + "-" + end.substring(6, 8)
                policies.put("policyEndTime", dateOperator4String("D", "+", "1", end, "yyyy-MM-dd") + " 23:59:59");
            }

            policiesArray.add(policies);
            PTMsg.put("efcPolicies", policiesArray);
            autoTask.tempValues.put("PTMsg", PTMsg);
        } else {
            PTMsg.put("JQendMark", "true");
            PTMsg.put("errorMsgJQ", "交强险已起保或有未到期保单");
            autoTask.tempValues.platformBack = PTMsg;
            PlatformUtil.doBackPlatformInfo(autoTask);
        }
    }

    if ("BI".equals(type)) {
        String policyEndTime = getSubStr(msg, "终保日期：", "：", "日", getDateWithMinRegex()).replace("\n", "").replace("年", "-").replace("月", "-") + ":00";
        if (msg.contains("商业险上年保险止期")) {
            policyEndTime = getSubStr(msg, "商业险上年保险止期：", "：", ".");
            if (policyEndTime.length() > 7) {
                policyEndTime = policyEndTime.substring(0, 4) + "-" + policyEndTime.substring(4, 6) + "-" + policyEndTime.substring(6, 8)
                policyEndTime = dateOperator4String("D", "+", "1", policyEndTime, "yyyy-MM-dd");
            }
        } else if (msg.contains("23时59分"))
            policyEndTime = dateOperator4String("D", "+", "1", policyEndTime, "yyyy-MM-dd");
        if (StringUtil.isNoEmpty(policyEndTime)) {
            if (msg.contains("终保日期：")) {
                String insCorpName = getSubStr(msg, "重复投保的保险公司：", "：", "</br>").replace("</br>", "");
                if (StringUtil.isEmpty(insCorpName))
                    insCorpName = getSubStr(msg, "重复投保的保险公司：", "：", "起保日期：").replace("\n", "");
                if (insCorpName.contains("</br>")) {
                    insCorpName = "中国太平洋财产保险股份有限公司"
                }
                policies.put("insCorpName", insCorpName);
                policies.put("policyStartTime", getSubStr(msg, "起保日期：", "：", "日", getDateWithMinRegex()).replace("\n", "").replace("年", "-").replace("月", "-")) + ":00";
                policies.put("policyEndTime", policyEndTime);
                if (msg.contains("时") && msg.contains("分</br>")) {
                    String policyStartTimeTmp = getSubStr(msg, "起保日期：", "：", "分</br>").replace("\n", "").replace("年", "-").replace("月", "-").replace("日", "").replace("时", ":") + ":00";
                    String policyEndTimeTmp = getSubStr(msg, "终保日期：", "：", "分</br>").replace("\n", "").replace("年", "-").replace("月", "-").replace("日", "").replace("时", ":") + ":00";
                    policies.put("policyStartTime", policyStartTimeTmp);
                    policies.put("policyEndTime", policyEndTimeTmp);
                }

            } else if (msg.contains("商业险上年保险止期")) {
                policies.put("policyStartTime", dateOperator4String("Y", "-", "1", policyEndTime, "yyyy-MM-dd") + " 23:59:59");
                policies.put("policyEndTime", policyEndTime + " 23:59:59");
            }
            policiesArray.add(policies);
            PTMsg.put("bizPolicies", policiesArray);
            autoTask.tempValues.put("PTMsg", PTMsg);
        } else {
            PTMsg.put("SYendMark", "true");
            PTMsg.remove("bizPolicies");
            PTMsg.put("errorMsgSY", "商业险已起保或有未到期保单");
            autoTask.tempValues.platformBack = PTMsg;
            PlatformUtil.doBackPlatformInfo(autoTask);
        }
    }
}

//是否同一个人
boolean isSamePrpCinsured(String nameA, String identifyNumberA, String nameB, String identifyNumberB) {
    if (nameA.equals(nameB) && identifyNumberA.equals(identifyNumberB))
        return true;
    return false;
}
//获取结束日期
String getEndDate4return(Date startDate) throws Exception {

    Date date = null;
    try {
        date = dateOperator("Y", '+', "1", startDate);
        date = dateOperator("D", '-', "1", date);
    } catch (Exception e) {
        e.printStackTrace();
        throw new Exception("终保日期转换错误");
    }

    return DateCalcUtil.getFormatDate(date, "yyyy-MM-dd") + " 23:59:59";
}

//根据流解析验证码
String getAuth(String str) throws Exception {
    return Dama2Web.getAuth(str, 1);
}
//计算交强险原价
String getEfcOrgCharge(String useProp, BigDecimal flag) {
    String efcOrgCharge = "";
    if ("1".equals(useProp)) {//家庭自用
        if (flag < 6)
            efcOrgCharge = "950"
        else
            efcOrgCharge = "1100";
    } else if ("10".equals(useProp)) {//企业非营业
        if (flag < 6)
            efcOrgCharge = "1000"
        else if (flag < 10)
            efcOrgCharge = "1130";
        else if (flag < 20)
            efcOrgCharge = "1220";
        else
            efcOrgCharge = "1270";
    } else if ("11".equals(useProp)) {//机关非营业
        if (flag < 6)
            efcOrgCharge = "950"
        else if (flag < 10)
            efcOrgCharge = "1070";
        else if (flag < 20)
            efcOrgCharge = "1140";
        else
            efcOrgCharge = "1320";
    } else if ("2".equals(useProp)) {//营业出租租赁
        if (flag < 6)
            efcOrgCharge = "1800"
        else if (flag < 10)
            efcOrgCharge = "2360";
        else if (flag < 20)
            efcOrgCharge = "2400";
        else if (flag < 36)
            efcOrgCharge = "2560";
        else
            efcOrgCharge = "3530";
    } else if ("3".equals(useProp)) {//营业城市公交
        if (flag < 10 && flag >= 6)
            efcOrgCharge = "2250";
        else if (flag < 20)
            efcOrgCharge = "2520";
        else if (flag < 36)
            efcOrgCharge = "3020";
        else
            efcOrgCharge = "3140";
    } else if ("4".equals(useProp)) {//营业公路客运
        if (flag < 10 && flag >= 6)
            efcOrgCharge = "2350";
        else if (flag < 20)
            efcOrgCharge = "2620";
        else if (flag < 36)
            efcOrgCharge = "3420";
        else
            efcOrgCharge = "4690";
    } else if ("12".equals(useProp)) {//非营业货车
        if (flag < 2)
            efcOrgCharge = "1200";
        else if (flag < 5)
            efcOrgCharge = "1470";
        else if (flag < 10)
            efcOrgCharge = "1650";
        else
            efcOrgCharge = "2220";
    } else if ("6".equals(useProp)) {//营业货车
        if (flag < 2)
            efcOrgCharge = "1850";
        else if (flag < 5)
            efcOrgCharge = "3070";
        else if (flag < 10)
            efcOrgCharge = "3450";
        else
            efcOrgCharge = "4480";
    }

    return efcOrgCharge;
}

String getModelNamewithoutCN(String modelName) {
    String regEx = "[a-zA-Z0-9 ？.-]";
    Pattern p = Pattern.compile(regEx);
    Matcher m = p.matcher(modelName);
    StringBuffer sb = new StringBuffer();
    while (m.find()) {
        sb.append(m.group());
    }
    StringBuilder str = new StringBuilder(sb.toString());
    while (str.toString().startsWith(" ")) {//||str.toString().endsWith(" ")
        str.delete(0, 1)
    }
    while (str.toString().endsWith(" ")) {//||
        str.delete(str.length() - 1, str.length())
    }
    return str.toString();
}
/**
 * 生成折旧月数
 * @param startDate 初登
 * @param endDate 起保
 * @return
 */
int getMonths(Date startDate, Date endDate) {
    Calendar gc1 = GregorianCalendar.getInstance();
    gc1.setTime(startDate);
    Calendar gc2 = GregorianCalendar.getInstance();
    gc2.setTime(endDate);

    int usedMonths = gc2.get(Calendar.MONTH) - gc1.get(Calendar.MONTH) + (gc2.get(Calendar.YEAR) - gc1.get(Calendar.YEAR)) * 12;

    if (gc1.get(Calendar.DAY_OF_MONTH) > gc2.get(Calendar.DAY_OF_MONTH)) {
        usedMonths = usedMonths - 1;
    }

    return usedMonths;
}


//int getMonths(Date startDate, Date endDate) {
//    int elapsed = 0;
//    Calendar gc1 = GregorianCalendar.getInstance();
//    gc1.setTime(startDate);
//    Calendar gc2 = GregorianCalendar.getInstance();
//    gc2.setTime(endDate);
//
//    gc1.clear(Calendar.MILLISECOND);
//    gc1.clear(Calendar.SECOND);
//    gc1.clear(Calendar.MINUTE);
//    gc1.clear(Calendar.HOUR_OF_DAY);
//    gc1.clear(Calendar.DATE);
//    int dayOfGc1=gc1.get(Calendar.DAY_OF_MONTH);
//
//    gc2.clear(Calendar.MILLISECOND);
//    gc2.clear(Calendar.SECOND);
//    gc2.clear(Calendar.MINUTE);
//    gc2.clear(Calendar.HOUR_OF_DAY);
//    gc2.clear(Calendar.DATE);
//    if (gc1.after(gc2)) {
//        return -1;
//    }
//    while (gc1.before(gc2)) {
//        gc1.add(Calendar.MONTH, 1);
//        //防止1月31变成2月28 然后DAY值变成28
//        if (dayOfGc1!=gc1.get(Calendar.DAY_OF_MONTH) && dayOfGc1<=gc1.getActualMaximum(Calendar.DAY_OF_MONTH))
//            gc1.set(Calendar.DAY_OF_MONTH,dayOfGc1);
//        elapsed++;
//    }
//    return elapsed;
//}

JSONObject getQueryPureriskAndVehicleInfoParam(String areaComCode) {
    return JSON.parseObject("{\"meta\":{},\"redata\":{\"plateNo\":\"\\u6caaNR6995\",\"carVIN\":\"5GALVBED4AJ223015\",\"searchInterFlag\":1,\"moldCharacterCode\":\"BKAAPI0005\",\"modelCode\":\"BBKAALUD0001\",\"engineNo\":\"LLT4AJ223015\",\"stRegisterDate\":\"2010-08-26\",\"usage\":\"101\",\"vehicleType\":\"01\",\"plateType\":\"02\",\"tonnage\":\"\",\"seatCount\":\"\",\"stInvoiceDate\":\"\"}}");
}

String getTaxVehicleType(AutoTask autoTask) {
    Enquiry enquiry = (Enquiry) autoTask.getTaskEntity();
    int useProps = enquiry.order.carInfo.useProps
    String taxVehicleType = "";
    if ("上海".equals(autoTask.configs.areaComCode)) {
        if (6 == useProps || 12 == useProps) {//货车

        } else if (15 == useProps || 16 == useProps) {//特种车

        } else {//客车
            int seatCnt = enquiry.order.carInfo.seatCnt;
            double displacement = enquiry.order.carInfo.displacement.doubleValue();
            if (seatCnt <= 9) {
                if (1 >= displacement)
                    taxVehicleType = "K01"
                else if (1.6 >= displacement)
                    taxVehicleType = "K02"
                else if (2.0 >= displacement)
                    taxVehicleType = "K03"
                else if (2.5 >= displacement)
                    taxVehicleType = "K04"
                else if (3 >= displacement)
                    taxVehicleType = "K05"
                else if (4 >= displacement)
                    taxVehicleType = "K06"
                else
                    taxVehicleType = "K07"
            } else if (seatCnt < 20)
                taxVehicleType = "K08"
            else
                taxVehicleType = "K09"
        }
    } else if ("北京".equals(autoTask.configs.areaComCode)) {
        taxVehicleType = "K11"
        if (6 == useProps || 12 == useProps) {//货车

        } else if (15 == useProps || 16 == useProps) {//特种车

        } else {//客车

        }
    }

    return taxVehicleType
}
//日期操作（String）
String dateOperator4String(String addType, String operator, String addDate, String date, String formatType) {
    Date d = getDate(date, formatType);
    d = dateOperator(addType, operator, addDate, d);
    return DateCalcUtil.getFormatDate(d, formatType);
}
//添加特别约定 传入特约编码列表
JSONArray getEngages(List<JSONObject> codes, SpecialClause specialClause, AutoTask autoTask) {

    JSONArray jsonArray = new JSONArray();
    for (JSONObject str : codes) {
        if (str.get("code").equals(specialClause.getCode())) {
            JSONObject specialClauseParam = new JSONObject();
            specialClauseParam.put("customAgreement", specialClause.getClauses())
            specialClauseParam.put("agreementId", str.get("id"))
            specialClauseParam.put("customSubstitute", "")
            jsonArray.add(specialClauseParam);
        }
    }
    return jsonArray;
}


String getErrCarDesc(String msg) {
    String desc = new String(msg);
    if (desc.contains("版") || desc.contains("型")) {
        boolean a = !"".contains(desc);
        boolean b = !"型尚版,".contains(desc)
        if (a)
            desc = desc.replace("版", "@")
        if (b)
            desc = desc.replace("型", "@")
        desc = desc.substring(desc.indexOf("@") - 2, desc.indexOf("@"))
    }
    return desc
}

void transformCarInfo(CarInfo carInfo) {
    try {
        String e = carInfo.getVin();
        if (!StringUtil.isNoEmpty(e) || e.length() < 17 || !e.startsWith("L") && !e.startsWith("l")) {
            carInfo.setGlassType(1);
        } else {
            carInfo.setGlassType(0);
        }
    } catch (Exception var2) {
    }

}

String getMobile(AutoTask autoTask) {
    StringBuffer mobile = new StringBuffer(StringUtil.getMobile());
    //如佛山要求按格式 12300XXXXXX 录入
    if (StringUtil.isNoEmpty((String) autoTask.configs.randomMobileTeplate) && autoTask.configs.randomMobileTeplate.toString().length() == 11) {
        String randomMobileTeplate = autoTask.configs.randomMobileTeplate.toString().toUpperCase();
        for (int i = 0; i < randomMobileTeplate.length(); i++) {
            if (isNumeric(randomMobileTeplate.substring(i, i + 1))) {
                mobile = mobile.replace(i, i + 1, randomMobileTeplate.substring(i, i + 1))
            }
        }
    }
    return mobile.toString()
}
//判断是否数字
boolean isNumeric(String str) {
    Pattern pattern = Pattern.compile("[0-9]*");
    return pattern.matcher(str).matches();
}

//对应车船税代码变更作出调整 山东上年（续保带出）的车船税代码为 数字，今年变更为字符
String checkTaxType(String taxType, AutoTask autoTask) {
    //北京与上海车船税为数字但模板与其他地区不一致，不处理
    boolean changeTaxType = !(autoTask.configs.areaComCode.toString().contains("上海") || autoTask.configs.areaComCode.toString().contains("北京"))
    if (changeTaxType)//当纳税类型与配置不一致时
        changeTaxType = isNumberTaxType(autoTask) != isNumeric(taxType)
    if (changeTaxType)
        return reMap(autoTask, "taxTypeChange", taxType)
    return taxType;
}

/**
 * 校验地址
 * @param address 地址信息
 * @param stratified 是否需要分层处理
 * @param autoTask 如需分层 把分层信息储存在autoTask.tempValues
 */
String checkAddress(String address, boolean stratified, AutoTask autoTask) {
    String addressTmp = address;
    if (addressTmp.contains("#") && addressTmp.contains("|")) {
        if (stratified) {
            int count = 0;
            Pattern p = Pattern.compile("\\|[0-9]+#");
            Matcher matcher = p.matcher(addressTmp);
            while (matcher.find()) {
                String stratifiedCode = matcher.group().replace("|", "").replace("#", "")
                if (0 == count) {
                    autoTask.tempValues.put("province", stratifiedCode);
                    count++;
                } else if (1 == count) {
                    autoTask.tempValues.put("cityCode", stratifiedCode);
                    count++;
                } else if (2 == count) {
                    autoTask.tempValues.put("countyCode", stratifiedCode);
                    count++;
                }
            }
            addressTmp = address.substring(address.lastIndexOf("#") + 1);
        } else {
            addressTmp = address.replaceAll("\\|[0-9]+#", "")
        }
    }
    return addressTmp;
}

//补充数据项覆盖
void supplyParamConversionInTempalte(AutoTask autoTask) {
    Enquiry enquiry = (Enquiry) autoTask.getTaskEntity();
    if (null != enquiry.misc?.supplyParam) {
        String tempAddress = "";

        if (StringUtil.isNoEmpty((String) enquiry.misc.supplyParam.drivingLicenseAddress)) {
            tempAddress = enquiry.misc.supplyParam.drivingLicenseAddress;
        }

        if (StringUtil.isNoEmpty(tempAddress)) {
            if (null != enquiry.order?.insurePerson)
                enquiry.order?.insurePerson.setAddress(tempAddress);
            if (null != enquiry.order?.insuredPersons)
                enquiry.order?.insuredPersons.get(0).setAddress(tempAddress);
            if (null != enquiry.order?.carOwnerInfo)
                enquiry.order?.carOwnerInfo.setAddress(tempAddress);
        }
        //BUG 17991 北京太保精灵 获取支付码的时候提示 车主地址为空
        if (enquiry?.misc?.supplyParam?.ownerAddress) {
            enquiry.order?.carOwnerInfo?.setAddress(enquiry?.misc?.supplyParam?.ownerAddress?.toString())
        }
    }
}


String getErrTip(String errMsg) {
    Map<String, String> map = new HashMap<>();
    map.put("MODELCODE_NOTALLMATCHING", "平台返回提示:选择车型与平台车型不一致，请通过精准查询定型！")
    return map.get(errMsg);
}

/**
 *  品牌名称            CarBrandName
 *  行业车型编码        JyCode
 *  车型编码        vehicleId
 *  新车购置价     purchasePrice
 *  新车购置价（含税）   actualValue
 *  类比价       analogyPrice
 *  类比价（含税）     taxAnalogyPrice
 *  车辆种类          CarKindName
 *  上市年份             year
 *  核定载客        seatCount
 *  核定载质量     modelLoad（千克）
 *  整备质量        fullLoad（千克）
 *  排气量         displacement
 * @return
 */
def static makeCarInfo() {
    def carInfo = [
            CarBrandName   : '',
            JyCode         : '',
            vehicleId      : '',
            purchasePrice  : '',
            actualValue    : '',
            analogyPrice   : '',
            taxAnalogyPrice: '',
            CarKindName    : '',
            year           : '',
            seatCount      : '',
            modelLoad      : '',
            fullLoad       : '',
            displacement   : '',
    ]
    carInfo
}

def static carType(keyName) {
    def carType = [
            "KE": "三十六座及三十六座以上客车",
            "NB": "三轮汽车",
            "KD": "二十座至三十六座以下客车",
            "HA": "二吨以下货车",
            "GB": "二吨至五吨以下挂车",
            "HB": "二吨至五吨以下货车",
            "GC": "五吨至十吨以下挂车",
            "HC": "五吨至十吨以下货车",
            "NA": "低速货车",
            "KA": "六座以下客车",
            "KB": "六座至十座以下客车",
            "JB": "兼用型拖拉机14.7KW以上及联合收割机",
            "JA": "兼用型拖拉机14.7KW及以下",
            "GD": "十吨以上挂车",
            "HD": "十吨以上货车",
            "KC": "十座至二十座以下客车",
            "MC": "摩托车排气量（250cc以上）及侧三轮",
            "MA": "摩托车排气量（50cc及以下）",
            "MB": "摩托车排气量（50cc－250cc含）",
            "ZA": "特种车一",
            "ZC": "特种车三",
            "ZB": "特种车二",
            "ZD": "特种车四",
            "JD": "运输型拖拉机14.7KW以上",
            "JC": "运输型拖拉机14.7KW及以下"
    ]
    def set = carType.keySet()
    if (set.contains(keyName)) {
        return carType."$keyName"
    }
    ''
}

/**
 * <AUTHOR>
 * @Description //车辆种类
 * @Date 2019-12-16 17:48
 * @Param [uc, modelLoad, seatCnt]
 * @return java.lang.Object
 */
def static getCarVehicleType(String uc, BigDecimal doutonnage, Integer seat, String plateNum, String carModelName) {
    def vehicleType = ''
    if (doutonnage && plateNum && carModelName) {
        if ((carModelName.contains("挂车") || plateNum.contains("挂")) && ("6" == uc || "12" == uc)) {
            if (!doutonnage || doutonnage < 2000.00) {
                vehicleType = "20";
            } else if (doutonnage >= 2000.00 && doutonnage < 10000.00) {
                vehicleType = "21";
                if (doutonnage >= 5000.00) {
                    vehicleType = "22";
                }
            } else if (doutonnage >= 10000.00) {
                vehicleType = "23";
            }
        } //正常货车
        if (!doutonnage || doutonnage < 2000.00) {
            vehicleType = "06";
        } else if (doutonnage >= 2000.00 && doutonnage < 10000.00) {
            vehicleType = "07";
            if (doutonnage >= 5000.00) {
                vehicleType = "08";
            }
        } else if (doutonnage >= 10000.00) {
            vehicleType = "09";
        }
        return vehicleType
    }
    //交管车辆类型
    if (seat < 6) {
        vehicleType = "01"
    } else if (seat < 10) {
        vehicleType = "02"
    } else if (seat >= 10 && seat < 20) {
        vehicleType = "03"
    } else if (seat >= 20 && seat < 36) {
        vehicleType = "04"
    } else if (seat >= 36) {
        vehicleType = "05"
    }
    vehicleType
}

/**
 * <AUTHOR>
 * @Description //解析雷达标志按钮返回结果
 * @Date 2019-12-16 19:09
 * @Param [resultList, jyCode]
 * @return java.lang.Object
 */
def static getResultByResultList(resultList, jyCode) {

    if (resultList.size() == 1) {
        return resultList[0]
    }
    resultList = resultList.sort {
        resultList.actualValue
    }
    resultList.find {
        if (it?.hyVehicleCode && it?.hyVehicleCode == jyCode) {
            it
        }
    }
}

def static getUserDefinedException(String msg) {
    print "测试3"
    throw new InsReturnException(Others, msg)
}
/**
 * 根据身份证获取生日 最后一位是 奇数 返回 1
 * @param idCard
 */
def static String getBirthDate(String idCard) {
    def JOINER = "-"
    def buffer = new StringBuffer()
    buffer.append(idCard.substring(6, 10)).append(JOINER).append(idCard.substring(10, 12)).append(JOINER).append(idCard.substring(12, 14))
    return buffer.toString()
}
/**
 * 根据身份证获取性别 最后一位是 奇数 返回 1
 * @param idCard
 * @return
 */
def static String getGender(String idCard) {
    int value = Integer.valueOf(idCard[-2..-2]).intValue()
    return value & 1
}

def static makeDataMap(person, config, entity, type = null) {
    return [
            certiCode     : person.idCard, //证件号码
            customerType  : "2", //todo 需要区分团体车投保人类型
            name          : person.name,
            gender        : getGender(person?.idCard as String), //性别 0 男 | 1 女
            issuer        : config.appPersonIssuer ?: "随县公安局", //发证机关 投保人发证机关,
            nation        : person.idCardType == 0 ? '汉族' : '',//民族     "idCardType":0,
            certiStartDate: config.appPersonCertiStartDate ?: '2019-05-19',//证件有效期起始
            certiEndDate  : config.appPersonCertiEndDate ?: '2039-05-19',//证件有效期截止日期
            birthDate     : getBirthDate(person?.idCard as String),
            address       : (person?.address ?: entity.misc?.supplyParam?.applicantAddress as String).replaceAll("\\|[0-9]+#", ""),
            telephone     : "",
            type          : type ?: 1,//holderVo 1 insureVo 2
    ]
}

def static baseDataMap(type = null) {
    return [
            certiCode     : "",
            customerType  : "1",
            name          : "",
            gender        : "",
            issuer        : "",
            nation        : "",
            certiStartDate: "",
            certiEndDate  : "",
            birthDate     : "",
            address       : "",
            telephone     : "",
            type          : type ?: "1",
    ]
}

def static String getSupplyParamValue(List supplyParam, Map tempValues, String key) {
    if (supplyParam) {
        if (!tempValues.supplyParamMap) {
            tempValues.supplyParamMap = rowToMap(supplyParam as List)
        }
        return (tempValues.supplyParamMap as Map)."$key"
    }
    return ""
}

static Map rowToMap(list) {
    list.collectEntries { [(it.itemcode): it.itemvalue] }
}

def makeInvocable() {
    //本地用
    //def path = "src/main/groovy/taipingyang/robot/js/" + "getRateForNewEnergy.js"
    //生产用
    //def path = "./taipingyang/robot/js" + "getRateForNewEnergy.js"
    String path = FileUtil.readResource("rateNew.js", common_2011.class, true);
    ScriptEngineManager scriptEngineManager = new ScriptEngineManager()
    ScriptEngine engine = scriptEngineManager.getEngineByName("js")
    engine.eval(path)
    Invocable invocable = (Invocable) engine;
    invocable
}

/**
 * 证件类型
 * @param cardtype
 * @return
 */
def getCardType(enquiry, cardtype) {
    def cardTypeReturn = ""
    def cardtypeMap = [:]
    cardtypeMap.put("0", "1")//身份证
    cardtypeMap.put("3", "3")//军官证士兵证
    cardtypeMap.put("4", "2")//护照
    cardtypeMap.put("5", "15")//港澳回乡证
    cardtypeMap.put("6", "6")//组织代码证
    cardtypeMap.put("7", "5")//团体其他
    cardtypeMap.put("8", "18")//社会信用代码
    cardtypeMap.put("9", "18")//税务
    cardtypeMap.put("10", "18")//营业执照
    if (cardtype?.toString()) {
        if (cardtypeMap.get(cardtype?.toString())) {
            cardTypeReturn = cardtypeMap.get(cardtype?.toString())
        }
    }
    //北京和上海 特殊转换一下代码
    if (["110000", "110100", "310000"].contains(enquiry?.order?.insureArea?.city?.toString())) {
        if ("8".equals(cardtype?.toString())) {
            if (["110000", "110100"].contains(enquiry?.order?.insureArea?.city?.toString())) {
                cardTypeReturn = "18" //统一社会信用代码
            } else {
                cardTypeReturn = "11" //营业执照
            }
        }
        if ("9".equals(cardtype?.toString())) {
            cardTypeReturn = "8"//税务
        }
        if ("10".equals(cardtype?.toString())) {
            cardTypeReturn = "11"//营业执照
        }
    }
    cardTypeReturn
}


def dateOperator(String addType, String operator, String addDate, Date date) {
    char operatorChar = '+';
    if ("-".equals(operator)) {
        operatorChar = '-';
    }
    if ("*".equals(operator)) {
        operatorChar = '*';
    }
    if ("/".equals(operator)) {
        operatorChar = '/';
    }
    return dateOperator(addType, operatorChar, addDate, date);
}

def dateOperator(String addType, char operator, String addDate, Date date) {
    int year, month, day;
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    year = calendar.get(Calendar.YEAR);
    month = calendar.get(Calendar.MONTH);
    day = calendar.get(Calendar.DAY_OF_YEAR);
    switch (addType) {
        case "Y":
            operatorDate(calendar, Calendar.YEAR, operator, Integer.valueOf(addDate));
            break;
        case "M":
            operatorDate(calendar, Calendar.MONTH, operator, Integer.valueOf(addDate));
            break;
        case "D":
            operatorDate(calendar, Calendar.DAY_OF_YEAR, operator, Integer.valueOf(addDate));
            break;
        default:
            break;
    }
    date = calendar.getTime();
    return date;
}

def getDate(String date, String type) throws Exception {
    String[] s;
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    Date myDate = sdf.parse(date);
    if (-1 != type.indexOf("+")) {
        s = type.split("[+]");
        return dateOperator(s[0], '+', s[1], myDate);
    } else if (-1 != type.indexOf("-")) {
        s = type.split("-");
        return dateOperator(s[0], '-', s[1], myDate);
    } else {
        return null;
    }

}

def operatorDate(Calendar calendar, int operType, char operator, int b) {
    int countDay = operator1(0, operator, b);
    calendar.add(operType, countDay);
    return calendar;
}

def operator1(int a, char opeartor, int b) {
    int sum = 0;
    switch (opeartor) {
        case '+':
            sum = a + b;
            break;
        case '-':
            sum = a - b;
            break;
        case '*':
            sum = a * b;
            break;
        case '/':
            sum = a / b;
            break;
    }
    return sum;
}

def getTimeDifference(String strDate, String endDate) {
    (int) ChronoUnit.DAYS.between(LocalDate.parse(strDate), LocalDate.parse(endDate))
}

def findSuiteFromApplyJson(applyJson, suiteCode) {
    applyJson = JSON.parseObject(applyJson as String)
    def baseSuiteInfo = applyJson.getJSONObject('baseSuiteInfo')
    def bizSuiteInfo = baseSuiteInfo.getJSONObject('bizSuiteInfo')
    def suites = bizSuiteInfo.getJSONArray('suites')
    return suites.find({ suite -> suite['code'] == suiteCode })
}

'2'