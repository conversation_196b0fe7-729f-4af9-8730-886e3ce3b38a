package taipingyang.robot


import com.alibaba.fastjson.JSON
import common.scripts.BaseScript_Http_Enq
import common.scripts.ScriptUtil
import groovy.transform.BaseScript
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotCar_2011

@BaseScript BaseScript_Http_Enq _

def reqBody = RobotCar_2011.makeQuickSaveReqBody(autoTask, entity)
String requestBody = JSON.toJSONString(reqBody)
requestBody = ScriptUtil.chineseToUnicode(requestBody)
autoTask.tempValues.put('saveParam', requestBody)
String saveParam = autoTask.tempValues.saveParam as String

head Robot2011Util.getDefaultHead(tempValues, config)

autoTask.params.clear()
Robot2011Util.genBody(tempValues, saveParam)