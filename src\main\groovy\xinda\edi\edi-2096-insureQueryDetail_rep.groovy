package xinda.edi

import com.cheche365.bc.exception.InsReturnException
import common.common_all
import xinda.edi.util.GuoRenUtil

GuoRenUtil.getResponse(root, enquiry)
//重新回写
if(!enquiry.carInfo.carModelName) {
    throw new InsReturnException('carModelName 空，停止回写')
}
tempValues.isNewEnergy = new common_all().checkEnergyType(enquiry.carInfo.carModelName, enquiry.carInfo.plateNum) != '0'
def data = root['RESPONSE_BODY']['data']

GuoRenUtil.writeBackInfo(data, enquiry, 2, tempValues.isNewEnergy, tempValues.nonMotorPremium)
