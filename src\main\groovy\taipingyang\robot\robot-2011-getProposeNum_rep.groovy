package taipingyang.robot


import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.task.AutoTask
import taipingyang.robot.module.Robot2011Util

AutoTask autoTask = autoTask
Enquiry entity = (Enquiry) autoTask?.taskEntity;

JSONObject queryQuotationPolicyResultObj = Robot2011Util.initResp(autoTask.tempValues)(autoTask.backRoot)

JSONArray jsonArray = queryQuotationPolicyResultObj.getJSONArray("result");

for (int i = 0; i < jsonArray.size(); i++) {
    String productType = jsonArray.getJSONObject(i).getString("productType")
    //商业险
    if ("11024400".equals(productType)) {
        entity.bizProposeNum = jsonArray.getJSONObject(i).getString("insuredNo")
    } else if ("11022400".equals(productType)) {//交强险
        entity.efcProposeNum = jsonArray.getJSONObject(i).getString("insuredNo")
    }
}
//bug 15324 【生产环境】广东太保精灵，机构想把精灵暂存成功返回的报价单号改成投保单号
def areaFlag = ["广州", "广州市"].contains(autoTask.configs.areaComCode.toString())
if (["广州", "广州市"].contains(autoTask.configs.areaComCode.toString())) {
    jsonArray.each {

    }

}
//回写一下非车总金额
if (entity.misc?.nonMotor) {
    entity.totalCharge = entity.totalCharge + new BigDecimal(entity.misc?.nonMotor?.discountCharge?.toString() ?: '0')
    entity.misc.totalCharge = entity.misc.totalCharge + new BigDecimal(entity.misc?.nonMotor?.discountCharge?.toString() ?: '0')
}

//核保暂存回写新非车险
if (jsonArray) {
    jsonArray.each {
        if (it.policyType == "0") {
            entity?.misc?.nonMotor?.accidentProposeCode = it.insuredNo
            if (areaFlag) {
                entity.bizProposeNum = it?.insuredNo
            }
        }
        if (areaFlag && it.policyType == "1") {
            entity.efcProposeNum = it?.insuredNo
        }
    }
}
