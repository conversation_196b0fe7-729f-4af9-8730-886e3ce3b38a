package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.task.AutoTask
import groovy.transform.Field
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import taipingyang.robot.module.Robot2011Util

import java.nio.charset.StandardCharsets
import java.time.LocalDate

@Field private static final Logger log = LoggerFactory.getLogger('robot-2011-policyDownload')

AutoTask autoTask = autoTask
Enquiry entity = (Enquiry) autoTask?.taskEntity;
def result = autoTask.tempValues.policyDownloadResult
def accidentApplyJson = JSON.parseObject(autoTask.applyJson)
//太保只能通过保单号查询下载
String queryNoBI = accidentApplyJson.sq.bizPolicyNo
String queryNoCI = accidentApplyJson.sq.efcPolicyNo
def queryNoNonMotor = entity?.misc?.nonMotor?.accidentPolicyCode ?: ""
if (!queryNoBI && !queryNoCI) {
    throw new InsReturnException("无保单号无法下载！")
}
def pathMap = [
        riverAttachment: "river_attachment",
        insuranceCode  : "2011",//保司
        provinceCode   : entity.order.insureArea.province ?: "-9999",//省份
        todayString    : LocalDate.now().toString(),//上传日期？
]

def cerNo = autoTask.tempValues.cerNo ?: (autoTask.tempValues.cerNo = result.result.holdVo.holderCerNo)
if (cerNo) {
    try {
        log.info('Cerno 解密内容：{}', cerNo)
        cerNo = Robot2011Util.rsaDecode(cerNo as String)
        log.info('Cerno 解密结果：{}', cerNo)
    } catch (e) {
        log.error('解析失败', e)
    }
}
if ((autoTask.configs.areaComCode as String) == "广州市" || (autoTask.configs.areaComCode as String) == "北京") {
    cerNo = entity.order.insurePerson.idCard
}

cerNo = URLEncoder.encode(Robot2011Util.rsaEncode(cerNo as String), StandardCharsets.UTF_8)

def robot_2011_special_util = new robot_2011_special_util()

if (queryNoBI) {
    robot_2011_special_util.dddd(autoTask.httpClient, queryNoBI, cerNo, entity, "biz", pathMap)
}

if (queryNoCI) {
    robot_2011_special_util.dddd(autoTask.httpClient, queryNoCI, cerNo, entity, "efc", pathMap)
}
if (queryNoNonMotor) {
    robot_2011_special_util.dddd(autoTask.httpClient, queryNoNonMotor, cerNo, entity, "nonMotor", pathMap)
}
