package renbao.robot

import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.utils.sender.HttpSender
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import org.apache.http.impl.client.CloseableHttpClient

import java.time.LocalDate

import static renbao.common.Constants.SUCCESS_CODE
import static renbao.robot.common_market.header
import static renbao.robot.common_market.hostPrefix


@BaseScript BaseScript_Http_Enq _


def commonMarket = new common_market()
def policyNo = entity?.bizProposeNum ?: entity?.efcProposeNum
def encryptedPolicyNo = commonMarket.marketEncrypt(policyNo)
def licenseNo = order.carInfo.plateNum
def areaAbbreviation = config['areaAbbreviation']
def header = header(autoTask)
def query = hostPrefix(autoTask) + "/khyx/newFront/qth/proposalsearch/query.do?prpallProposalRequestBody.licenseNo=${URLEncoder.encode(licenseNo, 'utf-8')}&prpallProposalRequestBody.businessNo=&prpallProposalRequestBody.policyNo=${URLEncoder.encode(encryptedPolicyNo, 'utf-8')}&prpallProposalRequestBody.insuredName=&prpallProposalRequestBody.frameNo=&prpallProposalRequestBody.operateDateStart=${LocalDate.now().plusMonths(-1).toString()}&prpallProposalRequestBody.operateDate=${LocalDate.now().toString()}&prpallProposalRequestBody.underWriteFlag=99&prpallProposalRequestBody.riskCode=1&prpallProposalRequestBody.queryComCode=&page=1&rows=10"
def result = HttpSender.doGet(autoTask.httpClient as CloseableHttpClient, query, header as Map<String, String>, null, "UTF-8",null, false)
def resultObj = JSONObject.parseObject(result as String)
assertTrue("支付申请失败：" + resultObj.toJSONString(), SUCCESS_CODE == resultObj['statusText'])
assertNotEmpty("支付申请失败：" + resultObj.toJSONString(), resultObj['data']['list'])
def quotationId = resultObj['data']['list'][0]['quotationId']
def relationUPolicyInfo = resultObj['data']['list'][0]['relationUPolicyInfo'] ?: ''
def relationProposalNo = policyNo.startsWith("TDAA") ? (entity?.efcProposeNum ?: '') : ''
def querySystemConfigByConfigCode = (autoTask?.configs?.hostPrefix ?: ("http://www." + areaAbbreviation + ".yxgl-picc.cn:9000")) + "/khyx/newFront/tm/common/tmtsystemconfig/querySystemConfigByConfigCode.do?configCode=carYSFControl"
HttpSender.doGet(autoTask.httpClient as CloseableHttpClient, querySystemConfigByConfigCode, header as Map<String, String>, null, "UTF-8",null, false)
def getsumPremiumNew = (autoTask?.configs?.hostPrefix ?: ("http://www." + areaAbbreviation + ".yxgl-picc.cn:9000")) + "/khyx/newFront/qth/price/getsumPremiumNew.do?policyNo=${policyNo}&relationUPolicyInfo=${URLEncoder.encode(relationUPolicyInfo as String, 'utf-8')}&proposalNoDDA=&quotationId=${quotationId}&ddaSumPremium=0&relationProposalNo=${relationProposalNo}"
result = HttpSender.doGet(autoTask.httpClient as CloseableHttpClient, getsumPremiumNew, header as Map<String, String>, null, "UTF-8",null, false)
resultObj = JSONObject.parseObject(result as String)
assertTrue("支付申请失败：" + resultObj.toJSONString(), SUCCESS_CODE == resultObj['statusText'])
def data = resultObj.getJSONObject('data')
def ciSum = data.getString('ciSum') ?: '0'
def biSum = data.getString('biSum') ?: '0'
def carShipTax = data.getString('carShipTax') ?: '0'
def joinSum = data.getString('joinSum') ?: '0'
def total = new BigDecimal(ciSum).add(new BigDecimal(biSum)).add(new BigDecimal(carShipTax)).add(new BigDecimal(joinSum))
def policyList
if (entity?.bizProposeNum ) {
    policyList = entity?.bizProposeNum
}
if (entity?.efcProposeNum) {
    policyList = policyList ? policyList + '%2C' + entity?.efcProposeNum : entity?.efcProposeNum
}
if (entity?.misc?.nonMotor?.accidentProposeCode) {
    policyList = policyList + '%2C' + entity?.misc?.nonMotor?.accidentProposeCode
}
def orderPay = (autoTask?.configs?.hostPrefix ?: ("http://www." + areaAbbreviation + ".yxgl-picc.cn:9000")) + "/khyx/newFront/qth/price/orderPay.do?policylist=${policyList}&policyfee=${total.toString()}&agentCode=${autoTask?.configs?.agentCode}&changeOnline=1" as String
//公户支付和个人支付有区别，公户需要手动选择支付方式目前人保支持四种（微信6，支付宝7，云闪付ysf，银联刷卡），个人支付是统一的码可以用微信支付宝云闪付直接扫
if ([6, 8, 9, 10].contains(entity.order.insurePerson.idCardType) || entity.order.insureArea.province == '440000') {
    def payType = 6
    if (autoTask?.tempValues['paytype'] && autoTask?.tempValues['paytype'] != 'wqrcodeB64') {
        payType = 7
    }
    orderPay = (autoTask?.configs?.hostPrefix ?: ("http://www." + areaAbbreviation + ".yxgl-picc.cn:9000")) + "/khyx/newFront/qth/price/orderPay.do?policylist=${policyList}&policyfee=${total.toString()}&payType=${payType}&changeOnline=0&ishbfq=0" as String
}

head(header)

tempValues['orderPay'] = orderPay



