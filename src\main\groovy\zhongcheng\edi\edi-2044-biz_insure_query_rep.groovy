package zhongcheng.edi

import com.cheche365.bc.exception.InsReturnException

import static zhongcheng.edi.edi_2044_common_new.formatTransferDate

def root = root
def edi_2044_common_new = new edi_2044_common_new()
def script = new edi_2044_common()
def responseCode = root?.HeadDto?.ResponseCode?.toString()
def errorCode = root?.HeadDto?.ErrorCode?.toString()
def transMessage = root?.HeadDto?.TransMessage?.toString() ?: "众城EDI交强险精确报价接口调用无返回"
if (!"0000".equals(errorCode)) {
    throw new InsReturnException(transMessage)
}
if (root?.BodyDto?.MainDto?.PolicyNo?.toString()) {
    enquiry?.SQ?.bizPolicyCode = root?.BodyDto?.MainDto?.PolicyNo?.toString()
}
Map enquiry = enquiry
def SQ = enquiry?.SQ
if (!SQ) {
    SQ = [:]
    enquiry.put("SQ", SQ)
}


//商业险起始日期
String startDate = root?.BodyDto?.MainDto?.StartDate?.toString()
if (startDate) {
    def EffectDate = edi_2044_common_new.TPYDateTimeToBC(startDate + "00")
    enquiry?.baseSuiteInfo?.bizSuiteInfo?.start = EffectDate
    //商业险终止日期
    String endDate = root?.BodyDto?.MainDto?.EndDate?.toString()
    def InvalidDate = edi_2044_common_new.TPYDateToBC(endDate.substring(0, 8)) + " 23:59:59"
    enquiry?.baseSuiteInfo?.efcSuiteInfo?.end = InvalidDate
}


def sumDiscount = root?.BodyDto?.MainDto?.SumDiscount?.toString() ? root?.BodyDto?.MainDto?.SumDiscount?.toDouble() : 0
def totalStandardPremium = root?.BodyDto?.MainDto?.TotalStandardPremium?.toString() ? root?.BodyDto?.MainDto?.TotalStandardPremium?.toDouble() : 0
def orgCharge = (totalStandardPremium?.toBigDecimal() + sumDiscount?.toBigDecimal())?.toDouble()  //原始保费

enquiry?.baseSuiteInfo?.bizSuiteInfo?.orgCharge = orgCharge
enquiry?.baseSuiteInfo?.bizSuiteInfo?.discountCharge = totalStandardPremium
SQ.put("bizCharge", totalStandardPremium)

def efcCharge = SQ?.efcCharge ?: 0   //交强险
def taxCharge = SQ?.taxCharge ?: 0   //车船税
SQ.put("totalCharge", (SQ?.bizCharge?.toBigDecimal() + efcCharge?.toBigDecimal() + taxCharge?.toBigDecimal())?.toDouble())

def suites = enquiry?.baseSuiteInfo?.bizSuiteInfo?.suites as List
def suiteMap = [:]
suites?.each {
    if (["RoadsideService", "DesignatedDriving", "VehicleInspection","SendForInspection"].contains(it?.code?.toString())) {
        suiteMap.put(it?.code, it.amount)
    }
}
if (suites) {
    //适配统一定价 清除原始的商业险结构体  保司返回什么拿什么
    suites.clear()
}
root?.BodyDto?.ItemKindDtoList?.ItemKindDto?.each {
    def policyList = edi_2044_common_new.ZhongChengToBCcodeList(it?.CoverageCode?.toString())
    if (policyList) {
        //标准保费
        def basicPremium = new BigDecimal(it?.BasicPremium?.toString() ?: "0").setScale(2, BigDecimal.ROUND_HALF_UP)
        //应缴保费
        def standardPremium = new BigDecimal(it?.Premium?.toString() ?: "0").setScale(2, BigDecimal.ROUND_HALF_UP)
        //险别折扣
        def discountSuit = new BigDecimal(it?.Discount?.toString() ?: "1").setScale(5, BigDecimal.ROUND_HALF_UP)
        basicPremium = standardPremium.divide(discountSuit, 2, BigDecimal.ROUND_HALF_UP)
        def sumLimit = new BigDecimal(it?.SumLimit?.toString() ?: "0")
        if (["Passenger", "CFMDPassenger"].contains(policyList.get(0))) {
            def seatP = enquiry?.carInfo?.seatCnt?.toInteger() - 1
            sumLimit = sumLimit.divide(new BigDecimal(seatP?.toString()))
        }
        if (["RoadsideService", "DesignatedDriving", "VehicleInspection","SendForInspection"].contains(policyList.get(0))) {
//            sumLimit = new BigDecimal(it?.Quantity?.toString() ?: "0").setScale(0, BigDecimal.ROUND_HALF_UP)
            sumLimit = suiteMap.get(policyList.get(0))
        }
        def suit = [:]
        suit.put("code", policyList.get(0))
        suit.put("name", policyList.get(1))
        suit.put("amount", sumLimit)
        suit.put("orgCharge", basicPremium)
        suit.put("discountRate", discountSuit)
        suit.put("discountCharge", standardPremium)
        suites.add(suit)
    }

}
enquiry?.baseSuiteInfo?.bizSuiteInfo?.suites = suites

//保险关系人信息列表
def InsuredDtoList = root?.BodyDto?.InsuredDtoList?.InsuredDto;
Integer n = InsuredDtoList?.size()

for (int i = 0; i < n; i++) {
    def InsuredDto = InsuredDtoList?.getAt(i)
    String InsuredFlag = InsuredDto?.InsuredFlag?.toString()       //2投保人，1被保人，3车主
    if (InsuredFlag == "1") {
        enquiry?.insuredPersonList[0]?.name = InsuredDto?.InsuredName?.toString() ?: ""
        enquiry?.insuredPersonList[0]?.idCardType = script.formatIdCardType(InsuredDto?.CertiType?.toString() ?: "")
        enquiry?.insuredPersonList[0]?.idCard = InsuredDto?.CertiCode?.toString() ?: ""
        enquiry?.insuredPersonList[0]?.sex = script.formatGender(InsuredDto?.Gender?.toString() ?: "");
        enquiry?.insuredPersonList[0]?.email = InsuredDto?.Email?.toString() ?: ""
        enquiry?.insuredPersonList[0]?.mobile = InsuredDto?.MobileNo?.toString() ?: ""
        enquiry?.insuredPersonList[0]?.address = InsuredDto?.Address?.toString() ?: ""
        enquiry?.insuredPersonList[0]?.phone = InsuredDto?.PhoneNumber?.toString() ?: ""

    } else if (InsuredFlag == "2") {
        //投保人
        enquiry?.insurePerson?.name = InsuredDto?.InsuredName?.toString() ?: ""
        enquiry?.insurePerson?.idCardType = script.formatIdCardType(InsuredDto?.CertiType?.toString() ?: "")
        enquiry?.insurePerson?.idCard = InsuredDto?.CertiCode?.toString() ?: ""
        enquiry?.insurePerson?.sex = script.formatGender(InsuredDto?.Gender?.toString() ?: "")
        enquiry?.insurePerson?.email = InsuredDto?.Email?.toString() ?: ""
        enquiry?.insurePerson?.mobile = InsuredDto?.MobileNo?.toString() ?: ""
        enquiry?.insurePerson?.address = InsuredDto?.Address?.toString() ?: ""
        enquiry?.insurePerson?.phone = InsuredDto?.PhoneNumber?.toString() ?: ""
    } else if (InsuredFlag == "3") {
        //车主
        enquiry?.carOwnerInfo?.name = InsuredDto?.InsuredName?.toString() ?: ""
        enquiry?.carOwnerInfo?.idCardType = script.formatIdCardType(InsuredDto?.CertiType?.toString() ?: "")
        enquiry?.carOwnerInfo?.idCard = InsuredDto?.CertiCode?.toString() ?: ""
        enquiry?.carOwnerInfo?.sex = script.formatGender(InsuredDto?.Gender?.toString() ?: "")
        enquiry?.carOwnerInfo?.email = InsuredDto?.Email?.toString() ?: ""
        enquiry?.carOwnerInfo?.mobile = InsuredDto?.MobileNo?.toString() ?: ""
        enquiry?.carOwnerInfo?.address = InsuredDto?.Address?.toString() ?: ""
        enquiry?.carOwnerInfo?.phone = InsuredDto?.PhoneNumber?.toString() ?: ""
    }
}


//车辆车型车价信息
def ItemCarDto = root?.BodyDto?.ItemCarDto;

if (ItemCarDto?.ActualValue?.toString()) {
    enquiry?.carInfo?.plateNum = ItemCarDto?.LicensePlateNo?.toString() ?: ""
    enquiry?.carInfo?.plateColor = script.formatPlateColor(ItemCarDto?.LicensePlateColorCode?.toString() ?: "")
    enquiry?.carInfo?.engineNum = ItemCarDto?.EngineNo?.toString() ?: ""
    enquiry?.carInfo?.vin = ItemCarDto?.Vin?.toString() ?: "";
    enquiry?.carInfo?.carModelName = ItemCarDto?.ModelName?.toString() ?: "";
    enquiry?.carInfo?.useProps = script.formatUseProps(ItemCarDto?.MotorUsageTypeCode?.toString() ?: "");
    enquiry?.carInfo?.modelLoad = ItemCarDto?.Tonnage?.toString() ? ItemCarDto?.Tonnage?.toBigDecimal() * 1000 : 0;
    enquiry?.carInfo?.displacement = ItemCarDto?.Haulage?.toString() ? ItemCarDto?.Haulage?.toDouble() : 0;
    enquiry?.carInfo?.price = ItemCarDto?.PurchasePrice?.toString() ? ItemCarDto?.PurchasePrice?.toBigDecimal() : 0;
    enquiry?.carInfo?.isTransfer = ItemCarDto?.ChgOwnerFlag?.toString() == "1" ? true : false;
    enquiry?.carInfo?.transferDate = formatTransferDate(ItemCarDto?.TransferDate?.toString())
    enquiry?.carInfo?.fullLoad = ItemCarDto?.WholeWeight?.toString() ? ItemCarDto?.WholeWeight?.toDouble()?.toInteger() : 0;
    enquiry?.carInfo?.rbCode = ItemCarDto?.ModelCode?.toString() ?: "";
    enquiry?.carInfo?.jyCode = ItemCarDto?.ModelCode?.toString() ?: "";
    enquiry?.carInfo?.carBrandName = ItemCarDto?.Brand?.toString() ?: "";
    enquiry?.carInfo?.plateType = script.formatPlateType(ItemCarDto?.LicensePlateType?.toString() ?: "");
}
