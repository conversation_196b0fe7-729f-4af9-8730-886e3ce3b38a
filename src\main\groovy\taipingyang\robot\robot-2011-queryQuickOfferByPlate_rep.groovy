package taipingyang.robot

import cn.hutool.core.util.ObjectUtil
import com.alibaba.fastjson.JSONObject
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import groovy.transform.Field
import taipingyang.robot.module.Robot2011Util

@BaseScript BaseScript_Http_Enq _

@Field JSONObject resp = null

check Robot2011Util.serializer(tempValues), {
    // 初始化
    resp = it
    if (ObjectUtil.isEmpty(resp)) {
        fail('续期信息查询失败')
    }

    def code = resp?.message?.code
    def msgStr = resp?.message?.message
    if ('success' != code) {
        log.warn('续期信息查询失败:{}', msgStr?.toString())
        def misc = entity.misc
        def errorType, errorMessage

        switch (true) {
            case { msgStr.contains('未找到潜客数据') || msgStr.contains('没有查询到续保信息') }:
                errorType = '无上年保单'
                errorMessage = '无上年续保单'
                break
            case { msgStr.contains('该网点的机构与该车辆的潜客数据机构不一致，无权查询') }:
                errorType = '权限不足'
                errorMessage = '权限不足'
                break
            case { msgStr.contains('系统繁忙') }:
                errorType = '出单系统状态异常'
                errorMessage = '系统繁忙'
                break
            default:
                errorType = null
                errorMessage = '查询失败'
        }

        misc.put('errorMessage', msgStr)
        misc.put('errorType', errorType)
        fail(errorMessage)
    }
}

output resp?.getJSONObject('result')
