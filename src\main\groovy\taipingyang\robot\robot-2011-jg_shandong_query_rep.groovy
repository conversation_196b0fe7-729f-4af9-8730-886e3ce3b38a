package taipingyang.robot

import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import taipingyang.robot.module.Robot2011Util

@BaseScript BaseScript_Http_Enq _

def querySDVehicleInfoResult = Robot2011Util.initResp(tempValues)(autoTask?.backRoot)

autoTask.tempValues?.plateTypeJg = querySDVehicleInfoResult?.result?.coverField?.plateType
autoTask.tempValues?.tpyRiskflagName = querySDVehicleInfoResult?.result?.coverField?.illegalBehavior
autoTask.tempValues?.vehicleStyle = querySDVehicleInfoResult?.result?.notCoverField?.vehicleStyle
autoTask.tempValues?.pmVehicleUsage = querySDVehicleInfoResult?.result?.notCoverField?.pmVehicleUsage
autoTask.tempValues?.isQueryJg = "1"


