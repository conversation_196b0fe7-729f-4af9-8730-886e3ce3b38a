package guoshou.edi

import cn.hutool.core.date.DateUtil
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import common.common_all
import guoshou.constants.GuoShouConstants

import java.lang.reflect.Method
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeFormatterBuilder

//保险code转换，目前新能源与非新能源code用的一套，但是实际上这是两个东西，所以要拆分成两套转换，不可以写成一种，防止有变化解耦
def getProductIdByKey(code, energyFlag) {//"自己公司code": ["三方code","自己公司描述", "三方描述"]

    if(energyFlag.equals("1")){
        switch (code) {
            case "VehicleDamage": ["413","车损险", "新能源汽车损失保险"]; break
            case "ThirdParty": ["414","三者险", "新能源汽车第三者责任保险"]; break
            case "Driver": [ '415', '司机责任险', '新能源汽车车上人员责任保险(驾驶人)']; break
            case "Passenger": ['416', '乘客责任险', '新能源汽车车上人员责任保险(乘客)']; break

            case "ANCVehicleDamage": ['458', '附加绝对免赔率特约条款(机动车损失保险)', '附加绝对免赔率特约条款（新能源汽车损失保险）']; break
            case "ANCThirdParty": ['459', '附加绝对免赔率特约条款(机动车第三者责任保险)', '附加绝对免赔率特约条款（新能源汽车第三者责任保险）']; break
            case "ANCDriver": ['460', '附加绝对免赔率特约条款(机动车车上人员责任保险(司机))', '附加绝对免赔率特约条款（新能源汽车车上人员责任保险-驾驶人）']; break
            case "ANCPassenger": ['461', '附加绝对免赔率特约条款(机动车车上人员责任保险(乘客))', '附加绝对免赔率特约条款（新能源汽车车上人员责任保险-乘客）']; break

            case "Wheel": ['435', '附加车轮单独损失险', '附加车轮单独损失险']; break
            case "ExtraDevice": ['436', '附加新增加设备损失险', '附加新增加设备损失险']; break
            case "Scratch": ['437', '附加车身划痕损失险', '附加车身划痕损失险']; break
            case "CompensationDuringRepair": ['439', '附加修理期间费用补偿险', '附加修理期间费用补偿险']; break
            case "GoodsOnVehicle": ['440', '车上货物责任险', '附加车上货物责任险']; break
            case "HolidayDouble": ['442', '附加法定节假日限额翻倍险', '附加法定节假日限额翻倍险']; break

            case "CFMDThirdParty": ['462', '精神损害抚慰金责任险（三者）', '附加精神损害抚慰金责任险（新能源汽车第三者责任保险）']; break
            case "CFMDDriver": ['463', '附加精神损害抚慰金责任险(机动车车上人员责任保险(司机))', '附加精神损害抚慰金责任险（新能源汽车车上人员责任保险-驾驶人）']; break
            case "CFMDPassenger": ['464', '附加精神损害抚慰金责任险(机动车车上人员责任保险(乘客))', '附加精神损害抚慰金责任险（新能源汽车车上人员责任保险-乘客）']; break

            case "NIHCThirdParty": ['465', '附加医保外用药责任险（三者）', '附加医保外医疗费用责任险（新能源汽车第三者责任保险）']; break
            case "NIHCDriver": ['466', '附加医保外用药责任险(机动车车上人员责任保险(司机))', '附加医保外医疗费用责任险（新能源汽车车上人员责任保险-驾驶人）']; break
            case "NIHCPassenger": ['467', '附加医保外用药责任险(机动车车上人员责任保险(乘客))', '附加医保外医疗费用责任险（新能源汽车车上人员责任保险-乘客）']; break

            case "NEGridBugDamage": ['468', '附加外部电网故障损失险', '附加外部电网故障损失险']; break
            case "NEChargerDamage": ['469', '附加自用充电桩损失保险', '附加自用充电桩损失保险']; break
            case "NEChargerDuty": ['470', '附加自用充电桩责任保险', '附加自用充电桩责任保险']; break

            case "RoadsideService": ['495', '道路救援服务特约条款', '附加新能源汽车道路救援服务特约条款']; break
            case "VehicleInspection": ['496', '车辆安全检测特约条款','附加新能源汽车车辆安全监测特约条款']; break
            case "DesignatedDriving": ['497', '代为驾驶服务特约条款', '附加新能源汽车代为驾驶服务特约条款']; break
            case "SendForInspection": ['498', '代为送检服务特约条款', '附加新能源汽车代为送检服务特约条款']; break
            default: [null, null, null]
        }
    }else{

        switch (code) {
            case "VehicleDamage": ["401","车损险", "机动车损失保险"]; break
            case "ThirdParty": ["402","三者险", "机动车第三者责任保险"]; break
            case "Driver": [ '403', '司机责任险', '机动车车上人员责任保险（驾驶人）']; break
            case "Passenger": ['404', '乘客责任险', '机动车车上人员责任保险（乘客）']; break

            case "Wheel": ['435', '附加车轮单独损失险', '附加车轮单独损失险']; break
            case "EngineDamageExclude": ['474', '发动机损坏除外特约条款', '附加发动机进水损坏除外特约条款']; break
            case "ANCVehicleDamage": ['431', '附加绝对免赔率特约条款(机动车损失保险)', '附加绝对免赔率特约险（机动车损失保险）']; break
            case "ANCThirdParty": ['432', '附加绝对免赔率特约条款(机动车第三者责任保险)', '附加绝对免赔率特约险（机动车第三者责任保险）']; break
            case "ANCDriver": ['433', '附加绝对免赔率特约条款(机动车车上人员责任保险(司机))', '附加绝对免赔率特约险（机动车车上人员责任保险-驾驶人）']; break
            case "ANCPassenger": ['434', '附加绝对免赔率特约条款(机动车车上人员责任保险(乘客))', '附加绝对免赔率特约险（机动车车上人员责任保险-乘客）']; break
            case "NIHCThirdParty": ['443', '附加医保外用药责任险（三者）', '附加医保外用药责任险（机动车第三者责任保险）']; break
            case "NIHCDriver": ['480', '附加医保外用药责任险(机动车车上人员责任保险(司机))', '附加医保外用药责任险（机动车车上人员责任保险-驾驶人）']; break
            case "NIHCPassenger": ['481', '附加医保外用药责任险(机动车车上人员责任保险(乘客))', '附加医保外用药责任险（机动车车上人员责任险保-乘客）']; break
            case "CFMDThirdParty": ['441', '精神损害抚慰金责任险（三者）', '附加精神损害抚慰金责任险（机动车第三者责任保险）']; break
            case "CFMDDriver": ['478', '附加精神损害抚慰金责任险(机动车车上人员责任保险(司机))', '附加精神损害抚慰金责任险（机动车车上人员责任保险-驾驶人）']; break
            case "CFMDPassenger": ['479', '附加精神损害抚慰金责任险(机动车车上人员责任保险(乘客))', '附加精神损害抚慰金责任险（机动车车上人员责任保险-乘客）']; break
            case "RoadsideService": ['444', '道路救援服务特约条款', '附加机动车道路救援服务特约条款']; break
            case "VehicleInspection": ['445', '车辆安全检测特约条款','附加机动车车辆安全监测特约条款']; break
            case "DesignatedDriving": ['446', '代为驾驶服务特约条款', '附加机动车代为驾驶服务特约条款']; break
            case "SendForInspection": ['447', '代为送检服务特约条款', '附加机动车代为送检服务特约条款']; break
            case "Scratch": ['437', '附加车身划痕损失险', '附加车身划痕损失险']; break
            case "CompensationDuringRepair": ['439', '附加修理期间费用补偿险', '附加修理期间费用补偿险']; break
            case "HolidayDouble": ['442', '附加法定节假日限额翻倍险', '附加法定节假日限额翻倍险']; break
            case "ExtraDevice": ['436', '附加新增加设备损失险', '附加新增加设备损失险']; break
            case "GoodsOnVehicle": ['440', '车上货物责任险', '附加车上货物责任险']; break
            default: [null, null, null]
        }
    }
}

//保险公司code对应自己公司转换 格式：保险公司code 公司code 公司描述  保险公司描述
def transThirdCodeToMySelf(code){
    switch (code){
    //新能源code
        case "413": ["VehicleDamage","车损险", "新能源汽车损失保险"]; break
        case "414": ["ThirdParty","三者险", "新能源汽车第三者责任保险"]; break
        case "415": [ 'Driver', '司机责任险', '新能源汽车车上人员责任保险(驾驶人)']; break
        case "416": ['Passenger', '乘客责任险', '新能源汽车车上人员责任保险(乘客)']; break

        case "458": ['ANCVehicleDamage', '附加绝对免赔率特约条款(机动车损失保险)', '附加绝对免赔率特约条款（新能源汽车损失保险）']; break
        case "459": ['ANCThirdParty', '附加绝对免赔率特约条款(机动车第三者责任保险)', '附加绝对免赔率特约条款（新能源汽车第三者责任保险）']; break
        case "460": ['ANCDriver', '附加绝对免赔率特约条款(机动车车上人员责任保险(司机))', '附加绝对免赔率特约条款（新能源汽车车上人员责任保险-驾驶人）']; break
        case "461": ['ANCPassenger', '附加绝对免赔率特约条款(机动车车上人员责任保险(乘客))', '附加绝对免赔率特约条款（新能源汽车车上人员责任保险-乘客）']; break

        case "462": ['CFMDThirdParty', '精神损害抚慰金责任险（三者）', '附加精神损害抚慰金责任险（新能源汽车第三者责任保险）']; break
        case "463": ['CFMDDriver', '附加精神损害抚慰金责任险(机动车车上人员责任保险(司机))', '附加精神损害抚慰金责任险（新能源汽车车上人员责任保险-驾驶人）']; break
        case "464": ['CFMDPassenger', '附加精神损害抚慰金责任险(机动车车上人员责任保险(乘客))', '附加精神损害抚慰金责任险（新能源汽车车上人员责任保险-乘客）']; break

        case "465": ['NIHCThirdParty', '附加医保外用药责任险（三者）', '附加医保外医疗费用责任险（新能源汽车第三者责任保险）']; break
        case "466": ['NIHCDriver', '附加医保外用药责任险(机动车车上人员责任保险(司机))', '附加医保外医疗费用责任险（新能源汽车车上人员责任保险-驾驶人）']; break
        case "467": ['NIHCPassenger', '附加医保外用药责任险(机动车车上人员责任保险(乘客))', '附加医保外医疗费用责任险（新能源汽车车上人员责任保险-乘客）']; break

        case "468": ['NEGridBugDamage', '附加外部电网故障损失险', '附加外部电网故障损失险']; break
        case "469": ['NEChargerDamage', '附加自用充电桩损失保险', '附加自用充电桩损失保险']; break
        case "470": ['NEChargerDuty', '附加自用充电桩责任保险', '附加自用充电桩责任保险']; break

        case "495": ['RoadsideService', '道路救援服务特约条款', '附加新能源汽车道路救援服务特约条款']; break
        case "496": ['VehicleInspection', '车辆安全检测特约条款','附加新能源汽车车辆安全监测特约条款']; break
        case "497": ['DesignatedDriving', '代为驾驶服务特约条款', '附加新能源汽车代为驾驶服务特约条款']; break
        case "498": ['SendForInspection', '代为送检服务特约条款', '附加新能源汽车代为送检服务特约条款']; break

    //汽油车code
        case "401": ["VehicleDamage","车损险", "机动车损失保险"]; break
        case "402": ["ThirdParty","三者险", "机动车第三者责任保险"]; break
        case "403": [ 'Driver', '司机责任险', '机动车车上人员责任保险（驾驶人）']; break
        case "404": ['Passenger', '乘客责任险', '机动车车上人员责任保险（乘客）']; break

        case "431": ['ANCVehicleDamage', '附加绝对免赔率特约条款(机动车损失保险)', '附加绝对免赔率特约险（机动车损失保险）']; break
        case "432": ['ANCThirdParty', '附加绝对免赔率特约条款(机动车第三者责任保险)', '附加绝对免赔率特约险（机动车第三者责任保险）']; break
        case "433": ['ANCDriver', '附加绝对免赔率特约条款(机动车车上人员责任保险(司机))', '附加绝对免赔率特约险（机动车车上人员责任保险-驾驶人）']; break
        case "434": ['ANCPassenger', '附加绝对免赔率特约条款(机动车车上人员责任保险(乘客))', '附加绝对免赔率特约险（机动车车上人员责任保险-乘客）']; break
        case "474": ['EngineDamageExclude', '发动机损坏除外特约条款', '附加发动机进水损坏除外特约条款']; break

        case "441": ['CFMDThirdParty', '精神损害抚慰金责任险（三者）', '附加精神损害抚慰金责任险（机动车第三者责任保险）']; break
        case "478": ['CFMDDriver', '附加精神损害抚慰金责任险(机动车车上人员责任保险(司机))', '附加精神损害抚慰金责任险（机动车车上人员责任保险-驾驶人）']; break
        case "479": ['CFMDPassenger', '附加精神损害抚慰金责任险(机动车车上人员责任保险(乘客))', '附加精神损害抚慰金责任险（机动车车上人员责任保险-乘客）']; break

        case "443": ['NIHCThirdParty', '附加医保外用药责任险（三者）', '附加医保外用药责任险（机动车第三者责任保险）']; break
        case "480": ['NIHCDriver', '附加医保外用药责任险(机动车车上人员责任保险(司机))', '附加医保外用药责任险（机动车车上人员责任保险-驾驶人）']; break
        case "481": ['NIHCPassenger', '附加医保外用药责任险(机动车车上人员责任保险(乘客))', '附加医保外用药责任险（机动车车上人员责任险保-乘客）']; break

        case "444": ['RoadsideService', '道路救援服务特约条款', '附加机动车道路救援服务特约条款']; break
        case "445": ['VehicleInspection', '车辆安全检测特约条款','附加机动车车辆安全监测特约条款']; break
        case "446": ['DesignatedDriving', '代为驾驶服务特约条款', '附加机动车代为驾驶服务特约条款']; break
        case "447": ['SendForInspection', '代为送检服务特约条款', '附加机动车代为送检服务特约条款']; break

    //公用code
        case "435": ['Wheel', '附加车轮单独损失险', '附加车轮单独损失险']; break
        case "436": ['ExtraDevice', '附加新增加设备损失险', '附加新增加设备损失险']; break
        case "437": ['Scratch', '附加车身划痕损失险', '附加车身划痕损失险']; break
        case "439": ['CompensationDuringRepair', '附加修理期间费用补偿险', '附加修理期间费用补偿险']; break
        case "440": ['GoodsOnVehicle', '车上货物责任险', '附加车上货物责任险']; break
        case "442": ['HolidayDouble', '附加法定节假日限额翻倍险', '附加法定节假日限额翻倍险']; break

        default: [null, null, null]
    }
}

//创建通用车主被保人投保人对象
static def setCustomerMess(enquiry, type, addressArray) {
    def customerMess = [:]
    def commonAll = new common_all()

    def sex
    if ("applicant" == type)  {
        // 投保人
        customerMess.customerType =  getNotCarOwnerUserType(enquiry?.insurePerson?.idCardType)
        customerMess.customerName = enquiry?.insurePerson?.name
        customerMess.identifyType = getIDCardType(enquiry?.insurePerson?.idCardType)[0]
        customerMess.identifyNumber = enquiry?.insurePerson?.idCard
        customerMess.identifyEffectiveStartDate = getIdentifyEffectiveStartDate(enquiry, "applicant")
        customerMess.identifyEffectiveEndDate = getIdentifyEffectiveEndDate(enquiry, "applicant")
        sex = getSex(enquiry?.insurePerson?.sex)
    }
    if ("insured" == type) {
        // 被保人
        customerMess.customerType = getNotCarOwnerUserType(enquiry?.insuredPersonList[0]?.idCardType)
        customerMess.customerName = enquiry?.insuredPersonList[0]?.name
        customerMess.identifyType = getIDCardType(enquiry?.insuredPersonList[0]?.idCardType)[0]
        customerMess.identifyNumber = enquiry?.insuredPersonList[0]?.idCard
        customerMess.identifyEffectiveStartDate = getIdentifyEffectiveStartDate(enquiry, "insured")
        customerMess.identifyEffectiveEndDate = getIdentifyEffectiveEndDate(enquiry, "insured")
        sex = getSex(enquiry?.insuredPersonList[0]?.sex)
    }
    if ("owner" == type) {
        // 车主
        customerMess.customerType = getCarUserType(enquiry?.carInfo?.carUserType)
        customerMess.customerName = enquiry?.carOwnerInfo?.name
        customerMess.identifyType = getIDCardType(enquiry?.carOwnerInfo?.idCardType)[0]
        customerMess.identifyNumber = enquiry?.carOwnerInfo?.idCard
        customerMess.identifyEffectiveEndDate = getIdentifyEffectiveEndDate(enquiry, "owner")
        customerMess.identifyEffectiveStartDate = getIdentifyEffectiveStartDate(enquiry, "applicant")
        sex = getSex(enquiry?.carOwnerInfo?.sex)
    }

    def flag
    if (type == "owner") {
        flag = enquiry?.carInfo?.carUserType == 0
    } else {
        def personalIdType=[0, 1, 2, 3, 4, 5, 7, 14, 19]
        flag = type == "insured" ?
                personalIdType.contains(enquiry?.insuredPersonList[0]?.idCardType) :
                personalIdType.contains(enquiry?.insurePerson?.idCardType)
    }

    if (flag) {
        // 个人车
        def plcCustomer = [
                "address"           : addressArray?.last(),
                "postalAddress"     : addressArray?.last(),
                "mobile"            : getSupply(enquiry, type + "Mobile"),
                "sex"               : sex,
                "citizenship"       : "CHN",
                "occupationCode"    : "9000000",
                "nationality"       : getNation(commonAll.getSupplyParam(enquiry, "applicantNation")),
                "signDepartmentName": commonAll.getSupplyParam(enquiry, "applicantIDCardIssuingAuthority"),
                "email"             : getSupply(enquiry, "applicantEmail"),
                "provinceCode"      : addressArray[0]?.toString(),
                "cityCode"          : addressArray[1].toString(),
                "districtCode"      : addressArray[2].toString(),
                "workType"          : "8",
                "workTypeMidClass"  : "800",
                "workTypeSmallClass": "80000",
        ]

        customerMess.plcCustomer = plcCustomer
    }else{
        // 企业车
        def plcGrpcustomer = [
                "linkerName"         : commonAll.getSupplyParam(enquiry, "liaisonName") ?: "",
                "postalAddress"      : addressArray?.last(),
                "mobile"             : getSupply(enquiry, type + "Mobile"),
                "organizationType"   : "3",
                "registeredPlaceCode": "CHN",
                "registyAddress"     : addressArray?.last(),
                "email"              : getSupply(enquiry, "applicantEmail"),
                "registAddCode"      : addressArray[0].toString(),
                "registCityCode"     : addressArray[1].toString(),
                "registDistrictCode" : addressArray[2].toString(),
                "ratingType"         : "340",
                "registeredCapital"  : "500000",
                "industrySCategory"  : "F51",
                "industryTCategory"  : "F514",
                "industryFCategory"  : "F5149",
        ]

        if (enquiry?.insArea?.province == "110000") {
            plcGrpcustomer.post = "100000"
        }

        if (enquiry?.insArea?.province == "510000") {
            plcGrpcustomer.industryCategory = "S9529" //其他社会团体
        }

        if ( type == "insurePerson" && enquiry?.insArea?.province == "320000") {
            plcGrpcustomer.payAccountName = enquiry?.insurePerson?.name
            plcGrpcustomer.relationshipCode = "99"
            plcGrpcustomer.remark = "自身"
        }

        customerMess.plcGrpcustomer = plcGrpcustomer
    }

    return customerMess
}


/**
 * 获取车主性质
 * @param param
 * @return
 */
static def getCarUserType(param){
    [
            0: "1", // 个人
            1: "2", // 企业
            2: "3", // 机关团体
    ].get(param) ?: ""
}

/**
 * 获取非车客户类型
 */
def getNonMotorCustomerType(idCardType) {
    [0, 1, 2, 3, 4, 5, 6, 7, 14].contains(idCardType) ? "1" : "2"
}

//投保和被保人属性信息，只要不是个人都归入组织不分企业机关
static def getNotCarOwnerUserType(param){
    def personalIdType=[0, 1, 2, 3, 4, 5, 7, 14, 19]
    return personalIdType.contains(param)?1:2
}

/**
 * 获取性别
 * @param param
 * @return
 */
static def getSex(param){
    [
            0: "1", // 男
            1: "2", // 女
    ].get(param) ?: "3" // 未知
}

static def setCommonDataForPost(config, businessNumber, transactionType) {
    [
            "policySort"     : config?.policySort,
            "operator"       : config?.operator,
            "operatorKey"    : config?.operatorKey,
            "handler1Code"   : config?.handler1Code,
            "comCode"        : config?.comCode,
            "transactionTime": getTimeByFormat(null),
            "transactionNo"  : UUID.randomUUID().toString().replace("-", ""),
            "businessNumber" : businessNumber,
            "ipAddress"      : "************",
            "transactionType": transactionType,
            "franchiserCode" : config.franchiserCode,
    ]
}

//格式化日期转为带T毫秒值时区的格式
static def getTimeByFormat(timeParam){
    def parse
    def pattern = DateTimeFormatter.ofPattern("yyyy-MM-dd\'T\'HH:mm:ss.SSS")
    if(timeParam==null){
        parse=LocalDateTime.now()
    }else {
        LocalDateTime oldFormatTime = LocalDateTime.parse(timeParam, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        parse = LocalDateTime.parse(oldFormatTime.format(pattern), pattern)
    }
    OffsetDateTime targetTime = OffsetDateTime.ofInstant(parse.atZone(ZoneId.systemDefault()).toInstant(), ZoneId.systemDefault())
    DateTimeFormatter targetFormat = new DateTimeFormatterBuilder().append(pattern).appendOffset("+HHMM", "+0000").toFormatter()
    def result= targetTime.format(targetFormat)
    return result
}

/**
 * 获取接口类型
 * @param serviceName
 * @return
 */
def getTransactionType(serviceName) {
    [
            "carQueryService"                  : ["G01", "车型查询服务"],
            "carActualPriceQueryService"       : ["G02", "车辆实际价值计算服务"],
            "premiumCalculateService"          : ["G03", "保费计算服务"],
            "proposalSaveService"              : ["G04", "投保单保存服务"],
            "autoAuditUploadService"           : ["G09", "影像上传服务"],
            "provisonalStatusQueryService"     : ["G06", "业务状态查询服务"],
            "policyQueryDetailService"         : ["G07", "业务详细信息查询服务"],
            "downReasonService"                : ["G11", "投保单下发修改信息查询服务"],
            "receiverCustomerIDCardInfoService": ["G05", "短信验证码获取及回填服务"],
            "carQueryServiceForBeiJing"        : ["G22", "北京交管查询接口"],
            "bjCarRecord"                      : ["G15", "北京新车备案接口"],
    ].get(serviceName) ?: ["", ""]
}

/**
 * 获取证件类型
 * @param param
 * @return
 */
static def getIDCardType(param){
    [
            0 : ["01", "居民身份证"],
            1 : ["02", "居民户口簿"],
            2 : ["05", "驾驶证"],
            3 : ["08", "军官证/警官证"],
            4 : ["18", "中国因私护照"],
            5 : ["17", "港澳居民来往内地通行证"],
            6 : ["07", "组织机构代码证"],
            7 : ["99", "其他个人证件"],
            8 : ["22", "统一社会信用代码"],
            9 : ["72", "税务登记证号"],
            10: ["73", "营业执照"],
            14: ["23", "港澳台居民身份证"],
            19: ["10", "外国人永久居住证"],
    ].get(param) ?: ["", ""]
}

/**
 * 获取车辆使用性质
 * @param param
 * @return
 */
static def getUseProps(param) {
    [
            1 : "8A", // 家庭自用
            2 : "9A", // 营业出租租赁
            3 : "9B", // 营业城市公交
            4 : "9C", // 营业公路客运
            6 : "9D", // 营业货运
            9 : "8D", // 非营业个人
            10: "8B", // 非营业企业
            11: "8C", // 非营业机关
            12: "8D", // 非营业货车
            17: "9A"  // 营业出租租赁
    ].get(param) ?: ""
}

/**
 * 获取车辆类型
 * @param value
 * @return
 */
static def getCarKindCode(value){
    [
            0 : "A0",
            1 : "A0",
            2 : "A0",
            3 : "A0",
            4 : "A0",
            5 : "A0",
            6 : "H0",
            7 : "A0",
            8 : "A0",
            9 : "A0",
            10: "A0",
            11: "A0",
            12: "H0",
            17: "A0"
    ].get(value) ?: ""
}

/**
 * 获取行驶证车辆类型
 * @param value
 * @return
 */
static def getVehicleStyle(value){
    [
            0 : ["载客汽车", "K21", "中型普通客车"],
            1 : ["载货汽车", "H21", "中型普通货车"],
            2 : ["半挂牵引车", "B21", "中型普通半挂车"],
            3 : ["低速载货汽车", "H51", "低速普通货车"],
            4 : ["挂车", "G21", "中型普通全挂车"],
            5 : ["客货两用车", "X99", "其它"],
            6 : ["集装箱", "X99", "其它"],
            7 : ["特种车", "S", "特种作业专用车"],
            8 : ["拖拉机", "T11", "大型轮式拖拉机"],
            9 : ["摩托车", "M11", "普通正三轮摩托车"],
            10: ["轻型摩托车", "M22", "轻便二轮摩托车"],
            11: ["侧三轮", "M15", "侧三轮摩托车"],
            12: ["运输型拖拉机", "T23", "手扶变形运输机"],
            13: ["轿车", "K33", "轿车"],
            14: ["越野车", "K32", "小型越野客车"],
            15: ["自卸汽车", "H27", "中型自卸货车"],
            16: ["牵引汽车", "Q21", "中型半挂牵引车"],
            17: ["大型客车", "K11", "大型普通客车"],
            18: ["中型客车", "K21", "中型普通客车"],
            19: ["小型客车", "K31", "小型普通客车"],
            20: ["出租轿车", "K33", "轿车"],
            21: ["微型车", "K43", "微型轿车"],
            22: ["三轮汽车", "N11", "三轮汽车"],
            23: ["专项作业车", "Z21", "中型专项作业车"],
            24: ["轮式专用机械", "J11", "轮式装载机械"],
            25: ["军用车辆", "X99", "其它"],
            26: ["武警车辆", "X99", "其它"],
            27: ["警用车辆", "X99", "其它"],
            28: ["外交车辆", "X99", "其它"],
            29: ["公交车辆", "X99", "其它"],
            31: ["轻型载货汽车", "H31", "轻型普通货车"],
            32: ["中型载货汽车", "H21", "中型普通货车"],
            33: ["重型载货汽车", "H11", "重型普通货车"]
    ].get(value) ?: ["其他", "X99", "其它"]

}

/**
 * 获取号牌种类
 * @param code
 * @return
 */
static def getPlateType(code){
    [
            0: ["01","大型汽车号牌","大型汽车号牌"],
            1: ["02","小型汽车号牌","小型汽车号牌"],
            2: ["03","使馆汽车号牌","使馆汽车号牌"],
            3: ["04","领馆汽车号牌","领馆汽车号牌"],
            4: ["05","境外汽车号牌","境外汽车号牌"],
            5: ["06","外籍汽车号牌","外籍汽车号牌"],
            6: ["25","两轮三轮摩托车号牌","其他"],
            7: ["25","轻便摩托车号牌","其他"],
            8: ["25","使馆摩托车号牌","其他"],
            9: ["25","领馆摩托车号牌","其他"],
            10:["25","境外摩托车号牌","其他"],
            11:["25","外籍摩托车","其他"],
            12:["25","农用运输车号牌","其他"],
            13:["14","拖拉机号牌","拖拉机号牌"],
            14:["15","挂车号牌","挂车号牌"],
            15:["16","教练汽车号牌","教练车号牌"],
            16:["25","教练摩托车号牌","其他"],
            17:["18","试验汽车号牌","试验汽车号牌"],
            18:["25","试验摩托车号牌","其他"],
            19:["20","临时入境汽车号牌","临时入境汽车号牌"],
            20:["25","临时入境摩托车号牌","其他"],
            21:["22","临时行驶车号牌","临时行驶车号牌"],
            22:["23","警用汽车号牌","公安警车号牌"],
            23:["25","警用摩托车号牌","其他"],
            24:["31","武警号牌","武警号牌"],
            25:["32","军队号牌","军队号牌"],
            26:["25","其他号牌","其他"]
        ].get(code) ?: ["25","","其他"]
}

/**
 * 获取新能源车牌类型
 * @param code
 * @return
 */
static def getNewEnergyPlateType(code){
    [
            0:["51","大型汽车号牌","大型新能源汽车"],
            1:["52","小型汽车号牌","小型新能源汽车"]
    ].get(code) ?: ["25","","其他"]
}

//设置请求签名
static def setRequestSignature(reqHeaders,jsonParams){
    def sitRequestSecretKey="MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBANRxvMq8HizWHl45kBl1JnGw6r6r++3C4DjNAeOrAcQRFzGzrT3WIOFF6pkJYZl+zDn1Csymqe/rlImSH3Jh4I41APz09KBtlQkI6+qwK4LjJUWUaMcVJew9SfiTNVXERIt18C7RyX2MuAoNV8VGUgqf76ql20N/tfwU4OPSHw51AgMBAAECgYEAp1WTNEkqp3xcXTGQDE1XY7PLozZKcMPP402vUEmxUWN41oBFU/Cm42oz2CkohEP4zynxQLOCJWV1EUtUk77+pPQnGCEYmUZUTuDUeWwD3u9JsAS2v83QJWvAPkjf7mCk7dIzTORQxxuNHqyOYKNqn1P4fe4gbcvAdURoRoAZpF0CQQD758pcd1ZFUoM8asUW1fD7RBY39gCZZ9SNQSDKJnbxYJqRSe0QumNVHRkcVQ/L25jh0EPkUZ5u0u56cYnlRHXPAkEA1+W+i0EtVu7jqU6gQ0zbWd/KOcjXDeanIPFcXp4DG4lZPAuSw9Kqz8Xbb1Yqq7dkPFsIFNYAl6wf7F0Tte9MewJBALyQnBhvdHLb+0Ukn3gimgtcwk0NpxEuehtq30KzXGH/cHTFo+HbxDOYXo2o1vRA48ZVghzNRA2tc7rQduraPl0CQQDRJb9xJ7LEhKgHXCPlDo9wgVtmnym2TbuaLjdNccWQ532KrauACJVwsjvhn5e2wfJYhddOWAI67IQAdiqiFTABAkA8QuD9xf45GNarchjGibovlPgdf+ExQn3n5HbgkqpckBaqTvgZ3ofYKMNIDlVuiOF7cFASvn1mW03z+VsRySmm"
    def reqSignature=new signMD5().signMD5(jsonParams,sitRequestSecretKey,"UTF-8")
    reqHeaders.put("Content-Type","application/json;charset=UTF-8")
    reqHeaders.put("signature",reqSignature)
    reqHeaders.put("Accept-Encoding","")
    return reqHeaders
}

/**
 * 获取险别类型
 * @param param
 * @return
 */
def getkindOfPlcicy(param){
    [
            "bizSuiteInfo"      : ["0521", "机动车综合商业保险"],
            "efcSuiteInfo"      : ["0507", "机动车交通事故责任强制保险"],
            "newEnergySuiteInfo": ["0531", "新能源综合商业保险"],
    ].get(param) ?: ["", ""]
}

/**
 * 获取民族
 * @param nation
 * @return
 */
static def getNation(nation){
    [
            "汉族": "01", "蒙古族": "02", "回族": "03", "藏族": "04", "维吾尔族": "05", "苗族": "06",
            "彝族": "07", "壮族": "08", "布依族": "09", "朝鲜族": "10", "满族": "11", "侗族": "12",
            "瑶族": "13", "白族": "14", "土家族": "15", "哈尼族": "16", "哈萨克族": "17", "赫哲族": "53",
            "黎族": "19", "傈僳族": "20", "佤族": "21", "畲族": "22", "高山族": "23", "拉祜族": "24",
            "水族": "25", "东乡族": "26", "纳西族": "27", "景颇族": "28", "柯尔克孜族": "29", "保安族": "47",
            "土族": "30", "仫佬族": "32", "布朗族": "34", "撒拉族": "35", "毛难族": "36", "基诺族": "56",
            "怒族": "42", "仡佬族": "37", "锡伯族": "38", "阿昌族": "39", "普米族": "40",
            "傣族": "18", "乌孜别克族": "43", "俄罗斯族": "44", "鄂温克族": "45", "德昂族": "46",
            "京族": "49", "裕固族": "48", "塔塔尔族": "50", "独龙族": "51", "鄂伦春族": "52",
            "羌族": "33", "门巴族": "54", "珞巴族": "55", "达斡尔族": "31", "塔吉克族": "41",
    ].get(nation) ?: "99"
}

def getCarFiledValue(tempValues,filedname,type){
    return getCarFiledValue(tempValues,filedname,type,null,null)
}

def getCarFiledValue(tempValues,filedname,type,enquiry,filedname2){
    return getCarFiledValue(tempValues,filedname,type,enquiry,filedname2,false,null,null)

}

//北京平台多走一个接口，有返回信息就用没有就用全国的，默认全国车辆信息兜底
def getCarFiledValue(tempValues,filedname,type,enquiry,filedname2,transFlag,transMethodName,transParamType){
    return getCarFiledValue(tempValues,filedname,type,enquiry,filedname2,transFlag,transMethodName,transParamType,-1)
}

def getCarFiledValue(tempValues,filedname,type,enquiry,filedname2,transFlag,transMethodName,transParamType,index){

    def carJson=tempValues.carMessResult
    def carJsonBJ=tempValues.carMessForBeiJing
    def carMess=carJsonBJ !=null && carJsonBJ.containsKey(filedname)?carJsonBJ:carJson.containsKey(filedname)?carJson:""
    def specialBJList=["vehicleDisplacement"]
    def specialBJ=carJsonBJ !=null && carJsonBJ.containsKey(filedname) && specialBJList.contains(filedname)?true:false

    def result
    if(carMess instanceof JSONObject){

        def temp
        switch (type){
            case "String":temp=carMess.getString(filedname);break
            case "BigDecimal":temp=carMess.getBigDecimal(filedname) ;break
            default:temp=carJson.getString(filedname)
        }
        if(specialBJ){
            if(filedname.equals("vehicleDisplacement")){
                temp=new BigDecimal(temp).divide(new BigDecimal("1000")).toString()
            }
        }
        result =temp

    }else{
        /**自定义nullValue不返回空双引号""，是因为发现必返字段值存在""，会导致无法分辨是属性没返回还是属性返回了但是返回一个""，所以自定义nullvalue。
         * enquiry不为null,key不存在，需要该属性值从磐石取值，以接口返回信息为主，没有则从磐石取值
         **/
        if(enquiry==null){
            result='nullValue'
        }else{

            if(transFlag){
                try {
                    Class clz=Class.forName("guoshou.edi.edi_2002_common_new")
                    def instance = clz.newInstance()
                    Method tempMethod=clz.getDeclaredMethod(transMethodName,transParamType)
                    def invoke = tempMethod.invoke(instance, enquiry?.carInfo?."$filedname2")
                    result=index<0?invoke:invoke[index]
                }catch(Exception e){
                    throw new InsReturnException("反射代码异常:"+e)
                }

            }else {
                result=enquiry?.carInfo?."$filedname2"
            }
        }

    }
    return result
}

/**
 * 获取号牌颜色
 * @param enquiry
 * @return
 */
def transPlateColor(enquiry) {
    if (enquiry?.carInfo?.plateNum == "新车未上牌" && enquiry?.carInfo?.carModelName?.contains("纯电")) {
        return "06" // 渐变绿
    }

    [
            0: "04", // 黄
            1: "01", // 蓝
            2: "06", // 渐变绿
            3: "07", // 黄绿双拼
    ].get(enquiry?.carInfo?.plateColor) ?: "99"
}


/**
 * 检查地址是否规范，规避范围：
 *  陕西省|610000#西安市|610100#市辖区|610101#西安市区
 *  湖北省|420000#湖北直辖县|429000#天门市|429006#湖北武汉
 * @param address
 * @return
 */
def checkAddress(address) {
    def provinceOrCity = [
            "河北", "山西", "黑龙江", "吉林", "辽宁", "江苏", "浙江",
            "安徽", "福建", "江西", "河南", "湖北", "湖南",
            "广东", "海南", "四川", "贵州", "云南", "陕西",
            "青海", "内蒙古", "广西", "宁夏", "新疆", "西安",
            "北京", "上海", "重庆"
    ]

    if (provinceOrCity.any {it -> address.contains(it)} && address?.toString()?.length() <= 6) {
        return true
    }

    false
}

/**
 * 地址适配
 * @param address
 * 适配范围：
 * 湖北省|420000#随州市|421300#曾都区|421303#百花镇牛家村
 * 湖北省|420000#随州市|421300#曾都区|421303#湖北省随州市百曾都区花镇牛家村
 * 湖北省|420000#随州市|421300#曾都区|421303#重庆市大足区龙水镇碾盘村
 * 湖北省|420000#随州市|421300#曾都区|421303#江西省南昌市大足区龙水镇碾盘村
 * 湖北省|420000#随州市|421300#曾都区|421303#江西省南昌市大足县龙水镇碾盘村
 */
def setAddressFormat(tempValues, address) {
    if (!address) {
        return ""
    }

    def addressList = address?.toString()?.split("#")
    def addressInfoLast = addressList.last()
    if ("quote" == tempValues?.taskType) {
        return addressInfoLast
    }

    if (["省", "市"].every {addressInfoLast.contains(it) }) {
        if (addressInfoLast.length() <= 6) {
            throw new InsReturnException("地址填写不规范，请添加区县、街道")
        }

        return addressList.last()
    }

    if (["北京市", "上海市", "天津市", "重庆市"].any { addressList.last().contains(it) }) {
        if (!addressInfoLast.contains("区") && !addressInfoLast.contains("县")) {
            throw new InsReturnException("地址填写不规范，请添加区县、街道")
        }

        return addressList.last()
    }

    if (checkAddress(addressInfoLast)) {
        throw new InsReturnException("地址填写不规范，请添加区县、街道")
    }

    def containsSpecialChars = { String str ->
        str =~ /[!@#$%^&*?<>]/
    }
    if (containsSpecialChars(addressInfoLast)) {
        throw new InsReturnException("地址包含特殊字符，请检查")
    }

    def isAllLetters = { str ->
        str.every { str.matches('^[a-zA-Z]+$') }
    }
    if (isAllLetters(addressInfoLast)) {
        throw new InsReturnException("地址全是英文字母，请检查")
    }

    if (["市", "区"].every {it -> addressInfoLast.contains(it)}) {
        return addressList.first().split("\\|").first() + addressInfoLast
    }

    def addressInfo = ""
    addressList.each { it ->
        addressInfo += it.split("\\|").first()
    }

    if (addressInfo.contains("市辖区")) {
        return addressInfo.replaceAll("市辖区", "")
    }

    if (addressInfo.length() < 6 || addressInfo.isNumber()) {
        throw new InsReturnException("地址填写不规范，请检查此地址：${addressInfo}")
    }

    addressInfo
}

//格式化地址 //湖北省|420000#随州市|421300#曾都区|421303#百花镇牛家村
def setAddressForCode(enquiry, address, tempValues) {
    def addressArray = []
    def city = enquiry.insArea?.city as String
    addressArray << enquiry.insArea?.province // 省
    addressArray << city // 市
    addressArray << checkLocation(city) // 区

    if (address) {
        addressArray.add(setAddressFormat(tempValues, address))
    } else {
        addressArray.add("")
    }

    addressArray

}

static def checkResultStatus(jsonResult, isThrow) {
    if (["0000", "0003"].contains(jsonResult?.result?.resultCode)) {
        return true
    } else {
        if (isThrow) {
            if (jsonResult?.result?.resultInfo) {
                throw new InsReturnException(jsonResult?.result?.resultInfo as String)
            } else {
                throw new InsReturnException(jsonResult?.result ?: "解析保司返回信息异常：${jsonResult}" as String)
            }
        }
        return false
    }

}

/**
 * 获取区县code
 * @param city
 * @return
 */
def checkLocation(city) {
    [
            '140100': '140105', '440100': '440103', '440200': '440203', '440300': '440303', '440400': '440403',
            '440500': '440507', '440600': '440604', '440700': '440783', '510100': '510104', '510600': '510603',
            '510700': '510703', '510900': '510903', '350200': '350203', '370800': '370811', '320500': '320505',
            '321300': '321302', '320700': '320703', '310100': '310101', '460100': '460105', '320200': '320213',
            '320800': '320803', '140400': '140403', '441900': '441991', '140200': "140212", '330200': '330281'
    ].get(city) ?: city.substring(0, 4) + "02"
}


/**
 * 获取影像类型  [磐石key,磐石描述，保司大key，保司小key，保司描述]
 * @param param
 * @return
 */
def getImageType(param) {
    [
            "3": ["3", "行驶证正页照", "CAR", "8201", "行驶证"],
            "4": ["4", "行驶证副页照", "CAR", "8201", "行驶证"],
            "0": ["0", "被保人身份证正面照", "CUSTOMER", "8101", "身份证/户口薄"],
            "1": ["1", "被保人身份证反面照", "CUSTOMER", "8101", "身份证/户口薄"],
            "19": ["19", "行驶证年审页照", "CAR", "8201", "行驶证"],
            "2": ["2", "被保人组织机构代码证照", "GROUP", "8001", "组织机构代码证"],
            "56": ["56", "被保人社会信用代码证照", "GROUP", "8002", "营业执照"],
            "20": ["20", "合格证", "CAR", "8203", "出厂合格证"],
            "14": ["14", "新车发票照", "CAR", "8202", "购车发票/二手车交易发票"],
            "48": ["48", "居住证", "CUSTOMER", "8105", "暂住证"],
            "54": ["54", "本地使用证明", "PRPALL", "8322", "异地车在本地使用证明"],
            "55": ["55", "完税证明", "PRPALL", "8305", "完税/免税/减税凭证"],
            "17": ["17", "上年商业险保单照", "PRPALL", "8306", "上年保险凭证"],
            "18": ["18", "上年交强险保单照", "PRPALL", "8306", "上年保险凭证"],
            "28": ["28", "投保人身份证正面照", "CUSTOMER", "8101", "身份证/户口薄"],
            "29": ["29", "投保人身份证反面照", "CUSTOMER", "8101", "身份证/户口薄"],
            "30": ["30", "投保人组织机构代码证", "GROUP", "8001", "组织机构代码证"],
            "57": ["57", "投保人社会信用代码证照", "GROUP", "8002", "营业执照"],
            "25": ["25", "车主身份证正面照", "CUSTOMER", "8101", "身份证/户口薄"],
            "26": ["26", "车主身份证反面照", "CUSTOMER", "8101", "身份证/户口薄"],
            "27": ["27", "车主组织机构代码证照", "GROUP", "8001", "组织机构代码证"],
            "58": ["58", "车主社会信用代码证照", "GROUP", "8001", "营业执照"],
            "49": ["49", "浮动告知书盖章（单位车）", "PRPALL", "8302", "交强险费率浮动告知书"],
            "50": ["50", "投保单盖公章照", "PRPALL", "8301", "投保单"],
            "51": ["51", "二手车交易发票", "CAR", "8202", "购车发票/二手车交易发票"],
            "52": ["52", "交强险不退保承诺书", "PRPALL", "8399", "其它"],
            "53": ["53", "上年理赔记录", "PRPALL", "8306", "上年保险凭证"],
            "24": ["24", "数字一致性证书第二页", "CAR", "8299", "其它"],
            "61": ["61", "验车单", "PRPALL", "8303", "验车照片"],
            "33": ["33", "前挡风玻璃", "PRPALL", "8303", "验车照片"],
            "36": ["36", "倒车镜前", "PRPALL", "8303", "验车照片"],
            "37": ["37", "倒车镜后", "PRPALL", "8303", "验车照片"],
            "31": ["31", "左车仪表盘内饰", "PRPALL", "8303", "验车照片"],
            "32": ["32", "右车仪表盘内饰", "PRPALL", "8303", "验车照片"],
            "34": ["34", "玻璃标识", "PRPALL", "8303", "验车照片"],
            "35": ["35", "打着火拍仪表盘", "PRPALL", "8303", "验车照片"],
            "12": ["12", "铭牌", "PRPALL", "8303", "验车照片"],
            "13": ["13", "发动机舱", "PRPALL", "8303", "验车照片"],
            "38": ["38", "所有大灯特写一", "PRPALL", "8303", "验车照片"],
            "39": ["39", "所有大灯特写二", "PRPALL", "8303", "验车照片"],
            "40": ["40", "所有大灯特写三", "PRPALL", "8303", "验车照片"],
            "41": ["41", "所有大灯特写四", "PRPALL", "8303", "验车照片"],
            "23": ["23", "关单", "CAR", "8299", "其它"],
            "47": ["47", "代理人资格证信息页", "PRPALL", "8399", "其它"],
            "43": ["43", "代理人身份证正面照", "CUSTOMER", "8101", "其它"],
            "44": ["44", "代理人身份证背面照", "CUSTOMER", "8101", "其它"],
            "42": ["42", "供应商logo", "GROUP", "8099", "其它"],
            "16": ["16", "本地身份证照", "CUSTOMER", "8101", "其它"],
            "46": ["46", "资格证正面照", "GROUP", "8099", "其它"],
            "45": ["45", "银行卡正面照", "GROUP", "8099", "其它"],
            "22": ["22", "驾驶证副页照", "CUSTOMER", "8102", "驾驶证"],
            "21": ["21", "驾驶证正页照", "CUSTOMER", "8103", "驾驶证"],
            "70": ["70", "关系证明", "CUSTOMER", "8199", "其它"],
            "71": ["71", "车辆登记证第一页", "CAR", "8204", "机动车辆登记证书"],
            "72": ["72", "车辆登记证第二页", "CAR", "8204", "机动车辆登记证书"],
            "73": ["73", "车辆登记证第三页", "CAR", "8204", "机动车辆登记证书"],
            "74": ["74", "车辆登记证第四页", "CAR", "8204", "机动车辆登记证书"],
            "100": ["100", "其它", "PRPALL", "8399", "其它"],
            "101": ["101", "从业人员执业证", "GROUP", "8099", "其它"],
            "102": ["102", "投保人签单时照片", "PRPALL", "8399", "其它"],
            "103": ["103", "销售人员与投保人正面合影", "PRPALL", "8399", "其它"],
            "104": ["104", "投保人在机构LOGO前正面照", "PRPALL", "8399", "其它"],
            "105": ["105", "加盖公章的单位委托书与组织机构代码证", "GROUP", "8099", "其它"],
            "106": ["106", "营业执照原件（或复印件加公章）合影", "GROUP", "8002", "营业执照"],
            "107": ["107", "代办人身份证正面", "CUSTOMER", "8101", "身份证/户口薄"],
            "108": ["108", "代办人身份证反面", "CUSTOMER", "8101", "身份证/户口薄"],
            "109": ["109", "代办人签署投保单时照片", "PRPALL", "8399", "其它"],
            "110": ["110", "销售人员与代办人合影", "PRPALL", "8399", "其它"],
            "5": ["5", "车辆正面照片", "PRPALL", "8303", "验车照片"],
            "6": ["6", "车辆正后照片", "PRPALL", "8303", "验车照片"],
            "7": ["7", "车辆前左45度照片", "PRPALL", "8303", "验车照片"],
            "8": ["8", "车辆前右45度照片", "PRPALL", "8303", "验车照片"],
            "9": ["9", "车辆后左45度照片", "PRPALL", "8303", "验车照片"],
            "10": ["10", "车辆后右45度照片", "PRPALL", "8303", "验车照片"],
            "11": ["11", "带车架号照片", "PRPALL", "8303", "验车照片"],
            "15": ["15", "人车合影", "PRPALL", "8303", "验车照片"],
            "59": ["59", "车辆正左面照", "PRPALL", "8303", "验车照片"],
            "60": ["60", "车辆正右面照", "PRPALL", "8303", "验车照片"],
            "111": ["111", "免责声明", "PRPALL", "8304", "免责条款告知书"],
            "89": ["89", "车主港澳回乡证反面照", "CUSTOMER", "8199", "车主港澳回乡证反面照"],
            "90": ["90", "车主港澳台居民居住证反面照", "CUSTOMER", "8199", "车主港澳台居民居住证反面照"],
            "91": ["91", "被保人港澳回乡证反面照", "CUSTOMER", "8199", "被保人港澳回乡证反面照"],
            "92": ["92", "被保人港澳台居民居住证反面照", "CUSTOMER", "8199", "被保人港澳台居民居住证反面照"],
            "93": ["93", "投保人港澳回乡证反面照", "CUSTOMER", "8199", "投保人港澳回乡证反面照"],
            "112": ["112", "退伍军人优待证", "CUSTOMER", "8110", "退伍军人优待证"],
            "94": ["94", "投保人港澳台居民居住证反面照", "CUSTOMER", "8199", "投保人港澳台居民居住证反面照"]
    ].get(param) ?: ["", "磐石未按照指定key传值默认其它", "PRPALL", "8399", "其它"]

}

// bigDecimal 类型转换
def toBigDecimal(value) {
    if (!value) {
        return new BigDecimal(0)
    }

    return value.toString().toBigDecimal()
}

// 非车适配手续费
def checkNonMotorAgentRate(value) {
    [
            "140000" : "0.2",
            "210000" : "0.35"
    ].get(value) ?: "0.25"
}


/**
 * 获取报错报文
 * @param root 提核返回报文
 * @return
 */
def getErrorMsg(root) {
    def pdfUtil = new edi_2002_pdfUtil()
    def bizError = []
    def efcError = []
    if (root?.businessData?.resultInfo?.sy) {
        root?.businessData?.resultInfo?.sy?.each { it ->
            def reg = "【" + pdfUtil.matchForReg(".*规则：", it["ruleID"])?.group(0) + it["description"] + "】"
            bizError << reg
        }
    }

    if (root?.businessData?.resultInfo?.jq) {
        root?.businessData?.resultInfo?.jq?.each { it ->
            def reg = "【" + pdfUtil.matchForReg(".*规则：", it["ruleID"])?.group(0) + it["description"] + "】"
            efcError << reg
        }
    }

    return ["efcError": efcError, "bizError": bizError]
}

/**
 * 获取排量
 * @param energyFlag
 * @param enquiry
 * @return
 */
def getDisplacement(energyFlag, enquiry, tempValues) {
    [
            "0" : tempValues.carMessResult?.vehicleDisplacement ?: enquiry.carInfo.displacement, // 油车
            "1" : 0, // 电车
            "3" : tempValues.carMessResult?.vehicleDisplacement ?: enquiry.carInfo.displacement// 插电式混合动力
    ].get(energyFlag) ?: 0
}

/**
 * 获取燃料类型
 * @param isEnergy 能源类型
 * @param enquiry
 */
static def getEnergyTypesCode(energyType, enquiry) {
    // 北京地区的燃料类型需要用字母表示
    if ("110000" == enquiry?.insArea?.province) {
        def beiJingEnergyMap = [
                "0": "A", // 燃油
                "1": "C", // 纯电
                "3": "O"  // 插电式混合动力
        ]
        return beiJingEnergyMap.get(energyType) ?: "A"
    }

    return energyType

}

/**
 * yyyy-MM-ddTHH:mm:ss.SSS+SSSS 转换为 yyyy-MM-dd HH:mm:ss
 * @param time
 */
def parsePolicyCreateTime(time) {
    if (time) {
        return DateUtil.parse(time as String, "yyyy-MM-dd'T'HH:mm:ss.SSSZ").toString("yyyy-MM-dd HH:ss:mm")
    }
}

/**
 * 获取是否关联单 1-关联单、2-单商业、3-单交强
 * @param enquiry
 */
def getRelationFlag(bizSuiteInfo, efcSuiteInfo) {
    if (bizSuiteInfo && efcSuiteInfo) {
        return "1"
    }

    if (bizSuiteInfo && !efcSuiteInfo) {
        return "2"
    }

    if (!bizSuiteInfo && efcSuiteInfo) {
        return "3"
    }

}

/**
 * 获取 supplyParam
 * @param autoTask
 * @param supplyParamKey
 * @return
 */
static def getSupply(enquiry, supplyParamKey) {
    def infoPrefix = [
            'applicant',
            'owner',
            'insured'
    ]

    def supplyParam = enquiry.supplyParam
    def result = supplyParam.find { it -> it.itemcode == supplyParamKey }
    if (!result) {
        def curPrefix = infoPrefix.find{it -> supplyParamKey?.startsWith(it)}
        def endString = supplyParamKey - curPrefix
        infoPrefix -= curPrefix
        result = supplyParam.find { it['itemcode'] == infoPrefix[0] + endString } ?:
                supplyParam.find { it['itemcode'] == infoPrefix[1] + endString }
    }

    result ? result.itemvalue : ""
}

/**
 * 格式化地址
 * @param address
 */
def formatAddress(address) {
    def matcher = address =~ /\|\d+#/
    return  [
            'province': (matcher[0] - '|' - '#'),
            'city'    : (matcher[1] - '|' - '#'),
            'district': (matcher[2] - '|' - '#'),
            'detail'  : address.replaceAll('\\|\\d{6}+#', '')
    ]
}

/**
 * 获取身份证起期
 * @param enquiry
 * @param type
 */
static def getIdentifyEffectiveStartDate(enquiry, type) {
    getSupply(enquiry, type + "IDCardRegistrationDate") ?: LocalDate.now().minusDays(1).toString()
}

/**
 * 获取身分之止期
 * @param enquiry
 * @param type
 * @return
 */
static def getIdentifyEffectiveEndDate(enquiry, type) {
    def endDate = getSupply(enquiry, type + "IDCardValidDate")
    if (endDate) {
        if ("长期" == endDate) {
            return "2999-12-30"
        }

        return endDate
    }

    LocalDate.now().minusDays(1).plusYears(10).toString()
}

/**
 * 获取号牌种类
 * @param energy
 * @param enquiry
 */
def getLicenseType(energyType, enquiry) {
    def plateType = enquiry?.carInfo?.plateType
    if (enquiry?.carInfo?.plateNum == "新车未上牌") {
        return ""
    }

    return energyType == "0" ? getPlateType(plateType)[0] : getNewEnergyPlateType(plateType)[0]
}

/**
 * 根据身份证获取年龄
 * @param idCard
 * @return
 */
static def getAge(String idCard, Integer idCardType) {
    if (idCard && idCard.length() != 18) {
        return ""
    }

    if ([6, 8, 9, 10].contains(idCardType)) {
        return ""
    }

    def birthday = idCard.substring(6, 14)
    return LocalDate.parse(birthday, DateTimeFormatter.ofPattern('yyyyMMdd')).until(LocalDate.now()).years
}

/**
 * 获取新车销售公司所在地市名称
 */
static def getSaleAreaName(city) {
    [
            "440100":"广州市", "440200":"韶关市", "440400":"珠海市", "440500":"汕头市", "440600":"佛山市",
            "440700":"江门市", "440800":"湛江市", "440900":"茂名市", "441200":"肇庆市", "441300":"惠州市",
            "441400":"梅州市", "441500":"汕尾市", "441600":"河源市", "441700":"阳江市", "441800":"清远市",
            "441900":"东莞市", "442000":"中山市", "445100":"潮州市", "445200":"揭阳市", "445300":"云浮市"
    ].get(city) ?: ""
}

/**
 * 回写商业险别信息
 * @param bizSuiteInfo 百川的商业险别信息
 * @param plcSolution 保司返回的商业险别信息
 * @return
 */
def backSuiteInfo(bizSuiteInfo, plcSolution) {
    def suiteList = []
    plcSolution.plcClauses.each { it ->
        def codeAndName = transThirdCodeToMySelf(it.clauseCode)
        def querySuitInfo = bizSuiteInfo.suites.find { suit -> suit.code == codeAndName[0]}
        def suite = [
                "discountRate"  : querySuitInfo?.discountRate ?: plcSolution.plcRation.finalRat ?: "",
                "code"          : codeAndName[0],
                "name"          : codeAndName[1],
                "discountCharge": it.oriCurPremium,
                "amount"        : it.oriCurAmount
        ]

        if (["Passenger", "NIHCPassenger"].contains(codeAndName[0])) {
            suite.amount = it.everyoneAmount
        }

        if (["RoadsideService", "VehicleInspection", "DesignatedDriving", "SendForInspection"].contains(codeAndName[0])) {
            suite.amount = it.compensationDays
        }

        def suiteInfo = bizSuiteInfo.suites.find { suiteInfo -> suiteInfo.code == codeAndName[0] }
        suite.share = suiteInfo ? suiteInfo.share : false
        suiteList << suite
    }

    bizSuiteInfo.suites = suiteList
}

/**
 * 回写险别时间
 * @param suiteInfo
 * @param soleActual
 * @return
 */
def backSuiteTime(suiteInfo, soleActual) {
    def reg = "(\\d{4}-\\d{2}-\\d{2})T(\\d{2}:\\d{2}:\\d{2})"

    def startTimeMatcher = soleActual.startTime =~ reg
    if (startTimeMatcher.find()) {
        suiteInfo.start = startTimeMatcher.group(1) + " " + startTimeMatcher.group(2)
    }

    def endTimeMatcher = soleActual.endTime =~ reg
    if (endTimeMatcher.find()) {
        suiteInfo.end = endTimeMatcher.group(1) + " " + endTimeMatcher.group(2)
    }
}

/**
 * 根据 type 设置节点
 * @param type 1：个人，2：公司
 * @param plcCustomer 个人节点信息
 * @param plcGrpCustomer 公司节点信息
 * @param plcInfo 节点信息 投保人/被保人
 * @return
 */
static def setCustomerOrGrpCustomer(type, plcCustomer, plcGrpCustomer, plcInfo) {
    type == "1" ? (plcInfo.plcCustomer = plcCustomer) :
            (plcInfo.plcGrpCustomer = plcGrpCustomer)
}

/**
 * 获取 localFlag
 * @param enquiry
 * @param tempValues
 */
static def getLocalFlag(enquiry, tempValues) {
    // 海南地区，localFlag 默认为 4
    if ("460000" == enquiry?.insArea?.province) {
        return "4"
    }

    if (tempValues.localFlag || tempValues.repeatCarModel) {
        return "1"
    }

    return null
}

/**
 * 获取方案代码
 * @param bizSuiteInfo
 * @param energyType
 */
static def getProductCode(bizSuiteInfo, energyType) {
    if (!bizSuiteInfo) {
        return GuoShouConstants.EFC
    }

    return "0" == energyType ? GuoShouConstants.BIZ : GuoShouConstants.BIZ_NEW_ENERGY
}
