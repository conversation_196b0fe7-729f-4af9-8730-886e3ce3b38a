# HBase数据迁移使用指南

## 概述

本文档介绍如何使用新实现的HBase数据迁移功能，将AutoTask中的大文本字段（applyJson、resultStr、feedbackJson）迁移到HBase存储。

## 功能特性

1. **智能双写机制**：
   - 双写开启：同时写入数据库表和HBase的大文本字段
   - 双写关闭：不写入数据库表的大文本字段，只写入HBase
2. **开关控制**：可以选择从数据库表或HBase查询数据
3. **降级机制**：HBase查询失败时自动降级到数据库查询
4. **异步处理**：HBase操作失败不影响主流程
5. **字段选择性写入**：根据开关状态智能决定哪些字段写入数据库表

## 配置项

在`application.yml`中添加以下配置：

```yaml
hbase:
  data:
    # 是否从HBase查询大文本字段数据
    # true: 从HBase查询  false: 从数据库表查询（默认）
    use-hbase-query: false

    # 是否启用双写
    # true: 同时写入数据库表和HBase的大文本字段（默认）
    # false: 不写入数据库表的大文本字段，只写入HBase
    enable-double-write: true
```

## 使用方式

### 1. 保存数据（自动双写）

```java
@Autowired
private AutoTaskService autoTaskService;

// 正常保存，如果启用双写，会自动保存到HBase
AutoTask autoTask = new AutoTask();
autoTask.setAutoTraceId("test-trace-id");
autoTask.setApplyJson("{\"test\": \"data\"}");
autoTask.setResultStr("处理结果");
autoTask.setFeedbackJson("{\"feedback\": \"success\"}");

autoTaskService.save(autoTask);
```

### 2. 查询数据（开关控制）

```java
// 根据开关控制从数据库表或HBase查询
AutoTask task = autoTaskService.getTaskWithLargeFields("test-trace-id");

// 传统查询方式（始终从数据库表查询）
AutoTask taskFromDb = autoTaskService.getById("test-trace-id");
```

### 3. 更新数据（自动双写）

```java
// 更新操作会自动同步到HBase（如果启用双写）
AutoTask updateTask = new AutoTask();
updateTask.setAutoTraceId("test-trace-id");
updateTask.setResultStr("更新后的结果");
updateTask.setFeedbackJson("{\"feedback\": \"updated\"}");

autoTaskService.updateClaimInfoByAutoTraceId(updateTask);
```

### 4. 运行时开关控制

```java
// 动态开启HBase查询
HBaseDataSwitch.setUseHBaseData(true);

// 动态关闭双写（重要：关闭后不再写入数据库表的大文本字段）
HBaseDataSwitch.setEnableDoubleWrite(false);

// 重置为默认配置
HBaseDataSwitch.resetToDefault();
```

### 5. 双写开关关闭时的行为示例

```java
// 关闭双写开关
HBaseDataSwitch.setEnableDoubleWrite(false);

// 保存数据
AutoTask autoTask = new AutoTask();
autoTask.setAutoTraceId("test-trace-id");
autoTask.setApplyJson("{\"test\": \"data\"}");      // 只保存到HBase
autoTask.setResultStr("处理结果");                   // 只保存到HBase
autoTask.setFeedbackJson("{\"feedback\": \"ok\"}"); // 只保存到HBase
autoTask.setTaskStatus("SUCCESS");                  // 保存到数据库表

autoTaskService.save(autoTask);

// 结果：
// 1. 数据库表中：autoTraceId、taskStatus等基本字段有值，applyJson、resultStr、feedbackJson为null
// 2. HBase中：完整的大文本字段数据
```

## 迁移策略

### 阶段1：双写阶段
```yaml
hbase:
  data:
    use-hbase-query: false      # 继续从数据库查询
    enable-double-write: true   # 启用双写
```

### 阶段2：切换阶段
```yaml
hbase:
  data:
    use-hbase-query: true       # 切换到HBase查询
    enable-double-write: true   # 保持双写
```

### 阶段3：完全迁移
```yaml
hbase:
  data:
    use-hbase-query: true       # 从HBase查询
    enable-double-write: false  # 关闭双写，不写数据库表的大文本字段，只写HBase
```

**重要说明**：
- **阶段1**：数据库表和HBase都写入大文本字段，查询从数据库表
- **阶段2**：数据库表和HBase都写入大文本字段，查询从HBase（有降级机制）
- **阶段3**：只有HBase写入大文本字段，数据库表的applyJson、resultStr、feedbackJson字段不再更新

## 测试建议

### 1. 单元测试

```java
@Test
public void testHBaseDoubleWrite() {
    // 启用双写
    HBaseDataSwitch.setEnableDoubleWrite(true);
    
    AutoTask autoTask = createTestAutoTask();
    autoTaskService.save(autoTask);
    
    // 验证数据库中的数据
    AutoTask dbTask = autoTaskService.getById(autoTask.getAutoTraceId());
    assertNotNull(dbTask);
    
    // 验证HBase中的数据
    AutoTaskLog hbaseLog = autoTaskLogRepository.findByAutoTraceId(autoTask.getAutoTraceId());
    assertNotNull(hbaseLog);
    assertEquals(autoTask.getApplyJson(), hbaseLog.getApplyJson());
}

@Test
public void testHBaseQuery() {
    // 切换到HBase查询
    HBaseDataSwitch.setUseHBaseData(true);
    
    AutoTask task = autoTaskService.getTaskWithLargeFields("test-trace-id");
    assertNotNull(task);
    assertNotNull(task.getApplyJson());
}
```

### 2. 集成测试

1. **数据一致性测试**：验证双写后数据库和HBase中的数据一致性
2. **性能测试**：对比从数据库和HBase查询的性能差异
3. **降级测试**：模拟HBase故障，验证降级机制
4. **并发测试**：验证高并发场景下的数据一致性

### 3. 监控指标

建议监控以下指标：
- HBase保存成功率
- HBase查询成功率
- 降级触发次数
- 查询响应时间对比

## 注意事项

1. **数据一致性**：双写期间可能存在短暂的数据不一致，建议在业务低峰期进行切换
2. **性能影响**：双写会增加写入延迟，HBase查询性能需要根据实际情况评估
3. **错误处理**：HBase操作失败不会影响主流程，但需要监控错误日志
4. **回滚方案**：如果HBase查询出现问题，可以快速切换回数据库查询

## 故障排查

### 常见问题

1. **HBase连接失败**
   - 检查HBase配置
   - 验证网络连接
   - 查看HBase服务状态

2. **数据不一致**
   - 检查双写是否正常工作
   - 验证数据转换逻辑
   - 查看错误日志

3. **性能问题**
   - 监控HBase查询时间
   - 检查HBase表设计
   - 考虑添加缓存

### 日志关键字

- "成功保存数据到HBase"
- "保存数据到HBase失败"
- "HBase查询失败，降级到数据库查询"
- "更新HBase数据失败"
