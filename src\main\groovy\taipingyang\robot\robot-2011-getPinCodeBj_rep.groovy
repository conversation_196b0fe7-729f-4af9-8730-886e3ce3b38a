package taipingyang.robot

import com.alibaba.fastjson.JSON
import taipingyang.robot.module.Robot2011Util

Map entity = enquiry
def TbUtil = new common_2011()
Map tempValues = null != entity.get("tempValues") ? entity.get("tempValues") : tempValues
Map header = new HashMap();
header.put("Content-Type", tempValues?.needSM4Flag ? 'text/plain' : "application/json;charset=UTF-8")

def smsconfirmResult = (String) root;
if (!smsconfirmResult) {
    TbUtil.getUserDefinedException("获取身份采集短信验证码失败！")
}

smsconfirmResult = Robot2011Util.decodeBody(tempValues, smsconfirmResult)
def smsConfirmJson = JSON.parseObject(smsconfirmResult)
if (smsConfirmJson?.message?.code != "success") {
    TbUtil.getUserDefinedException("获取身份采集短信验证码code失败，平台提示:" + smsConfirmJson?.message?.message?.toString())
}
def responseJson = [
        inscomcode: "2011",
        taskType  : "applypinbj",
        pinType   : entity?.pinType,
        taskId    : entity?.taskId,
        result    : true,
        statusCode: "2",
]

entity.clear()
entity << responseJson


