package com.cheche365.bc.admin.service.constant;

/**
 * Created by ya<PERSON><PERSON> on 2019/8/13 19:07
 */
public class PermissionCode {
    /**
     * 系统管理-用户管理-启用/停用
     */
    public static final String USERINFO_CHANGE_STATUS = "userinfo-changestatus";
    /**
     * 系统管理-用户管理-查看
     */
    public static final String USERINFO_ALL = "userinfo-all";
    /**
     * 用户管理-用户管理列表-查看
     */
    public static final String USERINFO_GETPAGE = "userinfo-getPage";
    /**
     * 用户管理-用户管理列表-编辑
     */
    public static final String USERINFO_UPDATE = "userinfo-update";
    /**
     * 用户管理-用户管理列表-新建
     */
    public static final String USERINFO_SAVE = "userinfo-save";
    /**
     * 用户管理-用户管理列表-启用/禁用
     */
    public static final String USERINFO_SET_STATUS = "userinfo-setstatus";
    /**
     * 用户管理-用户管理列表-删除
     */
    public static final String USERINFO_DELETE_BY_ID = "userinfo-delete";
    /**
     * 用户管理-角色与权限管理-查看
     */
    public static final String ROLEINFO_GETPAGE = "roleinfo-getPage";
    /**
     * 用户管理-角色与权限管理-新建
     */
    public static final String ROLEINFO_SAVE = "roleinfo-save";
    /**
     * 用户管理-角色与权限管理-编辑
     */
    public static final String ROLEINFO_UPDATE = "roleinfo-update";
    /**
     * 用户管理-角色与权限管理-启用/禁用
     */
    public static final String ROLEINFO_SET_STATUS = "roleinfo-setstatus";
    /**
     * 用户管理-角色与权限管理-删除
     */
    public static final String ROLEINFO_DELETE = "roleinfo-delete";
    /**
     * 用户管理-操作记录-查看
     */
    public static final String USERLOG_GETPAGE = "userlog-getPage";
    /**
     * 接口管理-查看
     */
    public static final String INTERFACE_GETPAGE = "interface-getPage";
    /**
     * 接口管理-新建
     */
    public static final String INTERFACE_SAVE = "interface-save";
    /**
     * 接口管理-编辑
     */
    public static final String INTERFACE_UPDATE = "interface-update";
    /**
     * 接口管理-删除
     */
    public static final String INTERFACE_DELETE = "interface-delete";
    /**
     * 模板管理-查看
     */
    public static final String TEMPLATE_GETPAGE = "template-getPage";
    /**
     * 模板管理-新建
     */
    public static final String TEMPLATE_SAVE = "template-save";
    /**
     * 模板管理-编辑
     */
    public static final String TEMPLATE_UPDATE = "template-update";
    /**
     * 模板管理-删除
     */
    public static final String TEMPLATE_DELETE = "template-delete";
    /**
     * 日志管理-查看
     */
    public static final String AUTOTASKLOG_GETPAGE = "autoTaskLog-getPage";
    /**
     * 异常配置-查看
     */
    public static final String ERRORCATEGORY_GETPAGE = "errorCategory-getPage";
    /**
     * 异常配置-新建
     */
    public static final String ERRORCATEGORY_SAVE = "errorCategory-save";
    /**
     * 异常配置-编辑
     */
    public static final String ERRORCATEGORY_UPDATE = "errorCategory-update";
    /**
     * 异常配置-删除
     */
    public static final String ERRORCATEGORY_DELETE = "errorCategory-delete";
    /**
     * 工作时间-查看
     */
    public static final String WORKERTIME_GETPAGE = "workerTime-getPage";
    /**
     * 工作时间-新建
     */
    public static final String WORKERTIME_SAVE = "workerTime-save";
    /**
     * 工作时间-编辑
     */
    public static final String WORKERTIME_UPDATE = "workerTime-update";
    /**
     * 工作时间-删除
     */
    public static final String WORKERTIME_DELETE = "workerTime-delete";

    /**
     * 平台信息码表-列表
     */
    public static final String PLATFORM_INFO_CODE_GETPAGE = "platformInfoCode-getPage";

    /**
     * 平台信息码表-新建
     */
    public static final String PLATFORM_INFO_CODE_SAVE = "platformInfoCode-save";

    /**
     * 平台信息码表-编辑
     */
    public static final String PLATFORM_INFO_CODE_UPDATE = "platformInfoCode-update";

    /**
     * 平台信息码表-删除
     */
    public static final String PLATFORM_INFO_CODE_DELETE = "platformInfoCode-delete";

    /**
     * 百川业务异常配置-查看
     */
    public static final String AUTOTASK_EXCEPTION_GETPAGE = "autoTaskExceptionConfig-getPage";

    /**
     * 百川业务异常配置-新增
     */
    public static final String AUTOTASK_EXCEPTION_ADD = "autoTaskExceptionConfig-add";

    /**
     * 百川业务异常配置-编辑
     */
    public static final String AUTOTASK_EXCEPTION_UPDATE = "autoTaskExceptionConfig-update";

    /**
     * 百川业务异常配置-删除
     */
    public static final String AUTOTASK_EXCEPTION_DELETE = "autoTaskExceptionConfig-delete";

    /**
     * 电子保单管理-查看
     */
    public static final String POLICYSOURCE_GETPAGE = "policySource-getPage";
    /**
     * 电子保单管理-删除
     */
    public static final String POLICYSOURCE_DELETE = "policySource-delete";
    /**
     * 电子保单管理-查看保单
     */
    public static final String POLICYSOURCE_GETPOLICY = "policySource-getPolicy";
    /**
     * 电子保单管理-批量删除
     */
    public static final String POLICYSOURCE_DELETE_BATCH = "policySource-deleteBatch";

    /**
     * 来源归类-新建
     */
    public static final String DATA_SOURCE_CONFIG_SAVE = "dataSourceConfig-save";

    /**
     * 来源归类-编辑
     */
    public static final String DATA_SOURCE_CONFIG_UPDATE = "dataSourceConfig-update";

    /**
     * 来源归类-删除
     */
    public static final String DATA_SOURCE_CONFIG_DELETE = "dataSourceConfig-delete";

}
