buildscript {
    repositories {
        maven {
            url "http://*************:8081/nexus/content/repositories/releases/"
            allowInsecureProtocol true
        }
        maven {
            url "http://*************:8081/nexus/content/repositories/snapshots/"
            allowInsecureProtocol true
        }
        mavenCentral()
        maven { url 'https://repo.spring.io/libs-snapshot' }
    }
}

allprojects {
    group 'com.cheche365.bc'
    version = '1.1.0'

    apply plugin: 'groovy'

    sourceCompatibility = 17
    targetCompatibility = 17

    def runArgs = project
            .gradle
            .getStartParameter()
            .taskRequests*.args
            .flatten()

    if (runArgs.contains('--tests') || runArgs.contains(':test')) {
        def testDir = runArgs
                .find { it.toString().startsWith('com.cheche365.bc.instest') }
                .tokenize('\\.')[4]
        if (!testDir) {
            throw new GradleException("测试目录未按约定配置")
        }
        sourceSets {
            main {
                groovy {
                    srcDirs = [
                            'src/main/groovy/common',
                            'src/main/groovy/com/cheche365/bc/entity',
                            "src/main/groovy/${testDir}"
                    ]
                }
            }
            test {
                groovy {
                    srcDirs = [
                            'src/test/groovy/com/cheche365/bc',
                            'src/test/groovy/com/cheche365/bc/utils',
                            "src/test/groovy/com/cheche365/bc/instest/${testDir}/edi",
                            "src/test/groovy/com/cheche365/bc/instest/${testDir}/robot"
                    ]
                }
            }
        }

        compileTestGroovy {
            include { FileTreeElement file ->
                def sysSeparator = File.separator
                return ['DataSource.java', 'SingleCompanyTest.java', 'RedisUtil.java'].contains(file.name) ||
                        file.getFile().absolutePath.contains("${sysSeparator + testDir + sysSeparator}")
            }
        }
    }

    [compileJava, compileTestJava, compileGroovy, compileTestGroovy, javadoc]*.options*.encoding = 'UTF-8'

    repositories {
        maven { url "https://maven.aliyun.com/nexus/content/groups/public/" }
        maven {
            url "http://*************:8081/nexus/content/repositories/releases/"
            allowInsecureProtocol true
        }
        maven {
            url "http://*************:8081/nexus/content/repositories/snapshots/"
            allowInsecureProtocol true
        }
        // 使用本地maven库
        mavenLocal()
        // 使用中央mav库
        mavenCentral()
    }


    dependencies {
        implementation('com.cheche365.bc:core:1.1.9-SNAPSHOT:plain') {
            exclude group: 'org.springframework.boot', module: 'spring-boot-starter-data-mongodb'
            exclude group: 'org.springframework.boot', module: 'spring-boot-starter-data-redis'
            exclude group: 'org.springframework.session', module: 'spring-session-data-redis'
        }
        implementation 'com.cheche365.common:cheche365-common-signature-enhance:1.0.0-SNAPSHOT'

        // 测试时需要groovy支持
        implementation 'org.codehaus.groovy:groovy-all:3.0.9'
        implementation('com.cheche365.redisson:redisson:1.0.5-SNAPSHOT') {
            exclude(group: 'org.springframework.boot', module: 'spring-boot-starter-aop')
        }
        implementation fileTree(dir: 'libs', includes: ['*.jar'])
    }
}
