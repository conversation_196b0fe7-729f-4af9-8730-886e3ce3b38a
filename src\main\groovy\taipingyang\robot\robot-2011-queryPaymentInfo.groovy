package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import taipingyang.robot.module.Robot2011Util

import java.time.LocalDate

@BaseScript BaseScript_Http_Enq _

if ("441900" != entity?.order?.insureArea?.city) {
    skip("非东莞，跳过交管")
}

JSONObject param = JSON.parseObject("{\n" +
        "\t\"meta\": {\n" +
        "\t\t\"pageSize\": 8\n" +
        "\t},\n" +
        "\t\"redata\": {\n" +
        "\t\t\"quotationNo\": \"\",\n" +
        "\t\t\"insuredNo\": \"\",\n" +
        "\t\t\"policyHolder\": \"\",\n" +
        "\t\t\"policyNo\": \"\",\n" +
        "\t\t\"partyName\": \"\",\n" +
        "\t\t\"paymentType\": \"\",\n" +
        "\t\t\"payStatus\": \"\",\n" +
        "\t\t\"payNo\": \"\",\n" +
        "\t\t\"chequeNo\": \"\",\n" +
        "\t\t\"dateType\": \"101\",\n" +
        "\t\t\"beginDate\": \"2022-10-06\",\n" +
        "\t\t\"endDate\": \"2022-10-20\",\n" +
        "\t\t\"plateNo\": \"京AU0393\"\n" +
        "\t}\n" +
        "}")

param?.redata?.beginDate = LocalDate.now().plusDays(-7).toString()
param?.redata?.endDate = LocalDate.now().toString()
param?.redata?.plateNo = entity?.order?.carInfo?.plateNum
if (entity?.order?.carInfo?.isNew) {
    param?.redata?.plateNo = "LS" + entity.order.carInfo.vin.substring(entity.order.carInfo.vin.length() - 6)
}

autoTask.params.clear()

head Robot2011Util.getDefaultHead(tempValues, config)

body Robot2011Util.genBody(tempValues, param.toString())
