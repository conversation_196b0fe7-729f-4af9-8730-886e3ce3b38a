package renbao.edi


import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.message.TaskStatus
import com.cheche365.bc.message.TaskType

import static common.common_all.*
import org.apache.commons.lang3.StringUtils

import static renbao.common.renbao_dict.insureFlag
import static renbao.common.renbao_dict.kindENNew
import static renbao.common.renbao_dict.kindNameNew
import static renbao.common.renbao_dict.natureTocheche



    def script  = new edi_common_2005()
    def response = root
    script.commonDeal(response)
    if (response?.Header?.responsehead?.response_code == 1) {
        def prpCmainVo = response?.Body?.GETPOLICYDETAILCONDITIONRTN?.BIZ_ENTITY?.PrpCmainVo
        def underWriteFlag = prpCmainVo.UnderWriteFlag.toString()
        if (["1", "3", "4", "5"].contains(underWriteFlag)) {
            def bizSuiteInfo = enquiry?.baseSuiteInfo?.bizSuiteInfo ?: [:]
            if (tempValues['taskType'] != TaskType.AUTO_INSURE.code) {
                def prpCitemCarVo = prpCmainVo?.PrpCitemCarVos?.PrpCitemCarVo
                prpCitemCarVo.each {
                    useNatureCode = it.UseNatureCode.toString()
                    if (StringUtils.isNotBlank(useNatureCode)) {
                        def useProp = natureTocheche(useNatureCode)
                        if (useProp)
                            enquiry['carInfo']['useProps'] = useProp
                    }
                }
                def SQ = enquiry['SQ'] = enquiry['SQ'] ?: [:]
                def suiteSet = prpCmainVo?.PrpCitemKindVos?.PrpCitemKindVo
                def bizDiscountCharge = prpCmainVo?.SumPremium?.toBigDecimal()//商业险折后
                def bizOrgCharge =new BigDecimal(prpCmainVo?.SumPremium?.toString() ?: '0').add(new BigDecimal(prpCmainVo?.SumDiscount?.toString() ?: '0'))//商业险原始
                def suiteList = []
                def shareAmount = shareAmount(enquiry)
                suiteSet.each {
                    def info = [:]
                    def code = it.KindCode.toString()
                    def enCode = kindENNew(code)
                    if (shareAmount && ['051063', '051072', '051073'].contains(code) && shareAmount.containsKey(enCode - 'NIHC') ) {
                        info << [share : true]
                    } else {
                        info << [share : false]
                    }
                    def name = kindNameNew(enCode)
                    def quantity = it.Quantity.toString()
                    info << [code : enCode]
                    info << [name : name]
                    info << [orgCharge : it.BenchMarkPremium ? it.BenchMarkPremium.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0]
                    info << [discountCharge : it.Premium ? it.Premium.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0]
                    info << [discountRate : it.Discount ? it.Discount.toBigDecimal().setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() : 0]
                    if ("051053".equals(code)){
                        info << [amount : it.UnitAmount ? it.UnitAmount.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0]
                    } else if (["051064", "051080", "051079", "051081"].contains(code)) {
                        info << [amount : quantity]
                    }
                    else {
                        info << [amount : it.Amount ? it.Amount.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0]
                    }

                    suiteList += info
                }
                bizSuiteInfo['discountRate'] = suiteList[0]?.discountRate
                bizSuiteInfo['orgCharge'] = bizOrgCharge
                bizSuiteInfo['discountCharge'] = bizDiscountCharge
                bizSuiteInfo['suites'] = suiteList
                SQ['bizCharge'] = bizDiscountCharge
                def efcCharge = new BigDecimal(SQ.get("efcCharge")?.toString() ?: "0")
                def taxCharge = new BigDecimal(SQ.get("taxCharge")?.toString() ?: "0")
                def totalCharge = bizDiscountCharge.add(efcCharge).add(taxCharge)
                SQ['totalCharge'] = totalCharge
            }
            def effectiveTime = script.getEffectiveTimeFromIns(prpCmainVo)
            bizSuiteInfo['start'] = effectiveTime['startTime']
            bizSuiteInfo['end'] = effectiveTime['endTime']
        } else if (["0", "6", "7", "9"].contains(underWriteFlag)) {
            enquiry['taskStatus'] = TaskStatus.AUTO_INSURE_WAIT_QUERY.getState()
        } else {
            enquiry['taskStatus'] = TaskStatus.AUTO_INSURE_FAILED.getState()
            def errorInfo = [:]
            errorInfo.errorcode = "11"
            errorInfo.errordesc = underWriteFlag ? insureFlag(underWriteFlag) : "核保状态查询失败!"
            enquiry.errorInfo = errorInfo
            throw new InsReturnException(errorInfo.errordesc as String)
        }
    } else {
        def msg = response?.Header?.responsehead?.error_message
        throw new InsReturnException(msg.toString())
    }

