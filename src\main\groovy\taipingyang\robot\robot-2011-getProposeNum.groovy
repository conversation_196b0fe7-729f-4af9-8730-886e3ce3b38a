package taipingyang.robot

import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotDict_2011

@BaseScript BaseScript_Http_Enq _

head Robot2011Util.getDefaultHead(tempValues, config)

body Robot2011Util.serializer(tempValues), {
    RobotDict_2011.makeBaseReqBody(['pageSize': 8]) {
        [
                "quotationNo"   : tempValues.quotationNo,
                "insuredNo"     : "",
                "policyNo"      : "",
                "licensePlate"  : "",
                "policyHolder"  : "",
                "insurant"      : "",
                "productType"   : "",
                "quotationState": "",
                "dateType"      : "101",
                "startDate"     : "",
                "endDate"       : "",
                "isPrint"       : ""
        ]
    }
}
