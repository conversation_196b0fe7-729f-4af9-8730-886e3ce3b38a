package renbao.robot


import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript

import static renbao.robot.common_market.header
import static renbao.robot.common_market.personInfoQueryUrl



@BaseScript BaseScript_Http_Enq _


assertNotNull('未送传被保人信息', entity.order.insuredPersons[0].idCard)

skip('被保人与投保人相同，跳过当前模板') {
    entity.order.insurePerson.idCard == entity.order.insuredPersons[0].idCard
}

head(header(autoTask))

tempValues['queryInsured'] = personInfoQueryUrl(autoTask, 'insured')
