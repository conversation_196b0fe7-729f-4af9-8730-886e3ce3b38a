package renbao.edi

import static renbao.common.renbao_dict.*
import static common.common_all.*


    /**
     * 报价单转投保单Q04
     */

    def configs = enquiry.configInfo.configMap
    def script = new edi_common_2005()
    def quotationNo = enquiry?.tempValues?.biQuotationNo ?: enquiry?.tempValues?.ciQuotationNo
    def queryId = UUID.randomUUID().toString()
    def carChecker = configs?.CarChecker
    // 电子投保标志，如果为团体只能是纸质投保为0，个人为电子投保
    def netFlag = [6, 8, 9, 10].contains(enquiry?.insurePerson?.idCardType) ? 0 : 1
    if (enquiry?.insArea['city'] == '440300') {
        netFlag = 0
    }
    // 保单类型： 0.监制保单 1 电子保单 2 同时有效
    def isNetProp = configs?.IsNetProp ?: 1
    if (enquiry?.insArea['city'] == '440100' || enquiry?.insArea['city'] == '441900') {
        isNetProp = 2
    }
    def email = configs?.policyEmail ?: script.applicantEmail(enquiry)
    def licenseColorCode = formatPlateColor(enquiry?.carInfo?.plateColor)
    def cstTime = script.getCurCSTtime()
    def piccAreaCode = script.getPiccAreaCode(enquiry, config)
    def assetAgentCode = configs.AssetAgentCode ?: ""
    def assetAgentName = configs.AssetAgentName ?: ""
    def assetAgentPhone = enquiry.insArea?.city == "440300" ? (configs?.AssetAgentPhone ?: "15989493114") : ""
    def ebs = enquiry?.SQ?.nonMotor?.accidentProposeCode
    def riskCode = ["ZBM", "EAD", "YEL", "EBS", "JDI", "LCO", "EAU"].find {ebs.toString().indexOf(it) > -1}
    def checkTime = script.getTimePattern('yyyy-MM-dd')
    def saleCompany = enquiry?.carInfo?.isNew ? (getSupplyParam(enquiry, "newCarSaleComName") ?: "") : ""
    def saleAreaCode = enquiry?.carInfo?.isNew ? (getSupplyParam(enquiry, "newCarSaleComCityCode") ?: "") : ""
    def sale4SFlag = enquiry?.carInfo?.isNew ? (getSupplyParam(enquiry, "isSaleBy4S") ?: "") : ""
    def appIdCardType = [6, 8, 9, 10].contains(enquiry?.insurePerson?.idCardType) ? 2 : 1
    def holdName = appIdCardType == 1 ? (getSupplyParam(enquiry, 'phoneHoldName') ?: enquiry?.insurePerson?.name) : (getSupplyParam(enquiry, 'phoneHoldName') ?: getSupplyParam(enquiry, 'liaisonName'))
    def holdIdentifyNumber = appIdCardType == 1 ? (getSupplyParam(enquiry, 'phoneHoldCertificateNo') ?: enquiry?.insurePerson?.idCard) : (getSupplyParam(enquiry, 'phoneHoldCertificateNo') ?: getSupplyParam(enquiry, 'liaisonIDCardNo'))
    def usedCarInvoiceAmount = (enquiry?.carInfo?.isTransfer && enquiry?.baseSuiteInfo?.bizSuiteInfo) ? carActualValue(autoTask) : (tempValues['carInvoice'] ? carActualValue(autoTask) : '')

    // 需求11612
    def localUse = (getSupplyParam(enquiry, 'isLocalDriving') ?:  enquiry?.carInfo?.isLocalDriving) ?: '1'
    def localLicense = (getSupplyParam(enquiry, 'isLocalRegistration') ?:  enquiry?.carInfo?.isLocalRegistration) ?: ('新车未上牌' == enquiry?.carInfo?.plateNum ? '1' : '')

    // 需求14583
    def drivingLicenseIssueDateFromSupply = getSupplyParam(enquiry, 'drivingLicenseIssueDate')
    def drivingLicenseIssueDate = drivingLicenseIssueDateFromSupply ?: ('新车未上牌' == enquiry?.carInfo?.plateNum ? enquiry?.carInfo?.firstRegDate?.substring(0, 10) : '')

    // 需求14733
    def specialMonopoly = configs?.specialMonopoly ?: ""
    def monopolyCode = configs?.monopolyCode ?: ""
    def monopolyFlag = monopolyCode ? "1" : ""
    def monopolyName = monopolyCode ? (configs?.monopolyName ?: "") : ""

    def reqXml =
        """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
            <soap:Header xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                <nshead:requesthead	xmlns:nshead="http://pub.webservice.cmp.com">
                    <nshead:request_type>Q04</nshead:request_type>
                    <nshead:uuid>${queryId}</nshead:uuid>
                    <nshead:sender>${config?.sender ?: '0595'}</nshead:sender>
                    <nshead:server_version>00000000</nshead:server_version>
                    <nshead:user>${config?.user ?: '0595'}</nshead:user>
                    <nshead:password>${config?.password ?: 'EC00FFFD8E9F7F2289752081793E9905'}</nshead:password>
                    <nshead:ChnlNo>${config?.ChnlNo ?: 'cheche'}</nshead:ChnlNo>
                    <nshead:areacode>${piccAreaCode}</nshead:areacode>
                    <nshead:flowintime>${cstTime}</nshead:flowintime>
                </nshead:requesthead>
            </soap:Header>
            <soapenv:Body>
                <pan:TRANSTEMP2POLICYREQ   xmlns:pan="http://pan.prpall.webservice.cmp.com">
                    <pan:BIZ_ENTITY> """
        reqXml += """<CarQuoteTransProposalReqList>"""
        reqXml += """<CarQuoteTransProposalReq>
                            <QuotationNo>${quotationNo}</QuotationNo>
                            <ColorCode>${licenseColorCode}</ColorCode>
                            <AliasName></AliasName>
                            <Resource>${config?.Resource ?: '0595'}</Resource>
                            <UsedCarInvoiceAmount>${usedCarInvoiceAmount}</UsedCarInvoiceAmount>
                            <Authorization>0</Authorization>
                            <LocalUse>${localUse}</LocalUse>
                            <LocalLicense>${localLicense}</LocalLicense>
                            <IssueDate>${drivingLicenseIssueDate}</IssueDate>
                            <SaleCompany>${saleCompany}</SaleCompany>
                            <SaleAreaCode>${saleAreaCode}</SaleAreaCode>
                            <Sale4SFlag>${sale4SFlag}</Sale4SFlag>"""
        if ("1" == specialMonopoly && monopolyCode) {
            reqXml += """<MonopolyCode>${monopolyCode}</MonopolyCode>
                         <MonopolyFlag>${monopolyFlag}</MonopolyFlag>
                         <MonopolyName>${monopolyName}</MonopolyName>"""
        }
        if (enquiry.insArea?.province == '310000') {
            reqXml += """<CarQuoteInsuredRealList>
                            <SerialNo>1</SerialNo>
                            <HoldName>${holdName}</HoldName>
                            <HoldIdentifyNumber>${holdIdentifyNumber}</HoldIdentifyNumber>
                            <HoldIdentifyType>01</HoldIdentifyType>
                            <HoldType>02</HoldType>
                        </CarQuoteInsuredRealList>"""
        }
        reqXml += """<CarQuoteCmainCarReq>
                        <CarCheckStatus>1</CarCheckStatus>
                        <CarChecker>${carChecker}</CarChecker>
                        <CarCheckTime>${checkTime}</CarCheckTime>
                        <NetSales1>${isNetProp}</NetSales1>
                        <NetSales2>${isNetProp}</NetSales2>
                        <IsNetProp>${netFlag}</IsNetProp>
                        <Email>${email}</Email>
                        <IsSendEmail>1</IsSendEmail>
                        <IdentityFlag>1</IdentityFlag>
                        <Image>0</Image>
                        <AssetAgentName>${assetAgentName}</AssetAgentName>
                        <AssetAgentCode>${assetAgentCode}</AssetAgentCode>
                        <AssetAgentPhone>${assetAgentPhone}</AssetAgentPhone>"""
        if ("1" == specialMonopoly && monopolyCode) {
            reqXml += """<MonopolyCode>${monopolyCode}</MonopolyCode>
                         <MonopolyFlag>${monopolyFlag}</MonopolyFlag>
                         <MonopolyName>${monopolyName}</MonopolyName>"""
        }
                     reqXml += """</CarQuoteCmainCarReq>
                 <CarQuoteEngageaReqList>"""
        //格式：险种0|特约代码995600|占位符值|字符串@#@险种0|特约代码995600|占位符值|字符串
        // 先 "@#@" 分隔每个特约，每个特约再 "|" 分隔
        def specialStr = enquiry?.specialStr ?: tempValues['specialStr']
        if (specialStr) {
           def specialArray = specialStr.split("@#@")
           specialArray.eachWithIndex { it, index ->
               def detail = it.split("\\|")
               if (detail.size() > 1) {
                   def code = detail[1]
                   def name = detail[2]
                   def clauses = ''
                   if (detail.size() > 3)
                       clauses = detail[3]
                   reqXml += """<CarQuoteEngageaReq>
                                    <ItemKindNo>${index}</ItemKindNo>
                                    <ClauseCode>${code}</ClauseCode>
                                    <ClauseName>${name}</ClauseName>
                                    <Clauses>${clauses}</Clauses>
                             </CarQuoteEngageaReq>"""
               }
           }
        }
        reqXml += """</CarQuoteEngageaReqList>
                </CarQuoteTransProposalReq>"""
        if (ebs) {
             reqXml += """ 
                 <CarQuoteTransProposalReq>
                    <RiskCode>${riskCode}</RiskCode>
                    <ProposalNoBI>${ebs}</ProposalNoBI>
                 </CarQuoteTransProposalReq>"""
        }
        reqXml += """</CarQuoteTransProposalReqList>"""
        reqXml += """</pan:BIZ_ENTITY>
                    <pan:EXTEND>
                    <pan:PARAM></pan:PARAM>
                    </pan:EXTEND>
                </pan:TRANSTEMP2POLICYREQ>
            </soapenv:Body>
        </soapenv:Envelope>"""

