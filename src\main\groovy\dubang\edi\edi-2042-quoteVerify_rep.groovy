package dubang.edi

import com.cheche365.bc.exception.InsReturnException
import dubang.edi.edi_2042_util as util

tempValues.checkUUID = ''
util.getResult(root, enquiry.configInfo.configMap.userCode)

def records = root?.result?.data?.records[0]
util.writeBack(records, enquiry, tempValues)
//调用规则引擎
if (!tempValues.repeateRule) {
    util.doRuleAndPlat(records, autoTask, enquiry, tempValues)
    if (tempValues?.geniusItemTotalDiscount) {
        tempValues?.repeateRule = 'true'
        def ex = new InsReturnException(InsReturnException.AllowRepeat, '规则返回折扣系数 重新报价')
        ex.setStep(2)
        throw ex
    }
}
