package com.cheche365.bc.instest.renbao.robot;

import com.cheche365.bc.SingleCompanyTest;
import com.cheche365.bc.message.TaskType;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.junit.Test;

/**
 * 人保精灵四代测试类
 */
public class RenBaoFourth extends SingleCompanyTest {


    private static final CloseableHttpClient httpClient;

    static {
        httpClient = HttpSender.buildHttpClient();
    }

    private AutoTask t;

    @Override
    protected void init() {
        this.comId = "2005";
        this.cityCode = "440100";
        this.org = "1244000000";
    }

    private void initAutoTask(String taskType) throws Exception {
        t = this.getMockDataFromFile();
        t.setTaskType(taskType);
        t.setHttpClient(httpClient);
    }

    private void doLogin() throws Exception {
        this.singleInterface("robot-2005-fourth-login",  "${loginUrl}", t, "四代登陆动作",HttpSender.HTTP_POST_METHOD);
    }

    private void doQuery() throws Exception {
        this.singleInterface("robot-2005-fourth-query",  "${queryUrl}", t, "四代查询",HttpSender.HTTP_POST_METHOD);
    }

    /**
     * 核保查询，承保查询
     * @throws Exception
     */
    @Test
    public void query() throws Exception {
        initAutoTask(TaskType.INSURE_QUERY.code);
        doLogin();
        doQuery();
    }

    private void doPay() throws Exception {
        this.singleInterface("robot-2005-fourth-pay",  "${payUrl}", t, "四代查询",HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void pay() throws Exception{
        initAutoTask(TaskType.PAY.code);
        doLogin();
        doPay();
    }

    private void doQuote() throws Exception {
        this.singleInterface("robot-2005-fourth-vehicleQuery",  "${vehicleQuery}", t, "车型名称查车",HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2005-fourth-customQueryP",  "${customQueryP}", t, "关系人查询",HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2005-fourth-customPersonAdd",  "${customPersonAdd}", t, "关系人查询",HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2005-fourth-vehicleQuery",  "${vehicleQuery}", t, "车型名称查车",HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2005-fourth-calActualValue",  "${calActualValue}", t, "关系人查询",HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2005-fourth-quote",  "${quoteUrl}", t, "报价",HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void quote() throws Exception {
        initAutoTask(TaskType.QUOTE.code);
        doLogin();
        doQuote();
    }

    private void doInsure() throws Exception {
        this.singleInterface("robot-2005-fourth-insure",  "${insureUrl}", t, "暂存",HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2005-fourth-img",  "${queryUrl}", t, "图片查询",HttpSender.HTTP_POST_METHOD);
    }
    //核保暂存
    @Test
    public void insure() throws Exception {
        initAutoTask(TaskType.INSURE.code);
        doLogin();
        doQuote();
        doInsure();
    }

    private void doAutoInsure() throws Exception {
        this.singleInterface("robot-2005-fourth-submit",  "${submitUrl}", t, "提核",HttpSender.HTTP_POST_METHOD);
    }

    //核保暂存
    @Test
    public void autoInsure() throws Exception {
        initAutoTask(TaskType.AUTO_INSURE.code);
        doLogin();
        doQuote();
        doInsure();
        doAutoInsure();
    }

    private void doDownload() throws Exception {
        this.singleInterface("robot-2005-fourth-efcdownload", "${queryUrl}", t, "交强保单", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2005-fourth-efcdownload", "${queryUrl}", t, "交强保单", HttpSender.HTTP_POST_METHOD);
    }

    //电子保单下载
    @Test
    public void downLoad() throws Exception {
        initAutoTask("policyDownload");
        doLogin();
        doDownload();
    }
}



