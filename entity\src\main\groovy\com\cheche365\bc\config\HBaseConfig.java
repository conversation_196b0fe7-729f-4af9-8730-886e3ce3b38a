package com.cheche365.bc.config;


import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@Configuration
public class HBaseConfig {

    @Autowired
    private HBaseProperties hBaseProperties;

    @Bean
    public org.apache.hadoop.conf.Configuration hbaseConfiguration() {
        org.apache.hadoop.conf.Configuration conf = HBaseConfiguration.create();
        conf.set(HConstants.ZOOKEEPER_QUORUM, hBaseProperties.getZkQuorum());
        conf.set(HConstants.ZOOKEEPER_ZNODE_PARENT, hBaseProperties.getZkZoneParent());
        return conf;
    }

    @Bean
    public Connection hbaseConnection(org.apache.hadoop.conf.Configuration hbaseConfiguration) throws IOException {
        return ConnectionFactory.createConnection(hbaseConfiguration);
    }

}
