package renbao.edi


import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.exception.TempSkipException
import com.cheche365.bc.utils.sender.HttpSender
import org.apache.commons.lang3.StringUtils

import java.nio.ByteBuffer
import java.nio.channels.FileChannel
import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.regex.Pattern
import static common.common_all.*


def commonDeal(response) {
    ['功能异常', '无响应', '没有接口的相关权限', '系统没有访问此接口权限', '访问受限', '数组下标越界', '空指针异常', '解析报文出错'].find {
        if (response.toString().indexOf(it) > -1) {
            throw new InsReturnException('没有接口的相关权限' == it ? '没有接口的相关权限！' : (['访问受限', '系统没有访问此接口权限', '无法选择服务经理'].contains(it) ? '请检查账号信息' : '保险公司接口异常,请稍后重试！'))
        }
    }

}

def carInsuredRelation(enquiry) {
    if (enquiry?.carInfo?.useProps == 1) {
        //私家车
        return enquiry?.carOwnerInfo?.idCard == enquiry?.insuredPersonList?.get(0)?.idCard ? 1 : 2
    } else {
        //公户车
        return enquiry?.carOwnerInfo?.idCard == enquiry?.insuredPersonList?.get(0)?.idCard ? 1 : 3
    }
}

def getCurCSTtime() {
    def localDateTime = LocalDateTime.now()
    localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS 'CST'"))
}

def getTimePattern(pattern) {
    def localDateTime = LocalDateTime.now()
    localDateTime.format(DateTimeFormatter.ofPattern(pattern))
}

def getCurime() {
    def localDateTime = LocalDateTime.now()
    localDateTime.format(DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss.SSS'))
}

def getNextYear(sDate) {
    def date = LocalDate.parse(sDate)
    def newYear = date.plusYears(1)
    newYear.plusDays(-1).toString()

}

def caculateDay(sDate, dayPlus, yearPlus) {
    def date = LocalDate.parse(sDate)
    def newYear = date.plusYears(yearPlus)
    newYear.plusDays(dayPlus).toString()
}

/**
 * 传入时间与当前时间间隔
 * @param sDate
 */
def intervalNow(sDate) {
    def today = LocalDate.now()
    def date = LocalDate.parse(sDate)
    daysDiff = ChronoUnit.DAYS.between(today, date)

}


def getStartDay(datetime) {
    def dateTimeFormatter = DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss')
    def localDate = LocalDate.parse(datetime, dateTimeFormatter)
    localDate.format(DateTimeFormatter.ofPattern('yyyy-MM-dd'))
}

def getHour(datetime) {
    def dateTimeFormatter = DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss')
    def time = LocalDateTime.parse(datetime, dateTimeFormatter)
    time.getHour()
}

def getMinute(datetime) {
    def dateTimeFormatter = DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss')
    def time = LocalDateTime.parse(datetime, dateTimeFormatter)
    time.getMinute()
}

def getNeedDay(sDate, pattern) {
    def dateTimeFormatter = DateTimeFormatter.ofPattern(pattern)
    def localDate = LocalDate.parse(sDate, dateTimeFormatter)
    localDate.format(DateTimeFormatter.ofPattern('yyyy-MM-dd'))

}

def getNeedTime(sDate, pattern, backPattern) {
    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern)
    LocalDateTime time = LocalDateTime.parse(sDate, dateTimeFormatter)
    time.format(DateTimeFormatter.ofPattern(backPattern))
}


def applicantMobile(enquiry) {
    def result = enquiry?.supplyParam?.find {
        it['itemcode'] == 'applicantMobile'
    }
    if (result) {
        return result['itemvalue']
    } else {
        throw new InsReturnException('补充数据项中投保人手机号为空，请检查！')
    }
}

def insuredMobile(enquiry) {

    def result = enquiry?.supplyParam?.find {
        it['itemcode'] == 'insuredMobile'
    }
    if (result) {
        return result['itemvalue']
    } else {
        throw new InsReturnException('补充数据项中被保人手机号为空，请检查！')
    }
}

def ownerMobile(enquiry) {

    def result = enquiry?.supplyParam?.find {
        it['itemcode'] == 'ownerMobile'
    }
    if (result) {
        return result['itemvalue']
    } else {
        throw new InsReturnException('补充数据项中车主手机号为空，请检查！')
    }
}

def applicantEmail(enquiry) {
    def result = enquiry?.supplyParam?.find {
        it['itemcode'] == 'applicantEmail'
    }
    if (result) {
        return result['itemvalue']
    } else {
        throw new InsReturnException('当保单类型为电子保单时，邮箱不能为空，补充数据项中投保人邮箱为空，请检查！')
    }
}

def applicantAdress(enquiry) {
    def result = enquiry?.supplyParam?.find {
        it['itemcode'] == 'applicantAddress'
    }
    if (result) {
        return result['itemvalue'].replaceAll('\\|\\d{6}+#', '')
    }
    return null
}

def insuredAdress(enquiry) {

    def result = enquiry?.supplyParam?.find {
        it['itemcode'] == 'insuredAddress'
    }
    if (result) {
        return result['itemvalue'].replaceAll('\\|\\d{6}+#', '')
    }
    return null
}

def ownerAdress(enquiry) {
    def result = enquiry?.supplyParam?.find {
        it['itemcode'] == 'ownerAddress'
    }
    if (result) {
        return result['itemvalue'].replaceAll('\\|\\d{6}+#', '')
    }
    return null
}

def ownerStart(enquiry) {
    def result = enquiry?.supplyParam?.find {
        it['itemcode'] == 'ownerIDCardRegistrationDate'
    }
    if (result) {
        return result['itemvalue']
    }
    return null
}

def ownerEnd(enquiry) {
    def result = enquiry?.supplyParam?.find {
        it['itemcode'] == 'ownerIDCardValidDate'
    }
    if (result) {
        if (result['itemvalue'] == '长期') {
            return '2199-12-31'
        } else {
            return result['itemvalue']
        }
    }
    return null
}

def appStart(enquiry) {
    def result = enquiry?.supplyParam?.find {
        it['itemcode'] == 'applicantIDCardRegistrationDate'
    }
    if (result) {
        return result['itemvalue']
    }
    return null

}

def appEnd(enquiry) {
    def result = enquiry?.supplyParam?.find {
        it['itemcode'] == 'applicantIDCardValidDate'
    }
    if (result) {
        if (result['itemvalue'] == '长期') {
            return '2199-12-31'
        } else {
            return result['itemvalue']
        }
    }
    return null
}

def insuredStart(enquiry) {
    def result = enquiry?.supplyParam?.find {
        it['itemcode'] == 'insuredIDCardRegistrationDate'
    }
    if (result) {
        return result['itemvalue']
    }
    return null
}

def insuredEnd(enquiry) {
    def result = enquiry?.supplyParam?.find {
        it['itemcode'] == 'insuredIDCardValidDate'
    }
    if (result) {
        if (result['itemvalue'] == '长期') {
            return '2199-12-31'
        } else {
            return result['itemvalue']
        }
    }
    return null
}

def getFile(imgUrl) {
    def file = new URL(imgUrl).withInputStream { is ->
        File.createTempFile('piccuk-upload-image', '.jpg').with { tmpFile ->
            tmpFile.withOutputStream { os ->
                os << is
            }
            tmpFile
        }
    }
    return file
}

def getFileMD5String(File file) {
    String ret
    FileInputStream input
    FileChannel ch
    def messageDigest

    try {
        messageDigest = MessageDigest.getInstance('MD5')
        input = new FileInputStream(file)
        ch = input.getChannel()

        ByteBuffer byteBuffer = ch.map(FileChannel.MapMode.READ_ONLY, 0,
                file.length())
        messageDigest.update(byteBuffer)
        ret = bytesToHex(messageDigest.digest())
        return ret
    } catch (e) {

    } finally {
        if (input != null) {
            try {
                input.close()
            } catch (e) {
            }
        }
        if (ch != null) {
            try {
                ch.close()
            } catch (e) {
            }
        }
    }

}

def bytesToHex(def bytes) {
    return bytesToHex(bytes, 0, bytes.length)

}

def bytesToHex(def bytes, int start, int end) {
    StringBuilder sb = new StringBuilder()
    for (int i = start; i < start + end; i++) {
        sb.append(byteToHex(bytes[i]))
    }
    return sb.toString()
}

def byteToHex(byte bt) {
    def HEX_DIGITS = ['0', '1', '2', '3', '4', '5',
                      '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'] as String[];
    return HEX_DIGITS[(bt & 0xf0) >> 4] + '' + HEX_DIGITS[bt & 0xf];

}


/**
 * 计算年龄
 * @param birthday 出生日期，yyyy-MM-dd
 * @return
 */
def calculateAge(birthday) {
    if (birthday instanceof Date) {
        birthday = new SimpleDateFormat('yyyy-MM-dd').format(birthday)
    }
    if (birthday)
        return LocalDate.parse(birthday, DateTimeFormatter.ofPattern('yyyy-MM-dd')).until(LocalDate.now()).years
    else
        return ''
}

def carproofno(enquiry) {
    def carproofno
    def arr = enquiry?.supplyParam
    if (arr) {
        arr.each {
            if (it['itemcode'] == 'carOriginProofNo' || it['itemcode'] == 'carproofno') {
                carproofno = it['itemvalue']
            }
        }
    }
//        if ('新车未上牌' == enquiry?.carInfo?.plateNum && !carproofno && enquiry?.insArea?.province.toString().startsWith('310')) {
//           throw new InsReturnException('新车补充信息必须包含车辆来历凭证编号')
//        }
    carproofno

}

def carproofdate(enquiry) {
    def carproofdate
    def arr = enquiry?.supplyParam
    if (arr) {
        arr.each {
            if (it['itemcode'] == 'carOriginProofDate' || it['itemcode'] == 'carproofdate') {
                carproofdate = it['itemvalue']
            }
        }
    }
//    if ('新车未上牌' == enquiry?.carInfo?.plateNum && !carproofdate && enquiry?.insArea?.province.toString().startsWith('310')) {
//        throw new InsReturnException('新车补充信息必须包含车辆来历凭证开具日期');
//    }
    carproofdate

}

def getAreaCode(enquiry) {
    return ['440300', '350200', '210200', '370200', '330200'].contains(enquiry?.insArea['city']) ? enquiry?.insArea['city'] : enquiry?.insArea['province']
}

def getPiccAreaCode(enquiry, config) {
    return config?.piccAreaCode ?: getAreaCode(enquiry) + '00'
}

def getZoneNo(enquiry, config) {
    return config?.zoneNo ?: getAreaCode(enquiry)
}

def lcoCheck(enquiry) {
    def sq = enquiry.SQ as JSONObject
    def str = sq.toJSONString()
    def m = str =~ /LCO/
    if (!m)
        throw new TempSkipException(1, '无LCO非车，跳过当前步骤！')

}

def getEnv(piccAreacode) {
    return 'https://esb.epicc.com.cn/InterfaceProxy/cmp/NoVehicleInProxyService' + '?zoneNo=' + piccAreacode
//    def env = System.getProperty("env") ?: "dev"
//    piccAreacode = env == 'dev' ? '00000000' : piccAreacode
//    return  (env == 'production' ? 'https://esb.epicc.com.cn/InterfaceProxy/cmp/NoVehicleInProxyService' : 'http://************:9101/InterfaceProxy/cmp/NoVehicleInProxyService') + '?zoneNo=' + piccAreacode
}

def invoice(enquiry, sourceType, policyNo, infoList) {
    def info = infoList.fpinfo
    def shortUrl
    info.each {
        shortUrl = it.shorturl.toString()
    }
    if (!shortUrl) throw new InsReturnException('未获取到发票下载链接')
    byte[] byteArr
    try (def httpClient = HttpSender.buildHttpClient()) {
        byteArr = HttpSender.doGet(httpClient, shortUrl as String, null, "UTF-8", null, true) as byte[]
    }
    if (byteArr && byteArr.size() > 0) {
        uploadOBS(enquiry, sourceType, byteArr, '2005', policyNo)
    } else {
        throw new InsReturnException('获取发票失败，请通过线下获取')
    }
}

def getCharactor(s) {
    def pattern = Pattern.compile("[a-zA-Z0-9]+")
    def matcher = pattern.matcher(s)
    def result = ''
    while (matcher.find()) {
        result += matcher.group()
    }
    return result
}

def getEffectiveTimeFromIns(response) {
    def startDate = response?.StartDate?.toString() ?: ""
    def endDate = response?.EndDate?.toString() ?: ""
    def startHour = response?.StartHour?.toString() ?: ''
    def startMinute = response?.StartMinute?.toString() ?: ''
    def endHour =  response?.EndHour?.toString() ?: ''
    def endMinute = response?.EndMinute?.toString() ?: ''
    if (StringUtils.isAnyBlank(startDate, endDate, startHour, startMinute, endHour, endMinute)) {
        throw new InsReturnException('保司返回车险起保终保时间不全，请检查!')
    }
    if (startHour.length() == 1) {
        startHour = "0" + startHour
    }
    if (startMinute.length() == 1) {
        startMinute = "0" + startMinute
    }
    def startTime = startDate + " " + startHour + ":" + startMinute + ":00"
    def endTime
    if (endHour == '24') {
        endTime = endDate + " 23:59:59"
    } else {
        if (endHour.length() == 1) {
            endHour = "0" + endHour
        }
        if (endMinute.length() == 1) {
            endMinute = "0" + endMinute
        }
        endTime = endDate + " " + endHour + ":" + endMinute + ":00"
    }
    return [
            'startTime' : startTime,
            'endTime' : endTime
    ]

}

