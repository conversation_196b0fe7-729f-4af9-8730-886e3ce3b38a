package dubang.edi

import com.cheche365.bc.exception.InsReturnException
import dubang.edi.edi_2042_util as util

util.getResult(root, enquiry.configInfo.configMap.userCode)
//筛选
def records = root?.result?.data?.records as List
def chooseCar
if(!records) {
    throw new InsReturnException('未返回车型数据')
}
if(records.size() > 1) {
    //精友code
    chooseCar = records.find {it.modelCode == enquiry?.carInfo?.jyCode}
    //价格一致
    if (!chooseCar) {
        chooseCar = records.find { it.purchasePrice == enquiry?.carInfo?.price }
    }
    //筛选最低价格
    if(!chooseCar) {
        def newRecords = records.sort { a, b ->
            return new BigDecimal(a.purchasePrice as String) - new BigDecimal(b.purchasePrice as String)
        }
        chooseCar = newRecords[0]
    }
} else {
    chooseCar = records[0]
}
//整备质量、排气量、座位数、使用性质 取上游
chooseCar << [
        'completekerbMass': enquiry?.carInfo?.fullLoad,
        'exaustScale'     : enquiry?.carInfo?.displacement,
        'seatCount'       : enquiry?.carInfo?.seatCnt
]
//选择燃油类型回写磐石
enquiry?.carInfo?.fuelType = chooseCar?.powerTypeCode as int
tempValues.carRecord = chooseCar
//车辆 国别类型，0:国产,1:合资,2:进口 对应报价的国别类型 0-B 1-B 2-A
tempValues.countryNature = '0101'
//producttype 产品体系：0101-机动车；0102-特种车；0103-摩托车拖拉机；0104-单程提车
tempValues.productType = '0101'
//riskcode 险种代码：0510-机动车；0511-特种车；0512-摩托车拖拉机；0513-单程提车；0515-新能源车
tempValues.riskCode = '0510'
//特种车
def syvehicletypecode = enquiry?.carInfo?.syvehicletypecode as String
if (syvehicletypecode.length() >= 2 && syvehicletypecode.substring(0, 2) in ['ZB', 'ZC']) {
    tempValues.productType = '0102'
    tempValues.riskCode = '0511'
}
//摩托车
if (syvehicletypecode in ['NB', 'MA', 'MB', 'MC']) {
    tempValues.productType = '0103'
    tempValues.riskCode = '0512'
}
