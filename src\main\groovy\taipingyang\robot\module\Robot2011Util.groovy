package taipingyang.robot.module

import cn.hutool.crypto.asymmetric.KeyType
import cn.hutool.crypto.asymmetric.RSA
import cn.hutool.http.HttpUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.utils.encrypt.AesDesEncryption
import com.cheche365.bc.utils.encrypt.EncryptEnum
import com.cheche365.bc.utils.encrypt.smUtils.SM4Util
import com.cheche365.bc.utils.sender.HttpSender
import common.scripts.ScriptUtil
import org.apache.http.impl.client.CloseableHttpClient
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 *
 * <AUTHOR>
 */
class Robot2011Util {

    private static final Logger log = LoggerFactory.getLogger(Robot2011Util.class)

    private static final int RETRY_TIMES = 3

    static aesEncrypt(String key, String content) {
        def aes = aes(key)
        return aes.encrypt(content)
    }

    static aesDecrypt(String key, String content) {
        def aes = aes(key)
        return aes.decrypt(content)
    }

    private static def aes(key) {
        return AesDesEncryption.builder()
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.AES_ECB_PKCS5Padding)
                .key(key as String)
                .keyFormat(EncryptEnum.KeyFormatEnum.UTF_8)
                .build()
    }

    static JSONObject safeParseObject(String jsonStr) {
        JSONObject result = null
        try {
            result = JSON.parseObject(jsonStr)
        } catch (Exception e) {
            log.warn("json parse error:${jsonStr}", e)
        }
        return result
    }

    static String fetch(CloseableHttpClient httpClient,
                        String url,
                        Map headers,
                        String httpMethod,
                        Map params,
                        String bodyStr) {
        if (!httpMethod) {
            throw new NullPointerException('Http Method不能为空')
        }

        //构建带参URL
        def reqUrl = url
        if (params) {
            reqUrl = HttpUtil.urlWithForm(url, params, RobotConstant_2011.DEFAULT_CHARSET, false)
        }

        //发起请求
        def responseBody = ''
        if (httpMethod == 'post') {
            responseBody = postWithRetry3Times(httpClient, reqUrl, headers, bodyStr)
        } else if (httpMethod == 'get') {
            responseBody = getWithRetry3Times(httpClient, reqUrl, headers)
        } else {
            throw new RuntimeException('不支持的Http Method:' + httpMethod)
        }

        return responseBody
    }

    static String getWithRetry3Times(
            CloseableHttpClient httpClient,
            String url,
            Map<String, String> headers
    ) {
        return HttpSender.doGet(httpClient, url, headers, null, RobotConstant_2011.DEFAULT_CHARSET_NAME, null, false)
    }

    /**
     * post请求，重试3次
     * @param httpClient
     * @param url
     * @param headers
     * @param bodyStr
     * @return
     */
    static String postWithRetry3Times(
            CloseableHttpClient httpClient,
            String url,
            Map<String, String> headers,
            String bodyStr) {
        return HttpSender.doPostWithRetry(
                RETRY_TIMES,
                httpClient,
                true,
                url,
                bodyStr,
                null,
                headers,
                RobotConstant_2011.DEFAULT_CHARSET_NAME,
                null,
                ''
        )
    }

    static String postWithRetry3Times(
            CloseableHttpClient httpClient,
            String url,
            String bodyStr,
            Map tempValues,
            Map config) {
        return postWithRetry3Times(httpClient, url, getDefaultHead(tempValues, config), bodyStr)
    }

    static String postWithRetry3Times(
            CloseableHttpClient httpClient,
            String url,
            String bodyStr) {
        return postWithRetry3Times(httpClient, url, RobotConstant_2011.DEFAULT_HTTP_HEAD, bodyStr)
    }

    static String postWithRetry3Times(
            CloseableHttpClient httpClient,
            String url,
            Map body) {
        String bodyStr = JSON.toJSONString(body)
        return postWithRetry3Times(httpClient, url, RobotConstant_2011.DEFAULT_HTTP_HEAD, bodyStr)
    }

    static String removeQuotationMarks(String str) {
        if (str.startsWith('\"') && str.endsWith('\"')) {
            return str.substring(1, str.length() - 1)
        }
        return str
    }

    static String amountToServiceTimes(String amount) {
        return amount.find(/\d+\.\d+/).toDouble().intValue().toString()
    }

    static Map<String, String> processAddress(address) {
        def matcher = address =~ RobotConstant_2011.REGEX_ADDRESS
        if (matcher.find()) {
            return [
                    'provinceName': matcher.group('provinceName'),
                    'provinceCode': matcher.group('provinceCode'),
                    'cityName'    : matcher.group('cityName'),
                    'cityCode'    : matcher.group('cityCode'),
                    'districtName': matcher.group('districtName'),
                    'districtCode': matcher.group('districtCode'),
                    'address'     : matcher.group('address')
            ]
        }

        return [
                'provinceName': '',
                'provinceCode': '',
                'cityName'    : '',
                'cityCode'    : '',
                'districtName': '',
                'districtCode': '',
                'address'     : ''
        ]
    }

    /**
     * RSA 解密
     * @param context
     * @return
     */
    static String rsaDecode(String context) {
        RSA rsa = new RSA(RobotConstant_2011.DEFAULT_RSA_PRIVATE_KEY, null)
        try {
            return rsa.decryptStr(context, KeyType.PrivateKey)
        } catch (Exception e) {
        }
        return context
    }

    static String rsaEncode(String context) {
        RSA rsa = new RSA(null, RobotConstant_2011.DEFAULT_RSA_PUBLIC_KEY)
        try {
            return rsa.encryptBase64(context, KeyType.PublicKey)
        } catch (Exception e) {
        }
        return context
    }

    static Map<String, String> getDefaultHead(Map tempValues, Map configs) {
        return [
                'Host'        : 'issue.cpic.com.cn',
                'Content-Type': tempValues?.needSM4Flag ? 'text/plain' : 'application/json;charset=UTF-8',
                'macAddress'  : configs?.mac_address as String
        ]
    }

    static String genBody(Map tempValues, Map data) {
        return genBody(tempValues, JSON.toJSONString(data))
    }

    static String genBody(Map tempValues, String data) {
        if (tempValues?.needSM4Flag) {
            def sm4Key = tempValues.sm4Key as String
            return genBody(sm4Key, data)
        }
        return data
    }

    static String genBody(String sm4Key, String data) {
        return SM4Util.encryptEcb(sm4Key, data)
    }

    static String decodeBody(Map tempValues, Object data) {
        if (!data) {
            return data
        }
        if (data instanceof String) {
            if (tempValues?.needSM4Flag) {
                def sm4Key = tempValues.sm4Key as String
                return decodeBody(sm4Key, data)
            }
        }
        return data
    }

    static String decodeBody(String sm4Key, String data) {
        return SM4Util.decryptEcb(sm4Key, data)
    }

    static JSONObject decodeBody2JsonObject(Map tempValues, String data) {
        return JSON.parseObject(decodeBody(tempValues, data))
    }

    static Closure<JSONObject> initResp(Map tempValues) {
        return { Object respBody ->
            if (respBody instanceof String) {
                def body = decodeBody(tempValues, respBody as String)
                return ScriptUtil.initResp(body)
            } else if (respBody instanceof JSONObject) {
                return respBody as JSONObject
            }
            return null
        }
    }

    static Closure<String> serializer(Map tempValues) {
        return { Object data ->
            def result = ScriptUtil.toJSONStringWithChinese(data)
            return genBody(tempValues, result)
        }
    }

}
