package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.sender.HttpSender
import com.google.common.base.Joiner
import groovy.transform.Field
import org.apache.commons.codec.binary.Base64
import org.apache.commons.lang3.StringUtils
import org.apache.http.impl.client.CloseableHttpClient
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import taipingyang.robot.module.Robot2011NonMotor
import taipingyang.robot.module.Robot2011Util

import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec
import java.time.LocalDate

@Field static final Logger logger = LoggerFactory.getLogger("robot_2011_new_accident_util")


//所有流程都要检查
static checkNewInitAccident(AutoTask autoTask) {
    if (getThisAreaFlag(autoTask)) {
        if (autoTask.tempValues.parProductCode) {
            return autoTask.tempValues.parentProductCode
        } else {
            def productCode = getMonMotor(autoTask)?.productCode as String
            return autoTask.tempValues.parentProductCode = getCoverageVosMap(productCode)
        }
    }
}

/**
 * 报价，暂存，核保 --> 新驾意险
 * @param autoTask
 * @param quotationNo
 * @param calculateResultObj
 */
def accident(AutoTask autoTask, String quotationNo = null, def calculateResultObj = null) {
    if (getThisAreaFlag(autoTask)) {
        if ((autoTask.taskType.contains("insure") || autoTask.taskType.contains("autoinsure") || autoTask.taskType.contains("quote")) && !autoTask.taskType.contains("insurequery") && !autoTask.taskType.contains("quotequery")) {
            def enquiry = autoTask.taskEntity as Enquiry
            def misc = enquiry.misc
            def nonMotor = misc['nonMotor']
            def productCode = nonMotor['productCode'] as String
            def dict = new robot_2011_dict()
            productCode = dict.nonMotorProductCodeMap(productCode) as String
            def result
            if (productCode in dict.nonMotorProductCodeList() || Robot2011NonMotor.isNewProcess(productCode)) {
                def util = new robot_2011_util()
                util.nonMotorCalculate(autoTask)
                result = util.queryMsbProduct(autoTask)
            } else {
                result = insureNewAccident(autoTask, quotationNo)
            }
            //暂存回写 核保回写newBackAccident
            backAccident(autoTask, result[0])
        }
        //核保查询回写
        if (autoTask.taskType.contains("insurequery") || autoTask.taskType.contains("approvedquery") || autoTask.taskType.contains("quotequery")) {
            //可能传可能不传 non
            backAccident(autoTask, calculateResultObj)
        }
    }
}

static void onlySDQuote(autoTask) {
    def calculateUrl = "https://issue.cpic.com.cn/ecar/insure/calculate"
    String saveInsureURL = "https://issue.cpic.com.cn/ecar/insure/saveInsureInfo";
    autoTask.reqHeaders.put("Content-Type", "application/json;charset=utf-8")
    autoTask.reqHeaders.Origin = "https://issue.cpic.com.cn"
    autoTask.reqHeaders.Host = "issue.cpic.com.cn"
    autoTask.reqHeaders.Referer = "https://issue.cpic.com.cn/ecar/view/portal/page/car_insurance/insurancePlan.html"
    def saveInsureParamJson = JSON.parseObject(autoTask.tempValues.saveInsureParam)
    saveInsureParamJson.redata.productFlag = "0"
    if (autoTask?.tempValues?.NEFlag) {
        saveInsureParamJson.redata.productFlag = "5"
    }
    saveInsureParamJson.redata.isRenew = "0"
    saveInsureParamJson.redata.remove("insuredVo")
    saveInsureParamJson.redata.remove("inType")
    saveInsureParamJson.redata.ecarvo = [
            negotiatedValue: saveInsureParamJson.redata.ecarvo.negotiatedValue,
            actualValue    : saveInsureParamJson.redata.ecarvo.actualValue
    ]
    if (autoTask.tempValues.newAccCalculateResultObj?.result?.compulsoryInsuransVo) {
        saveInsureParamJson.redata.compulsoryInsuransVo = autoTask.tempValues.newAccCalculateResultObj.result.compulsoryInsuransVo
        saveInsureParamJson.redata.commercialAgreementVos = autoTask.tempValues.newAccCalculateResultObj.result.commercialAgreementVos
    }
    //报价
    def calculateResult = HttpSender.doPostWithRetry(5, autoTask?.httpClient, true, calculateUrl, JSON.toJSONString(saveInsureParamJson), null, autoTask.reqHeaders, "UTF-8", null, "");
    def calculateResultJson = JSON.parseObject(calculateResult)
    if ("failed".equals(calculateResultJson?.message?.code)) {
        throw new InsReturnException(calculateResultJson?.message?.message?.toString())
    }
    //暂存
    saveInsureParamJson.redata.compulsoryInsuransVo = calculateResultJson.result.compulsoryInsuransVo
    saveInsureParamJson.redata.commercial = calculateResultJson.result.commercial
    saveInsureParamJson.redata.compulsory = calculateResultJson.result.compulsory
    saveInsureParamJson.redata.commercialInsuransVo = calculateResultJson.result.commercialInsuransVo
    saveInsureParamJson.redata.quoteInsuranceVos = calculateResultJson.result.quoteInsuranceVos
    saveInsureParamJson.redata.accident = calculateResultJson.result.accident
    saveInsureParamJson.redata.commercialAgreementVos = calculateResultJson.result.commercialAgreementVos
    saveInsureParamJson.redata.ecarvo = calculateResultJson.result.ecarvo
    saveInsureParamJson.redata.chargingPostList = calculateResultJson?.result?.chargingPostList
    saveInsureParamJson.redata.isClickQuotation = "1"
    saveInsureParamJson.redata.isShowAccidentPlan = false
    saveInsureParamJson.redata.isPurchaseNoncar = "0"

    def saveInsureResult = HttpSender.doPostWithRetry(5, autoTask?.httpClient, true, saveInsureURL, JSON.toJSONString(saveInsureParamJson), null, autoTask.reqHeaders, "UTF-8", null, "");
}

//获取特殊折扣
static cldPremiumConfigSpecial(AutoTask autoTask, String productCode) {
    Enquiry entity = autoTask.taskEntity as Enquiry
    autoTask.reqHeaders.Host = 'dwx.cpic.com.cn'
    autoTask.reqHeaders.Origin = 'https://dwx.cpic.com.cn'
    autoTask.reqHeaders['Content-Type'] = 'application/json'
    autoTask.reqHeaders.Referer = URLDecoder.decode(autoTask.reqHeaders.Referer, 'UTF-8')
    def param = [
            "coverProductCode": getPlans()."$productCode".classesCode,
            "type"            : "P-E1002",
            "factorVal"       : String.valueOf(entity?.order?.carInfo?.seatCnt),//seatNum
            "planCode"        : productCode
    ]
    def requestBody = Encrypt(JSON.toJSONString(param), 'TdOvQRBA7Byx5uJE')
    def url = 'https://dwx.cpic.com.cn/nonvehicle/napi/cldPremiumConfig/specialHandler'
    def httpClient = autoTask.httpClient as CloseableHttpClient
    def response = HttpSender.doPost(httpClient, true, url, requestBody, null, autoTask.reqHeaders, 'UTF-8', null, '')
    return JSON.parseObject(Decrypt(response, 'TdOvQRBA7Byx5uJE'))
}

static insureNewAccident(AutoTask autoTask, String quotationNo) {
    //因为需要报价返回的最新的报价的时间，so需要放到报价之后
    def hashCodeResultJson = doGetHashCode(autoTask, quotationNo)
    def getCarTempParamData = doGetCarTempParam(autoTask, hashCodeResultJson)
    def busiNo = hashCodeResultJson.getJSONObject('result').getString('binary')
    def productCode = getMonMotor(autoTask).productCode
    if (autoTask?.configs?.channelCode) {
        autoTask?.tempValues?.channelCode = getChannelCode(autoTask?.configs?.channelCode as String)
    }
    def suffix = Map2Url([
            plcPlanCode  : autoTask.tempValues.parentProductCode,
            sourceType   : "WWCD",
            busiNo       : busiNo,
            isProductList: 1
    ])
    Map initPrdeJson = doInitPrde(autoTask, suffix)
    def exendJsonMap = getexendJsonMap(initPrdeJson?.data?.temp?.exendJson as List)
    //河北不需要特殊折扣
    if (!"P23E998002100000057".equals(productCode.toString())) {
        def specialJson = cldPremiumConfigSpecial(autoTask, productCode as String)
        if (specialJson && specialJson.msg.toString() == "响应成功") {
            autoTask.tempValues.plcPremium = specialJson.data.premium
            autoTask.tempValues.plcAmount = specialJson.data.amount
        }
    }

    def submitPolicyResultJson = doSubmitPolicy(autoTask, productCode as String, initPrdeJson, suffix)
    def urlMap = [
            branchCode     : exendJsonMap?."query.branchCode",
            paymentId      : submitPolicyResultJson?.data?.paymentId,
            groupOrderId   : exendJsonMap?."query.groupOrderId",
            healthNoticeUrl: "",
    ]

    suffix += Map2Url([
            agentCode  : autoTask?.configs?.agentCode ?: exendJsonMap?."query.agentCode",
            channelCode: autoTask?.tempValues?.channelCode ?: exendJsonMap?."query.channelCode",
    ])
    autoTask.reqHeaders.Referer = baseMapAsSC().submitPolicyReferer + suffix
    def pushUrl = baseMapAsSC().pushUrl + "?" + Map2Url(urlMap)
    String pushResult = HttpSender.doGet(autoTask.httpClient as CloseableHttpClient, pushUrl, autoTask.reqHeaders, null, "UTF-8", null, false);
    if (pushResult) {
        def pushJson = JSON.parseObject(pushResult)
        if (pushJson.data == "购物车推送成功") {
            //查询当前的状态并存储和回写
            return doQueryMsbProduct(autoTask, quotationNo)
        }
    }
    throw new InsReturnException(99, "insureNewAccident 失败！")
}

//查询购物车是否成功并且返回
static def doQueryMsbProduct(AutoTask autoTask, String quotationNo) {
//    autoTask.reqHeaders.Referer = baseMapAsSC().queryProductDetailReferer
    def doQueryMsbProductResult = HttpSender.doPostWithRetry(3, autoTask.httpClient, true, baseMapAsSC().queryMsbProductUrl,
            getQueryProductDetailStr(autoTask, quotationNo), null, autoTask.reqHeaders, "UTF-8", null, "");
    if (doQueryMsbProductResult) {
        def doQueryMsbProductJson = JSON.parseObject(doQueryMsbProductResult)
        if (doQueryMsbProductJson?.message?.code == "success") {
            autoTask.tempValues.new_accident_quotationNo = doQueryMsbProductJson.result[0].quotationNo
            autoTask.tempValues.new_accident_orderNo = doQueryMsbProductJson.result[0].orderNo
            return doQueryMsbProductJson.result
        }
    }
    throw new InsReturnException(99, "非车险购物车推送失败！")
}

static doSubmitPolicy(AutoTask autoTask, String productCode, Map initPrdeJson, suffix) {
    Enquiry entity = autoTask.taskEntity as Enquiry
    def accidentApplyJson = JSON.parseObject(autoTask.applyJson)
    def calculateParamJson = JSON.parseObject(autoTask.tempValues?.calculateParamJson)
    def bizSuiteInfo = entity?.order?.suiteInfo?.bizSuiteInfo
    def efcSuiteInfo = entity?.order?.suiteInfo?.efcSuiteInfo
    def accidentStart = calculateParamJson?.redata?.compulsoryInsuransVo?.stStartDate ?: bizSuiteInfo?.start ?: efcSuiteInfo?.start ?: ""
    def accidentEnd = calculateParamJson?.redata?.compulsoryInsuransVo?.stEndDate ?: bizSuiteInfo?.end ?: efcSuiteInfo?.end ?: ""
    //5座的车选了7座 可以选择  POST https://dwx.cpic.com.cn/nonvehicle/napi/cldPremiumConfig/special HTTP/1.1 submitPolicy中的 plcPremium plcAmount 会发生改变
//    def check = checkPersonList(autoTask, suffix, entity)
//    if (check) {
//        if (check.code.toString() == "201" || check.data?.code?.toString() == "401") {
//            throw new InsReturnException(99, "选择非车险失败，平台返回：" + (check.data.message as String))
//        }
//    }
    def getSubmitParam = getSubmitParam(autoTask, entity, accidentApplyJson, accidentStart, accidentEnd, initPrdeJson, productCode)
    getSubmitParam = Encrypt(JSON.toJSONString(getSubmitParam), "TdOvQRBA7Byx5uJE")
    autoTask.reqHeaders.Referer = baseMapAsSC().submitPolicyReferer + "?" + suffix
    String doSubmitPolicyResult = HttpSender.doPostWithRetry(3, autoTask.httpClient, true, baseMapAsSC().submitPolicyUrl, getSubmitParam, null, autoTask.reqHeaders, "UTF-8", null, "");
    if (doSubmitPolicyResult) {
        def doSubmitPolicyJson = JSON.parseObject(Decrypt(doSubmitPolicyResult, "TdOvQRBA7Byx5uJE"))
        def statusMessage = doSubmitPolicyJson?.data?.errMsg ?: doSubmitPolicyJson?.msg
        if (!"0".equals(doSubmitPolicyJson?.code?.toString())) {
            throw new InsReturnException(99, "选择非车险失败，平台返回：" + (statusMessage as String))
        }
        return doSubmitPolicyJson
    }
}

/**
 * 获取hash
 * @param autoTask
 * @param quotationNo
 * @return
 */
static def doGetHashCode(AutoTask autoTask, String quotationNo) {
    autoTask.reqHeaders.Referer = baseMapAsSC().queryProductDetailReferer
    def reqBody = getQueryProductDetailStr(autoTask, quotationNo)
    reqBody = Robot2011Util.genBody(autoTask.tempValues, reqBody)
    def header = JSON.parseObject(JSON.toJSONString(autoTask.reqHeaders))
    header.put('Content-Type', autoTask.tempValues.needSM4Flag ? 'text/plain' : 'application/json;charset=UTF-8')
    def doGetHashCodeResult = HttpSender.doPostWithRetry(3, autoTask.httpClient as CloseableHttpClient, true,
            baseMapAsSC().queryProductDetailUrl,
            reqBody,
            null, header,
            "UTF-8", null, "");
    doGetHashCodeResult = Robot2011Util.decodeBody(autoTask.tempValues, doGetHashCodeResult)
    return JSON.parseObject(doGetHashCodeResult)
}

/**
 *
 * @param autoTask
 * @param hashCode
 * @param productCode
 * @return
 *
 */
static def doInitPrde(AutoTask autoTask, String suffix) {
    autoTask.reqHeaders.remove('Origin')
    autoTask.reqHeaders.remove('Content-Type')
    def initPrdUrl = baseMapAsSC().initPrdUrl + "?" + suffix
    String initPrdResult = HttpSender.doGet(autoTask.httpClient as CloseableHttpClient, initPrdUrl, autoTask.reqHeaders, null, "UTF-8", null, false);
    initPrdResult = Decrypt(initPrdResult, "TdOvQRBA7Byx5uJE")
    if (initPrdResult.contains("产品已下线")) {
        throw new InsReturnException("该款非车产品以下线，请及时更新")
    }
    logger.info("非车险投保初始化产品信息:{}", initPrdResult)
    return JSON.parseObject(initPrdResult)
}

//报价回写  暂存回写 核保回写 承保查询回写 核保查询
def backAccident(AutoTask autoTask, def result = null) {
    if (getThisAreaFlag(autoTask)) {
        Enquiry entity = autoTask.taskEntity as Enquiry
        def nonMotor = getMonMotor(autoTask) ?: [:]
        //所有情况都可取
        def premium = result?.orderAmount ?: getPlans().get(getMonMotor(autoTask)?.productCode as String).premium
        if (!premium) {
            throw new InsReturnException(99, "premium 为 null！")
        }
        if (["P23E598002100001718", "P23E598002100001713", "P23E598002100001714", "P23E598002100001716",
             "P23E598002100001717", "P23XZ98002100000010", "P23XZ98002100000017", "P23XZ98002100000018",
             "P23XZ98002200000219", "P23XZ98002200000524", "P23XZ98002200000525", "P23XZ98002200000527",
             "P23XZ98002200000218", "P23XZ98002200000026", "P23XZ98002200000027", "P23XZ98002200000021",
             "P23XZ98002200000020", "P23XZ98002200000093", "P23XZ98002200000095", "P23XZ98002200001042",
             "P23XZ98002200001043", "P23XZ98002200001044", "P23XZ98002200001045", "P23XZ98002200001046",
             "P23XZ98002200001047", "P23XZ98002200001048", "P23XZ98002200001049", "P23XZ98002200001050",
             "P23XZ98002200001051", "P23XZ98002200001052", "P23XZ98002200001053", "P23E598002200000461",
             "P241898002200000073", "P241898002200000075", "P241898002200000076", "P241898002200000085",
             "P241898002200000326", "P241898002200000353", "P241898002200000354", "P241898002200000458",
             "P241898002200000459", "P241898002200000460", "P241898002200000066", "P241898002200000072",
             "P23E598002100002269", "P23E598002100002268", "P241898002200000529", "P241898002200000535",
             "P241898002200001072", "P241898002200001073", "P241898002200000871", "P241898002200000870",
             "P241898002200000815", "P241898002200001169", "P241898002200001170", "P241898002200000264",
             "P241898002200000243"].contains(getMonMotor(autoTask)?.productCode as String)) {
            //拿到这值不需要计算
            if (!result?.orderAmount) {
                premium = new BigDecimal(premium.toString()).multiply(new BigDecimal(entity.order.carInfo.seatCnt.toString())).setScale(2, BigDecimal.ROUND_HALF_DOWN)
                //任我行2022（基础版） 是basePrice+seatCnt*premium
                if (["P23XZ98002100000010", "P23XZ98002100000017", "P23XZ98002100000018", "P23XZ98002200000219",
                     "P23XZ98002200000524", "P23XZ98002200000525", "P23XZ98002200000527", "P23XZ98002200000218",
                     "P23XZ98002200000026", "P23XZ98002200000027", "P23XZ98002200000021", "P23XZ98002200000020",
                     "P23XZ98002200001042", "P23XZ98002200001043", "P23XZ98002200001044", "P23XZ98002200001045", "P23XZ98002200001046",
                     "P23XZ98002200001047", "P23XZ98002200001048", "P23XZ98002200001049", "P23XZ98002200001050",
                     "P23XZ98002200001051", "P23XZ98002200001052", "P23XZ98002200001053", "P23E598002200000461",
                     "P241898002200000073", "P241898002200000075", "P241898002200000076", "P241898002200000085",
                     "P241898002200000326", "P241898002200000353", "P241898002200000354", "P241898002200000458",
                     "P241898002200000459", "P241898002200000460", "P241898002200000066", "P241898002200000072",
                     "P23E598002100002269", "P23E598002100002268", "P241898002200000529", "P241898002200000535",
                     "P241898002200001072", "P241898002200001073", "P241898002200000871", "P241898002200000870",
                     "P241898002200000815", "P241898002200001169", "P241898002200001170", "P241898002200000264",
                     "P241898002200000243"].contains(getMonMotor(autoTask)?.productCode as String)) {
                    premium = premium.add(new BigDecimal(getPlans().get(getMonMotor(autoTask)?.productCode as String).basePrice)).setScale(2, BigDecimal.ROUND_HALF_DOWN)
                }
            }
        }


        if (entity.totalCharge && !(autoTask.taskType.contains("insurequery") || autoTask.taskType.contains("approvedquery") || autoTask.taskType.contains("quotequery"))) {
            entity.totalCharge = entity.totalCharge.add(new BigDecimal(premium))
        }

        nonMotor.discountCharge = autoTask.tempValues.plcPremium ?: premium
        nonMotor.start = result?.result?.commercialInsuransVo?.stStartDate ?: result?.insuranceStartDate ?: result?.result?.compulsoryInsuransVo?.stStartDate + ":00"
        nonMotor.end = result?.result?.commercialInsuransVo?.stEndDate ?: result?.insuranceEndDate ?: result?.result?.compulsoryInsuransVo?.stEndDate + ":00"


        if (autoTask.taskType.contains("autoinsure") || autoTask.taskType.contains("insurequery") || autoTask.taskType.contains("approvedquery") || autoTask.taskType.contains("quotequery")) {
            def dict = new robot_2011_dict()
            nonMotor.accidentProposeCode = entity.bizProposeNum //投保单号
            if (autoTask.taskType.contains("insurequery") || autoTask.taskType.contains("approvedquery") || autoTask.taskType.contains("quotequery")) {
                nonMotor.productCode = autoTask?.configs?.areaComCode == '山东' ? dict.nonMotorProductCodeMapToBc(result.planCode) : result?.planCode
                nonMotor.count = "1"
                if (autoTask.taskType.contains("approvedquery")) {
                    nonMotor.accidentPolicyCode = result?.policyNo //保单号
                }
            }
        }
        //报价时规则无回写？这个设计是真的垃圾
        autoTask.tempValues.nonMotor = nonMotor
        entity?.misc?.nonMotor = nonMotor
    }

}

static getSubmitParam(autoTask, Enquiry entity, Map accidentApplyJson, String accidentStart, String accidentEnd, Map initPrdeJson, String productCode) {
    def exendJsonMap = getexendJsonMap(initPrdeJson?.data?.temp?.exendJson as List)
    def city = entity.order.insureArea.city
    def deptCode = autoTask.configs.agent_code.toString()[0..2] ?: "07M"
    def address = entity.order?.insurePerson?.address ?: entity.order?.insuredPersons[0]?.address ?: entity.order?.carOwnerInfo?.address ?: "江苏省苏黎世飞马小区"
    def earnMap = [:]
    //P23E598002000001776
    if (getThisAreaFlag(autoTask)) {
        //广州地区需要根据开始日期加一年
        earnMap.endDate = LocalDate.parse(accidentStart[0..9]).plusYears(1).toString()
    }

    def param = [
            "saleType"        : exendJsonMap?."query.saleType",
            "sourceType"      : exendJsonMap?."query.sourceType",
            "branchCode"      : exendJsonMap?."query.branchCode",
            "productCode"     : exendJsonMap?."query.plcPlanCode",
            "groupOrderId"    : exendJsonMap?."query.groupOrderId",
            "tempId"          : initPrdeJson?.data?.temp?.id,
            "carInfo"         : [
                    "extend1"   : exendJsonMap?."query.purchasePriceJY",
                    "extend2"   : exendJsonMap?."query.vehicleKind",
                    "extend3"   : exendJsonMap?."query.vehicleClassName",
                    "carColour" : getCarColour(exendJsonMap?."query.plateColor"?.toString()),
                    "carNumber" : entity.order.carInfo.plateNum,
                    "carVin"    : entity.order.carInfo.vin,
                    "carEngine" : entity.order.carInfo.engineNum,
                    "carSeating": entity.order.carInfo.seatCnt?.toString()
            ],
            "policyList"      : [
                    [
                            "persons"   : getPersons(entity, accidentApplyJson, exendJsonMap, productCode),
                            "policy"    : [
                                    "tempId"          : initPrdeJson?.data?.temp?.id,
                                    "branchCode"      : exendJsonMap?."query.branchCode",
                                    "productName"     : initPrdeJson?.data?.productName,
                                    "sourceType"      : exendJsonMap?."query.sourceType",
                                    "payBillOrNot"    : "0", //支付了吗？
                                    "plcElcflag"      : 0,//电子保单？
                                    "endDate"         : accidentEnd[0..9],//广州地区需要根据开始日期加一年
                                    "startDate"       : accidentStart[0..9], //报价修改后的时间
                                    "groupOrderId"    : exendJsonMap?."query.groupOrderId",
                                    "productCode"     : initPrdeJson?.data?.productId,
                                    "offsitBranchCode": exendJsonMap?."query.branchCode",
                                    "planCode"        : productCode,// todo
                                    "planGroupCode"   : getPlans()."$productCode".planGroupCode,
                                    "plcCopies"       : 1, //?
                                    //人身险会打折？
                                    "plcPremium"      : autoTask.tempValues.plcPremium ?: getPlans()."$productCode".premium, //todo 还会打折 打折的价格和当前的价格相同吗？  dwx.cpic.com.cn	/nonvehicle/napi/cldPremiumConfig/special
                                    "plcAmount"       : autoTask.tempValues.plcAmount ?: getPlans()."$productCode".sumCoverage,
                                    "planName"        : getPlans()."$productCode".planName,
                                    "agentCode"       : autoTask?.configs?.agentCode ?: exendJsonMap?."query.agentCode",
                                    "handlerCode"     : exendJsonMap?."query.hdlrCode",
                                    "lifeempNo"       : "",
                                    "saleType"        : "S002", //todo ?
                                    "tbrcode"         : "",
                                    "autoPartnerName" : exendJsonMap?."query.autoPartnerName",
                                    "deptgroupCode"   : city, //todo 找 POST https://dwx.cpic.com.cn/nonvehicle/napi/commonBus/postCommonBus
                                    "deptCode"        : deptCode, //  agent_code前三位
                                    "channelCode"     : autoTask?.tempValues?.channelCode ?: exendJsonMap?."query.channelCode",
                                    "recordWay"       : exendJsonMap?."query.recordWay",
                                    "diySaleCode"     : "1",//todo
                                    "autoPartnerCode" : exendJsonMap?."query.autoPartnerCode",

                            ] << earnMap,
                            "underWrite": [
                                    "plcBase" : [:],
                                    "planList": [
                                            [
                                                    "plnBase"   : [
                                                            "plnMainClassesCode": initPrdeJson?.data?.classesCode,
                                                            "groupInsuranceFlag": getPlans()."$productCode".groupInsuranceFlag // ?
                                                    ],
                                                    //
                                                    "specialRyx": [
                                                            "factorList": getFactorList(exendJsonMap, productCode, address)
                                                    ],
                                                    "elcPolicy" : getElcPolicy(entity, accidentApplyJson)
                                            ]
                                    ]
                            ]
                    ]
            ],
            "iseeId"          : "",
            "iscombineProduct": "0" //todo ?
    ]
    if (["P23E598002100001718", "P23E598002100001713", "P23E598002100001714", "P23E598002100001716", "P23E598002100001717", "P23E598002000001777"].contains(productCode)) {

        param.policyList[0]?.handlerList = JSONObject.parseArray(exendJsonMap?."query.handlerList"?.toString())
        param.policyList[0]?.policyExtend = new JSONObject()
        param.policyList[0]?.policyExtend?.handlerName = ""
        param.policyList[0]?.policyExtend?.dcLicenseNbr = ""
        param.policyList[0]?.policyExtend?.dcOrgName = ""
        param.policyList[0]?.policyExtend?.validateIspqc = "1"
        //param.policyList[0]?.policy?.channelCode = "37"
    }
    param
}

static getThisAreaFlag(AutoTask autoTask) {
    return ["四川", "山东", "东莞", "广州", "广州市", "宁波", "河北", "福建", "重庆", "南京", "江苏", "江西", "湖北", "武汉", "北京", "沈阳"].contains(autoTask.configs.areaComCode.toString())
}

static getMonMotor(AutoTask autoTask) {
    if (autoTask.tempValues.nonMotor) {
        return autoTask.tempValues.nonMotor
    }
    Enquiry entity = autoTask.taskEntity as Enquiry
    autoTask.tempValues.nonMotor = entity?.misc?.nonMotor
    return entity?.misc?.nonMotor
}

static List getFactorList(Map exendJsonMap, String productCode, address) {
    def factorList = [
            [
                    "factorValue": "O5001",
                    "factorCode" : "O5001"
            ],
            [
                    "factorValue": exendJsonMap?."query.factors.KP000.value",
                    "factorCode" : "KP000"
            ],
            [ //座位号
              "factorValue": exendJsonMap?."query.factors.E1002.value",
              "factorCode" : "E1002"
            ],
            [
                    "factorValue": exendJsonMap?."query.factors.E1000.value",
                    "factorCode" : "E1000"
            ],
            [
                    "factorValue": "CO000",
                    "factorCode" : "CO000"
            ]
    ]
//    NY000 全新任我行三代5座，7座都需要 //任我行二代经济款五座 广州市需要 或者直接请求getPlans
    def needNY000List = ["PD20201101S00001003", "PD20201101S00001005", "PD20210106S00001839", "PD20210106S00001840", "PD20210630S00004072", "PD20210823S00005585"]
    //任我行二代经济款五座 广州市
    //广州市2代也需要
    if (needNY000List.contains(getCoverageVosMap(productCode))) {
        factorList.add([
                "factorValue": "NY000",
                "factorCode" : "NY000"
        ]) as List
    }
//    鲁行单交强驾乘  P23E598002100000906 不需要CO000
    def noCO000List = ["PD20210511S00003316"]
    if (noCO000List.contains(getCoverageVosMap(productCode))) {
        return factorList.findAll {
            it.factorValue != "CO000" || it.factorCode != "CO000"
        }
    }
    //任我行三代升级款
    def noKP000List = ["PD20210630S00004072", "PD20210615S00003798"]
    if (noKP000List.contains(getCoverageVosMap(productCode))) {
        return factorList.findAll {
            it.factorCode != "KP000"
        }
    }
    def noList = ["PD20210111S00001892"]
    if (noList.contains(getCoverageVosMap(productCode))) {
        return []
    }
    def hebeiList = ["PD20210316S00002566"]
    if (hebeiList.contains(getCoverageVosMap(productCode))) {
        return [[
                        "factorValue": "O5001",
                        "factorCode" : "O5001"
                ]]
    }
    def ningboList = ["PD20220319S00010073"]
    if (ningboList.contains(getCoverageVosMap(productCode))) {
        if ("P23XZ98002200000728".equals(productCode)) {
            factorList.add([
                    "factorValue": "O5010",
                    "factorCode" : "O5010"
            ])
        }
        if ("P23XZ98002200000739".equals(productCode)) {
            factorList.add([
                    "factorValue": "O5008",
                    "factorCode" : "O5008"
            ])
        }

        return factorList.findAll {
            it.factorCode != "O5001"
        }
    }
    //鲁行单交强驾乘三代 2023年3月8日增加
    def rwx2022baseList = ["PD20211222S00008067", "PD20220124S00009026", "PD20220126S00009058",
                           "PD20221008S00015581", "PD20221008S00015580", "PD20221123S00016784"]
    if (rwx2022baseList.contains(getCoverageVosMap(productCode))) {

        factorList.add([
                "factorValue": "O5007",
                "factorCode" : "O5007"
        ])
        return factorList.findAll {
            it.factorCode != "O5001"
        }
    }
    def renwoxing2022List = ["PD20211222S00008075", "PD20220303S00009691"]
    //任我行2022需要地址
    if (renwoxing2022List.contains(getCoverageVosMap(productCode))) {

        factorList.add([
                "factorValue": address,
                "factorCode" : "NM000"
        ])
        factorList.add([
                "factorValue": "O5007",
                "factorCode" : "O5007"
        ])
        return factorList.findAll {
            it.factorCode != "O5001"
        }
    }
    def CXBList = ["PD20220107S00008582", "PD20220107S00008585"]
    if (CXBList.contains(getCoverageVosMap(productCode))) {
        factorList.add([
                "factorValue": "O5010",
                "factorCode" : "O5010"
        ])
        return factorList.findAll {
            it.factorCode != "O5001"
        }
    }
    //莞非营运货车驾乘险2022
    def HCList = ["PD20220206S00009097"]
    if (HCList.contains(getCoverageVosMap(productCode))) {
        factorList.add([
                "factorValue": "O5005",
                "factorCode" : "O5005"
        ])
        return factorList.findAll {
            it.factorCode != "O5001"
        }
    }
    def DGList = ["PD20220503S00010992", "PD20220520S00011326", "PD20220520S00011334"]
    if (DGList.contains(getCoverageVosMap(productCode))) {
        factorList.add([
                "factorValue": "F7003",
                "factorCode" : "F7003"
        ])
        return factorList.findAll {
            it.factorCode != "O5001"
        }
    }
    def CXWYList = ["PD20220616S00012207", "PD20221019S00015826"]
    if (CXWYList.contains(getCoverageVosMap(productCode))) {
        //企业非营运
        factorList.add([
                "factorValue": "O5022",
                "factorCode" : "O5022"
        ])
        //2吨以下货车
        factorList.add([
                "factorValue": "I2005",
                "factorCode" : "I2005"
        ])
        //I2006  2-5吨货车
        return factorList.findAll {
            it.factorCode != "O5001" && it.factorCode != "CO000"
        }
    }
    //2023年3月8日 畅行无忧假如
    def GDList = ["PD20221021S00015953", "PD20221103S00016224", "PD20221109S00016347"]
    if (GDList.contains(getCoverageVosMap(productCode))) {
        if (["PD20221021S00015953", "PD20221109S00016347"].contains(getCoverageVosMap(productCode))) {
            //营业
            factorList.add([
                    "factorValue": "O5015",
                    "factorCode" : "O5015"
            ])
        }
        if (["PD20221103S00016224"].contains(getCoverageVosMap(productCode))) {
            //非营业
            factorList.add([
                    "factorValue": "O5022",
                    "factorCode" : "O5022"
            ])
        }
        //2吨以下货车
        factorList.add([
                "factorValue": "I2005",
                "factorCode" : "I2005"
        ])
        if (["PD20221109S00016347"].contains(getCoverageVosMap(productCode))) {
            return factorList.findAll {
                it.factorCode != "O5001" && it.factorCode != "CO000"
            }
        }
        //I2006  2-5吨货车
        return factorList.findAll {
            it.factorCode != "O5001"
        }
    }
    def jdbList = ["PD20211108S00007330", "PD20221128S00016963"]
    if (jdbList.contains(getCoverageVosMap(productCode))) {
        factorList.add([
                "factorValue": "O5007",
                "factorCode" : "O5007"
        ])
        return factorList.findAll {
            it.factorCode != "O5001"
        }
    }
    def CXBList2 = ["PD20221103S00016200", "PD20221207S00017287", "PD20221215S00017499"]
    if (CXBList2.contains(getCoverageVosMap(productCode))) {
        factorList.add([
                "factorValue": "O5010",
                "factorCode" : "O5010"
        ])
        factorList.add([
                "factorValue": "A0004",
                "factorCode" : "A0004"
        ])
        if ("PD20221103S00016200".equals(getCoverageVosMap(productCode))) {
            factorList.add([
                    "factorValue": "I2000",
                    "factorCode" : "I2000"
            ])
        }
        if ("PD20221207S00017287".equals(getCoverageVosMap(productCode))) {
            factorList.add([
                    "factorValue": "I2001",
                    "factorCode" : "I2001"
            ])
        }
        return factorList.findAll {
            it.factorCode != "O5001"
        }
    }
    if ("PD20221014S00015741".equals(getCoverageVosMap(productCode))) {
        factorList = factorList.findAll {
            it.factorCode == "E1000" || it.factorCode == "E1002"
        }
        factorList.add([
                "factorValue": "E1001",
                "factorCode" : "E1001"
        ])
        factorList.add([
                "factorValue": "O5000",
                "factorCode" : "O5000"
        ])
        return factorList;
    }
    return factorList
}

static Map getElcPolicy(Enquiry entity, Map accidentApplyJson) {
    //获取投保人的邮箱
    def robot_2011_accident_util = new robot_2011_accident_util()
    //获取投保人的邮箱
    def applicantEmail = robot_2011_accident_util.getSupplyParam("applicantEmail", accidentApplyJson) ?: StringUtil.getMobile() + "@163.com"
    //获取投保人的手机号
    def applicantMobile = robot_2011_accident_util.getSupplyParam("applicantMobile", accidentApplyJson) ?: StringUtil.getMobile()
    def appPerson = entity.order.insurePerson //投保人
    return [
            "elcEmlFlag": "1",//todo 写死
            "elcMsgFlag": "1",//todo 写死
            "elcMobile" : appPerson.mobile ?: applicantMobile,
            "elcEmail"  : appPerson.email ?: applicantEmail
    ]
}

// 根据不同险种组装不同用户信息

static List getPersons(Enquiry entity, Map accidentApplyJson, Map exendJsonMap, String productCode) {

    //获取投保人的邮箱
    def robot_2011_accident_util = new robot_2011_accident_util()
    //获取投保人的邮箱
    def applicantEmail = robot_2011_accident_util.getSupplyParam("applicantEmail", accidentApplyJson)
    //获取投保人的手机号
    def applicantMobile = robot_2011_accident_util.getSupplyParam("applicantMobile", accidentApplyJson)

    def appPerson = entity.order.insurePerson //投保人
    def certificationType = "1"
    if (["6", "8", "9", "10"].contains(appPerson?.idCardType?.toString())) {
        certificationType = "20"
    }

    //todo 根据险种判断是否需要
    def type_2List = ["PD20201012S00000609", "PD20210111S00001892", "PD20210225S00002349", "PD20210511S00003316", "PD20210316S00002566"]
    def Persons = [[
                           "personType"       : 1,
                           "branchCode"       : exendJsonMap?."query.branchCode",
                           "certificationNo"  : appPerson.idCard,
                           "personName"       : appPerson.name,
                           "certificationType": certificationType,//todo 后面有Bug再支持台胞证等
                           "mobilePhone"      : appPerson.mobile ?: applicantMobile,
                           "emailAddress"     : appPerson.email ?: applicantEmail
                   ]]
    if (type_2List.contains(getCoverageVosMap(productCode))) {
        //获取被保人的手机号
        def insuredMobile = robot_2011_accident_util.getSupplyParam("insuredMobile", accidentApplyJson)
        def insuredPerson = entity.order.insuredPersons[0]//被保人信息
        Persons.add([
                "personType"            : 2,
                "branchCode"            : exendJsonMap?."query.branchCode",
                "cldPersonInfoAuxoliary": [
                        "customerWithApplicant": 4,
                        "isrdIfMainInsured"    : "1"
                ],
                "certificationNo"       : insuredPerson.idCard,
                "personName"            : insuredPerson.name,
                "certificationType"     : "1",
                "mobilePhone"           : insuredPerson.mobile ?: insuredMobile,
                "customerWithApplicant" : 4
        ])
    }
    return Persons
}


static getCoverageVosMap(String productCode) {

    def dict = new robot_2011_dict()
    productCode = dict.nonMotorProductCodeMap(productCode)
    def parProductCode = ""
    baseListAsSC().each {
        if (productCode in it.value) {
            parProductCode = it.key
        }
    }
    return parProductCode
}

//获取 doInitPrde  中返回的exendJson list 变为Map
static Map getexendJsonMap(List list) {
    list?.collectEntries { [(it.path): it.value] }
}


//检测身份信息是否正确
static def checkPersonList(AutoTask autoTask, String suffix, Enquiry entity) {
    autoTask.reqHeaders.Referer = baseMapAsSC().submitPolicyReferer + "?" + suffix
    def appPerson = entity.order.insurePerson //投保人
    def personList = [
            [
                    "personType"     : 1,
                    "certificationNo": appPerson.idCard,
                    "customName"     : appPerson.name,
            ]
    ]
    def checkPersonListResult = HttpSender.doPostWithRetry(3, autoTask.httpClient, true, baseMapAsSC().checkPersonListUrl,
            JSON.toJSONString(personList), null, autoTask.reqHeaders, "UTF-8", null, "");
    return JSON.parseObject(checkPersonListResult)
}

//山东规则后再次非车险报价前 删除原有单子
static deleteMsbProduct(AutoTask autoTask) {
    //只能是这个险种才行
    def productCode = getMonMotor(autoTask).productCode
    if (productCode == "P23E598002100000906") {
        def paramString = '{"meta":{},"redata":{"quotationNo":"QUOTATIONNO","orderNo":"ORDERNO"}}'.replace("ORDERNO", autoTask.tempValues.new_accident_orderNo.toString()).replace("QUOTATIONNO", autoTask.tempValues.new_accident_quotationNo.toString())
        autoTask.reqHeaders.Referer = baseMapAsSC().queryProductDetailReferer
        def deleteMsbProductResult = HttpSender.doPostWithRetry(3, autoTask.httpClient, true, baseMapAsSC().deleteMsbProduct,
                paramString, null, autoTask.reqHeaders, "UTF-8", null, "");

        if (getThisAreaFlag(autoTask)) {
            if (autoTask.taskType.contains("autoinsure")) {
                def result = insureNewAccident(autoTask, autoTask.tempValues.new_accident_quotationNo.toString())
                //暂存回写 核保回写
                backAccident(autoTask, result[0])
                //todo 加上 只有山东地区就可以了
                if (autoTask.taskType.contains("autoinsure") && ["山东"].contains(autoTask?.configs?.areaComCode as String)) {
                    //设置以及添加过了新非车险标签，再次报价核保就行
                    onlySDQuote(autoTask)
                }
            }
        }
    }
}

static baseMapAsSC() {
    [
            queryProductDetailUrl    : "https://issue.cpic.com.cn/ecar/msb/queryProductDetail",
            submitPolicyUrl          : "https://dwx.cpic.com.cn/nonvehicle/napi/insurance/submitPolicy",
            initPrdUrl               : "https://dwx.cpic.com.cn/nonvehicle/napi/insurance/initPrd",
            pushUrl                  : "https://dwx.cpic.com.cn/nonvehicle/napi/cartPush/push",
            checkPersonListUrl       : "https://dwx.cpic.com.cn/nonvehicle/napi/insurance/checkPersonList",
            getFormConfigUrl         : "https://dwx.cpic.com.cn/nonvehicle/napi/adsplanconfig/getFormConfig",
            queryMsbProductUrl       : "https://issue.cpic.com.cn/ecar/msb/queryMsbProduct",
            queryProductDetailReferer: "https://issue.cpic.com.cn/ecar/view/portal/page/quotation_merge/quick_quotation.html",
            submitPolicyReferer      : "https://dwx.cpic.com.cn/cxNew/dwx/mobilemsb/",
            special                  : "https://dwx.cpic.com.cn/nonvehicle/napi/cldPremiumConfig/special",
            deleteMsbProduct         : "https://issue.cpic.com.cn/ecar/msb/deleteMsbProduct",
    ]
}

//四川地区 productCode planCode
static baseListAsSC() {
    [
            "PD20201012S00000609" : ["P23C198002000000660", "P23C198002000000659", "P23C198002000000658", "P23C198002000000657"],
            //全新任我行一代 5座 ABC  四川  && 山东地区 &&广州 &&东莞  全新任我行一代A款（5座）
            "PD20201130S00001298" : ["P23E598002000001776", "P23E598002000001778", "P23E598002000001780"],
            //全新任我行一代 7座 ABC 四川  && 山东地区 &&广州 &&东莞
            "PD20201130S00001303" : ["P23E598002000001777", "P23E598002000001779", "P23E598002000001781"],
            //全新任我行三代 5座 经济 豪华 尊贵 钻石 四川&&东莞 && 广州市
            "PD20201101S00001003" : ["P23E598002000001588", "P23E598002000001591", "P23E598002000001594", "P23E598002000001596"],
            //全新任我行三代 7座 经济 豪华 尊贵 钻石 四川&&东莞  && 广州市
            "PD20201101S00001005" : ["P23E598002000001589", "P23E598002000001592", "P23E598002000001595", "P23E598002000001597"],
            //山东地区  山东渠道2013版驾意险
            "PD20210111S00001892" : ["P23E998002000000109"],
            //任我行二代经济款五座 广州市
            PD20210106S00001839   : ["P23E598002000001699", "P23E598002000001771", "P23E598002000001773",],
            //任我行二代经济款7座 广州市
            PD20210106S00001840   : ["P23E598002000001700", "P23E598002000001772", "P23E598002000001775",],
            PD20210304S00002422   : ["P23E598002100000416", "P23E598002100000415"],
            //鲁行渠道驾乘（基础版）5,7,8座
            PD20210225S00002349   : ["P23E598002100000355", "P23E598002100000358", "P23E598002100000364"],
            //鲁行单交强驾乘
            PD20210511S00003316   : ["P23E598002100000906"],
            //任我行三代升级款
            PD20210630S00004072   : ["P23E598002100000652", "P23E598002100000667", "P23E598002100000668", "P23E598002100001375", "P23E598002100001374"],
            PD20210615S00003798   : ["P23E598002100000883", "P23E598002100000888", "P23E598002100000886", "P23E598002100000875", "P23E598002100000874", "P23E598002100000882", "P23E598002100000884", "", "P23E598002100000887", "P23E598002100000889", "P23E598002100000885",],
            //河北 驾意险-车险专属02
            PD20210316S00002566   : ["P23E998002100000057"],
            //任我行三代新
            PD20210823S00005585   : ["P23E598002100001718", "P23E598002100001713", "P23E598002100001714", "P23E598002100001716", "P23E598002100001717"],
            //任我行2022升级款
            "PD20211222S00008075" : ["P23XZ98002100000020", "P23XZ98002100000012", "P23XZ98002100000021"],
            //任我行2022（基础版）
            "PD20211222S00008067" : ["P23XZ98002100000010", "P23XZ98002100000017", "P23XZ98002100000018"],
            //鲁行保（代步版）升级款
            "PD20220124S00009026" : ["P23XZ98002200000218"],
            //鲁行单交强驾乘-升级款
            "PD20220126S00009058" : ["P23XZ98002200000219"],
            //鲁行渠道驾乘升级款
            "PD20220303S00009691" : ["P23XZ98002200000524", "P23XZ98002200000525", "P23XZ98002200000527"],
            //畅行保宁波版一代
            "PD20220319S00010073" : ["P23XZ98002200000739", "P23XZ98002200000728"],
            //畅行保（党政机关及企业客车专属）-七座
            "PD20220107S00008585" : ["P23XZ98002200000026", "P23XZ98002200000027"],
            //畅行保（党政机关及企业客车专属）-五座
            "PD20220107S00008582" : ["P23XZ98002200000021", "P23XZ98002200000020"],
            //莞非营运货车驾乘险2022
            "PD20220206S00009097" : ["P23XZ98002200000095", "P23XZ98002200000093"],
            //莞非营运货车驾乘险2022-方案一
            "PD20220503S00010992" : ["P23XZ98002200001042", "P23XZ98002200001043", "P23XZ98002200001044", "P23XZ98002200001045"],
            //莞非营运货车驾乘险2022-方案二
            "PD20220520S00011326" : ["P23XZ98002200001046", "P23XZ98002200001047", "P23XZ98002200001048", "P23XZ98002200001049"],
            //莞非营运货车驾乘险2022-方案三
            "PD20220520S00011334" : ["P23XZ98002200001050", "P23XZ98002200001051", "P23XZ98002200001052", "P23XZ98002200001053"],
            //畅行无忧-非营业货车
            "PD20220616S00012207" : ["P23E598002200000461"],
            //任我行六代（升级版）
            "PD20221008S00015581" : ["P241898002200000073", "P241898002200000075", "P241898002200000076", "P241898002200000085"],
            //东莞太享行2022
            "PD20221021S00015953" : ["P241898002200000326", "P241898002200000353", "P241898002200000354"],
            //任我行六代（基础版）
            "PD20221008S00015580" : ["P241898002200000066", "P241898002200000072"],
            //车险新加多保
            "PD20211108S00007330" : ["P23E598002100002269", "P23E598002100002268"],
            //新加多保2022
            "PD20221128S00016963" : ["P241898002200000871", "P241898002200000870"],
            //鲁行单交强驾乘三代
            "PD20221123S00016784" : ["P241898002200000815"],
            //畅行保（山东专属）二代
            "PD20221215S00017499" : ["P241898002200001169", "P241898002200001170"],
            // 【全国】任我行七代(基础版)
            'PD20230830S00025106' : ['P241898002300001480', 'P241898002300001481'], // 任我行七代(基础版)-A款, 任我行七代(基础版)-B款
            // 【全国】任我行七代（升级版）
            'PD20230830S00025094' : ['P241898002300001482', 'P241898002300001483', 'P241898002300001484', 'P241898002300001485', 'P241898002300001486', 'P241898002300001487'],
            // 【全国】畅行无忧2.0-非营业货车
            'PD20221019S00015826' : ['P241898002200000243', 'P241898002200000244', 'P241898002200000245'],
            // 【全国】畅行无忧2.0-2吨以下营业货车
            'PD20221109S00016347' : ['P241898002200000264', 'P241898002200000265', 'P241898002200000266'],
            // 【全国】畅行无忧2.0-2吨至10吨营业货车
            'PD20221109S00016348' : ['P241898002200000267', 'P241898002200000268', 'P241898002200000270'],
            // 【全国】畅行保2.0-3至5座党政机关及企业客车专属产品
            'PD20221103S00016200' : ['P241898002200000529', 'P241898002200000535', 'P241898002200000536', 'P241898002200000537', 'P241898002200000538'],
            // 【全国】畅行保2.0-6至9座党政机关及企业客车专属产品
            'PD20221207S00017287' : ['P241898002200001072', 'P241898002200001073', 'P241898002200001074', 'P241898002200001075', 'P241898002200001076'],
            // 【山东】鲁行单交强驾乘三代
            'P241898002200000815' : 'PD20221123S00016784',
            // 【广东】广东非营运货车驾乘险（新）
            'PD20221103S00016224' : ['P241898002200000458', 'P241898002200000459', 'P241898002200000460', 'P241898002200000461'],
            // 【重庆】私车保全经济版
            'PD20230519S00021718' : ['P241898002300000761', 'P241898002300000764'],
            // 【重庆】君安行交通工具意外险
            'PD20220923S00015416' : ['P241998002200000007', 'P241998002200000008', 'P241998002200000009'],
            // 【重庆】畅行无忧2.0-6座以下非营业客车
            'PD20221014S00015725' : ['P241898002200000204', 'P241898002200000225', 'P241898002200000224', 'P241898002200000225'],
            //【北京】新外网-驾意险80元方案5/7座
            'PD20221014S00015741' : ['P241898002200000112', 'P241898002200000127'],
            //任我行七代-2024新版（基础版）- A款\B款
            "PDL20240418A00006904": ['PL2418980024A10000172', 'PL2418980024A10000198'],

            //任我行七代（升级版）-2024新版- A款\B款\C款
            "PDL20240418A00006952": ['PL2418980024A10000192', 'PL2418980024A10000193', 'PL2418980024A10000194'],
            //粤享行六代（代步版）-A款
            "PDL20240717A00010664": ['PL2418980024A10000751'],
            //畅行保3.0 （两座）
            "PDL20250326A00021047": ['PL242M980025A10000079', 'PL242M980025A10000080', 'PL242M980025A10000081'],
            //畅行保3.0 （三座及以上）
            "PDL20250326A00021048": ['PL242M980025A10000088', 'PL242M980025A10000089', 'PL242M980025A10000090']
    ]
}
//四川地区 planCode 详细
static getPlans() {
    [
            //国民意外2020
            "P23C198002000000660": [
                    "planCode"         : "P23C198002000000660",
                    "planName"         : "国民意外2020-计划A",
                    "planGroupCode"    : "PG20201012S00000427",
                    "planGroupName"    : "国民意外（2020版）",
                    "planNickName"     : "国民意外2020-计划A",
                    "classesCode"      : "23C1980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1210150.00,
                    "premium"          : 125.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "S"

            ],
            "P23C198002000000659": [
                    "planCode"         : "P23C198002000000659",
                    "planName"         : "国民意外2020-计划B",
                    "planGroupCode"    : "PG20201012S00000427",
                    "planGroupName"    : "国民意外（2020版）",
                    "planNickName"     : "国民意外2020-计划B",
                    "classesCode"      : "23C1980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1613350.00,
                    "premium"          : 158.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "S"
            ],
            "P23C198002000000658": [
                    "planCode"        : "P23C198002000000658",
                    "planName"        : "国民意外2020-计划C",
                    "planGroupCode"   : "PG20201012S00000427",
                    "planGroupName"   : "国民意外（2020版）",
                    "planNickName"    : "国民意外2020-计划C",
                    "classesCode"     : "23C1980000000001",
                    "classesName"     : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"  : 1,
                    "sumCoverage"     : 2770580.00,
                    "premium"         : 245.00,
                    groupInsuranceFlag: "S"
            ],
            "P23C198002000000657": [
                    "planCode"        : "P23C198002000000657",
                    "planName"        : "国民意外2020-计划D",
                    "planGroupCode"   : "PG20201012S00000427",
                    "planGroupName"   : "国民意外（2020版）",
                    "planNickName"    : "国民意外2020-计划D",
                    "classesCode"     : "23C1980000000001",
                    "classesName"     : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"  : 1,
                    "sumCoverage"     : 4830900.00,
                    "premium"         : 348.00,
                    groupInsuranceFlag: "S",
            ],


            //全新任我行一代 5座 ABC
            "P23E598002000001776": [
                    "planCode"         : "P23E598002000001776",
                    "planName"         : "全新任我行一代A款（5座）",
                    "planGroupCode"    : "PG20201130S00000897",
                    "planGroupName"    : "任我行一代（5座）",
                    "planNickName"     : "全新任我行一代A款（5座）",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 552500.00,
                    "premium"          : 220.00,
                    "maxIntervalNumber": 5,
                    groupInsuranceFlag : "G"
            ],
            "P23E598002000001778": [
                    "planCode"         : "P23E598002000001778",
                    "planName"         : "全新任我行一代B款（5座）",
                    "planGroupCode"    : "PG20201130S00000898",
                    "planGroupName"    : "任我行一代（5座）",
                    "planNickName"     : "全新任我行一代B款（5座）",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1078750.00,
                    "premium"          : 400.00,
                    "maxIntervalNumber": 5,
                    groupInsuranceFlag : "G"//
            ],
            "P23E598002000001780": [
                    "planCode"         : "P23E598002000001780",
                    "planName"         : "全新任我行一代C款（5座）",
                    "planGroupCode"    : "PG20201130S00000899",
                    "planGroupName"    : "任我行一代（5座）",
                    "planNickName"     : "全新任我行一代C款（5座）",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 2654500.00,
                    "premium"          : 850.00,
                    "maxIntervalNumber": 5,
                    groupInsuranceFlag : "G" //电子保单问题？ 四川是G 其他地区是什么？
            ],
//            全新任我行一代 7座 ABC
            "P23E598002000001781": [
                    "planCode"         : "P23E598002000001781",
                    "planName"         : "全新任我行一代C款（7座）",
                    "planGroupCode"    : "PG20201130S00000928",
                    "planGroupName"    : "全新任我行一代",
                    "planNickName"     : "全新任我行一代C款（7座）",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 3715100.00,
                    "premium"          : 1150.00,
                    "maxIntervalNumber": 7,
                    groupInsuranceFlag : "G",
            ],
            "P23E598002000001779": [
                    "planCode"         : "P23E598002000001779",
                    "planName"         : "全新任我行一代B款（7座）",
                    "planGroupCode"    : "PG20201130S00000927",
                    "planGroupName"    : "全新任我行一代",
                    "planNickName"     : "全新任我行一代B款（7座）",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1509050.00,
                    "premium"          : 550.00,
                    "maxIntervalNumber": 7,
                    groupInsuranceFlag : "G",
            ],
            "P23E598002000001777": [
                    "planCode"         : "P23E598002000001777",
                    "planName"         : "全新任我行一代A款（7座）",
                    "planGroupCode"    : "PG20201130S00000926",
                    "planGroupName"    : "全新任我行一代",
                    "planNickName"     : "全新任我行一代A款（7座）",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 772700.00,
                    "premium"          : 300.00,
                    "maxIntervalNumber": 7,
                    groupInsuranceFlag : "G",
            ],
            //任我行三代 5 座
            "P23E598002000001588": [
                    "planCode"         : "P23E598002000001588",
                    "planName"         : "任我行三代经济款（5座）新外网",
                    "planGroupCode"    : "PG20201101S00000685",
                    "planGroupName"    : "全新任我行三代5座",
                    "planNickName"     : "任我行三代经济款（5座）新外网",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 4850300.00,
                    "premium"          : 350.00,
                    "maxIntervalNumber": 5,
                    groupInsuranceFlag : "G",
            ],
            "P23E598002000001591": [
                    "planCode"         : "P23E598002000001591",
                    "planName"         : "任我行三代豪华款（5座）新外网",
                    "planGroupCode"    : "PG20201101S00000685",
                    "planGroupName"    : "全新任我行三代5座",
                    "planNickName"     : "任我行三代豪华款（5座）新外网",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 9450550.00,
                    "premium"          : 500.00,
                    "maxIntervalNumber": 5,
                    groupInsuranceFlag : "G",
            ],
            "P23E598002000001594": [
                    "planCode"         : "P23E598002000001594",
                    "planName"         : "任我行三代尊贵款（5座）新外网",
                    "planGroupCode"    : "PG20201101S00000685",
                    "planGroupName"    : "全新任我行三代5座",
                    "planNickName"     : "任我行三代尊贵款（5座）新外网",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 18701050.00,
                    "premium"          : 800.00,
                    "maxIntervalNumber": 5,
                    groupInsuranceFlag : "G",
            ],
            "P23E598002000001596": [
                    "planCode"         : "P23E598002000001596",
                    "planName"         : "任我行三代钻石版（5座）新外网",
                    "planGroupCode"    : "PG20201101S00000685",
                    "planGroupName"    : "全新任我行三代5座",
                    "planNickName"     : "任我行三代钻石版（5座）新外网",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 31201550.00,
                    "premium"          : 1388.00,
                    "maxIntervalNumber": 5,
                    groupInsuranceFlag : "G",
            ],
            //任我行三代 7座
            "P23E598002000001589": [
                    "planCode"         : "P23E598002000001589",
                    "planName"         : "任我行三代经济款（7座）新外网",
                    "planGroupCode"    : "PG20201101S00000687",
                    "planGroupName"    : "全新任我行三代7座",
                    "planNickName"     : "任我行三代经济款（7座）新外网",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 6710400.00,
                    "premium"          : 490.00,
                    "maxIntervalNumber": 7,
                    groupInsuranceFlag : "G",
            ],
            "P23E598002000001592": [
                    "planCode"         : "P23E598002000001592",
                    "planName"         : "任我行三代豪华款（7座）新外网",
                    "planGroupCode"    : "PG20201101S00000687",
                    "planGroupName"    : "全新任我行三代7座",
                    "planNickName"     : "任我行三代豪华款（7座）新外网",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 13150750.00,
                    "premium"          : 700.00,
                    "maxIntervalNumber": 7,
                    groupInsuranceFlag : "G",
            ],
            "P23E598002000001595": [
                    "planCode"         : "P23E598002000001595",
                    "planName"         : "任我行三代尊贵款（7座）新外网",
                    "planGroupCode"    : "PG20201101S00000687",
                    "planGroupName"    : "全新任我行三代7座",
                    "planNickName"     : "任我行三代尊贵款（7座）新外网",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 26101450.00,
                    "premium"          : 1120.00,
                    "maxIntervalNumber": 7,
                    groupInsuranceFlag : "G",
            ],
            "P23E598002000001597": [
                    "planCode"         : "P23E598002000001597",
                    "planName"         : "任我行三代钻石版（7座）新外网",
                    "planGroupCode"    : "PG20201101S00000687",
                    "planGroupName"    : "全新任我行三代7座",
                    "planNickName"     : "任我行三代钻石版（7座）新外网",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 43602150.00,
                    "premium"          : 1888.00,
                    "maxIntervalNumber": 7,
                    groupInsuranceFlag : "G",
            ],
            // 山东渠道2013版驾意险
            "P23E998002000000109": [
                    "planCode"         : "P23E998002000000109",
                    "planName"         : "山东2019版新私家车驾意险基础款-118元01",
                    "planGroupCode"    : "PG20210111S00001856",
                    "planGroupName"    : "山东渠道2013版驾意险",
                    "planNickName"     : "山东2019版新私家车驾意险基础款-118元01",
                    "classesCode"      : "23E9980000000001",
                    "classesName"      : "驾意险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 320400.00,
                    "premium"          : 118.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "S",
            ],
            //任我行二代经济款五座 广州市
            "P23E598002000001699": [
                    "planCode"         : "P23E598002000001699",
                    "planName"         : "全新任我行二代经济款（5座）",
                    "planGroupCode"    : "PG20210106S00001792",
                    "planGroupName"    : "全新任我行二代（5座）",
                    "planNickName"     : "全新任我行二代经济款（5座）",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1655550.00,
                    "premium"          : 658.00,
                    "maxIntervalNumber": 5,
                    groupInsuranceFlag : "G",
            ],
            "P23E598002000001771": [
                    "planCode"         : "P23E598002000001771",
                    "planName"         : "全新任我行二代精英版（5座）",
                    "planGroupCode"    : "PG20210106S00001793",
                    "planGroupName"    : "全新任我行二代（5座）",
                    "planNickName"     : "全新任我行二代精英版（5座）",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 3280800.00,
                    "premium"          : 838.00,
                    "maxIntervalNumber": 5,
                    groupInsuranceFlag : "G",
            ],
            "P23E598002000001773": [
                    "planCode"         : "P23E598002000001773",
                    "planName"         : "全新任我行二代豪华版（5座）",
                    "planGroupCode"    : "PG20210106S00001794",
                    "planGroupName"    : "全新任我行二代（5座）",
                    "planNickName"     : "全新任我行二代豪华版（5座）",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 7856550.00,
                    "premium"          : 1288.00,
                    "maxIntervalNumber": 5,
                    groupInsuranceFlag : "G",
            ],
            "P23E598002000001700": [
                    "planCode"         : "P23E598002000001700",
                    "planName"         : "全新任我行二代经济款（7座）",
                    "planGroupCode"    : "PG20210106S00001795",
                    "planGroupName"    : "全新任我行二代(7座)",
                    "planNickName"     : "全新任我行二代经济款（7座）",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 2275750.00,
                    "premium"          : 738.00,
                    "maxIntervalNumber": 7,
                    groupInsuranceFlag : "G",
            ],
            "P23E598002000001772": [
                    "planCode"         : "P23E598002000001772",
                    "planName"         : "全新任我行二代精英版（7座）",
                    "planGroupCode"    : "PG20210106S00001796",
                    "planGroupName"    : "全新任我行二代(7座)",
                    "planNickName"     : "全新任我行二代精英版（7座）",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 4511100.00,
                    "premium"          : 988.00,
                    "maxIntervalNumber": 7,
                    groupInsuranceFlag : "G",
            ],
            "P23E598002000001775": [
                    "planCode"         : "P23E598002000001775",
                    "planName"         : "全新任我行二代豪华版（7座）",
                    "planGroupCode"    : "PG20210106S00001797",
                    "planGroupName"    : "全新任我行二代(7座)",
                    "planNickName"     : "全新任我行二代豪华版（7座）",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "驾乘人员人身意外伤害保险（2013版）",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 10917150.00,
                    "premium"          : 1588.00,
                    "maxIntervalNumber": 7,
                    groupInsuranceFlag : "G",
            ],
            //四川 川分驾乘意（100元）新外网 5
            "P23E598002100000416": [
                    "planCode"          : "P23E598002100000416",
                    "productCode"       : "PD20210304S00002422",
                    "planGroupCode"     : "PG20210304S00002529",
                    "planGroupName"     : "川分驾乘意（100元）新外网 5",
                    "planName"          : "川分驾乘100元(5座)",
                    "classesCode"       : "23E5980000000001",
                    "calcFlag"          : "P",
                    "premium"           : 100.00,
                    "sumCoverage"       : 550000.00,
                    "classesSourceCode" : "ryx",
                    "groupInsuranceFlag": "G",
                    "planNickName"      : "川分驾乘100元(5座)",
            ],
            //四川 川分驾乘意（100元）新外网 7
            "P23E598002100000415": [
                    "planCode"          : "P23E598002100000415",
                    "productCode"       : "PD20210304S00002422",
                    "planGroupCode"     : "PG20210304S00002530",
                    "planGroupName"     : "川分驾乘意（100元）新外网 7",
                    "planName"          : "川分驾乘150元(7座)",
                    "classesCode"       : "23E5980000000001",
                    "calcFlag"          : "P",
                    "premium"           : 150.00,
                    "sumCoverage"       : 770000,
                    "classesSourceCode" : "ryx",
                    "groupInsuranceFlag": "G",
                    "planNickName"      : "川分驾乘100元(7座)",
            ],
            //鲁行渠道驾乘（基础版）5座
            "P23E598002100000355": [
                    "planCode"          : "P23E598002100000355",
                    "productCode"       : "PD20210225S00002349",
                    "planGroupCode"     : "PG20210225S00002408",
                    "planName"          : "鲁行渠道驾乘（基础版）5座",
                    "classesCode"       : "23E5980000000001",
                    "calcFlag"          : "P",
                    "premium"           : 150.00,
                    "sumCoverage"       : 550000,
                    "classesSourceCode" : "ryx",
                    "rangeCalc"         : false,
                    "groupInsuranceFlag": "G",
                    "day"               : false,
                    "planNickName"      : "鲁行渠道驾乘（基础版）5座",
            ],
            //鲁行渠道驾乘（基础版）7座
            "P23E598002100000358": [
                    "productCode"       : "PD20210225S00002349",
                    "planCode"          : "P23E598002100000358",
                    "planGroupCode"     : "PG20210225S00002408",
                    "planName"          : "鲁行渠道驾乘（基础版）7座",
                    "classesCode"       : "23E5980000000001",
                    "calcFlag"          : "P",
                    "premium"           : 210.00,
                    "classesSourceCode" : "ryx",
                    "sumCoverage"       : 770000,
                    "rangeCalc"         : false,
                    "groupInsuranceFlag": "G",
                    "planNickName"      : "鲁行渠道驾乘（基础版）7座",
                    "sort"              : 1,
            ],
            //鲁行渠道驾乘（基础版）8座
            "P23E598002100000364": [
                    "productCode"       : "PD20210225S00002349",
                    "planCode"          : "P23E598002100000364",
                    "planGroupCode"     : "PG20210225S00002408",
                    "planName"          : "鲁行渠道驾乘（基础版）8座",
                    "classesCode"       : "23E5980000000001",
                    "calcFlag"          : "P",
                    "premium"           : 240.00,
                    "classesSourceCode" : "ryx",
                    "rangeCalc"         : false,
                    "sumCoverage"       : 880000,
                    "groupInsuranceFlag": "G",
                    "day"               : false,
                    "planNickName"      : "鲁行渠道驾乘（基础版）8座",
                    "sort"              : 2,
            ],
            //鲁行单交强驾乘
            "P23E598002100000906": [
                    "productCode"       : "PD20210511S00003316",
                    "planCode"          : "P23E598002100000906",
                    "planGroupCode"     : "PG20210511S00003290",
                    "planName"          : "鲁行单交强驾乘",
                    "classesCode"       : "23E5980000000001",
                    "calcFlag"          : "P",
                    "premium"           : 16.00,
                    "sumCoverage"       : 55030.00,
                    "classesSourceCode" : "ryx",
                    "rangeCalc"         : false,
                    "groupInsuranceFlag": "G",
                    "day"               : false,
                    "planNickName"      : "鲁行单交强驾乘",
                    "sort"              : 0,
            ],
            "P23E598002100000652": [//任我行三代升级款    经济款 需要special
                                    "planCode"          : "P23E598002100000652",
                                    "productCode"       : "PD20210630S00004072",
                                    "planGroupCode"     : "PG20210630S00003988",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 105.00,
                                    "sumCoverage"       : 1430100.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "任我行三代经济款",
                                    "planName"          : "任我行三代经济款",
                                    "sort"              : 0,
            ],
            "P23E598002100000667": [//任我行三代豪华款     豪华款 需要special
                                    "productCode"       : "PD20210630S00004072",
                                    "planCode"          : "P23E598002100000667",
                                    "planGroupCode"     : "PG20210630S00003988",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 150.00,
                                    "sumCoverage"       : 2650150.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "任我行三代豪华款",
                                    "planName"          : "任我行三代豪华款",
                                    "sort"              : 0,
            ],
            "P23E598002100000668": [//任我行三代尊贵款      需要special
                                    "productCode"       : "PD20210630S00004072",
                                    "planCode"          : "P23E598002100000668",
                                    "planGroupCode"     : "PG20210630S00003988",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 186.00,
                                    "sumCoverage"       : 3900250.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "任我行三代尊贵款",
                                    "planName"          : "任我行三代尊贵款",
                                    "sort"              : 0,
            ],
            "P23E598002100001375": [//任我行三代尊享款（升级版）     需要special
                                    "productCode"       : "PD20210630S00004072",
                                    "planCode"          : "P23E598002100001375",
                                    "planGroupCode"     : "PG20210630S00003988",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 232.00,
                                    "sumCoverage"       : 2750250.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "任我行三代尊享款（升级版）",
                                    "planName"          : "任我行三代尊享款（升级版）",
                                    "sort"              : 0,
            ],
            "P23E598002100001374": [//任我行三代钻石版（升级版）     需要special
                                    "productCode"       : "PD20210630S00004072",
                                    "planCode"          : "P23E598002100001374",
                                    "planGroupCode"     : "PG20210630S00003988",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 392.00,
                                    "sumCoverage"       : 4500350.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "任我行三代钻石版（升级版）",
                                    "planName"          : "任我行三代钻石版（升级版）",
                                    "sort"              : 0,
            ],

            //随行保驾乘险 5座
            "P23E598002100000883": [//东莞随行保驾乘险-5座钻石款（新）     需要special
                                    "productCode"       : "PD20210615S00003798",
                                    "planCode"          : "P23E598002100000883",
                                    "planGroupCode"     : "PG20210615S00003783",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 1389.00,
                                    "sumCoverage"       : 6504600.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "东莞随行保驾乘险-5座钻石款（新）",
                                    "planName"          : "东莞随行保驾乘险-5座钻石款（新）",
                                    "sort"              : 0,
            ],
            "P23E598002100000888": [//东莞随行保驾乘险-5座尊享款     需要special
                                    "productCode"       : "PD20210615S00003798",
                                    "planCode"          : "P23E598002100000888",
                                    "planGroupCode"     : "PG20210615S00003783",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 759.00,
                                    "sumCoverage"       : 4303050.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "东莞随行保驾乘险-5座尊享款",
                                    "planName"          : "东莞随行保驾乘险-5座尊享款",
                                    "sort"              : 0,
            ],
            "P23E598002100000886": [//东莞随行保驾乘险-5座豪华款     需要special
                                    "productCode"       : "PD20210615S00003798",
                                    "planCode"          : "P23E598002100000886",
                                    "planGroupCode"     : "PG20210615S00003783",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 469.00,
                                    "sumCoverage"       : 2150800.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "东莞随行保驾乘险-5座豪华款",
                                    "planName"          : "东莞随行保驾乘险-5座豪华款",
                                    "sort"              : 0,
            ],
            "P23E598002100000875": [//东莞随行保驾乘险-5座精英款     需要special
                                    "productCode"       : "PD20210615S00003798",
                                    "planCode"          : "P23E598002100000875",
                                    "planGroupCode"     : "PG20210615S00003783",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 339.00,
                                    "sumCoverage"       : 800500.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "东莞随行保驾乘险-5座精英款",
                                    "planName"          : "东莞随行保驾乘险-5座精英款",
                                    "sort"              : 0,
            ],
            "P23E598002100000874": [//东莞随行保驾乘险-5座经济款    需要special
                                    "productCode"       : "PD20210615S00003798",
                                    "planCode"          : "P23E598002100000874",
                                    "planGroupCode"     : "PG20210615S00003783",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 209.00,
                                    "sumCoverage"       : 1120700.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "东莞随行保驾乘险-5座经济款",
                                    "planName"          : "东莞随行保驾乘险-5座经济款",
                                    "sort"              : 0,
            ],


            "P23E598002100000882": [//东莞随行保驾乘险-7座经济款    需要special
                                    "productCode"       : "PD20210615S00003798",
                                    "planCode"          : "P23E598002100000882",
                                    "planGroupCode"     : "PG20210615S00003783",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 289.00,
                                    "sumCoverage"       : 3011100.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "东莞随行保驾乘险-7座经济款",
                                    "planName"          : "任我行三代钻石版（升级版）",
                                    "sort"              : 0,
            ],
            "P23E598002100000884": [//任我行三代钻石版（升级版）     需要special
                                    "productCode"       : "PD20210615S00003798",
                                    "planCode"          : "P23E598002100000884",
                                    "planGroupCode"     : "PG20210615S00003783",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 459.00,
                                    "sumCoverage"       : 6023450.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "东莞随行保驾乘险-7座精英款",
                                    "planName"          : "东莞随行保驾乘险-7座精英款",
                                    "sort"              : 0,
            ],
            "P23E598002100000887": [//东莞随行保驾乘险-7座豪华款     需要special
                                    "productCode"       : "PD20210615S00003798",
                                    "planCode"          : "P23E598002100000887",
                                    "planGroupCode"     : "PG20210615S00003783",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 629.00,
                                    "sumCoverage"       : 9105200.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "东莞随行保驾乘险-7座豪华款",
                                    "planName"          : "东莞随行保驾乘险-7座豪华款",
                                    "sort"              : 0,
            ],
            "P23E598002100000889": [//东莞随行保驾乘险-7座尊享款     需要special
                                    "productCode"       : "PD20210615S00003798",
                                    "planCode"          : "P23E598002100000889",
                                    "planGroupCode"     : "PG20210615S00003783",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 979.00,
                                    "sumCoverage"       : 15602150.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "东莞随行保驾乘险-7座尊享款",
                                    "planName"          : "东莞随行保驾乘险-7座尊享款",
                                    "sort"              : 0,
            ],
            "P23E598002100000885": [//东莞随行保驾乘险-7座钻石款    需要special
                                    "productCode"       : "PD20210615S00003798",
                                    "planCode"          : "P23E598002100000885",
                                    "planGroupCode"     : "PG20210615S00003783",
                                    "classesCode"       : "23E5980000000001",
                                    "premium"           : 1829.00,
                                    "sumCoverage"       : 15602150.00,
                                    "classesSourceCode" : "ryx",
                                    "rangeCalc"         : false,
                                    "groupInsuranceFlag": "G",
                                    "day"               : false,
                                    "planNickName"      : "东莞随行保驾乘险-7座钻石款",
                                    "planName"          : "东莞随行保驾乘险-7座钻石款",
                                    "sort"              : 0,
            ],
            //河北
            "P23E998002100000057": [
                    "productCode"      : "PD20210316S00002566",
                    "planCode"         : "P23E998002100000057",
                    "planName"         : "驾意险-车险专属02",
                    "planGroupCode"    : "PG20210316S00002679",
                    "planGroupName"    : "国民意外（2020版）",
                    "planNickName"     : "国民意外2020-计划A",
                    "classesCode"      : "23E9980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 63000.00,
                    "premium"          : 108.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "S"

            ],
            //福建 任我行三代经济款（升级版）
            "P23E598002100001718": [
                    "productCode"      : "PD20210823S00005585",
                    "planCode"         : "P23E598002100001718",
                    "planName"         : "任我行三代经济款（升级版）",
                    "planGroupCode"    : "PG20210823S00005144",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 63000.00,
                    "premium"          : 70.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G"

            ],
            //福建 任我行三代豪华款（升级版）
            "P23E598002100001713": [
                    "productCode"      : "PD20210823S00005585",
                    "planCode"         : "P23E598002100001713",
                    "planName"         : "任我行三代豪华款（升级版）",
                    "planGroupCode"    : "PG20210823S00005144",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 63000.00,
                    "premium"          : 100.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G"

            ],
            //任我行三代尊贵款（升级版）
            "P23E598002100001714": [
                    "productCode"      : "PD20210823S00005585",
                    "planCode"         : "P23E598002100001714",
                    "planName"         : "任我行三代尊贵款（升级版）",
                    "planGroupCode"    : "PG20210823S00005144",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 63000.00,
                    "premium"          : 130.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G"
            ],
            //任我行三代尊享款（升级版）
            "P23E598002100001716": [
                    "productCode"      : "PD20210823S00005585",
                    "planCode"         : "P23E598002100001716",
                    "planName"         : "任我行三代尊享款（升级版）",
                    "planGroupCode"    : "PG20210823S00005144",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 63000.00,
                    "premium"          : 160.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G"
            ],
            //任我行三代钻石版（升级版）
            "P23E598002100001717": [
                    "productCode"      : "PD20210823S00005585",
                    "planCode"         : "P23E598002100001717",
                    "planName"         : "任我行三代钻石版（升级版）",
                    "planGroupCode"    : "PG20210823S00005144",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 63000.00,
                    "premium"          : 276.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G"
            ],
            //任我行2022（升级版）-黄金款
            "P23XZ98002100000012": [
                    "productCode"      : "PD20211222S00008075",
                    "planCode"         : "P23XZ98002100000012",
                    "planName"         : "任我行2022（升级版）-黄金款",
                    "planGroupCode"    : "PG20211222S00007206",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 11203050.00,
                    "premium"          : 400.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G"
            ],
            //任我行2022（升级版）-铂金款
            "P23XZ98002100000020": [
                    "productCode"      : "PD20211222S00008075",
                    "planCode"         : "P23XZ98002100000020",
                    "planName"         : "任我行2022（升级版）-铂金款",
                    "planGroupCode"    : "PG20211222S00007206",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 15535550.00,
                    "premium"          : 650.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G"
            ],
            //任我行2022（升级版）-钻石款
            "P23XZ98002100000021": [
                    "productCode"      : "PD20211222S00008075",
                    "planCode"         : "P23XZ98002100000021",
                    "planName"         : "任我行2022（升级版）-钻石款",
                    "planGroupCode"    : "PG20211222S00007206",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 13619550.00,
                    "premium"          : 1000.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G"
            ],
            //任我行2022（基础版）-经济款
            "P23XZ98002100000010": [
                    "productCode"      : "PD20211222S00008067",
                    "planCode"         : "P23XZ98002100000010",
                    "planName"         : "任我行2022（基础版）-经济款",
                    "planGroupCode"    : "PG20211222S00007195",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 3303550.00,
                    "premium"          : 11.10,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 164.50
            ],
            //任我行2022（基础版）-精英款
            "P23XZ98002100000017": [
                    "productCode"      : "PD20211222S00008067",
                    "planCode"         : "P23XZ98002100000017",
                    "planName"         : "任我行2022（基础版）-精英款",
                    "planGroupCode"    : "PG20211222S00007195",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 6353800.00,
                    "premium"          : 15.28,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 203.60
            ],
            //任我行2022（基础版）-豪华款
            "P23XZ98002100000018": [
                    "productCode"      : "PD20211222S00008067",
                    "planCode"         : "P23XZ98002100000018",
                    "planName"         : "任我行2022（基础版）-豪华款",
                    "planGroupCode"    : "PG20211222S00007195",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 9454050.00,
                    "premium"          : 21.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 245.00
            ],
            //鲁行保（代步版）升级款
            "P23XZ98002200000218": [
                    "productCode"      : "PD20220124S00009026",
                    "planCode"         : "P23XZ98002200000218",
                    "planName"         : "鲁行保（代步版）升级款",
                    "planGroupCode"    : "PG20220124S00008017",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1551250.00,
                    "premium"          : 18.50,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 207.50
            ],
            //鲁行单交强驾乘-升级款
            "P23XZ98002200000219": [
                    "productCode"      : "PD20220126S00009058",
                    "planCode"         : "P23XZ98002200000219",
                    "planName"         : "鲁行单交强驾乘-升级款",
                    "planGroupCode"    : "PG20220126S00008046",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1551250.00,
                    "premium"          : 4.50,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 77.50
            ],
            //鲁行渠道驾乘升级款（基础版）
            "P23XZ98002200000524": [
                    "productCode"      : "PD20220303S00009691",
                    "planCode"         : "P23XZ98002200000524",
                    "planName"         : "鲁行渠道驾乘升级款（基础版）",
                    "planGroupCode"    : "PG20220303S00008592",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 2011300.00,
                    "premium"          : 7.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 125.00
            ],
            //鲁行渠道驾乘升级款（经典版）
            "P23XZ98002200000525": [
                    "productCode"      : "PD20220303S00009691",
                    "planCode"         : "P23XZ98002200000525",
                    "planName"         : "鲁行渠道驾乘升级款（经典版）",
                    "planGroupCode"    : "PG20220303S00008592",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 3231300.00,
                    "premium"          : 10.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 190.00
            ],
            //鲁行渠道驾乘升级款（高端版）
            "P23XZ98002200000527": [
                    "productCode"      : "PD20220303S00009691",
                    "planCode"         : "P23XZ98002200000527",
                    "planName"         : "鲁行渠道驾乘升级款（高端版）",
                    "planGroupCode"    : "PG20220303S00008592",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 4651500.00,
                    "premium"          : 13.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 265.00
            ],
            //畅行保宁波版一代-黄金款（五座）
            "P23XZ98002200000728": [
                    "productCode"      : "PD20220319S00010073",
                    "planCode"         : "P23XZ98002200000728",
                    "planName"         : "畅行保宁波版一代-黄金款（五座）",
                    "planGroupCode"    : "PG20220319S00008960",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 14352500.00,
                    "premium"          : 350.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //畅行保宁波版一代-黄金款（七座）
            "P23XZ98002200000739": [
                    "productCode"      : "PD20220319S00010073",
                    "planCode"         : "P23XZ98002200000739",
                    "planName"         : "畅行保宁波版一代-黄金款（七座）",
                    "planGroupCode"    : "PG20220319S00008960",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 14352500.00,
                    "premium"          : 450.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //畅行保（党政机关及企业客车专属）-七座黄金款
            "P23XZ98002200000026": [
                    "productCode"      : "PD20220107S00008585",
                    "planCode"         : "P23XZ98002200000026",
                    "planName"         : "畅行保（党政机关及企业客车专属）-七座黄金款",
                    "planGroupCode"    : "PG20220107S00007658",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 14353400.00,
                    "premium"          : 24.75,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 276.75
            ],
            //畅行保（党政机关及企业客车专属）-七座铂金款
            "P23XZ98002200000027": [
                    "productCode"      : "PD20220107S00008585",
                    "planCode"         : "P23XZ98002200000027",
                    "planName"         : "畅行保（党政机关及企业客车专属）-七座铂金款",
                    "planGroupCode"    : "PG20220107S00007658",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 19606100.00,
                    "premium"          : 39.40,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 424.20
            ],
            //畅行保（党政机关及企业客车专属）-五座黄金款"
            "P23XZ98002200000020": [
                    "productCode"      : "PD20220107S00008582",
                    "planCode"         : "P23XZ98002200000020",
                    "planName"         : "畅行保（党政机关及企业客车专属）-五座黄金款",
                    "planGroupCode"    : "PG20220107S00007654",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 14353400.00,
                    "premium"          : 24.75,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 226.25
            ],
            //畅行保（党政机关及企业客车专属）-五座铂金款
            "P23XZ98002200000021": [
                    "productCode"      : "PD20220107S00008582",
                    "planCode"         : "P23XZ98002200000021",
                    "planName"         : "畅行保（党政机关及企业客车专属）-五座铂金款",
                    "planGroupCode"    : "PG20220107S00007654",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 19606100.00,
                    "premium"          : 39.40,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 403.00
            ],
            //莞非营运货车驾乘险方案一
            "P23XZ98002200000093": [
                    "productCode"      : "PD20220206S00009097",
                    "planCode"         : "P23XZ98002200000093",
                    "planName"         : "莞非营运货车驾乘险方案一",
                    "planGroupCode"    : "PG20220206S00008089",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 550250.00,
                    "premium"          : 90.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //莞非营运货车驾乘险方案二
            "P23XZ98002200000095": [
                    "productCode"      : "PD20220206S00009097",
                    "planCode"         : "P23XZ98002200000095",
                    "planName"         : "莞非营运货车驾乘险方案二",
                    "planGroupCode"    : "PG20220206S00008089",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1100400.00,
                    "premium"          : 160.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //莞非营运货车驾乘险2022-方案一（2座）
            "P23XZ98002200001042": [
                    "productCode"      : "PD20220503S00010992",
                    "planCode"         : "P23XZ98002200001042",
                    "planName"         : "莞非营运货车驾乘险2022-方案一（2座）",
                    "planGroupCode"    : "PG20220503S00009813",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 221100.00,
                    "premium"          : 90.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //莞非营运货车驾乘险2022-方案一（3座）
            "P23XZ98002200001043": [
                    "productCode"      : "PD20220503S00010992",
                    "planCode"         : "P23XZ98002200001043",
                    "planName"         : "莞非营运货车驾乘险2022-方案一（3座）",
                    "planGroupCode"    : "PG20220503S00009813",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 331150.00,
                    "premium"          : 90.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //莞非营运货车驾乘险2022-方案一（4座）
            "P23XZ98002200001044": [
                    "productCode"      : "PD20220503S00010992",
                    "planCode"         : "P23XZ98002200001044",
                    "planName"         : "莞非营运货车驾乘险2022-方案一（4座）",
                    "planGroupCode"    : "PG20220503S00009813",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 441200.00,
                    "premium"          : 90.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //莞非营运货车驾乘险2022-方案一（5座）
            "P23XZ98002200001045": [
                    "productCode"      : "PD20220503S00010992",
                    "planCode"         : "P23XZ98002200001045",
                    "planName"         : "莞非营运货车驾乘险2022-方案一（5座）",
                    "planGroupCode"    : "PG20220503S00009813",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 551250.00,
                    "premium"          : 90.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //莞非营运货车驾乘险2022-方案二
            "P23XZ98002200001046": [
                    "productCode"      : "PD20220520S00011326",
                    "planCode"         : "P23XZ98002200001046",
                    "planName"         : "莞非营运货车驾乘险2022方案二",
                    "planGroupCode"    : "PG20220520S00010138",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 441160.00,
                    "premium"          : 160.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //莞非营运货车驾乘险2022-方案二（3座）
            "P23XZ98002200001047": [
                    "productCode"      : "PD20220520S00011326",
                    "planCode"         : "P23XZ98002200001047",
                    "planName"         : "莞非营运货车驾乘险2022-方案二（3座）",
                    "planGroupCode"    : "PG20220520S00010138",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 661240.00,
                    "premium"          : 160.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //莞非营运货车驾乘险2022-方案二（4座）
            "P23XZ98002200001048": [
                    "productCode"      : "PD20220520S00011326",
                    "planCode"         : "P23XZ98002200001048",
                    "planName"         : "莞非营运货车驾乘险2022-方案二（4座）",
                    "planGroupCode"    : "PG20220520S00010138",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 881320.00,
                    "premium"          : 160.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //莞非营运货车驾乘险2022-方案二（5座）
            "P23XZ98002200001049": [
                    "productCode"      : "PD20220520S00011326",
                    "planCode"         : "P23XZ98002200001049",
                    "planName"         : "莞非营运货车驾乘险2022-方案二（5座）",
                    "planGroupCode"    : "PG20220520S00010138",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1101400.00,
                    "premium"          : 160.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //莞非营运货车驾乘险2022方案三
            "P23XZ98002200001050": [
                    "productCode"      : "PD20220520S00011334",
                    "planCode"         : "P23XZ98002200001050",
                    "planName"         : "莞非营运货车驾乘险2022方案三",
                    "planGroupCode"    : "PG20220520S00010144",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 661200.00,
                    "premium"          : 240.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //莞非营运货车驾乘险2022-方案三（3座）
            "P23XZ98002200001051": [
                    "productCode"      : "PD20220520S00011334",
                    "planCode"         : "P23XZ98002200001051",
                    "planName"         : "莞非营运货车驾乘险2022方案三（3座）",
                    "planGroupCode"    : "PG20220520S00010144",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 991300.00,
                    "premium"          : 240.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //莞非营运货车驾乘险2022-方案三（4座）
            "P23XZ98002200001052": [
                    "productCode"      : "PD20220520S00011334",
                    "planCode"         : "P23XZ98002200001052",
                    "planName"         : "莞非营运货车驾乘险2022方案三（4座）",
                    "planGroupCode"    : "PG20220520S00010144",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1321400.00,
                    "premium"          : 240.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //莞非营运货车驾乘险2022-方案三（5座）
            "P23XZ98002200001053": [
                    "productCode"      : "PD20220520S00011334",
                    "planCode"         : "P23XZ98002200001053",
                    "planName"         : "莞非营运货车驾乘险2022方案三（5座）",
                    "planGroupCode"    : "PG20220520S00010144",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1651500.00,
                    "premium"          : 240.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //畅行无忧-非营业货车（A款）
            "P23E598002200000461": [
                    "productCode"      : "PD20220616S00012207",
                    "planCode"         : "P23E598002200000461",
                    "planName"         : "畅行无忧-非营业货车（A款）",
                    "planGroupCode"    : "PG20220616S00010911",
                    "classesCode"      : "23XZ980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1001500.00,
                    "premium"          : 360.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //任我行六代（升级版）-A款
            "P241898002200000073": [
                    "productCode"      : "PD20221008S00015581",
                    "planCode"         : "P241898002200000073",
                    "planName"         : "任我行六代（升级版）-A款",
                    "planGroupCode"    : "PG20221008S00013877",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 9077000.00,
                    "premium"          : 62.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 50.00
            ],
            //任我行六代（升级版）-B款
            "P241898002200000075": [
                    "productCode"      : "PD20221008S00015581",
                    "planCode"         : "P241898002200000075",
                    "planName"         : "任我行六代（升级版）-B款",
                    "planGroupCode"    : "PG20221008S00013877",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 10897500.00,
                    "premium"          : 74.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 60.00
            ],
            //任我行六代（升级版）-C款
            "P241898002200000076": [
                    "productCode"      : "PD20221008S00015581",
                    "planCode"         : "P241898002200000076",
                    "planName"         : "任我行六代（升级版）-C款",
                    "planGroupCode"    : "PG20221008S00013877",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 7042500.00,
                    "premium"          : 77.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 135.00
            ],
            //任我行六代（升级版）-D款
            "P241898002200000085": [
                    "productCode"      : "PD20221008S00015581",
                    "planCode"         : "P241898002200000085",
                    "planName"         : "任我行六代（升级版）-D款",
                    "planGroupCode"    : "PG20221008S00013877",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 9402500.00,
                    "premium"          : 99.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 165.00
            ],
            //东莞太享行-方案一
            "P241898002200000326": [
                    "productCode"      : "PD20221021S00015953",
                    "planCode"         : "P241898002200000326",
                    "planName"         : "东莞太享行-方案一",
                    "planGroupCode"    : "PG20221021S00014103",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 285250.00,
                    "premium"          : 100.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //东莞太享行-方案二
            "P241898002200000353": [
                    "productCode"      : "PD20221021S00015953",
                    "planCode"         : "P241898002200000353",
                    "planName"         : "东莞太享行-方案二",
                    "planGroupCode"    : "PG20221021S00014103",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 560250.00,
                    "premium"          : 160.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //东莞太享行-方案三
            "P241898002200000354": [
                    "productCode"      : "PD20221021S00015953",
                    "planCode"         : "P241898002200000354",
                    "planName"         : "东莞太享行-方案三",
                    "planGroupCode"    : "PG20221021S00014103",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1110250.00,
                    "premium"          : 290.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //新广东非营运货车驾乘险-方案一
            "P241898002200000458": [
                    "productCode"      : "PD20221103S00016224",
                    "planCode"         : "P241898002200000458",
                    "planName"         : "新广东非营运货车驾乘险-方案一",
                    "planGroupCode"    : "PG20221103S00014360",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 800500.00,
                    "premium"          : 90.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //新广东非营运货车驾乘险-方案二
            "P241898002200000459": [
                    "productCode"      : "PD20221103S00016224",
                    "planCode"         : "P241898002200000459",
                    "planName"         : "新广东非营运货车驾乘险-方案二",
                    "planGroupCode"    : "PG20221103S00014360",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1500500.00,
                    "premium"          : 160.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //新广东非营运货车驾乘险-方案三
            "P241898002200000460": [
                    "productCode"      : "PD20221103S00016224",
                    "planCode"         : "P241898002200000460",
                    "planName"         : "新广东非营运货车驾乘险-方案三",
                    "planGroupCode"    : "PG20221103S00014360",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 2150500.00,
                    "premium"          : 240.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //任我行六代（升级版）-A款
            "P241898002200000066": [
                    "productCode"      : "PD20221008S00015580",
                    "planCode"         : "P241898002200000066",
                    "planName"         : "任我行六代（基础版）-A款",
                    "planGroupCode"    : "PG20221008S00013876",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 3415500.00,
                    "premium"          : 44.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 8.00
            ],
            //任我行六代（升级版）-B款
            "P241898002200000072": [
                    "productCode"      : "PD20221008S00015580",
                    "planCode"         : "P241898002200000072",
                    "planName"         : "任我行六代（基础版）-B款",
                    "planGroupCode"    : "PG20221008S00013876",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 6615750.00,
                    "premium"          : 56.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 8.00
            ],
            //车险新加多保（七座）
            "P23E598002100002269": [
                    "productCode"      : "PD20211108S00007330",
                    "planCode"         : "P23E598002100002269",
                    "planName"         : "车险新加多保（七座）",
                    "planGroupCode"    : "PG20211108S00006457",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 182000.00,
                    "premium"          : 0.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 56.00
            ],
            //车险新加多保（五座）
            "P23E598002100002268": [
                    "productCode"      : "PD20211108S00007330",
                    "planCode"         : "P23E598002100002268",
                    "planName"         : "车险新加多保（五座）",
                    "planGroupCode"    : "PG20211108S00006457",
                    "classesCode"      : "23E5980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 182000.00,
                    "premium"          : 0.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 40.00
            ],
            //新畅行保（3-5座黄金款）-党政机关及企业客车专属产品
            "P241898002200000529": [
                    "productCode"      : "PD20221103S00016200",
                    "planCode"         : "P241898002200000529",
                    "planName"         : "新畅行保（3-5座黄金款）-党政机关及企业客车专属产品",
                    "planGroupCode"    : "PG20221103S00014338",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 4268500.00,
                    "premium"          : 74.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //新畅行保（3-5座铂金款）-党政机关及企业客车专属产品
            "P241898002200000535": [
                    "productCode"      : "PD20221103S00016200",
                    "planCode"         : "P241898002200000535",
                    "planName"         : "新畅行保（3-5座铂金款）-党政机关及企业客车专属产品",
                    "planGroupCode"    : "PG20221103S00014338",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 6029000.00,
                    "premium"          : 126.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //新畅行保（6-9座黄金款）-党政机关及企业客车专属产品
            "P241898002200001072": [
                    "productCode"      : "PD20221207S00017287",
                    "planCode"         : "P241898002200001072",
                    "planName"         : "新畅行保（6-9座黄金款）-党政机关及企业客车专属产品",
                    "planGroupCode"    : "PG20221207S00015456",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 5975900.00,
                    "premium"          : 67.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 1.00
            ],
            //新畅行保（6-9座铂金款）-党政机关及企业客车专属产品
            "P241898002200001073": [
                    "productCode"      : "PD20221207S00017287",
                    "planCode"         : "P241898002200001073",
                    "planName"         : "新畅行保（6-9座铂金款）-党政机关及企业客车专属产品",
                    "planGroupCode"    : "PG20221207S00015456",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 8440600.00,
                    "premium"          : 107.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 1.00
            ],
            //新加多保2022（五座）
            "P241898002200000871": [
                    "productCode"      : "PD20221128S00016963",
                    "planCode"         : "P241898002200000871",
                    "planName"         : "新加多保2022（五座）",
                    "planGroupCode"    : "PG20221128S00015042",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 140000.00,
                    "premium"          : 0.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 40.00
            ],
            //新加多保2022（七座）
            "P241898002200000870": [
                    "productCode"      : "PD20221128S00016963",
                    "planCode"         : "P241898002200000870",
                    "planName"         : "新加多保2022（七座）",
                    "planGroupCode"    : "PG20221128S00015042",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 196000.00,
                    "premium"          : 0.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 56.00
            ],
            //鲁行单交强驾乘三代
            "P241898002200000815": [
                    "productCode"      : "PD20221123S00016784",
                    "planCode"         : "P241898002200000815",
                    "planName"         : "鲁行单交强驾乘三代",
                    "planGroupCode"    : "PG20221123S00014895",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1655250.00,
                    "premium"          : 18.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 10.00
            ],
            //畅行保（山东专属）二代-基础版
            "P241898002200001169": [
                    "productCode"      : "PD20221215S00017499",
                    "planCode"         : "P241898002200001169",
                    "planName"         : "畅行保（山东专属）二代-基础版",
                    "planGroupCode"    : "PG20221215S00015620",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 1513500.00,
                    "premium"          : 46.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //畅行保（山东专属）二代-升级版
            "P241898002200001170": [
                    "productCode"      : "PD20221215S00017499",
                    "planCode"         : "P241898002200001170",
                    "planName"         : "畅行保（山东专属）二代-升级版",
                    "planGroupCode"    : "PG20221215S00015620",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 3516000.00,
                    "premium"          : 60.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 0.00
            ],
            //新畅行无忧-非营业货车（A款）
            "P241898002200000243": [
                    "productCode"      : "PD20221019S00015826",
                    "planCode"         : "P241898002200000243",
                    "planName"         : "新畅行无忧-非营业货车（A款）",
                    "planGroupCode"    : "PG20221019S00014044",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 420600.00,
                    "premium"          : 0.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 360.00
            ],
            //新畅行无忧-2吨以下营业货车（A款）
            "P241898002200000264": [
                    "productCode"      : "PD20221109S00016347",
                    "planCode"         : "P241898002200000264",
                    "planName"         : "新畅行无忧-2吨以下营业货车（A款）",
                    "planGroupCode"    : "PG20221109S00014497",
                    "classesCode"      : "2418980000000001",
                    "classesName"      : "个人人身意外伤害保险(13版单证通用)",
                    "maxSalesNumber"   : 1,
                    "sumCoverage"      : 420600.00,
                    "premium"          : 0.00,
                    "maxIntervalNumber": 1,
                    groupInsuranceFlag : "G",
                    "basePrice"        : 450.00
            ]
            ,
            "P241898002200000112": [
                    "productCode"       : "PD20221014S00015741",
                    "planCode"          : "P241898002200000112",
                    "planName"          : "新外网-驾意险80元方案-5座",
                    "planGroupCode"     : "PG20221014S00013995",
                    "classesCode"       : "2418980000000001",
                    "classesName"       : null,
                    "maxSalesNumber"    : 1,
                    "sumCoverage"       : 460000.00,
                    "premium"           : 80.00,
                    "maxIntervalNumber" : 1,
                    "groupInsuranceFlag": "G"
            ],
            //新外网-驾意险80元方案-5座
            "P241898002200000127": [
                    "productCode"       : "PD20221014S00015741",
                    "planCode"          : "P241898002200000127",
                    "planName"          : "新外网-驾意险80元方案-7座",
                    "planGroupCode"     : "PG20221014S00013995",
                    "classesCode"       : "2418980000000001",
                    "classesName"       : null,
                    "maxSalesNumber"    : 1,
                    "sumCoverage"       : 644000.00,
                    "premium"           : 80.00,
                    "maxIntervalNumber" : 1,
                    "groupInsuranceFlag": "G"
            ]
    ]
}


static String getQueryProductDetailStr(autoTask, String quotationNo) {
    def queryMsgAgent = autoTask?.tempValues?.queryMsgAgent as Map
    def detail4 = autoTask.configs?.detail4 ?: queryMsgAgent?.detail4
    def noCarAgencyCode = autoTask.configs?.agentCode ?: queryMsgAgent?.noCarAgencyCode
    def agentCode = autoTask.configs?.agent_code ?: queryMsgAgent?.msbAgentVoList?.get(0)?.agentCode
    def requestBody = [
            "meta"  : [:],
            "redata": [
                    "agencyyxydm"     : "",
                    "lifeInsSalerCode": "",
                    "quotationNo"     : quotationNo,
                    "userIp"          : "issue.cpic.com.cn",
                    "detail4"         : detail4,
                    "noCarAgencyCode" : noCarAgencyCode,
                    "agentCode"       : agentCode
            ]
    ]
    return JSON.toJSONString(requestBody)
}
/**
 * map to url
 * @param map
 * @return
 */
static Map2Url(Map map) {
    return Joiner.on("&")
    // 用指定符号代替空值,key 或者value 为null都会被替换
            .useForNull("")
            .withKeyValueSeparator("=")
            .join(map);
}
//四川
static getChannelCode(String channelCodeStr) {

    if (StringUtils.isNumeric(channelCodeStr)) {
        if (channelCodeMap().values().contains(channelCodeStr)) {
            return channelCodeStr
        }
    } else {
        return channelCodeMap()."$channelCodeStr"
    }

}

static channelCodeMap() {
    return [
            "直拓"                       : "12",
            "普通专业代理（不含车商、网销）": "21",
            "普通兼业代理（不含车商、网销）": "22",
            "个人营销"                   : "23",
            "银团代产"                   : "25",
            "银行代理"                   : "27",
            "车商兼业代理"               : "28",
            "普通经纪（不含车商、网销）"    : "31",
            "个代产"                     : "32",
            "寿险营销便利店"             : "36",
            "车商专业代理"               : "37",
            "产险营销便利店"             : "38",
            "车商经纪"                   : "39",
            "个人营销（网销)"             : "55"
    ]
}


/*
var  t= "MSBTOM2400000000"
        var e = "d7QyxJcNCyC235J2a0LphM81aoOkWXaTid1daneLpWkLoGk073q+vGOxg4dabHpB7SCbVx5C+FH3gLu5JGHiTl0oZ36EyDGa+T+Oe3rhht92nc2LBGYGRFgreaFOKHHhU44ZdaCyMnWixcfULtt9bghSHUxXmt1faewTpjfC5qOAFIyh+t8zBHoPUwgSZrxWjBe1GzDozPVlkpZUCVZCzK80GY0sy6kkXkZqam0neeFh2n5am1MudHDYJrQY9jYF4AabqGNB7sWQVpGXzjIAexdrnuNaVQiODopUoLBX8TEOx6hfGAUtbqWmCB0M5Xu+Y2MQUNjWnJ5moql12XRYOj8z9AWt4FqwQgjoUEPdftzcB0eggqGDHexll0gMfF1Fyz8ufavsVkZNEbPFnl4X6nJStN3yzGfGTEh9k7HgD+gL0eZ7KctkSaHet0S7iHiOEuc1pZ2vDTfZmQOHdzmz85abjNSXbBwnFgzmvvNgmxeWJfic8wUSkx51d4ayuLsaMu4PScVsKQ6Yz2PVIJ9mUhPxt9YlAGdOBP9qXN6LN+ZUO4hWxpIdIUURE551rkKMLGQWCM22UyV/XqS+MYNf+1i9AF7RzGk/TegtjpSvP7fJBGfyrBcZeK31Ci7+7iBRh4h0TboYa3IpfV1rCjQTKm81x1RKN0pyvFkZHdjNqmBTEb5+zJZsUymgnrLqE5bw"
 */
// 加密  AES ecb  128  sKey.length() == 16
public static String Encrypt(String sSrc, String sKey) throws Exception {
    byte[] raw = sKey.getBytes("utf-8");
    SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
    Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");//"算法/模式/补码方式"
    cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
    byte[] encrypted = cipher.doFinal(sSrc.getBytes("utf-8"));
    return new Base64().encodeToString(encrypted);//此处使用BASE64做转码功能，同时能起到2次加密的作用。
}

// 解密 AES ecb  128  sKey.length() == 16
public static String Decrypt(String sSrc, String sKey) throws Exception {
    byte[] raw = sKey.getBytes("utf-8");
    SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
    Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
    cipher.init(Cipher.DECRYPT_MODE, skeySpec);
    byte[] encrypted1 = new Base64().decode(sSrc);//先用base64解密
    byte[] original = cipher.doFinal(encrypted1);
    String originalString = new String(original, "utf-8");
    return originalString;
}

static String getCarColour(val) {
    def map = [
            "1": "CO000"
    ]
    return val ? map.get(val) ?: "CO000" : "CO000"
}

static JSONObject doGetCarTempParam(AutoTask autoTask, JSONObject hashCodeResultJson) {
    def message = hashCodeResultJson.getJSONObject('message')
    if (message?.getString('code') != 'success') {
        throw new InsReturnException("非车险投保失败:" + hashCodeResultJson?.getString("message"))
    }
    JSONObject returnData
    def result = hashCodeResultJson.getJSONObject('result')
    def binary = result.getString('binary')
    def returnUrl = result.getString('returnURL')
    def decodeHashCode = URLDecoder.decode(binary, "UTF-8")
    String url = cn.hutool.core.codec.Base64.decodeStr(returnUrl, "UTF-8")
    if (url.contains('hashCode')) {
        returnData = JSON.parseObject(decodeHashCode)
        return returnData
    }
    def getCarTempParamUrl = 'https://dwx.cpic.com.cn/nonvehicle/napi/otherTip/getCarTempParam'
    def reqHeader = [
            'Content-Type': 'application/json;charset=UTF-8'
    ]
    def requestBody = [
            'busiNo': binary
    ]
    def reqBody = JSON.toJSONString(requestBody)
    reqBody = Robot2011Util.aesEncrypt('TdOvQRBA7Byx5uJE', reqBody)
    def response = HttpSender.doPost(autoTask.httpClient as CloseableHttpClient, true, getCarTempParamUrl, reqBody, null, reqHeader, 'UTF-8', null, '')
    if (!response) {
        throw new InsReturnException("非车险投保失败：网络错误")
    }
    response = Robot2011Util.removeQuotationMarks(response)
    response = Robot2011Util.aesDecrypt("TdOvQRBA7Byx5uJE", response)
    def respBody = JSON.parseObject(response)
    if (respBody.getInteger("code") != 0) {
        throw new InsReturnException(InsReturnException.Others, "太平洋精灵非车险查询错误，无法获取hashCode")
    }
    def data = respBody.getString("data")
    def returnStr = Robot2011Util.aesDecrypt("MSBTOM2400000000", data)
    logger.info("getCarTempParam响应内容:{}", returnStr)
    returnData = JSON.parseObject(returnStr)
    return returnData
}


'5'