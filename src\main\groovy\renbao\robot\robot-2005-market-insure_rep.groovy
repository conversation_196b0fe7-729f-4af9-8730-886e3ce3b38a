package renbao.robot

import com.alibaba.fastjson.JSONObject
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import groovy.transform.Field

import static renbao.common.Constants.SUCCESS_CODE
import static renbao.common.Constants.EFC_PROPOSAL_NO_PREFIX
import static renbao.common.Constants.BIZ_PROPOSAL_NO_PREFIX

@BaseScript BaseScript_Http_Enq _

@Field JSONObject resp = null

check {
    assertTrue("报价转投保异常：" + it?.data?.errorMsg as String, SUCCESS_CODE == it['statusText'] && (it.toJSONString().contains(EFC_PROPOSAL_NO_PREFIX) || it.toJSONString().contains(BIZ_PROPOSAL_NO_PREFIX)))
    resp = it
}

entity.bizProposeNum = resp['data']['proposalNoBI']
entity.efcProposeNum = resp['data']['proposalNoCI']
tempValues.vehicleIdJsy =  resp['data']['vehicleIdJsy']
