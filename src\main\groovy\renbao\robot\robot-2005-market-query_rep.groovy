package renbao.robot

import cn.hutool.core.collection.CollectionUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.model.car.BizSuiteInfo
import com.cheche365.bc.model.car.CarOwnerInfo
import com.cheche365.bc.model.car.EfcSuiteInfo
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.model.car.InsurePerson
import com.cheche365.bc.model.car.SuiteDef
import com.cheche365.bc.model.car.TaxSuiteInfo
import com.cheche365.bc.utils.sender.HttpSender
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import groovy.transform.Field

import static common.common_all.*
import org.apache.http.impl.client.CloseableHttpClient

import static renbao.common.Constants.SUCCESS_CODE
import static renbao.common.renbao_dict.*
import static renbao.robot.common_market.hostPrefix

import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter


@BaseScript BaseScript_Http_Enq _

@Field JSONObject resp = null
check {
    assertTrue("投保单信息获取失败：" + it.toJSONString(), SUCCESS_CODE == it['statusText'])
    resp = it
}

def data = resp['data'] as JSONObject
def dataBi = data['prpCmainVoBi']
def dataCi = data['prpCmainVoCi']
if (autoTask?.taskType?.contains("approvedquery")) {
    def policyNoBi = dataBi?.policyNo
    def policyNoCi = dataCi?.policyNo
    assertFalse('投保单号：' + entity.efcProposeNum + '未获取到对应保单号，未承保', entity.efcProposeNum && !policyNoCi)
    assertFalse('投保单号：' + entity.bizProposeNum + '未获取到对应保单号，未承保', entity.bizProposeNum && !policyNoBi)
    entity.bizPolicyCode = policyNoBi
    entity.efcPolicyCode = policyNoCi
    initData(autoTask, data)
} else {
    def statusBi = dataBi?.underWriteFlag
    def statusCi = dataCi?.underWriteFlag
    if (entity.efcProposeNum && statusCi && !['5', '3', '1', '4'].contains(statusCi)) {
        fail('投保单号：' + entity.efcProposeNum + '，未核保通过')
    }
    if (entity.bizProposeNum && statusBi && !['5', '3', '1', '4'].contains(statusBi)) {
        fail('投保单号：' + entity.bizProposeNum + '未核保通过')
    }
    initData(autoTask, data)
}
//需求13101回写PICCSCORE人保分
def piccScore = data['prpCmainVo']['piccScore']
def applyJson = JSON.parse(autoTask?.applyJson as String)
def definition = [:]
if (entity.bizProposeNum) {
    definition['application.bizScore'] = piccScore
}
if (entity.efcProposeNum) {
    definition['application.trafficScore'] = piccScore
}
applyJson['definition'] = definition
autoTask?.applyJson = JSON.toJSONString(applyJson)
//非车险需要用报价单查询
def nonMortorFlag = false
//非车险需要用报价单查询
def commonMarket = new common_market()
def header = commonMarket.header(autoTask)
def areaAbbreviation = autoTask?.configs['areaAbbreviation']
def startDate = DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss').format(LocalDateTime.now().plusDays(-9))
def endDate =  DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss').format(LocalDateTime.now())
def query = hostPrefix(autoTask) + "/khyx/newFront/qth/quotesearch/query.do?rows=10&page=1&customerCName=&licenseNo=${URLEncoder.encode(entity.order.carInfo.plateNum, 'utf-8')}&qtBeginDate=${URLEncoder.encode(startDate, 'utf-8')}&qtEndDate=${URLEncoder.encode(endDate, 'utf-8')}&sourceFlag=0&quotationState=0&msgSended=0&umTUtiiSales.userName=&umTUtiiSales.groupUserCode=&quoteSearchPageNum=1"
def queryResult = HttpSender.doGet(autoTask.httpClient as CloseableHttpClient, query, header as Map<String, String>,null, "UTF-8",null, false)
if (queryResult.contains('Success')) {
    def queryResultObj = JSONObject.parseObject(queryResult)
    def list = queryResultObj.getJSONObject('data').getJSONArray('list')
    if (list && list.size() > 0) {
        def targetProposal = list.find { it ->
            it['proposalNoBI'] == entity.bizProposeNum
        }
        if (targetProposal && targetProposal['quotationNoEBS']) {
            def nonMotorProposalNo = targetProposal['quotationNoEBS']
            def sumPremium = targetProposal['jointSalesPremium']
            def totalCharge = entity.totalCharge.add(sumPremium.toString().toBigDecimal()).setScale(2, BigDecimal.ROUND_HALF_UP)
            entity.totalCharge = totalCharge
            entity.misc = entity.misc ?: [:]
            entity.misc.nonMotor = entity.misc.nonMotor ?: [:]
            entity.misc.nonMotor.discountCharge = sumPremium
            nonMortorFlag = true
        }
    }
}
if (entity?.misc?.nonMotor?.discountCharge && !nonMortorFlag) {
    //需求13101,有非车，但是非车信息未查到
    entity.totalCharge = entity.totalCharge.add(entity?.misc?.nonMotor?.discountCharge?.toString()?.toBigDecimal()).setScale(2, BigDecimal.ROUND_HALF_UP)
}

def initData(autoTask, JSONObject data) {
    def totalCharge = 0.0
    def enquiry = autoTask.taskEntity as Enquiry
    initCar(autoTask, data)
    initPerson(autoTask, data)
    def biData = data['prpCItemKindVosBiExceptSpecial']
    if (biData && (biData as JSONArray).size()) {
        enquiry?.order?.suiteInfo?.bizSuiteInfo?.suites?.clear()
        enquiry?.order?.suiteInfo?.bizSuiteInfo = new BizSuiteInfo()
        enquiry?.order?.suiteInfo?.bizSuiteInfo?.suites = [:]
        biData.each { it ->
            initBiz(autoTask, it as JSONObject)
        }
        def iStartHour = data.getJSONObject("prpCmainVoBi").getIntValue("startHour")
        def startHour = ''
        if (iStartHour < 10) {
            startHour = '0' + iStartHour
        } else {
            startHour = iStartHour.toString()
        }
        def startMinute = ''
        def iStartMinute = data.getJSONObject("prpCmainVoBi").getIntValue("startMinute")
        if (iStartMinute < 10) {
            startMinute = '0' + iStartMinute
        } else {
            startMinute = iStartMinute.toString()
        }
        def iEndHour = data.getJSONObject("prpCmainVoBi").getIntValue("endHour")
        def endHour = ''
        if (iEndHour < 10) {
            endHour = '0' + iEndHour
        } else {
            endHour = iEndHour.toString()
        }
        def endMinute = ''
        def iEndMinute = data.getJSONObject("prpCmainVoBi").getIntValue("endMinute")
        if (iEndMinute < 10) {
            endMinute = '0' + iEndMinute
        } else {
            endMinute = iEndMinute.toString()
        }
        if (iEndHour == 24) {
            endHour = '23'
            endMinute = '59'
        }
        if (Objects.isNull(enquiry.order.suiteInfo.bizSuiteInfo)) {
            enquiry.order.suiteInfo.bizSuiteInfo = new BizSuiteInfo()
        }
        enquiry.order.suiteInfo.bizSuiteInfo.start = data.getJSONObject("prpCmainVoBi").getString("startDate").substring(0, 10) + ' ' + startHour + ':' + startMinute + ':00'
        enquiry.order.suiteInfo.bizSuiteInfo.end = data.getJSONObject("prpCmainVoBi").getString("endDate").substring(0, 10) + ' ' + endHour + ':' + endMinute + ((endHour == '23' && endMinute == '59') ? ':59' : ':00')
        enquiry.order.suiteInfo?.bizSuiteInfo?.discountRate = new BigDecimal(data.getJSONObject("prpCmainVoBi").get("discount").toString())
        enquiry.order.suiteInfo?.bizSuiteInfo?.orgCharge = new BigDecimal(data.getJSONObject("prpCmainVoBi").get("sumBenchPremium").toString())
        enquiry.order.suiteInfo?.bizSuiteInfo?.discountCharge = new BigDecimal(data.getJSONObject("prpCmainVoBi").get("sumPremium").toString())
        totalCharge += enquiry.order.suiteInfo.bizSuiteInfo.discountCharge
    }
    if (data['prpCmainVoCi']) {
        def iStartHour = data.getJSONObject("prpCmainVoCi").getIntValue("startHour")
        def startHour = ''
        if (iStartHour < 10) {
            startHour = '0' + iStartHour
        } else {
            startHour = iStartHour.toString()
        }
        def startMinute = ''
        def iStartMinute = data.getJSONObject("prpCmainVoCi").getIntValue("startMinute")
        if (iStartMinute < 10) {
            startMinute = '0' + iStartMinute
        } else {
            startMinute = iStartMinute.toString()
        }
        def iEndHour = data.getJSONObject("prpCmainVoCi").getIntValue("endHour")
        def endHour = ''
        if (iEndHour < 10) {
            endHour = '0' + iEndHour
        } else {
            endHour = iEndHour.toString()
        }
        def endMinute = ''
        def iEndMinute = data.getJSONObject("prpCmainVoCi").getIntValue("endMinute")
        if (iEndMinute < 10) {
            endMinute = '0' + iEndMinute
        } else {
            endMinute = iEndMinute.toString()
        }
        if (iEndHour == 24) {
            endHour = '23'
            endMinute = '59'
        }
        if (Objects.isNull(enquiry.order.suiteInfo.efcSuiteInfo)) {
            enquiry.order.suiteInfo.efcSuiteInfo = new EfcSuiteInfo()
        }
        enquiry.order.suiteInfo.efcSuiteInfo.start = data.getJSONObject("prpCmainVoCi").getString("startDate").substring(0, 10) + ' ' + startHour + ':' + startMinute + ':00'
        enquiry.order.suiteInfo.efcSuiteInfo.end = data.getJSONObject("prpCmainVoCi").getString("endDate").substring(0, 10) + ' ' + endHour + ':' + endMinute + ((endHour == '23' && endMinute == '59') ? ':59' : ':00')
        enquiry.order.suiteInfo?.efcSuiteInfo?.discountRate = new BigDecimal(data.getJSONObject("prpCmainVoCi").get("discount").toString())
        enquiry.order.suiteInfo?.efcSuiteInfo?.orgCharge = new BigDecimal(data.getJSONObject("prpCmainVoCi").get("sumBenchPremium").toString())
        enquiry.order.suiteInfo?.efcSuiteInfo?.discountCharge = new BigDecimal(data.getJSONObject("prpCmainVoCi").get("sumPremium").toString())
        totalCharge += enquiry.order.suiteInfo.efcSuiteInfo.discountCharge
        if (!enquiry?.order?.suiteInfo?.taxSuiteInfo) {
            enquiry?.order?.suiteInfo?.taxSuiteInfo = new TaxSuiteInfo()
        }
        enquiry.order.suiteInfo.taxSuiteInfo.discountCharge = new BigDecimal(data.getJSONObject("prpCmainVoCi").get("sumPayTax").toString())
        totalCharge += enquiry.order.suiteInfo.taxSuiteInfo.discountCharge
    }

    if (CollectionUtil.isNotEmpty(data?.prpCmainVo?.relationUPolicyInfolist as JSONArray)) {
        entity.misc = entity.misc ?: [:]
        entity.misc.nonMotor = entity.misc.nonMotor ?: [:]
        entity.misc.nonMotor?.accidentProposeCode = data?.prpCmainVo?.relationUPolicyInfolist[0]?.connectProposalNo
    }

    enquiry?.totalCharge = totalCharge
}
def initBiz(autoTask, JSONObject data) {
    def enquiry = autoTask.taskEntity as Enquiry
    def shareMap = enquiry?.order?.suiteInfo?.bizSuiteInfo?.suites ? shareAmount(enquiry) : null
    def code = data.get("kindCode")
    def checheCode = kindENNew(code)
    def suite = new SuiteDef()
    suite.setCode(checheCode)
    def amount = data.get("amount") ? new BigDecimal(data.get("amount").toString()) : 0
    def netPremium = data.get("netPremium") ? new BigDecimal(data.get("netPremium").toString()) : 0
    if (["ANCVehicleDamage", "ANCThirdParty", "ANCPassenger", "ANCDriver"].contains(checheCode))
        amount = data.get("deductibleRate") ? new BigDecimal(data.get("deductibleRate").toString()) : 0
    if (["Passenger"].contains(checheCode))
        amount = amount.div((enquiry.order.carInfo.seatCnt - 1))
    if (["RoadsideService", "VehicleInspection", "DesignatedDriving", "SendForInspection"].contains(checheCode)) {
        if (autoTask.taskType.contains("query")) {
            if ("VehicleInspection" == checheCode)
                amount =  new BigDecimal("1")
            else
                amount =  new BigDecimal(data.get("quantity").toString())
        } else {
            amount = enquiry.order.suiteInfo.bizSuiteInfo.suites[checheCode].amount
        }
    }
    if (['NIHCThirdParty', 'NIHCDriver', 'NIHCPassenger'].contains(checheCode)) {
        if (shareMap && shareMap.containsKey(checheCode - 'NIHC')) {
            suite.setShare(true)
        }
        if ('NIHCPassenger' == checheCode)
            amount = amount.div((enquiry.order.carInfo.seatCnt - 1))
    }
    suite.setAmount(amount as BigDecimal)
    def discount = new BigDecimal(data.get("discount").toString())
    suite.setDiscountRate(discount)
    suite.setDiscountNoTaxCharge(netPremium as BigDecimal)
    def orgCharge = new BigDecimal(data.get("benchMarkPremium").toString())
    suite.setOrgCharge(orgCharge)
    def discountCharge = new BigDecimal(data.get("premium").toString())
    suite.setDiscountCharge(discountCharge)
    enquiry.order.suiteInfo.bizSuiteInfo.suites.put(checheCode, suite)
}
def initCar(autoTask, JSONObject data) {
    def prpCitemCarVos = data.getJSONObject('prpCmainVoCi')?.getJSONArray('prpCitemCarVos')
    if (!prpCitemCarVos) {
        prpCitemCarVos = data.getJSONObject('prpCmainVo')?.getJSONArray('prpCitemCarVos')
    }
    def carInfo = prpCitemCarVos[0] as JSONObject
    def enquiry = autoTask.taskEntity as Enquiry
    if (carInfo.get("displacement")) {
        enquiry.order.carInfo.displacement = new BigDecimal(carInfo.get("displacement").toString())
    }
    enquiry.order.carInfo.carModelName = carInfo.getString("brandName")
    enquiry.order.carInfo.seatCnt = carInfo.getIntValue("seatCount")
    if (carInfo.get("purchasePrice")) {
        enquiry.order.carInfo.price = new BigDecimal(carInfo.get("purchasePrice").toString())
    }
    enquiry.order.carInfo.jyCode = carInfo.getString("modelCode").trim()
    if (carInfo['enrollDate']) {
        enquiry.order.carInfo.firstRegDate = new SimpleDateFormat('yyyy-MM-dd HH:mm:ss').parse(carInfo['enrollDate'] as String)
    }
    enquiry.order.carInfo.useProps = natureTocheche(carInfo['useNatureCode'])
}
def initPerson(autoTask, JSONObject data) {
    def prpCmainVo = data.getJSONObject('prpCmainVo')
    def renewInsuredVos = prpCmainVo.getJSONArray('renewInsuredVos')
    def enquiry = autoTask.taskEntity as Enquiry
    if (renewInsuredVos.size() == 1) {
        def name = renewInsuredVos[0]['insuredName']
        def idCard = renewInsuredVos[0]['identifyNumber']
        def idCardType = getCheCheCardType(renewInsuredVos[0]['identifyType']) ?: 0
        if ([6, 8, 9, 10].contains(idCardType)) {
            idCard = renewInsuredVos[0]['unifiedSocialCreditCode']
        }
        enquiry.order.carOwnerInfo = new CarOwnerInfo()
        enquiry.order.carOwnerInfo.name = name
        enquiry.order.carOwnerInfo.idCard = idCard
        enquiry.order.carOwnerInfo.idCardType = idCardType
        def insurePerson = new InsurePerson()
        insurePerson.name = name
        insurePerson.idCard = idCard
        insurePerson.idCardType = idCardType
        enquiry.order.insurePerson = insurePerson
        enquiry.order.insuredPersons = []
        enquiry.order.insuredPersons[0] = insurePerson
    } else {
        renewInsuredVos.each { info ->
            def insuredFlag = info['insuredFlag'] as String
            def name = info['insuredName']
            def idCard = info['identifyNumber']
            def idCardType = getCheCheCardType(info['identifyType']) ?: 0
            if ([6, 8, 9, 10].contains(idCardType)) {
                idCard = renewInsuredVos[0]['unifiedSocialCreditCode']
            }
            if (insuredFlag.startsWith('110')) {
                //投被保人一致
                def insurePerson = new InsurePerson()
                insurePerson.name = name
                insurePerson.idCard = idCard
                insurePerson.idCardType = idCardType
                enquiry.order.insurePerson = insurePerson
                enquiry.order.insuredPersons = []
                enquiry.order.insuredPersons[0] = insurePerson
            } else if (insuredFlag.startsWith('101')) {
                //投保人车主一致
                enquiry.order.carOwnerInfo = new CarOwnerInfo()
                enquiry.order.carOwnerInfo.name = name
                enquiry.order.carOwnerInfo.idCard = idCard
                enquiry.order.carOwnerInfo.idCardType = idCardType
                enquiry.order.insurePerson = new InsurePerson()
                enquiry.order.insurePerson.name = name
                enquiry.order.insurePerson.idCard = idCard
                enquiry.order.insurePerson.idCardType = idCardType
            } else if (insuredFlag.startsWith('011')) {
                //被保人车主一致
                enquiry.order.carOwnerInfo = new CarOwnerInfo()
                enquiry.order.carOwnerInfo.name = name
                enquiry.order.carOwnerInfo.idCard = idCard
                enquiry.order.carOwnerInfo.idCardType = idCardType
                def insurePerson = new InsurePerson()
                insurePerson.name = name
                insurePerson.idCard = idCard
                insurePerson.idCardType = idCardType
                enquiry.order.insuredPersons = []
                enquiry.order.insuredPersons[0] = insurePerson
            } else {
                //都不一样
                if (insuredFlag.startsWith('100')) {
                    def insurePerson = new InsurePerson()
                    insurePerson.name = name
                    insurePerson.idCard = idCard
                    insurePerson.idCardType = idCardType
                    enquiry.order.insurePerson = insurePerson
                } else if (insuredFlag.startsWith('010')) {
                    enquiry.order.insuredPersons = []
                    def insurePerson = new InsurePerson()
                    insurePerson.name = name
                    insurePerson.idCard = idCard
                    insurePerson.idCardType = idCardType
                    enquiry.order.insuredPersons = []
                    enquiry.order.insuredPersons[0] = insurePerson
                } else if (insuredFlag.startsWith('001')) {
                    enquiry.order.carOwnerInfo = new CarOwnerInfo()
                    enquiry.order.carOwnerInfo.name = name
                    enquiry.order.carOwnerInfo.idCard = idCard
                    enquiry.order.carOwnerInfo.idCardType = idCardType
                }
            }

        }
    }
}
