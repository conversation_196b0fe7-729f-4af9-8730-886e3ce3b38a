package renbao.edi

import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.model.RuleInfoKey
import com.cheche365.bc.utils.PlatformUtil
import com.cheche365.bc.utils.RuleUtil
import com.cheche365.bc.utils.dama.Dama2Web
import com.cheche365.bc.utils.sender.HttpSender
import org.apache.commons.collections4.MapUtils
import org.apache.commons.lang3.StringUtils
import static renbao.common.renbao_dict.*
import static common.common_all.*
import static renbao.common.Constants.JSON_CONTENT_TYPE
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.time.LocalDate
import java.util.regex.Pattern



def script  = new edi_common_2005()
    def response = root
    script.commonDeal(response)
    def _INSURE_DURATION_DAY_MAPPING = [
        '110000' : 60,//北京
        '130000' : 60,//河北
        '330000' : 60,//浙江
        '650000' : 60,//新疆
        '360000' : 60,//江西
        '120000' : 90,//天津
        '540000' : 90,//西藏
        '310000' : 55,//上海
        '460000' : 45,//海南
        '140000' : 45,//山西
        '320000' : 40//江苏
    ]
    def insureDurationDay = config?.insureDurationDays ? Integer.valueOf(config?.insureDurationDays as String)  : (_INSURE_DURATION_DAY_MAPPING[(enquiry.insArea['province']) as String] ?: 30)
    def msg = response?.Header?.responsehead?.error_message?.toString()
    // 目前发现保司返回状态码0，错误（重复投保，车型错误，报文解析异常，功能异常等）
    // 状态 2 重复投保，转保验证码
    //1 正常算费成功，但是也会有重复投保，如果重复投保，ReInsureItemList节点下不为空
    if (response?.Header?.responsehead?.response_code != 0) {
         //非文字提示重复投保
        def reInsureItemList = response?.Body?.TEMPSTORAGERTN?.BIZ_ENTITY?.ReInsureItemList
        if (reInsureItemList && reInsureItemList.ReInsureItem && !msg.contains("重复投保")) {
            def reInsureItem = reInsureItemList.ReInsureItem
            reInsure(enquiry, reInsureItem, insureDurationDay)
        }
        //软文提示重复投保
        if (msg.indexOf("重复投保") > -1 && msg.indexOf("起保日期") > -1) {
            promptRepeatMsg(enquiry, msg, insureDurationDay)
        }
        //转保验证码
        transferCode(enquiry, response)
        //险种初始化
        initKind(enquiry, response, tempValues)

    } else {

        if (msg.indexOf("重复投保") > -1 && msg.indexOf("起保日期") > -1) {
            //直接提示重复投保终保日期，不返回算费结果
            directRepeat(enquiry, msg, insureDurationDay)
        } else if (msg.indexOf("保险期限") > -1) {
            //直接返回上年保单保险期限，不返回算费结果
            repeatTimeLimit(enquiry, msg, insureDurationDay)
        } else if (msg.indexOf("车型不一致") > -1) {
            // 需求14494
            boolean requiresAutoCorrectCarModelCC = MapUtils.getBoolean(config, "requiresAutoCorrectCarModelCC", true)
            if (!requiresAutoCorrectCarModelCC) {
                throw new InsReturnException(msg)
            }

            def carModelName
            def reg = /车款名称：(.*?)是否使用/
            def matcher = msg =~ reg
            if (matcher.find()) {
                carModelName = matcher.group(1)
            }
            if (carModelName && carModelName.contains(" ")) {
                carModelName = carModelName.substring(0, carModelName.lastIndexOf(" "))
            }
            carModelName = carModelName ?: enquiry.carInfo.carModelName
            def modelCode = msg.substring(msg.lastIndexOf(",") + 1, msg.length())
            if (modelCode.contains("|")) {
                def modelCodeArr = modelCode.split("\\|")
                modelCode = modelCodeArr[1]
            }
            carModelName = URLEncoder.encode(carModelName as String, "utf-8")
            adjustVehicleCode(enquiry, msg, tempValues, carModelName, modelCode, config)
        } else if (msg.contains('关系人证件已超过有效期')) {
            def updatePerson = tempValues['updatePerson']
            if (updatePerson) {
                throw new InsReturnException(msg)
            }
            tempValues['updatePerson'] = true
            def piccAreaCode = script.getPiccAreaCode(enquiry, config)
            config['comId'] = piccAreaCode
            enquiry?.configInfo?.configMap?.comId = piccAreaCode
            def personInfo = enquiry?.carOwnerInfo
            checkPersonInfo(config, personInfo)
            updatePersonInfo(enquiry, personInfo, 'owner', config)
            if (enquiry?.carOwnerInfo?.idCard != enquiry?.insurePerson?.idCard) {
                personInfo = enquiry?.insurePerson
                checkPersonInfo(config, personInfo)
                updatePersonInfo(enquiry, personInfo, 'applicant', config)
            }
            if (enquiry?.carOwnerInfo?.idCard != enquiry?.insuredPersonList?.get(0)?.idCard && enquiry?.insuredPersonList?.get(0)?.idCard != enquiry?.insurePerson?.idCard) {
                personInfo = enquiry?.insuredPersonList?.get(0)
                checkPersonInfo(config, personInfo)
                updatePersonInfo(enquiry, personInfo, 'insured', config)
            }
            throw new InsReturnException(InsReturnException.AllowRepeat, '关系人证件有效期修改完成再次报价')
        } else if (msg.contains('团体客户信息未在客户管理系统维')) {
            def addPerson = tempValues['addPerson']
            if (addPerson) {
                throw new InsReturnException(msg)
            }
            tempValues['addPerson'] = true
            def personInfo = enquiry?.carOwnerInfo
            if ([6, 8, 9, 10].contains(enquiry?.carOwnerInfo?.idCardType)) {
                addPersonInfo(enquiry, personInfo, 'owner', config)
            }
            if ([6, 8, 9, 10].contains(enquiry?.insurePerson?.idCardType) && enquiry?.carOwnerInfo?.idCard != enquiry?.insurePerson?.idCard) {
                personInfo = enquiry?.insurePerson
                addPersonInfo(enquiry, personInfo, 'applicant', config)
            }
            if ([6, 8, 9, 10].contains(enquiry?.insuredPersonList?.get(0)?.idCardType) && enquiry?.carOwnerInfo?.idCard != enquiry?.insuredPersonList?.get(0)?.idCard && enquiry?.insuredPersonList?.get(0)?.idCard != enquiry?.insurePerson?.idCard) {
                personInfo = enquiry?.insuredPersonList?.get(0)
                addPersonInfo(enquiry, personInfo, 'insured', config)
            }
            throw new InsReturnException(InsReturnException.AllowRepeat, '新增团体信息完成再次报价')
        } else if (msg.contains('车型对应车型唯一ID与平台返回的车型唯一ID不一致')) {
            if (enquiry['carInfo']['isNew']) {
                def vehicleStyleUniqueIdList = tempValues.vehicleStyleUniqueIdList
                if (vehicleStyleUniqueIdList && vehicleStyleUniqueIdList?.size() > 0) {
                    def uniqueIdIndex = tempValues?.uniqueIdIndex
                    if (uniqueIdIndex < vehicleStyleUniqueIdList?.size() - 1) {
                        tempValues?.uniqueIdIndex = uniqueIdIndex + 1
                        throw new InsReturnException(InsReturnException.AllowRepeat, '校正车型唯一id，再次报价')
                    } else {
                        throw new InsReturnException(msg)
                    }
                } else {
                    throw new InsReturnException(msg)
                }
            } else {
                adjustVehicleCode(enquiry, msg, tempValues, enquiry['carInfo']['carModelName'], tempValues['platModelCode'], config)
            }
        }
        else {
            throw new InsReturnException(msg);
        }

    }

def checkPersonInfo(config, personInfo) {
    def name = personInfo?.name
    def idCard = personInfo?.idCard
    def identifyType = identifyType(personInfo?.idCardType)
    def checkUrl = config?.customCheckUrl ?: 'https://esb.epicc.com.cn/cmpService/ProxyServices/isExistByThreeElements'
    def header = [
            "Content-Type": JSON_CONTENT_TYPE,
            "Authcode"     : config?.customCheckAuthCode ?: "CF9175708E3DB93ECEFB28F89B807BAB"
    ]
    def param = [
            "customerCName": name,
            "identifyNumber": idCard,
            "identifyType": identifyType,
            "customerType": 'P'
    ] as JSONObject
    def result = HttpSender.doPost(null, checkUrl, param.toJSONString(), null, header, 'utf-8')
    def customerCode
    if (result?.contains('客户编码为')) {
        def resultJson = JSONObject.parse(result)
        def statusText = resultJson['statusText'] as String
        def pattern = Pattern.compile("\\d+")
        def matcher = pattern.matcher(statusText)
        while (matcher.find()) {
            customerCode = matcher.group()
        }
    }
    if (customerCode) {
        personInfo << ['unifyCustCode' : customerCode]
    } else {
        throw new InsReturnException("关系人证件已超过有效期！修改有效期失败")
    }
}

def updatePersonInfo(enquiry, personInfo, role, config) {
    personInfoDateValid(enquiry, personInfo, role)
    def unifyCustCode = personInfo?.unifyCustCode
    def name = personInfo?.name
    def idCard = personInfo?.idCard
    def identifyType = identifyType(personInfo?.idCardType)
    def startDateValid = personInfo?.startDateValid
    def dateValid = personInfo?.dateValid
    def updateUrl = config?.customUpdateUrl ?: 'https://esb.epicc.com.cn/cmpService/ProxyServices/identifyDateUpdate'
    def header = [
            "Content-Type": JSON_CONTENT_TYPE,
            "Authcode": config?.customUpdateAuthCode ?: "1DF49C26B1274F27EAE9EE20E961355D",
            "comId": config?.comId,
            "comCode": config?.ComCode,
            "userCode": config?.UserCode,
    ]
    def param = [
            "sysCode": "05210164",
            "identifyDateInfo": [
                "unifyCustCode": unifyCustCode,
                "customerName": name,
                "identifyNumber": idCard,
                "identifyType": identifyType,
                "startDateValid": startDateValid,
                "dateValid": dateValid
            ],
    ] as JSONObject
    def result = HttpSender.doPost(null, updateUrl, param.toJSONString(), null, header, 'utf-8')
    if (!result.contains('修改成功')) {
        throw new InsReturnException("关系人证件已超过有效期！修改有效期失败")
    }
    //经验证，保司修改成功后如果马上发起报价，保司会返回报价失败，所以这里延迟3秒
    Thread.sleep(5000)
}

def personInfoDateValid(enquiry, personInfo, role) {
    def dateValid = getSupplyParam(enquiry, role + 'IDCardValidDate') ?: arithmeticDay(LocalDate.now().toString(), -1, 0, 10)
    def startDateValid = getSupplyParam(enquiry, role + 'IDCardRegistrationDate') ?: arithmeticDay(LocalDate.now().toString(), -1, 0, 0)
    personInfo << ['startDateValid' : startDateValid]
    personInfo << ['dateValid' : dateValid]
}

def addPersonInfo(enquiry, personInfo, role, config) {

    def liaisonName = getSupplyParam(enquiry, 'liaisonName') as String
    if (StringUtils.isBlank(liaisonName)) {
        throw new InsReturnException('团体客户信息未在客户管理系统维护，无联系人无法维护')
    }
    def liaisonMobile = getSupplyParam(enquiry, 'liaisonMobile')
    def liaisonIDCardNo = getSupplyParam(enquiry, 'liaisonIDCardNo')
    personInfo['liaisonName'] = liaisonName
    personInfo['liaisonMobile'] = liaisonMobile
    personInfo['liaisonIDCardNo'] = liaisonIDCardNo
    personInfoDateValid(enquiry, personInfo, role)
    def script = new edi_common_2005()
    def piccAreaCode = script.getPiccAreaCode(enquiry, config)
    def addUrl = config?.customAddUrl ?: 'https://esb.epicc.com.cn/cmpService/ProxyServices/addCustomerBeforeInterface'
    def header = [
            "Content-Type": JSON_CONTENT_TYPE,
            "Authcode": config?.customAddAuthCode ?: "9DE82F21BD1329F379FACF751C8941F2",
            "comId": piccAreaCode,
            "comCode": config?.ComCode,
            "userCode": config?.UserCode,
    ]
    def identifyType = identifyType(personInfo?.idCardType)
    def param = """
        {
              "userCode": "${config?.UserCode}",
              "comId": "${piccAreaCode}",
              "comCode": "${config?.ComCode}",
              "headVersion": "V2.0.12.0",
              "sysCode": "05210164",
              "groupCustFlag": "1",
              "isSeparate": "0",
              "searchTy": "1",
              "scene": "新增",
              "requestData": {
                "orgCustomer": {
                  "orgMain": {
                    "unifyCustCode": "",
                    "customerCName": "${personInfo?.name}",
                    "customerEName": "",
                    "businessSource": null,
                    "countriesRegions": "CHN",
                    "resident": "A",
                    "businessSort": "300",
                    "workerQuantity": 100,
                    "registFund": 0.00,
                    "possessNature": "",
                    "fatherCode": "",
                    "regionCode": "",
                    "operatingStatus": "",
                    "certificateDate": "",
                    "businessType": "",
                    "isradardata": "1",
                    "registFundCurrency": "人民币",
                    "registerType": "小型企业",
                    "income": 0,
                    "assets": 0,
                    "registerManagement": "",
                    "organizationSort": "",
                    "jionSocialFlag": "1",
                    "groupScope": "",
                    "contactPersonName": "${personInfo?.liaisonName}",
                    "topLevelFlag": "",
                    "leaderFlagCodeOfWorld": "",
                    "fatherOrganization": "",
                    "validStatus": "1"
                  },
                  "orgIdentifyList": [
                    {
                      "unifyCustCode": "",
                      "seqNo": 0,
                      "identifyType": "${identifyType}",
                      "identifyNumber": "${personInfo?.idCard}",
                      "startDateValid": "${personInfo?.startDateValid}",
                      "dateValid": "${personInfo?.dateValid}",
                      "validStatus": "1"
                    }
                  ],
                  "orgPhoneList": [
                    {
                      "unifyCustCode": "",
                      "seqNo": 0,
                      "comId": "${piccAreaCode}",
                      "phoneNumber": "${personInfo?.liaisonMobile ?: ''}",
                      "phoneProperties": "01",
                      "customerRelations": "",
                      "isDefault": "1",
                      "phoneType": "1",
                      "messageValidate": "",
                      "validStatus": "1"
                    }
                  ],
                  "orgAddressList": [
                    {
                      "unifyCustCode": "",
                      "seqNo": 0,
                      "addressCName": "",
                      "addressEName": "",
                      "addressType": "1",
                      "postcode": "",
                      "isDefault": "1",
                      "province": "",
                      "city": "",
                      "area": "",
                      "validStatus": "1"
                    }
                  ],
                  "orgOthercontact": {
                    "unifyCustCode": "",
                    "weChat": "",
                    "email": "",
                    "isRadarData": "",
                    "validStatus": "1"
                  },
                  "orgTaxpayer": {
                    "unifyCustCode": "",
                    "taxpayerType": "1",
                    "econKind": "1",
                    "revenueCode": "",
                    "phoneNumber": "",
                    "address": "",
                    "bank": "",
                    "bankView": "",
                    "account": "",
                    "taxpayerAuditStatus": "",
                    "validStatus": "1"
                  },
                  "orgAccountList": [
                    {
                      "unifyCustCode": "",
                      "seqNo": 0,
                      "bank": "",
                      "bankView": "",
                      "branchBank": "",
                      "account": "",
                      "accountType": "",
                      "isDefault": "",
                      "validStatus": "1"
                    }
                  ],
                  "orgBenefitList": [
                    {
                      "unifyCustCode": "",
                      "seqNo": 0,
                      "benefitCName": "",
                      "benefitAddress": "",
                      "benefitIdentType": "",
                      "benefitIdentNumber": "",
                      "startDateValid": "",
                      "dateValid": "",
                      "benefitJudgeType": "",
                      "validStatus": "1"
                    }
                  ],
                  "uniRisklevel": {
                    "unifyCustCode": "",
                    "serialNo": 0,
                    "customerType": "2",
                    "organizeCode": "${personInfo?.idCard}",
                    "identifyStartDate": "${personInfo?.startDateValid}",
                    "identifyEndDate": "${personInfo?.dateValid}",
                    "nationality": "",
                    "relateCountry": "",
                    "fatfFlag": "",
                    "blackListFlag": "",
                    "vipFlag": "",
                    "industryType": "",
                    "industryDuty": "",
                    "companyNature": "",
                    "businessRange": "",
                    "shareholder": "",
                    "largeCash": "",
                    "sumPayFee": 0.00,
                    "sumPayInterVal": "",
                    "sysAutoCreditLevel": "",
                    "creditLevel": "",
                    "creatorCode": "",
                    "createDate": "${script.getTimePattern('yyyy-MM-dd HH:mm:ss')}",
                    "updaterCode": "",
                    "updateDate": "${script.getTimePattern('yyyy-MM-dd HH:mm:ss')}",
                    "remark": "",
                    "validStatus": "1",
                    "flag": "",
                    "residence": "",
                    "leaderName": "",
                    "leaderIdType": "",
                    "leaderId": "",
                    "leaderIdInvalidDate": "",
                    "holdingPersonName": "",
                    "holdingPersonIdType": "",
                    "holdingPersonId": "",
                    "holdingPersonInvalidDate": "",
                    "agentName": "",
                    "agentIdType": "",
                    "agentIdentifyNumber": "",
                    "agentInvalidDate": ""
                  }
                }
              }
            }
    """
    def result = HttpSender.doPost(null, addUrl as String, param.toString(), null, header as Map<String,String>, 'utf-8')
    Logger log = LoggerFactory.getLogger("人保新增关系人")
    log.info("任务： {} 新增关系人: {},保司返回结果：{}", enquiry['businessId'], personInfo?.name, result)
   if (result.contains('Success')) {
       def jsonResult = JSONObject.parse(result) as JSONObject
       def auditStatus = jsonResult.getJSONObject('data').getString('auditStatus')
        if ('0' == auditStatus || '3' == auditStatus) {
            throw new InsReturnException('团体客户信息未在客户管理系统维护，新增失败')
        } else if ('1' == auditStatus) {
            throw new InsReturnException('团体客户信息未在客户管理系统维护，新增待审批，稍后再报价')
        }
       //经验证，保司修改成功后如果马上发起报价，保司会返回报价失败，所以这里延迟3秒
       Thread.sleep(5000)
   } else {
       throw new InsReturnException('团体客户信息未在客户管理系统维护，新增失败')
   }
}

//非文字提示重复投保，会返回算费数据
def reInsure(enquiry, reInsureItem, insureDurationDay) {
    def script  = new edi_common_2005()
    reInsureItem.each {
        def expireDate = it.ExpireDate
        if (expireDate && expireDate.toString()) {
            def lastEnd = expireDate.toString()
            enquiry.baseSuiteInfo.bizSuiteInfo.start = lastEnd
            //需求9349要求取消校正交强
            if (lastEnd.contains("00:00")) {
                enquiry.baseSuiteInfo.bizSuiteInfo.end = script.caculateDay(lastEnd.substring(0, 10).toString(), -1, 1) + " 23:59:59"
            } else {
                enquiry.baseSuiteInfo.bizSuiteInfo.end = script.caculateDay(lastEnd.substring(0, 10).toString(), 0, 1) + " 23:59:59"
            }
            def dayDiff = script.intervalNow(lastEnd.substring(0, 10))
            if (dayDiff > insureDurationDay) {
                def repeatMsg = "(商) ${enquiry.baseSuiteInfo.bizSuiteInfo.start.substring(0, 10)}，起保日期必须在当前日期的规定时间范围内"
                throw new InsReturnException(repeatMsg)
            } else {
                def ex = new InsReturnException(InsReturnException.AllowRepeat, "商业险重复投保，再次报价");
                ex.setStep(1)
                throw ex
            }
        }
    }
}

//软文提示重复投保，返回险种算费结果
def promptRepeatMsg(enquiry, msg, insureDurationDay) {
    //重复投保提示：与其他公司重复投保！警告：车牌号“桂AS330N”的保单发生重复投保，与其重复投保的其它公司的保单信息如下：投保确认码 02HAIC450021081089948596169519;保单号 6051106030120210009480;起保日期 2021-08-27 00:00;终保日期 2022-08-27 00:00;车牌号 桂A528NQ;号牌种类 02;车架号 LZWACAGA3G7064595;发动机号 UG41421288;地区 450000。
    //重复投保提示：与其他公司重复投保！警告：车牌号“桂LNG565”的保单发生重复投保，与其重复投保的其它公司的保单信息如下：投保确认码 02TPBX450021091550656974714628;保单号 21190800105210029535;起保日期 2021-09-04 00:00;终保日期 2022-09-03 23:59;车牌号 桂L12796;号牌种类 02;车架号 LSVDP49F362235224;发动机号 205239;地区 450000。
    def timeReg = '.*?终保日期.*?(\\d{4}-\\d{2}-\\d{2}( \\d{2}:\\d{2}(:\\d{2})?)?)'
    def matcher = Pattern.compile(timeReg).matcher(msg)
    def endDate
    if (matcher.find()) {
        endDate = matcher.group(1)
    }
    if (endDate) {
        def script  = new edi_common_2005()
        def sDate = endDate.toString().substring(0, 10)
        def dayDiff = script.intervalNow(sDate)

        if (endDate.toString().length() == 10) {
            if (enquiry.baseSuiteInfo?.efcSuiteInfo?.start) {
                enquiry.baseSuiteInfo.efcSuiteInfo.start = endDate.toString() + " 00:00:00"
                enquiry.baseSuiteInfo.efcSuiteInfo.end = script.caculateDay(endDate.toString(), -1 , 1) + " 23:59:59"
            }
            if (enquiry.baseSuiteInfo?.bizSuiteInfo?.start) {
                enquiry.baseSuiteInfo.bizSuiteInfo.start = endDate.toString() + " 00:00:00"
                enquiry.baseSuiteInfo.bizSuiteInfo.end = script.caculateDay(endDate.toString(), -1 , 1) + " 23:59:59"
            }
        } else if (endDate.toString().length() == 16) {
            def minute = endDate.toString().substring(10, 16)
            if (enquiry.baseSuiteInfo?.bizSuiteInfo?.start) {
                enquiry.baseSuiteInfo?.bizSuiteInfo?.start = endDate.toString().contains("23:59") ? (script.caculateDay(sDate, 1 , 0) + " 00:00:00") : (endDate.toString() + ":00")
                enquiry.baseSuiteInfo?.bizSuiteInfo?.end = endDate.toString().contains("23:59") ? (script.caculateDay(sDate, 0 , 1) + " 23:59:59") :(endDate.toString().contains(" 00:00") ? (script.caculateDay(sDate, -1, 1) + " 23:59:59") : (script.caculateDay(sDate, 0 , 1) + " 23:59:59"))
            }
            if (enquiry.baseSuiteInfo?.efcSuiteInfo?.start) {
                enquiry.baseSuiteInfo.efcSuiteInfo.start = endDate.toString().contains("23:59") ? (script.caculateDay(sDate, 1 , 0) + " 00:00:00") : (endDate.toString() + ":00")
                enquiry.baseSuiteInfo?.efcSuiteInfo?.end = endDate.toString().contains("23:59") ? (script.caculateDay(sDate, 0 , 1) + " 23:59:59") :(endDate.toString().contains(" 00:00") ? (script.caculateDay(sDate, -1, 1) + " 23:59:59") : (script.caculateDay(sDate, 0 , 1) + minute + ":00"))
            }

        } else {
            def last = endDate.toString().substring(10, 19)
            if (enquiry.baseSuiteInfo?.efcSuiteInfo?.start) {
                enquiry.baseSuiteInfo.efcSuiteInfo.start = endDate
                enquiry.baseSuiteInfo.efcSuiteInfo.end = script.getNextYear(sDate.toString()) + last
            }
            if (enquiry.baseSuiteInfo?.bizSuiteInfo?.start) {
                enquiry.baseSuiteInfo.bizSuiteInfo.start = endDate
                enquiry.baseSuiteInfo.bizSuiteInfo.end = script.getNextYear(sDate.toString()) + ' 23:59:59'
            }
        }
        if (dayDiff > insureDurationDay) {
            def repeatMsg = ''
            if (enquiry.baseSuiteInfo?.efcSuiteInfo?.start)
                repeatMsg += "(交) ${enquiry.baseSuiteInfo.efcSuiteInfo.start.substring(0, 10)} "
            if (enquiry.baseSuiteInfo?.bizSuiteInfo?.start)
                repeatMsg += "(商) ${enquiry.baseSuiteInfo.bizSuiteInfo.start.substring(0, 10)}"
            repeatMsg += '，起保日期必须在当前日期的规定时间范围内'
            throw new InsReturnException(repeatMsg)
        }
    }
    def ex = new InsReturnException(InsReturnException.AllowRepeat, "重复投保，再次报价");
    ex.setStep(1)
    throw ex
}

//转保验证码
def transferCode(enquiry, response) {
    enquiry?.tempValues = enquiry?.tempValues ?: [:]
    def CarQuoteBasePartBIRsp = response?.Body?.TEMPSTORAGERTN?.BIZ_ENTITY?.CarQuoteBasePartBIRsp
    def checkCodeBI = CarQuoteBasePartBIRsp.CheckCode
    def CarQuoteBasePartCIRsp = response?.Body?.TEMPSTORAGERTN?.BIZ_ENTITY?.CarQuoteBasePartCIRsp
    def checkCodeCI = CarQuoteBasePartCIRsp.CheckCode
    if (checkCodeCI && checkCodeCI.toString()) {
        def answerCI = Dama2Web.getAuth(checkCodeCI.toString(), 1)
        if (answerCI.length() == 4) {
            answerCI = answerCI.replaceAll("0", "O")
            def querySequenceNoCI = CarQuoteBasePartCIRsp.QuerySequenceNo.toString()
            def isRenewalFlagCI =  CarQuoteBasePartCIRsp.IsRenewalFlag.toString()
            enquiry?.tempValues['answerci'] = answerCI
            enquiry?.tempValues['querySequenceNoCI'] = querySequenceNoCI
            enquiry?.tempValues['isRenewalFlagCI'] = isRenewalFlagCI

        } else {
            throw new InsReturnException("交强转保验证码识别失败")
        }

    }
    if (checkCodeBI && checkCodeBI.toString()) {
        def answerBI = Dama2Web.getAuth(checkCodeBI.toString(), 1)
        if (answerBI.length() == 4) {
            answerBI = answerBI.replaceAll("0", "O")
            def querySequenceNoBI = CarQuoteBasePartBIRsp.QuerySequenceNo.toString()
            def isRenewalFlagBI = CarQuoteBasePartBIRsp.IsRenewalFlag.toString()
            enquiry?.tempValues['answerbi'] = answerBI
            enquiry?.tempValues['querySequenceNoBI'] = querySequenceNoBI
            enquiry?.tempValues['isRenewalFlagBI'] = isRenewalFlagBI
        } else {
            throw new InsReturnException("商业转保验证码识别失败")
        }
    }
    if (checkCodeBI.toString() || checkCodeCI.toString()) {
        throw new InsReturnException(InsReturnException.AllowRepeat, "转保验证码,继续报价")
    }
}

//险种数据初始化，折扣调整及平台信息回写
def initKind(enquiry, response, tempValues) {
    def SQ = enquiry?.SQ = enquiry?.SQ ?: [:]
    enquiry?.tempValues = enquiry?.tempValues ?: [:]
    def carQuoteGenRspList = response?.Body?.TEMPSTORAGERTN?.BIZ_ENTITY?.CarQuoteGenRspList
    def ciQuotationNo
    def biQuotationNo
    def bizInfo = [:]
    def bizCharge =  new BigDecimal("0.0")
    def efcCharge =  new BigDecimal("0.0")
    def taxCharge =  new BigDecimal("0.0")
    def trafficDiscountRate = 0
    def bussDiscountRate
    def definition = [:]
    def platformBack = [:]
    if (carQuoteGenRspList) {
        def carQuoteGenRsp = carQuoteGenRspList.CarQuoteGenRsp
        carQuoteGenRsp.each {
            def riskCode  = it.RiskCode
            if ("DAA".equals(riskCode.toString())) {
                //商业
                biQuotationNo = it.QuotationNo?.toString()
                bussDiscountRate =  it.Discount ? it.Discount.toBigDecimal().setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() : 0
                enquiry?.baseSuiteInfo?.bizSuiteInfo?.discountRate = bussDiscountRate
                bizCharge = it.SumPremium ? it.SumPremium.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0
                def orgCharge =  it.SumPremiumB ? it.SumPremiumB.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0
                enquiry?.baseSuiteInfo?.bizSuiteInfo?.orgCharge = orgCharge
                enquiry?.baseSuiteInfo?.bizSuiteInfo?.discountCharge = bizCharge
                def carQuoteGenItemKindFeeList = it.CarQuoteGenItemKindFeeList
                carQuoteGenItemKindFeeList.each {
                    def carQuoteGenItemKindFee = it.CarQuoteGenItemKindFee
                    carQuoteGenItemKindFee.each {
                        def info = [:]
                        def code = it.KindCode.toString()
                        info << [KindCode : code]
                        info << [BenchMarkPremium : it.BenchMarkPremium ? it.BenchMarkPremium.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0]
                        info << [Premium : it.Premium ? it.Premium.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0]
                        info << [Discount : it.Discount ? it.Discount.toBigDecimal().setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() : 0]
                        info << [Amount : it.Amount ? it.Amount.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0]
                        bizInfo.put(code,info)
                    }
                }
                def bizScore = it.PICCScore?.toString()
                definition[PlatformKey.bizScore] = bizScore
            } else {
                ///险别保费信息
                def carQuoteGenItemKindFeeList = it.CarQuoteGenItemKindFeeList
                ciQuotationNo = it.QuotationNo.toString()
                def CarQuoteGenItemKindFee = carQuoteGenItemKindFeeList.CarQuoteGenItemKindFee
                CarQuoteGenItemKindFee.each {
                    def sum = it.Amount ? it.Amount.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0//保额
                    enquiry?.baseSuiteInfo?.efcSuiteInfo?.amount = sum
                }
                enquiry?.baseSuiteInfo?.efcSuiteInfo?.orgCharge = it.SumPremiumB ? it.SumPremiumB.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0//标准保费
                enquiry?.baseSuiteInfo?.efcSuiteInfo?.discountCharge = it.SumPremium ? it.SumPremium.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0//应交保费
                enquiry?.baseSuiteInfo?.efcSuiteInfo?.discountRate = it.Discount ? it.Discount.toBigDecimal().setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() : 0//折扣率
                trafficDiscountRate = it.Discount ? it.Discount.toBigDecimal().setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() : 0
                //车船税
                enquiry?.baseSuiteInfo?.taxSuiteInfo = [:]
                enquiry?.baseSuiteInfo?.taxSuiteInfo?.charge = it.SumPayTax ? it.SumPayTax.toBigDecimal().setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() : 0
                taxCharge = it.SumPayTax ? it.SumPayTax.toBigDecimal().setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() : 0
                efcCharge = it.SumPremium  ? it.SumPremium.toBigDecimal().setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() : 0

                def efcScore = it.PICCScore?.toString()
                definition[PlatformKey.TRAFFIC_SCORE] = efcScore

            }
        }
    }
    def CarQuoteFactorList = response?.Body?.TEMPSTORAGERTN?.BIZ_ENTITY?.CarQuoteFactorList
    if (CarQuoteFactorList) {
        def CarQuoteFactor = CarQuoteFactorList.CarQuoteFactor
        CarQuoteFactor.each {
            def condition = it.Condition.toString()
            def rate = it.Rate.toString() ?: ""

            if ("无赔款优待优惠系数" == condition && rate) {
                definition[PlatformKey.noClaimDiscountCoefficient] = rate.toBigDecimal().divide(100)
                platformBack[PlatformKey.noClaimDiscountCoefficient] = rate.toBigDecimal().divide(100)
            }
            if ("自主定价优惠系数" == condition && rate) {
                definition[PlatformKey.selfRate] = rate.toBigDecimal().divide(100)
                platformBack[PlatformKey.selfRate] = rate.toBigDecimal().divide(100)

            }
        }
    }
    platformBack['definition'] = definition
    if (enquiry.carInfo.plateNum == '新车未上牌') {
        platformBack[PlatformKey.firstInsureType] = "新车首次投保"
    } else {
        if (enquiry?.tempValues?.answerbi || enquiry?.tempValues?.answerci) {
            platformBack[PlatformKey.firstInsureType] = "旧车首次投保"
        } else {
            platformBack[PlatformKey.firstInsureType] = "非首次投保"
        }
    }
    //需求14190
    def userYears = useYear(enquiry.carInfo.firstRegDate, enquiry?.baseSuiteInfo?.bizSuiteInfo?.start ?: enquiry?.baseSuiteInfo?.efcSuiteInfo?.start)
    def loyalty = userYears > 0 ? (response?.Header?.responsehead?.error_message?.toString()?.contains('此单符合续保条件') ? '1' : '2') : '0'
    platformBack[PlatformKey.application_loyalty] = loyalty
    definition[PlatformKey.application_loyalty] = loyalty
    tempValues[PlatformKey.platformBack] = platformBack
    if (tempValues['platformSaveUrl']) {
        PlatformUtil.doBackPlatformInfo(autoTask, enquiry, tempValues)
    }
    enquiry['definition'] = definition
    //需求11833
    if (SQ && SQ['discountRates'] && !tempValues['hadAdjustDiscount']) {
        //NCD
        def noClaimDiscountCoefficient = definition[PlatformKey.noClaimDiscountCoefficient]
        def bedRockDiscount
        //手动录入自主系数合计
        def inputTotalDiscount = SQ['discountRates']['geniusItem.inputTotalDiscount']
        if (inputTotalDiscount && noClaimDiscountCoefficient && !bedRockDiscount)
            bedRockDiscount =  Double.valueOf(inputTotalDiscount.toString()) * Double.valueOf(noClaimDiscountCoefficient.toString())

        //折扣系数
        def totalDiscount = SQ['discountRates']['geniusItem.totalDiscount']
        if (totalDiscount && noClaimDiscountCoefficient && !bedRockDiscount)
            bedRockDiscount = Double.valueOf(totalDiscount.toString()) * Double.valueOf(noClaimDiscountCoefficient.toString())
        if (bedRockDiscount) {
            //需求11139给的公式，最后乘100是因为保司页面提示
            def preDiscount = bedRockDiscount * 100
            tempValues['preDiscount'] = preDiscount
        }
    }

    if (!tempValues['doRuleInfo'] && !tempValues['hadAdjustDiscount']) {
        //只调用一次规则标志
        tempValues['doRuleInfo'] = true
        RuleUtil.doRuleInfo(autoTask, enquiry, tempValues, '2005', '', '')
        if (autoTask?.tempValues?.ruleInfo) {
            def ruleInfo = (JSONObject) autoTask.tempValues.ruleInfo
            def ruleDiscount = ruleInfo[(RuleInfoKey.geniusItem_policyDiscount)]
            if (ruleDiscount) {
                tempValues['preDiscount'] = ruleDiscount.toString().toBigDecimal() * 100
            }
            //需求12202
//                if (!tempValues['adjustProjectCode'] && ruleInfo['geniusItem.itemCode'] && ruleDiscount) {
//                    tempValues['adjustProjectCode'] = true
//                    config?.ProjectCode = ruleInfo['geniusItem.itemCode']
//                    throw new InsReturnException(InsReturnException.AllowRepeat, "调整折扣和项目代码再次报价")
//                }
//                if (!tempValues['adjustProjectCode'] && ruleInfo['geniusItem.itemCode'] && !ruleDiscount && ruleInfo['geniusItem.itemCode'] != config?.ProjectCode) {
//                    tempValues['adjustProjectCode'] = true
//                    config?.ProjectCode = ruleInfo['geniusItem.itemCode']
//                    throw new InsReturnException(InsReturnException.AllowRepeat, "调整项目代码再次报价")
//                }

        }
    }

    if (!tempValues['adjustDiscount'] && tempValues['preDiscount'] && !tempValues['hadAdjustDiscount'] && !tempValues['adjust']) {
        //报价后只调整一次折扣标志，此字段不影响车生态统一价
        tempValues['adjustDiscount'] = true
        throw new InsReturnException(InsReturnException.AllowRepeat, "调整折扣再次报价")
    }

    if (bizInfo) {
        enquiry?.baseSuiteInfo?.bizSuiteInfo?.suites?.each {
            def code = it.code
            def kindCode = kindCodeNew(code)
            it.discountRate = bizInfo.get(kindCode).Discount
            it.orgCharge = bizInfo.get(kindCode).BenchMarkPremium
            it.discountCharge = bizInfo.get(kindCode).Premium
            if (!["051053", "051065", "051066", "051067", "051068", "051064", "051080", "051081", "051079", "051071", "051085"].contains(kindCode)){
                it.amount = bizInfo.get(kindCode).Amount
            }
            if ('051073' == kindCode) {
                it.amount = (bizInfo.get(kindCode).Amount as Double).intValue() / (Double.valueOf(enquiry['carInfo']['seatCnt']).intValue() - 1)
            }
            def vehicleDamageAdjust = tempValues['vehicleDamageAdjust']
            if ('051050' == kindCode && enquiry['SQ']['discountRates'] && enquiry['SQ']['discountRates']['geniusItem.vehicleDamage.coverageDiscount'] && !vehicleDamageAdjust) {
                def coverageDiscount = enquiry['SQ']['discountRates']['geniusItem.vehicleDamage.coverageDiscount']
                def adjustAmount = (bizInfo.get(kindCode).Amount as BigDecimal).multiply(coverageDiscount.toString().toBigDecimal()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()
                tempValues['adjustVehicleDamageAmount'] = adjustAmount
                tempValues['vehicleDamageAdjust'] = true
                throw new InsReturnException(InsReturnException.AllowRepeat, "调整车损保额再次报价")
            }
        }
    }

    def totalCharge = new BigDecimal("0.0")
    if (biQuotationNo) {
        SQ['bizCharge'] = bizCharge
        SQ['bizTempId'] = biQuotationNo
        SQ['bussDiscountRate'] = bussDiscountRate
        enquiry?.tempValues['biQuotationNo'] = biQuotationNo
    }

    if (ciQuotationNo) {
        SQ['taxCharge'] = taxCharge
        SQ['efcCharge'] = efcCharge
        SQ['efcTempId'] = ciQuotationNo
        SQ['trafficDiscountRate'] = trafficDiscountRate
        enquiry?.tempValues['ciQuotationNo'] = ciQuotationNo
    }
    totalCharge = totalCharge.add(bizCharge).add(taxCharge).add(efcCharge).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()
    SQ['totalCharge'] = totalCharge
//        def insMessage = response?.Header?.responsehead?.error_message.toString()
//        SQ['insMessage'] = insMessage
    def fixedPremium = config?.fixedPremium
    def projectCode = config?.ProjectCode
    def adjust = tempValues['adjust']
    if (fixedPremium && projectCode && !adjust) {
        tempValues['adjust'] = true
        //商业险原始保费 * （特殊折扣） = 报价平台传过来商业险保费
        def efcOrgCharge = enquiry?.baseSuiteInfo?.efcSuiteInfo?.orgCharge
        def adjustCharge = new BigDecimal(fixedPremium.toString())
        if (efcOrgCharge && efcOrgCharge > 0) {
            adjustCharge =  adjustCharge.subtract(efcCharge)
        }
        if (enquiry?.baseSuiteInfo?.bizSuiteInfo?.orgCharge && enquiry?.baseSuiteInfo?.bizSuiteInfo?.orgCharge > 0) {
            def preDiscount = (adjustCharge.divide(enquiry?.baseSuiteInfo?.bizSuiteInfo?.orgCharge as BigDecimal, 4, BigDecimal.ROUND_HALF_UP) * 100).doubleValue()
            tempValues['preDiscount'] = preDiscount
            throw new InsReturnException(InsReturnException.AllowRepeat, "统一定价调整折扣再次报价")
        }
    }
}

//直接提示重复投保，不返回险种算费结果
def directRepeat(enquiry, msg, insureDurationDay) {
    // def msg = "商业险重复投保:保单号:PDAA20183701WAM0001950；起保日期:2019-02-17；终保日期:2020-02-17。交强险平台返回异常：车牌号“鲁A9J583”的保单发生重复投保，与其重复投保的本公司的保单信息如下：投保确认码 02PICC370018001545207949223724;保单号 PDZA201837010000351292;起保日期 2019-01-17 00:00;终保日期 2020-01-17 00:00;车牌号 鲁A9J583;号牌种类 02;车架号 LFMJ44AF4F3079650;发动机号 T200126;地区 370000。"
    //mag = "商业险重复投保:交强险平台返回异常：车牌号“鲁AX815Q”的保单发生重复投保，与其重复投保的其它公司的保单信息如下：车架号 JM7BL04Z2B1189956;发动机号 Z6915842;地区 370000。"
    //msg = "交强险平台返回异常：车牌号“鲁A2J878”的保单发生重复投保，与其重复投保的其它公司的保单信息如下：投保确认码 02CICP370019001546505181420600;保单号 0219379820230332000998;起保日期 2019-01-26 00:00;终保日期 2020-01-26 00:00;车牌号 鲁A2J878;号牌种类 02;车架号 LVSFCAME17F216110;发动机号 7J35618;地区 370000。"
    //msg = "交强险平台返回异常：车牌号“鲁AN6U15”的保单发生重复投保，与其重复投保的其它公司的保单信息如下：投保确认码 02TPBX370019001572245787224136;保单号 21130804105190030428;起保日期 2019-11-25 00:00;终保日期 2020-11-24 23:59;车牌号 鲁AN6U15;号牌种类 02;车架号 LB37624S2AL039571;发动机号 A4N214405;地区 370000"
//            def msg = "交强险平台返回异常：车牌号“鲁AN6U15”的保单发生重复投保，与其重复投保的其它公司的保单信息如下：投保确认码 02TPBX370019001572245787224136;保单号 21130804105190030428;起保日期 2019-11-25 00:00;终保日期 2020-11-24 23:59;车牌号 鲁AN6U15;号牌种类 02;车架号 LB37624S2AL039571;发动机号 A4N214405;地区 370000"
//            def msg = "商业险重复投保:保单号:PDAA20204521D000006444；起保日期:2020-05-11；终保日期:2021-05-11。交强险平台返回异常：车牌号“桂AF530H”的保单发生重复投保，与其重复投保的本公司的保单信息如下：投保确认码 02PICC450020001588211216528316;保单号 PDZA20204521D000006546;起保日期 2020-05-10 17:00;终保日期 2021-05-10 17:00;车牌号 桂AF530H;号牌种类 02;车架号 LHMB4DGN3HA011038;发动机号 A049289;地区 450000。"
    //商业险重复投保:保单号:PDAA20211102D000301867；起保日期:2021-09-26；终保日期:2022-09-26。交强险平台返回异常：号牌号码“京ADA8746”的保单发生重复投保，与其重复投保的本公司的保单信息如下：投保确认码 02PICC110021060234938608759903;保单号 PDZA20211102D000321169;起保日期 2021-09-26 00:00;终保日期 2022-09-26 00:00;号牌号码 京ADA8746;号牌种类 52;车架号 5YJ3E7EA8KF406170;发动机号 TG119223001MC33D1;地区 110000
    def script = new edi_common_2005()
    def bizReg = '商业险.*?终保日期.*?(\\d{4}-\\d{2}-\\d{2}( \\d{2}:\\d{2}(:\\d{2})?)?)'
    def efcReg = '交强险.*?终保日期.*?(\\d{4}-\\d{2}-\\d{2}( \\d{2}:\\d{2}(:\\d{2})?)?)'
    def bizEndDate
    def matcher = Pattern.compile(bizReg).matcher(msg)
    if (matcher.find()) {
        bizEndDate = matcher.group(1)
    }
    def efcEndDate
    matcher = Pattern.compile(efcReg).matcher(msg)
    if (matcher.find()) {
        efcEndDate = matcher.group(1)
    }
    if (bizEndDate) {
        def sDate = bizEndDate.toString().substring(0, 10)
        def dayDiff = script.intervalNow(sDate)

        if (bizEndDate.toString().length() == 10) {
            enquiry.baseSuiteInfo.bizSuiteInfo.start = bizEndDate.toString() + " 00:00:00"
            enquiry.baseSuiteInfo.bizSuiteInfo.end = script.caculateDay(bizEndDate.toString(), -1 , 1) + " 23:59:59"
        } else if (bizEndDate.toString().length() == 16) {
            def minute = bizEndDate.toString().substring(10, 16)
            enquiry.baseSuiteInfo.bizSuiteInfo.start = bizEndDate.toString().contains("23:59") ? (script.caculateDay(sDate, 1 , 0) + " 00:00:00") : (bizEndDate.toString() + ":00")
            enquiry.baseSuiteInfo.bizSuiteInfo.end = bizEndDate.toString().contains("23:59") ? (script.caculateDay(sDate, 0 , 1) + " 23:59:59") :(bizEndDate.toString().contains(" 00:00") ? (script.caculateDay(sDate, -1, 1) + " 23:59:59") : (script.caculateDay(sDate, 0 , 1) + " 23:59:59"))
        }
        else {
            def last = bizEndDate.toString().substring(10, 19)
            enquiry.baseSuiteInfo.bizSuiteInfo.start = bizEndDate
            enquiry.baseSuiteInfo.bizSuiteInfo.end = script.getNextYear(sDate.toString()) + last
        }

        if (dayDiff > insureDurationDay) {
            def repeatMsg = ''
            if (efcEndDate)
                repeatMsg +=  "(交) ${efcEndDate.substring(0, 10)} "
            repeatMsg += "(商) ${enquiry.baseSuiteInfo.bizSuiteInfo.start.substring(0, 10)}"
            repeatMsg += '，起保日期必须在当前日期的规定时间范围内'
            throw new InsReturnException(repeatMsg)
        }
    }
    if (efcEndDate) {
        def sDate = efcEndDate.toString().substring(0, 10)
        def dayDiff = script.intervalNow(sDate)
        if (efcEndDate.toString().length() == 10) {
            enquiry.baseSuiteInfo.efcSuiteInfo.start = efcEndDate.toString() + " 00:00:00"
            enquiry.baseSuiteInfo.efcSuiteInfo.end = script.caculateDay(efcEndDate.toString(), -1 , 1) + " 23:59:59"
        } else if (efcEndDate.toString().length() == 16) {
            def minute = efcEndDate.toString().substring(10, 16)
            enquiry.baseSuiteInfo.efcSuiteInfo.start = efcEndDate.toString().contains("23:59") ? (script.caculateDay(sDate.toString(), 1 , 0) + " 00:00:00") : (efcEndDate + ":00")
            enquiry.baseSuiteInfo.efcSuiteInfo.end = efcEndDate.toString().contains("23:59") ? (script.caculateDay(sDate.toString(), 0, 1) + " 23:59:59") : (efcEndDate.toString().contains(" 00:00") ? (script.caculateDay(sDate, -1, 1) + " 23:59:59") : (script.caculateDay(sDate, 0, 1) + minute + ":00"))

        } else {
            def last = efcEndDate.toString().substring(10, 19)
            enquiry.baseSuiteInfo.efcSuiteInfo.start = bizEndDate
            enquiry.baseSuiteInfo.efcSuiteInfo.end = script.getNextYear(sDate.toString()) + last
        }
        if (dayDiff > insureDurationDay) {
            msg =  "(交) ${enquiry.baseSuiteInfo.efcSuiteInfo.start.substring(0, 10)} "
            if (bizEndDate)
                msg += "(商) ${bizEndDate.substring(0, 10)}"
            msg += '，起保日期必须在当前日期的规定时间范围内'
            throw new InsReturnException(msg)
        }
    }
    def ex = new InsReturnException(InsReturnException.AllowRepeat, "重复投保，再次报价");
    ex.setStep(1)
    throw ex
}

def repeatTimeLimit(enquiry, msg, insureDurationDay) {
    //"交强险平台返回异常：该车已经在本公司投保了同类型的险种，保险期限是[201912170000-202012170000],请核对并确认在我公司投保。"
    // 交强险平台返回异常：该车已经在其他公司投保了同类型的险种，保险期限是[20200201-20210131],请核对并确认在我公司投保。
    def script = new edi_common_2005()
    def dateArr = []
    def p = Pattern.compile("(\\d{12})")
    def m = p.matcher(msg)
    while (m.find()) {
        dateArr.add(m.group())
    }
    if (dateArr.size() > 0) {
        //202012170000
        //202012162359
        def str = dateArr[1]
        def sDate = str.toString().substring(0, 8)
        def startDay = script.getNeedDay(sDate, "yyyyMMdd")
        def dayDiff = script.intervalNow(startDay)
        enquiry.baseSuiteInfo.efcSuiteInfo.start = str.toString().contains("2359") ? (script.caculateDay(startDay, 1, 0) + " 00:00:00") : (script.getNeedTime(str, "yyyyMMddHHmm", "yyyy-MM-dd HH:mm") + ":00")
        def minute = enquiry.baseSuiteInfo.efcSuiteInfo.start.toString().substring(10, 19)
        enquiry.baseSuiteInfo.efcSuiteInfo.end = str.toString().contains("2359") ? (script.caculateDay(startDay, 0, 1) + " 23:59:59") : (script.caculateDay(startDay, 0, 1) + minute)
        if ( enquiry.baseSuiteInfo.efcSuiteInfo.end.toString().contains("00:00:00")) {
            enquiry.baseSuiteInfo.efcSuiteInfo.end = script.caculateDay(startDay, -1, 1) + " 23:59:59"
        }
        if (dayDiff > insureDurationDay) {
            def repeatMsg = "(交) ${enquiry.baseSuiteInfo.efcSuiteInfo.start.substring(0, 10)} "
            repeatMsg += '，起保日期必须在当前日期的规定时间范围内'
            throw new InsReturnException(repeatMsg)
        }
        def ex = new InsReturnException(InsReturnException.AllowRepeat, "重复投保，再次报价")
        ex.setStep(1)
        throw ex
    } else {
        p = Pattern.compile("(\\d{8})-(\\d{8})")
        m = p.matcher(msg);
        while (m.find()) {
            dateArr.add(m.group())
        }
        //20210131
        if (dateArr.size() > 0) {
            def s = dateArr[0]
            def lastEnd = s.toString().split("-")[1]
            def lastEndDay = script.getNeedDay(lastEnd, "yyyyMMdd")
            def dayDiff = script.intervalNow(lastEndDay.toString())
            enquiry.baseSuiteInfo.efcSuiteInfo.start = script.caculateDay(lastEndDay.toString(), 1, 0) + " 00:00:00"
            enquiry.baseSuiteInfo.efcSuiteInfo.end = script.caculateDay(lastEndDay.toString(), 0, 1) + " 23:59:59"
            if (dayDiff > insureDurationDay) {
                def repeatMsg = "(交) ${enquiry.baseSuiteInfo.efcSuiteInfo.start.substring(0, 10)} "
                repeatMsg += '，起保日期必须在当前日期的规定时间范围内'
                throw new InsReturnException(repeatMsg)
            }
            def ex = new InsReturnException(InsReturnException.AllowRepeat, "重复投保，再次报价");
            ex.setStep(1)
            throw ex
        } else {
            throw new InsReturnException(msg)
        }

    }
}

//校正车型编码
def adjustVehicleCode(enquiry, msg, tempValues, carModelName, platCode, config) {
    if ((tempValues as Map).containsKey('jyCodeList') ) {
        def jyCodeList = tempValues?.jyCodeList
        if (jyCodeList.size() > 0) {
            enquiry?.carInfo?.jyCode = jyCodeList.remove(0)
            enquiry?.carInfo?.rbCode = enquiry?.carInfo?.rbCode
            throw new InsReturnException(InsReturnException.AllowRepeat, '调整车型编码再次报价')
        } else {
            throw new InsReturnException(msg)
        }
    } else if (!tempValues['jyCheck']) {
        tempValues['jyCheck'] = true
        def rootIns = config?.rootIns ?: "2021"
        def adjustProcessType = config?.adjustProcessType ?: 'edi'
        def jyCodeList = platModelCodeToJyCode(adjustProcessType, platCode, carModelName, rootIns, '2005', enquiry['carInfo']['vin'])
        if (jyCodeList) {
            enquiry?.carInfo?.jyCode = jyCodeList.remove(0)
            enquiry?.carInfo?.rbCode = enquiry?.carInfo?.jyCode
            tempValues?.jyCodeList = jyCodeList
            throw new InsReturnException(InsReturnException.AllowRepeat, "调整车型编码再次报价")
        } else {
            //未查到精友code。如果是车生态 按需求12465处理
            if (!tempValues['PresaleCarFlag'] && tempValues['requestSource'] && tempValues['requestSource']['sourceProduct'] == 'che_eco') {
                tempValues['PresaleCarFlag'] = '1'
                throw new InsReturnException(InsReturnException.AllowRepeat, '车生态预售车调整车型编码再次报价')
            } else {
                throw new InsReturnException(msg)
            }
        }
    } else {
        throw new InsReturnException(msg)
    }
}
