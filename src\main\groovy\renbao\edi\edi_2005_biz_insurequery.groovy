package renbao.edi

import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.exception.TempSkipException
import com.cheche365.bc.message.TaskStatus
import com.cheche365.bc.message.TaskType




    def proposalNo = enquiry?.SQ?.bizProposeNum
    if (!proposalNo) {
        throw new TempSkipException(1,"商业险投保单号为空，跳过当前步骤！");
    }
    if (proposalNo.toString().startsWith('TDZA')) {
        throw new InsReturnException('商业险投保单号录入错误，需要以TDAA为开头')
    }
    if (tempValues['taskType'] == TaskType.AUTO_INSURE.code && enquiry?.taskStatus != TaskStatus.AUTO_INSURE_SUCCESS.state) {
        throw new TempSkipException(1, "当前任务为自核且核保状态非自核成功，跳过当前步骤")
    }
    def script = new edi_common_2005()
    def queryId = UUID.randomUUID().toString()
    def cstTime = script.getCurCSTtime()
    def piccAreaCode = script.getPiccAreaCode(enquiry, config)

    def reqXml =
                """<soapenv:Envelope 	xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
                    <soap:Header 	xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                        <nshead:requesthead	xmlns:nshead="http://pub.webservice.cmp.com">
                            <nshead:request_type>Q13</nshead:request_type>
                            <nshead:uuid>${queryId}</nshead:uuid>
                            <nshead:sender>${config?.sender ?: '0595'}</nshead:sender>
                            <nshead:server_version>00000000</nshead:server_version>
                            <nshead:user>${config?.user ?: '0595'}</nshead:user>
                            <nshead:password>${config?.password ?: 'EC00FFFD8E9F7F2289752081793E9905'}</nshead:password>
                            <nshead:ChnlNo>${config?.ChnlNo ?: 'cheche'}</nshead:ChnlNo>
                            <nshead:areacode>${piccAreaCode}</nshead:areacode>
                            <nshead:flowintime>${cstTime}</nshead:flowintime>
                        </nshead:requesthead>
                    </soap:Header>
                    <soapenv:Body>
                        <pan:GETPOLICYDETAILCONDITIONREQ  	xmlns:pan="http://pan.prpall.webservice.cmp.com">
                            <pan:BIZ_ENTITY>
                                <PolicyDetailCondition>
                                    <ProposalNo>${proposalNo}</ProposalNo>
                                    <Resource>${config?.Resource ?: '0595'}</Resource>
                                </PolicyDetailCondition>
                            </pan:BIZ_ENTITY>
                            <pan:APP_INFO>
                                <pan:MAKECOME>string</pan:MAKECOME>
                                <pan:REQMOD>string</pan:REQMOD>
                            </pan:APP_INFO>
                        </pan:GETPOLICYDETAILCONDITIONREQ >
                    </soapenv:Body>
                </soapenv:Envelope>"""
