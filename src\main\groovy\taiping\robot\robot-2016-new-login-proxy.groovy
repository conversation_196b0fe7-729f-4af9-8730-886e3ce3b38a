package taiping.robot

import com.cheche365.bc.utils.sender.HttpSender
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import groovy.transform.Field

import static com.cheche365.bc.utils.sender.HttpSender.buildHttpClient

@BaseScript BaseScript_Http_Enq _
/**
 * 请求头
 */
@Field private static final Map<String, String> HEADERS = [
        'Content-Type': 'application/json'
]


assertNotBlank('请设置必要参数 出单系统用户名（位置：autoTask.configs.login）', config?.login as String)

autoTask.setHttpClient(buildHttpClient(HttpSender.getSSLSocketFactoryNoCheck()))

config.LoginPassword = config.LoginPassword ?: config?.password

head HEADERS

jsonBody config.loginByCookie ? this.&loginByCookie : this.&loginByPassword

/**
 * 通过用户名密码登录
 *
 * @return
 */
Map loginByPassword() {
    [
            'username' : config.login,
            'password' : config.LoginPassword,
            'checkUrl' : '/api/system/checkCarCode/getCheckCarCode',
            'loginType': 'password'
    ]
}

/**
 * 通过cookie登录
 *
 * @return
 */
Map loginByCookie() {
    def cookies = config.cookie as String
    if (!cookies) {
        fail('请设置必要参数 出单系统cookie（位置：autoTask.configs.cookie）')
    }
    def result = [
            'username' : config.login,
            'checkUrl' : '/api/system/checkCarCode/getCheckCarCode',
            'loginType': 'cookie',
            'cookies'  : [:]
    ]
    // 解析cookie字符串为Map
    def cookieMap = [:]
    cookies.split(';').each { kv ->
        def pair = kv.trim().split('=', 2)
        if (pair.length == 2) {
            cookieMap[pair[0].trim()] = pair[1].trim()
        }
    }

    result.cookies = cookieMap

    // 返回需要的cookie值
    return result
}