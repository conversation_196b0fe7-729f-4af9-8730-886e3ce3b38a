package guoshou.edi

import com.cheche365.bc.exception.TempSkipException
import groovy.transform.BaseScript
import guoshou.constants.GuoShouConstants
import guoshou.script.BaseScript_2002

import java.time.LocalDate

import static common.common_all.getSupplyParam
import static common.common_all.skipNewCarRegistration
import static guoshou.edi.edi_2002_common_new.*

@BaseScript BaseScript_2002 _

if (skipNewCarRegistration(enquiry)) {
    throw new TempSkipException(1, "不满足地区要求或者不是新车或者未投保交强险，跳过此接口")
}

body(tempValues, reqHeaders as Map, GuoShouConstants.NEW_CAR_RECORD, [
        "engineNo"            : carInfo.engineNum,
        "frameNo"             : carInfo.vin,
        "carOwner"            : enquiry.carOwnerInfo?.name,
        "carOwnerIdentifyType": getIDCardType(enquiry.carOwnerInfo?.idCardType)[0],
        "carOwnerIdentifyNo"  : enquiry.carOwnerInfo?.idCard,
        "seatCount"           : carInfo.seatCnt, // 核定载客
        "tonCount"            : carInfo.modelLoad, // 核定载质量
        "wholeWeight"         : carInfoQuery.wholeWeight?.toString(), // 整备质量
        "exhaustScale"        : (carInfo.displacement?.toBigDecimal() * 1000)?.stripTrailingZeros()?.toPlainString(), // 排量
        "fuleType"            : getEnergyTypesCode(tempValues.energyType, enquiry), // 燃料种类
        "certificateType"     : "01", // 车辆来历凭证种类
        "certificateNo"       : getSupplyParam(enquiry, "carOriginProofNo") ?: config.carCertificateNo, // 车辆来历凭证编号
        "certificateDate"     : getSupplyParam(enquiry, "carOriginProofDate") ?: LocalDate.now().toString(), // 开具车辆来历凭证日期
        "carType"             : getVehicleStyle(carInfo.jgVehicleType)[1],
        "labelModel"          : carInfoQuery.fcVehicle,
        "carKindCode"         : getCarKindCode(carInfo.useProps as Integer)
])

