package common.scripts

import cn.hutool.core.util.ObjectUtil
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.exception.TempSkipException
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.model.car.Order
import com.cheche365.bc.task.AutoTask
import groovy.transform.stc.ClosureParams
import groovy.transform.stc.FromString
import org.apache.http.impl.client.CloseableHttpClient
import org.jsoup.nodes.Document
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 发送请求模版，只做响应校验和数据输出到tempValues
 *
 * <AUTHOR>
 */
abstract class BaseScript_Http_Enq extends Script {

    static final Logger log = LoggerFactory.getLogger(BaseScript_Http_Enq.class)

    AutoTask autoTask

    Enquiry entity

    Order order

    Map<String, Object> config

    Map<String, Object> tempValues

    abstract def runScript()

    def preRun() {
        this.autoTask = this.binding.getVariable('autoTask') as AutoTask
        this.entity = (Enquiry) this.autoTask?.taskEntity
        this.config = this.autoTask?.configs
        this.tempValues = this.autoTask?.tempValues
        this.order = this.entity.order
    }

    def run() {
        preRun()
        runScript()
    }

    /**
     * 跳过模版判断
     * @param tip 提示语
     * @param closure
     */
    static void skip(String tip = '无需执行，跳过当前模板', Closure<Boolean> closure) {
        skip(tip, closure())
    }

    /**
     * 跳过模版判断
     * @param tip 提示语
     * @param condition条件
     */
    static void skip(String tip = '无需执行，跳过当前模板', Boolean condition = Boolean.FALSE) {
        if (condition) {
            throw new TempSkipException(1, tip)
        }
    }

    /**
     * 请求头
     * @param closure
     */
    void head(Closure<Map> closure) {
        this.autoTask.reqHeaders.clear()
        this.autoTask.reqHeaders << closure()
    }

    void head(Map head) {
        this.autoTask.reqHeaders.clear()
        this.autoTask.reqHeaders << head
    }

    <R> String jsonBody(Closure<String> serializer = JSON.&toJSONString, Closure<R> closure) {
        autoTask.params.clear()
        def data = closure()
        serializer(data)
    }

    <R> String jsonBodyWithUnicode(Closure<R> closure) {
        autoTask.params.clear()
        def data = closure()
        return ScriptUtil.toJSONStringWithChinese(data)
    }

    String bodyWithUnicode(Closure<String> closure) {
        autoTask.params.clear()
        ScriptUtil.chineseToUnicode(closure())
    }

    String body(String bodyStr) {
        autoTask.params.clear()
        bodyStr
    }

    <R> String body(Closure<String> serializer, Closure<R> closure) {
        autoTask.params.clear()
        def data = closure()
        serializer(data)
    }

    void form(Map<String, Object> params) {
        autoTask.params.clear()
        autoTask.params << params
    }

    void form(Closure<Map<String, Object>> closure) {
        autoTask.params.clear()
        autoTask.params << closure()
    }

    /**
     * 响应报文校验
     * @param closure
     */
    void check(@ClosureParams(value = FromString.class, options = ['com.alibaba.fastjson.JSONObject']) Closure closure) {
        def resp = ScriptUtil.initResp(autoTask.backRoot)
        closure(resp)
    }

    <R> void check(Closure<R> initResp, Closure closure) {
        def resp = initResp(autoTask.backRoot)
        closure(resp)
    }

    JSONObject getResp(@ClosureParams(value = FromString.class, options = ['com.alibaba.fastjson.JSONObject']) Closure checkClosure) {
        def resp = ScriptUtil.initResp(autoTask.backRoot)
        if (ObjectUtil.isNotNull(checkClosure)) {
            checkClosure(resp)
        }
        return resp
    }

    void checkDoc(@ClosureParams(value = FromString.class, options = ['org.jsoup.nodes.Document']) Closure closure) {
        def resp = ScriptUtil.initRespDoc(autoTask.backRoot)
        closure(resp)
    }

    Document getRespDoc(@ClosureParams(value = FromString.class, options = ['org.jsoup.nodes.Document']) Closure checkClosure) {
        def resp = ScriptUtil.initRespDoc(autoTask.backRoot)
        if (ObjectUtil.isNotNull(checkClosure)) {
            checkClosure(resp)
        }
        return resp
    }
    /**
     * 输出数据
     * @param tempValuesKey 输出到模板的key
     * @param closure 执行的代码
     */
    void output(String tempValuesKey = '', Closure<Map> closure) {
        def data = closure()
        if (data) {
            if (StrUtil.isBlank(tempValuesKey)) {
                tempValuesKey = getTemplateName()
            }
            if (tempValuesKey) {
                this.tempValues[tempValuesKey] = data
            }
        }
    }

    /**
     * 输出数据
     * @param tempValuesKey 模板key
     * @param data 输出的数据
     */
    void output(String tempValuesKey = '', Map data) {
        if (data) {
            if (StrUtil.isBlank(tempValuesKey)) {
                tempValuesKey = getTemplateName()
            }
            if (tempValuesKey) {
                this.tempValues[tempValuesKey] = data
            }
        }
    }

    void output(String tempValuesKey, List data) {
        if (data) {
            if (StrUtil.isBlank(tempValuesKey)) {
                tempValuesKey = getTemplateName()
            }
            if (tempValuesKey) {
                this.tempValues[tempValuesKey] = data
            }
        }
    }

    Map getTemplateValues(String tempValuesKey = '') {
        String key = tempValuesKey ?: getTemplateName()
        return this.tempValues[key] as Map
    }

    /**
     * 获取模板名称
     * 如果未设置tempValuesKey则获取当前模板的后缀名
     * 如：robot-2011-xxxx_rep->xxxx
     * @return 返回模板名称
     */
    protected String getTemplateName() {
        def scriptName = this.getClass().getSimpleName()
        def pattern = /^(robot|edi)-\d{4}-(.*?)(?:_rep)?$/
        def matcher = scriptName =~ pattern

        if (matcher.matches()) {
            return matcher.group(2)
        }
        return null
    }

    /**
     * 获取平台信息
     * @return
     */
    JSONObject getPTMsg() {
        return (JSONObject) tempValues.computeIfAbsent('PTMsg', { k -> new JSONObject() })
    }

    //保司异常
    static void fail(String msg = '保司异常', int code = InsReturnException.Others) {
        throw new InsReturnException(code, msg)
    }

    static void assertTrue(String msg = '保司异常', int code = InsReturnException.Others, boolean condition) {
        if (!condition) {
            fail(msg, code)
        }
    }

    static void assertFalse(String msg = '保司异常', int code = InsReturnException.Others, boolean condition) {
        assertTrue(msg, code, !condition)
    }

    static void assertEquals(String msg = '保司异常', int code = InsReturnException.Others, Object expected, Object actual) {
        assertTrue(msg, code, ObjectUtil.equal(expected, actual))
    }

    static void assertNotEquals(String msg = '保司异常', int code = InsReturnException.Others, Object expected, Object actual) {
        assertTrue(msg, code, ObjectUtil.notEqual(expected, actual))
    }

    static void assertNotEmpty(String msg = '保司异常', int code = InsReturnException.Others, Object obj) {
        assertTrue(msg, code, ObjectUtil.isNotEmpty(obj))
    }

    static void assertEmpty(String msg = '保司异常', int code = InsReturnException.Others, Object obj) {
        assertTrue(msg, code, ObjectUtil.isEmpty(obj))
    }

    static void assertNotNull(String msg = '保司异常', int code = InsReturnException.Others, Object obj) {
        assertTrue(msg, code, ObjectUtil.isNotNull(obj))
    }

    static void assertNull(String msg = '保司异常', int code = InsReturnException.Others, Object obj) {
        assertTrue(msg, code, ObjectUtil.isNull(obj))
    }

    static void assertNotBlank(String msg = '保司异常', int code = InsReturnException.Others, CharSequence str) {
        assertTrue(msg, code, StrUtil.isNotBlank(str))
    }

    static void assertBlank(String msg = '保司异常', int code = InsReturnException.Others, CharSequence str) {
        assertTrue(msg, code, StrUtil.isBlank(str))
    }

    static void retry(String msg = '重试当前模板', int step = 1) {
        def ex = new InsReturnException(InsReturnException.AllowRepeat, msg)
        ex.setStep(step)
        throw ex
    }

    byte[] downloadByGet(@DelegatesTo(HttpBuilder.class) Closure closure) throws Exception {
        return ScriptUtil.downloadByGet(autoTask.httpClient as CloseableHttpClient, closure)
    }

    String doGet(@DelegatesTo(HttpBuilder.class) Closure closure) throws Exception {
        return ScriptUtil.doGet(autoTask.httpClient as CloseableHttpClient, closure)
    }

    byte[] downloadByPost(@DelegatesTo(HttpBuilder.class) Closure<?> closure) throws Exception {
        return ScriptUtil.downloadByPost(autoTask.httpClient as CloseableHttpClient, closure)
    }

    String doPost(@DelegatesTo(HttpBuilder.class) Closure closure) throws Exception {
        return ScriptUtil.doPost(autoTask.httpClient as CloseableHttpClient, closure)
    }

    String doPut(@DelegatesTo(HttpBuilder.class) Closure closure) throws Exception {
        return ScriptUtil.doPut(autoTask.httpClient as CloseableHttpClient, closure)
    }

    String doDelete(@DelegatesTo(HttpBuilder.class) Closure closure) throws Exception {
        return ScriptUtil.doDelete(autoTask.httpClient as CloseableHttpClient, closure)
    }

    static def retry(int times = 3, Closure closure) {
        ScriptUtil.retry(times, closure)
    }

    /**
     * 主动发起重试
     * 需配合 retry 方法使用
     * @param message
     */
    static def retryHttp(String message = '请求重试') {
        throw new HttpRetryException(message)
    }
}
