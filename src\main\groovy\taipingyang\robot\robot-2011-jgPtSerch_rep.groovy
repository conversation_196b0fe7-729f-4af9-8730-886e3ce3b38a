package taipingyang.robot

import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.NumberUtil
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.model.car.CarOwnerInfo
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import groovy.transform.Field
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotDict_2011

@BaseScript BaseScript_Http_Enq _

@Field JSONObject resp = null

check Robot2011Util.serializer(tempValues), { JSONObject it ->
    //非空判断
    assertNotNull '获取交管平台信息失败', it

    //验证码错误
    if (it.toJSONString().contains('录入的校验码有误')) {
        retry('即将重新获取交管验证码')
    }

    //查询错误
    def message = it?.getJSONObject('message')
    if ('success' != message?.getString('code')) {
        fail("获取交管平台信息,平台返回:${message?.getString('message')}")
    }

    resp = it.getJSONArray('result')?.getJSONObject(0)

    assertNotEmpty('获取交管平台信息失败', resp)
}

//是否是交管查询
boolean isTrafficQueryTask = null != autoTask.applyJson && autoTask.applyJson.contains("trafficQuery");

//车牌号
String carMark = resp.getString('carMark')
//发动机号
String engineNo = resp.getString('engineNo')
//车架号
String rackNo = resp.getString('rackNo')
//交管车辆类型
String vehicleStyle = resp.getString('vehicleStyle')
//车辆信息
def carInfo = order.carInfo

if (!isTrafficQueryTask) {
    if (carMark != carInfo.plateNum) {
        fail("交管平台信息校验失败，投保车牌号为${order.carInfo.plateNum},平台返回车牌号为${carMark}")
    }
    if (engineNo != carInfo.engineNum) {
        fail("交管平台信息校验失败，投保发动机号为${order.carInfo.engineNum},平台返回发动机号为${engineNo}")
    }
    if (rackNo != carInfo.vin) {
        fail("交管平台信息校验失败，投保车架号为${order.carInfo.vin},平台返回车架号为${rackNo}")
    }
    if (StrUtil.isBlank(vehicleStyle)) {
        fail('获取的交管车辆类型为空')
    }
}

//导入交管平台信息
carInfo.tap {
    seatCnt = resp.getInteger('limitLoadPerson')
    ratedPassengerCapacity = seatCnt
    fullLoad = resp.getBigDecimal('wholeWeight')
    def exhaustScale = resp.getBigDecimal('displacement')
    if (exhaustScale) {
        displacement = NumberUtil.div(exhaustScale, new BigDecimal('1000'), 3)
    }
    def vehicleRegisterDate = resp.getString('vehicleRegisterDate')
    if (vehicleRegisterDate) {
        firstRegDate = DateUtil.parse(vehicleRegisterDate, DatePattern.NORM_DATE_PATTERN)
    }
}

String vehicleModel = resp.getString('vehicleModel')
autoTask?.tempValues?.vehicleModel = vehicleModel

//仅交管查询时才导入的项
if (isTrafficQueryTask) {
    carInfo.tap {
        plateNum = carMark
        engineNum = engineNo
        vin = rackNo
        def vehicleBrand1 = resp.getString('vehicleBrand1')?.replace('牌', '')
        carBrandName = vehicleBrand1 + vehicleModel
        modelLoad = resp.getBigDecimal('limitLoad')
        def transferDate = resp.getString('transferDate')
        if (transferDate) {
            it.transferDate = DateUtil.parse(transferDate, DatePattern.NORM_DATE_PATTERN)
        }
        jgVehicleType = RobotDict_2011.getValue('vehicleStyle', vehicleStyle)?.toInteger() ?: 30
    }

    if (null == entity.order.carOwnerInfo)
        entity.order.carOwnerInfo = new CarOwnerInfo()
    entity.order.carOwnerInfo.name = resp.getString('ownerName')
}

autoTask.tempValues.put("vehicleStyle", vehicleStyle)
autoTask.tempValues.put("plateTypeJg", resp.getString('vehicleType'))
autoTask.tempValues.put("finishGetJGinfo", "true")