package com.cheche365.bc.entity.hbase;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * HBase action_log 表模型
 * 用于存储动作日志信息，包括基本信息和大文本内容
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActionLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * HBase表名
     */
    public static final String TABLE_NAME = "action_log";

    /**
     * 列族定义
     */
    public static final String CF_INFO = "info";

    public static final String CF_DATA = "data";

    public static final String CF_ERROR = "error";

    /**
     * 动作ID，用于确保rowKey唯一性
     */
    private String actionId;

    /**
     * 关联的auto_task的ID
     */
    private String autoTraceId;
    // ========== 基本信息字段 (存储在info列族) ==========
    /**
     * 动作名称
     */
    private String actionName;

    /**
     * 请求URL
     */
    private String url;

    /**
     * 发送时间
     */
    private LocalDateTime sendTime;

    /**
     * 接收时间
     */
    private LocalDateTime receiveTime;

    // ========== 大文本字段 (存储在data列族) ==========

    /**
     * 请求体 (大文本)
     */
    private String requestBody;

    /**
     * 响应体 (大文本)
     */
    private String responseBody;

    /**
     * 输入任务体 (大文本)
     */
    private String inTaskBody;

    /**
     * 输出任务体 (大文本)
     */
    private String outTaskBody;

    // ========== 异常信息列族 (存储在error列族) ==========

    /**
     * 异常信息 (大文本)
     */
    private String exceptionInfo;

    /**
     * 生成HBase的rowKey
     * 格式：[salt]_[autoTraceId]_[timestamp]_[actionId]
     *
     * @return rowKey字符串
     */
    public String generateRowKey() {
        if (autoTraceId == null || autoTraceId.isEmpty()) {
            throw new IllegalArgumentException("autoTraceId不能为空");
        }
        if (actionId == null || actionId.isEmpty()) {
            throw new IllegalArgumentException("actionId不能为空");
        }

        int salt = Math.floorMod(autoTraceId.hashCode(), 10);
        long timestamp = sendTime != null ?
            java.sql.Timestamp.valueOf(sendTime).getTime() :
            System.currentTimeMillis();

        return salt + "_" + autoTraceId + "_" + timestamp + "_" + actionId;
    }

    /**
     * 获取盐值
     *
     * @return 盐值 (0-9)
     */
    public int getSalt() {
        if (autoTraceId == null || autoTraceId.isEmpty()) {
            return 0;
        }
        return Math.floorMod(autoTraceId.hashCode(), 10);
    }

    /**
     * 生成扫描前缀，用于查询特定autoTraceId的所有动作日志
     * 格式：[salt]_[autoTraceId]_
     *
     * @param autoTraceId 任务ID
     * @return 扫描前缀
     */
    public static String generateScanPrefix(String autoTraceId) {
        if (autoTraceId == null || autoTraceId.isEmpty()) {
            throw new IllegalArgumentException("autoTraceId不能为空");
        }
        int salt = Math.floorMod(autoTraceId.hashCode(), 10);
        return salt + "_" + autoTraceId + "_";
    }
}
