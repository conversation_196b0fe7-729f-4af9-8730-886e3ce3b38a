package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.tools.StringUtil
import taipingyang.robot.module.Robot2011Util

Map entity = enquiry
Map tempValues = null != entity.get("tempValues") ? entity.get("tempValues") : tempValues
Map postParameters = postParameters
Map reqHeaders = reqHeaders

Map header = new HashMap();
header.put("Content-Type", tempValues?.needSM4Flag ? 'text/plain' : "application/json;charset=utf-8")

JSONObject param = new JSONObject()

String insuredNo = entity?.SQ?.bizProposeNum
if (StringUtil.isEmpty(insuredNo))
    insuredNo = entity?.SQ?.efcProposeNum;
if (StringUtil.isEmpty(insuredNo))
    throw new InsReturnException("查询用投保单号为空，请确认查询数据正确性")
tempValues.put("insuredNo", insuredNo);

param = JSON.parseObject('{"meta":{"pageSize":8},"redata":{"quotationNo":"","insuredNo":"","policyHolder":"","partyName":"","insuredStartDate":"","insuredEndDate":""}}')
param.getJSONObject("redata").put("insuredNo", insuredNo)

String queryPayJson = param.toJSONString();
reqHeaders.put("Content-Type",  tempValues?.needSM4Flag ? 'text/plain' : "application/json;charset=utf-8")
postParameters.clear()
tempValues.put("reqHeaders", reqHeaders)
entity.put("tempValues", tempValues)
Robot2011Util.genBody(tempValues, queryPayJson)