package renbao.robot

import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.model.car.Enquiry
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import groovy.transform.Field

import static renbao.common.Constants.SUCCESS_CODE



@BaseScript BaseScript_Http_Enq _

@Field JSONObject resp = null

check {
    assertTrue("非车算费异常：" + it.toJSONString(), SUCCESS_CODE == it['statusText'])
    resp = it
}

def data = resp['data']
def nonMotorProposalNo = data['quotationNo']
if (nonMotorProposalNo.startsWith('TEBS')) {
    def sumPremium = data['sumPremium'] as String
    def totalCharge = entity.totalCharge.add(sumPremium.toBigDecimal()).setScale(2, BigDecimal.ROUND_HALF_UP)
    entity.totalCharge = totalCharge
    entity.misc.nonMotor.discountCharge = sumPremium.toBigDecimal()
    entity.misc.nonMotor.accidentProposeCode = nonMotorProposalNo
} else {
    autoTask.tempValues['nonMotorProposalNo'] = nonMotorProposalNo
}


