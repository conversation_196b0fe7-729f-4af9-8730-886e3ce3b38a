package renbao.edi




def script =  new edi_common_2005()
def common = new edi_nscommon_2005()
def cur = script.getTimePattern("yyyy-MM-dd HH:mm:ss")
def sessionId = root.Package.Header.SessionId.toString()
def order = root.Package.Response.Order
def tBOrderId = order.TBOrderId.toString()
def subOrders = order.SubOrderList.SubOrder
def biztBOrderId
def bizPremium
def bizProposalNo
def efctBOrderId
def efcPremium
def efcProposalNo
subOrders.each { sub ->
    if (sub.'@type' == 'biz') {
        bizProposalNo = sub.ProposalNo.toString()
        bizPremium =  sub.Premium.toString()
        biztBOrderId = sub.TBOrderId.toString()

    }
    if (sub.'@type' == 'force') {
        efcProposalNo = sub.ProposalNo.toString()
        efcPremium = sub.Premium.toString()
        efctBOrderId = sub.TBOrderId.toString()
    }
}
def premium = new BigDecimal(0.00)
if (efcProposalNo)
    premium = premium.add(efcPremium?.toBigDecimal())
if (bizProposalNo)
    premium = premium.add(bizPremium?.toBigDecimal())
def requestXml = """
<?xml version="1.0" encoding="GBK" standalone="yes"?>
<PackageList>
    <Package>
        <Header>
            <Version>2</Version>
            <RequestType>230</RequestType>
            <InsureType>100</InsureType>
            <SessionId>${sessionId}</SessionId>
            <SellerId>230</SellerId>
            <SendTime>${cur}</SendTime>
            <Status>100</Status>
            <ErrorMessage/>
        </Header>"""
def request = """<Request><Order><TBOrderId>${tBOrderId}</TBOrderId><Premium>${premium.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue().toString()}</Premium><PayNo/><SubOrderList>"""
if (bizProposalNo) {
    request += """<SubOrder type="biz"><TBOrderId>${biztBOrderId}</TBOrderId><ItemId/><Premium>${bizPremium}</Premium><ProposalNo>${bizProposalNo}</ProposalNo></SubOrder>"""
}
if (efcProposalNo) {
    request += """<SubOrder type="force"><TBOrderId>${efctBOrderId}</TBOrderId><ItemId/><Premium>${efcPremium}</Premium><ProposalNo>${efcProposalNo}</ProposalNo></SubOrder>"""
}
request += """</SubOrderList></Order></Request>"""
requestXml += request
def sign = common.encrypt(request)
requestXml += """<Sign>${sign}</Sign></Package></PackageList>"""
