package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import taipingyang.robot.module.Robot2011Util

@BaseScript BaseScript_Http_Enq _

def TbUtil = new common_2011()
def saveResult = (String) autoTask.backRoot;
saveResult = Robot2011Util.decodeBody(tempValues, saveResult)
JSONObject saveResultObj = JSON.parseObject(saveResult);

if (!autoTask.tempValues.quotationNo) {
    def data = saveResultObj.getJSONObject("result")
    autoTask.tempValues.put('quotationNo', data.getString("quotationNo"))
}

//URL
boolean ExistBI = null != autoTask.tempValues.ExistBI ? autoTask.tempValues.ExistBI : null != entity.order.suiteInfo.bizSuiteInfo;
boolean ExistCI = null != autoTask.tempValues.ExistCI ? autoTask.tempValues.ExistCI : null != entity.order.suiteInfo.efcSuiteInfo;

//报价流程不回写单号
if (!autoTask.getTaskType().endsWith("quote")) {
    String billNo = (String) TbUtil.getFromJson(saveResultObj, "result.quotationNo");

    if (ExistBI && !ExistCI) {
        entity.setBizProposeNum(billNo)
    } else if (ExistCI && !ExistBI) {
        entity.setEfcProposeNum(billNo)
    } else if (ExistBI && ExistCI) {
        entity.setBizProposeNum(billNo)
        entity.setEfcProposeNum(billNo)
    }
    autoTask.taskEntity = entity

    JSONObject submitInsureInfoParam = JSON.parse(TbUtil.getBaseParam());
    submitInsureInfoParam.getJSONObject("redata").put("quotationNo", billNo);
    if (null != autoTask.configs.get("mac_address"))
        submitInsureInfoParam.getJSONObject("redata").put("macAddress", autoTask.configs.get("mac_address"));
    submitInsureInfoParam.getJSONObject("redata").put("terminalNo", autoTask.configs.get("mac_address"));
    autoTask.tempValues.put("submitInsureInfoParam", submitInsureInfoParam.toJSONString());
}

