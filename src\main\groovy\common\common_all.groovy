package common

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.constants.Constants
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.car.BaseSuiteInfo
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.RedisUtil
import com.cheche365.bc.utils.sdas.SDASUtils
import com.cheche365.bc.utils.sender.HttpSender
import com.google.common.base.Joiner
import com.google.common.collect.Lists
import groovy.transform.Memoized
import org.apache.commons.lang3.StringUtils
import org.apache.http.entity.ByteArrayEntity
import org.apache.pdfbox.multipdf.PDFMergerUtility
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.util.Assert

import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.spec.SecretKeySpec
import java.security.NoSuchAlgorithmException
import java.security.SecureRandom
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.regex.Pattern

/**
 * 替换验证码字符
 * @param code
 * @return
 */
static def transformationCode(code) {
    if (code) {
        code = code.replace('0', 'O').replace('l', "1")
    } else {
        throw new InsReturnException("效验码为空")
    }
}
/**
 * 车辆使用年限
 * @param start 初登日期
 * @param end 商业或交强投保起期
 * @return
 */
static def useYear(start, end) {
    def dt1 = start instanceof Date ? start.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()
            : LocalDateTime.parse(start as String, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
    def dt2 = LocalDateTime.parse(end as String, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
    def year = ChronoUnit.YEARS.between(dt1, dt2)
    if (year == 0) {
        def month = ChronoUnit.MONTHS.between(dt1, dt2)
        return month < 9 ? 0 : 1
    }
    return year
}
/**
 * 初始化特殊证件类型
 * @param enquiry 精灵为Enquiry类型,EDI为Map类型
 * @param needCheckedIdCardType 需要检查的类型集合,
 * 例:利宝公司,港澳回乡证 与 香港身份证,台胞证 均对应出单系统的其他证件类型,所以回写回来后只需在needCheckedIdCardType中添加 类型7即可
 */
static def checkIdCardType(enquiry, needCheckedIdCardType) {

    if (needCheckedIdCardType) {
        def personList
        if (enquiry instanceof Enquiry) {
            personList = [enquiry?.order?.carOwnerInfo, enquiry?.order?.insurePerson, enquiry?.order?.insuredPersons.get(0)]
        } else {
            personList = [enquiry?.carOwnerInfo, enquiry?.insurePerson, enquiry?.insuredPersonList.get(0)]
        }
        personList.each {
            def idCardType = it.idCardType
            def idCard = it.idCard
            if (needCheckedIdCardType.contains(idCardType)) {
                idCard = idCard.replace("(", "").replace(")", "")
                if (idCard.length() == 11) {
                    it.idCardType = 5 //港澳回乡证
                } else if (idCard.length() == 8) {
                    idCard = idCard.toUpperCase()
                    char firstCode = idCard.charAt(0)
                    if (firstCode <= 90 && firstCode >= 65) {
                        it.idCardType = 11//香港身份证
                    } else {
                        it.idCardType = 12//台胞证
                    }
                }
            }
        }
    }
}

//文件前缀截取
static def bytesToHexString(byte[] src) {
    def builder = new StringBuilder()
    if (src == null || src.length <= 0) {
        return null
    }
    String hv
    for (int i = 0; i < src.length; i++) {
        // 以十六进制（基数 16）无符号整数形式返回一个整数参数的字符串表示形式，并转换为大写
        hv = Integer.toHexString(src[i] & 0xFF).toUpperCase()
        if (hv.length() < 2) {
            builder.append(0)
        }
        builder.append(hv)
    }
    return builder.toString()
}

/**
 * 判断是否重复投保，重复投保时自动修正时间
 * @param enquiry 请求实体
 * @param msg 保险公司提示信息
 * @return boolean，是否重复投保
 */
//case1: msg = "商业险重复投保:保单号:PDAA20204521D000006444；起保日期:2020-05-11；终保日期:2021-05-11。交强险平台返回异常：车牌号“桂AF530H”的保单发生重复投保，与其重复投保的本公司的保单信息如下：投保确认码 02PICC450020001588211216528316保单号 PDZA20204521D000006546起保日期 2020-05-10 17:00终保日期 2021-05-10 17:00车牌号 桂AF530H号牌种类 02车架号 LHMB4DGN3HA011038发动机号 A049289地区 450000。"
//case2: 失败:平台校验：警告：车牌号“川AUV956”的保单发生重复投保，与其重复投保的其它公司的保单信息如下：投保确认码 02CHAC510020001585882825524290保单号 6020000080120200007857起保日期 2020-04-30 00:00终保日期 2021-04-30 00:00车牌号 川AL88K8号牌种类 02车架号 LGBF5AE05DR201624发动机号 822914T地区 510000。
static def timeAdjust(enquiry, msg) {
    if (!msg.contains('重复投保')) {
        return false
    }
    def suiteInfo = enquiry instanceof Enquiry ? enquiry.order.suiteInfo : enquiry.baseSuiteInfo as BaseSuiteInfo
    def repeatReg = '终保.*?(\\d{4}-\\d{2}-\\d{2}( \\d{2}:\\d{2}(:\\d{2})?)?)'
    def efcReg = "交强险.*?${repeatReg}"
    def bizReg = "商业险.*?${repeatReg}"
    if (matcherDate(msg, efcReg, suiteInfo?.efcSuiteInfo).mathes |
            matcherDate(msg, bizReg, suiteInfo?.bizSuiteInfo).mathes) { // case1
        return true
    } else { // case2
        return matcherDate(msg, repeatReg, suiteInfo?.efcSuiteInfo).mathes |
                matcherDate(msg, repeatReg, suiteInfo?.bizSuiteInfo).mathes
    }
}

static def matcherDate(msg, repeatReg = '终保.*?(\\d{4}-\\d{2}-\\d{2}( \\d{2}:\\d{2}(:\\d{2})?)?)', suite = [:]) {
    def matcher = Pattern.compile(repeatReg).matcher(msg as String)
    if (!matcher.find()) return [mathes: false]
    def dtf = DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss')
    def strEndDate = matcher.group(1) + (matcher.group(3) ? ""
            : matcher.group(2) ? ':00'
            : ' 00:00:00')
    def endDate = LocalDateTime.parse(strEndDate, dtf)
    suite?.start = endDate.format(dtf)
    if (strEndDate.contains('23:59'))
        suite?.start = endDate.toLocalDate().plusDays(1).toString() + ' 00:00:00'
    suite?.end = endDate.plusYears(1).format(dtf)
    [start: suite?.start, end: suite?.end, mathes: true]
}
//构建SDAS唯一标识 data
static def makeSDASDataRobot(autoTask, policyNo) {
    if (policyNo) {
        def enquiry = autoTask.getTaskEntity() as Enquiry
        def todayString = LocalDate.now().toString()
        def provinceCode = enquiry?.getOrder()?.getInsureArea()?.getProvince() ?: "-9999"
        def insuranceCode = autoTask.companyId?.substring(0, 4) ?: "-99999"
        return makePathList(insuranceCode, provinceCode, todayString, policyNo)
    }
    throw new InsReturnException("保单号为空")
}

static def makeSDASDataEDI(enquiry, policyNo) {
    if (policyNo) {
        def todayString = LocalDate.now().toString()
        def provinceCode = enquiry?.insArea?.province ?: "-9999"
        def insuranceCode = enquiry?.orgCode ?: "-99999"
        return makePathList(insuranceCode, provinceCode, todayString, policyNo)
    }
    throw new InsReturnException("保单号为空")
}

static def makePathList(insuranceCode, provinceCode, todayString, policyNo) {
    def pathList = []
    pathList.add("river_attachment")
    pathList.add(insuranceCode)
    pathList.add(provinceCode)
    pathList.add(todayString)
    def path = Joiner.on("/").join(pathList).concat("/")
    String data = path + policyNo + "_" + System.currentTimeMillis() + ".pdf"
    data
}


/**
 * 某些公司如人保四代，登录后所有请求头需要带token，登录后将token存redis，有效期使用者自行设置，key规则：头:保司编号:登录账号,time:有效期单位秒
 * @param comId 保司编号
 * @param orgCode 机构编号
 * @param login 配置信息中的login即登录账号
 * @param token 登录后保司返回的token
 * @param time 缓存有效期，单位秒
 * @return
 */

static def setToken(comId, login, token, time) {
    def key = Constants.SESSION_CACHE_KEY_PREFIX + comId + ":" + login
    if (!time)
        time = 1800
    RedisUtil.set(key, token, time)
}

static def getToken(comId, login) {
    def key = Constants.SESSION_CACHE_KEY_PREFIX + comId + ":" + login
    RedisUtil.get(key)
}

// 把两个pdf合并成一个 frist,second传入的都是byte[]
static def mergePDF(first, second) {
    def byteOutput = new ByteArrayOutputStream(12)
    def inputFirst = null
    def inputSecond = null

    try {
        inputFirst = new ByteArrayInputStream(first)
        inputSecond = new ByteArrayInputStream(second)

        def mergerPDF = new PDFMergerUtility()
        mergerPDF.addSource(inputFirst)
        mergerPDF.addSource(inputSecond)
        mergerPDF.setDestinationStream(byteOutput)
        mergerPDF.mergeDocuments()

        return byteOutput.toByteArray()
    } finally {
        // 关闭所有流
        if (inputFirst != null) inputFirst.close()
        if (inputSecond != null) inputSecond.close()
        if (byteOutput != null) byteOutput.close()
    }
}

static def policyQueryByTypeInitTimeParams(autoTask, daysToAdd) {
    if (autoTask?.configs?.queryType) {
        String now = LocalDate.now()
        autoTask?.configs?.startDate = now.toString()
        autoTask?.configs?.endDate = LocalDate.now().plusDays(daysToAdd).toString()
    }
}


static def policyQueryByTypeInitListParams(autoTask, converterKey) {
    def batchNumber = autoTask?.configs?.policyNoList ?: autoTask?.configs?.licenseList ?: autoTask?.configs?.vinList
    def paramMap = [:]
    paramMap.batchValues = batchNumber.split(",")
    if (StringUtil.isNoEmpty(autoTask?.configs?.policyNoList))
        paramMap.queryKey = converterKey[1]
    else if (StringUtil.isNoEmpty(autoTask?.configs?.licenseList))
        paramMap.queryKey = converterKey[2]
    else if (StringUtil.isNoEmpty(autoTask?.configs?.vinList))
        paramMap.queryKey = converterKey[3]
    else
        throw new InsReturnException("保单抓取失败，条件为空！")
    paramMap
}

/**
 * 互联互通保单回调加密解密工具类
 */
static def encrypt(content, password) throws Exception {
    def DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding"// 默认的加密算法
    def cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM)// 创建密码器

    byte[] byteContent = content.getBytes("utf-8")

    cipher.init(Cipher.ENCRYPT_MODE, getSecretKey(password))// 初始化为加密模式的密码器

    byte[] result = cipher.doFinal(byteContent)// 加密

    return parseByte2HexStr(result) // 转换成16进制返回
}

static def decrypt(content, password) throws Exception {
    def DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding"// 默认的加密算法
    // 实例化
    def cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM)

    // 使用密钥初始化，设置为解密模式
    cipher.init(Cipher.DECRYPT_MODE, getSecretKey(password))

    // 执行操作
    byte[] result = cipher.doFinal(parseHexStr2Byte(content))

    return new String(result, "utf-8")
}


/**
 * 生成加密秘钥
 *
 * @return
 */
static def getSecretKey(password) throws NoSuchAlgorithmException {
    def KEY_ALGORITHM = "AES"
    // 返回生成指定算法密钥生成器的 KeyGenerator 对象
    def kg = KeyGenerator.getInstance(KEY_ALGORITHM)
    def secureRandom = SecureRandom.getInstance("SHA1PRNG")
    secureRandom.setSeed(password.getBytes())
    // AES 要求密钥长度为 128
    kg.init(128, secureRandom)
    // 生成一个密钥
    def secretKey = kg.generateKey()
    return new SecretKeySpec(secretKey.getEncoded(), KEY_ALGORITHM)// 转换为AES专用密钥
}

/**
 * 将16进制转换为二进制*
 */
static def parseHexStr2Byte(String hexStr) {
    if (hexStr.length() < 1)
        return null
    byte[] result = new byte[hexStr.length() / 2]
    for (int i = 0; i < hexStr.length() / 2; i++) {
        int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16)
        int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16)
        result[i] = (byte) (high * 16 + low)
    }
    return result
}

/**
 * 将二进制转换成16进制*
 */
static def parseByte2HexStr(byte[] buf) {
    def sb = new StringBuffer()
    for (int i = 0; i < buf.length; i++) {
        def hex = Integer.toHexString(buf[i] & 0xFF)
        if (hex.length() == 1) {
            hex = '0' + hex
        }
        sb.append(hex.toUpperCase())
    }
    return sb.toString()
}
//澎湃保互联互通调用方法
static def sendData(enquiry, simplifyInfo, urlKey, companyName) {
    Logger LOG = LoggerFactory.getLogger("sendDatatoPengPaiBao")
    def pParams = """{
                    "failedToast": false,
                    "restCode": 1,
                    "restContext": {
                        "data": ${new JSONObject().toJSONString(simplifyInfo)}
                     },
                    "success": true
                }"""

    def _URL_MAPPING = [
            'cPUrl'    : 'https://saas.pengpaibao.com/baoying-car-insurance/tasks/sync', //车险澎湃保地址
            'fcPUrl'   : 'https://saas.pengpaibao.com/baoying-life-insurance/tasks/sync', //非车险澎湃保地址
            'cpPUrl'   : 'https://saas.pengpaibao.com/baoying-car-insurance/tasks/endorse', //车险批单澎湃保地址
            'fpPUrl'   : 'https://saas.pengpaibao.com/baoying-life-insurance/tasks/endorse', //非车险批单澎湃保地址
            'claimPUrl': 'https://saas.pengpaibao.com/baoying-car-insurance/tasks/claims'    //理赔澎湃保地址
    ]
    def url = _URL_MAPPING[(urlKey)]
    LOG.info("澎湃保地址：{}，保司{}推送互联互通数据至澎湃保,发送报文为：{}", url, companyName, pParams)
    try {
        def response = HttpSender.doPost(url, pParams)
        def jsonObj = JSONObject.parseObject(response)
        if (jsonObj && jsonObj['code'] == 200)
            enquiry['taskStatus'] = 'D'
        else
            enquiry['taskStatus'] = 'D1'
        LOG.info("保司{}互联互通数据推送澎湃保，返回报文：{}", companyName, response)
        pParams
    } catch (Exception e) {
        enquiry['taskStatus'] = 'D1'
        return "澎湃保地址：" + url + "，保司" + companyName + "推送互联互通数据至澎湃保,推送失败：" + e.toString()
    }
}

static def getppbUrl() {
    //车险澎湃保地址
    String cPUrl = "https://saas.pengpaibao.com/baoying-car-insurance/tasks/sync"
    //非车险澎湃保地址
    String fcPUrl = "https://saas.pengpaibao.com/baoying-life-insurance/tasks/sync"
    //车险批单澎湃保地址
    String cpPUrl = "https://saas.pengpaibao.com/baoying-car-insurance/tasks/endorse"
    //非车险批单澎湃保地址
    String fpPUrl = "https://saas.pengpaibao.com/baoying-life-insurance/tasks/endorse"
    //理赔澎湃保地址
    String claimPUrl = "https://saas.pengpaibao.com/baoying-car-insurance/tasks/claims"
    cPUrl + "," + fcPUrl + "," + cpPUrl + "," + fpPUrl + "," + claimPUrl
}


static def getKindName(key) {
    def kind = [
            'VehicleDamage'                : '机动车损失险',
            'ThirdParty'                   : '第三者责任险',
            'Driver'                       : '司机责任险',
            'Passenger'                    : '乘客责任险',
            'Wheel'                        : '附加车轮单独损失险',
            'ExtraDevice'                  : '附加新增加设备损失险',
            'Scratch'                      : '附加车身划痕损失险',
            'SpecifyingPlant'              : '附加保险人指定修理厂特约险',
            'CompensationDuringRepair'     : '附加修理期间费用补偿险',
            'EngineDamageExclude'          : '发动机损坏除外特约条款',
            'GoodsOnVehicle'               : '车上货物责任险',
            'CompensationForMentalDistress': '精神损害抚慰金责任险',
            'CFMDThirdParty'               : '精神损害抚慰金责任险(三者险)',
            'CFMDDriver'                   : '精神损害抚慰金责任险(司机险)',
            'CFMDPassenger'                : '精神损害抚慰金责任险(乘客险)',
            'HolidayDouble'                : '附加法定节假日限额翻倍险 ',
            'NonInHealthCare'              : '附加医保外用药责任险',
            'NIHCThirdParty'               : '附加医保外用药责任险(三者险)',
            'NIHCDriver'                   : '附加医保外用药责任险(司机险)',
            'NIHCPassenger'                : '附加医保外用药责任险(乘客险)',
            'RoadsideService'              : '道路救援服务特约条款',
            'VehicleInspection'            : '车辆安全检测特约条款',
            'DesignatedDriving'            : '代为驾驶服务特约条款',
            'SendForInspection'            : '代为送检服务特约条款',
            'ANCVehicleDamage'             : '附加绝对免赔率特约条款(机动车损失保险)',
            'ANCThirdParty'                : '附加绝对免赔率特约条款(机动车第三者责任保险)',
            'ANCDriver'                    : '附加绝对免赔率特约条款(机动车车上人员责任保险(司机))',
            'ANCPassenger'                 : '附加绝对免赔率特约条款(机动车车上人员责任保险(乘客))',
            'ANCInGeneral'                 : '附加绝对免赔率特约条款',
            'SVTheft'                      : '特种车全车盗抢保险',
            'SVEquipmentExt'               : '特种车辆固定设备、仪器损坏扩展条款',
            'SVDamageExt'                  : '起重、装卸、挖掘车辆损失扩展条款',
            'NEGridBugDamage'              : '附加外部电网故障损失险',
            'NEChargerDamage'              : '附加自用充电桩损失保险',
            'NEChargerDuty'                : '附加自用充电桩责任保险',
            'NEFireDouble'                 : '附加火灾事故限额翻倍险',
            'NESoftwareDamage'             : '附加智能辅助驾驶软件损失补偿险'

    ]
    return kind[(key)]
}

// 通过品牌信号+车牌号判断能源类型
static def checkEnergyType(carModelName, plateNum) {
    def type = '0'
    if (carModelName.contains('纯电')) {
        type = '1'
    } else if (carModelName.contains('插电')) {
        type = '3'
    } else {
        if (plateNum.size() == 8) {
            if (plateNum[2] == 'D') {
                type = '1'
            }
            if (plateNum[2] == 'F') {
                type = '3'
            }
        }
    }
    type
}

static def getSupplyParam(enquiry, key) {
    def result = enquiry?.supplyParam?.find {
        it['itemcode'] == key
    }
    result ? result['itemvalue'] : null
}

static def nonLocalFlag(enquiry) {
    def province
    def plateNum
    def _PROVINCE_PREFIX_MAPPING = [
            '130000': '冀',
            '140000': '晋',
            '150000': '蒙',
            '210000': '辽',
            '220000': '吉',
            '230000': '黑',
            '320000': '苏',
            '330000': '浙',
            '340000': '皖',
            '350000': '闽',
            '360000': '赣',
            '370000': '鲁',
            '410000': '豫',
            '420000': '鄂',
            '430000': '湘',
            '440000': '粤',
            '450000': '桂',
            '460000': '琼',
            '510000': '川',
            '520000': '贵',
            '530000': '云',
            '540000': '藏',
            '610000': '陕',
            '620000': '甘',
            '630000': '青',
            '640000': '宁',
            '650000': '新',
            '710000': '台',
            '110000': '京',
            '310000': '沪',
            '120000': '津',
            '500000': '渝'
    ]
    if (enquiry instanceof Map) {
        province = enquiry.insArea['province']
        plateNum = enquiry.carInfo['plateNum']
    } else {
        def entity = (Enquiry) enquiry
        province = entity.order.insureArea.province
        plateNum = entity.order.carInfo.plateNum
    }
    if ('新车未上牌' == plateNum)
        return true
    def prefix = _PROVINCE_PREFIX_MAPPING[(province)]
    return plateNum.startsWith(prefix)
}

static def getAddress(autoTask, key) {
    def addressKey = [
            'applicantAddress',
            'insuredAddress',
            'ownerAddress'
    ]
    def applyJson = JSON.parse(autoTask.applyJson as String)
    def supplyParam = applyJson['supplyParam']
    def result = supplyParam.find {
        it['itemcode'] == key
    }
    if (!result) {
        addressKey -= key
        result = supplyParam.find { it['itemcode'] == addressKey[0] } ?: supplyParam.find { it['itemcode'] == addressKey[1] }
    }
    if (result) {
        def address = result['itemvalue'] as String
        def reg = /^(?<provinceName>.*?)\|(?<provinceCode>\d{6})#(?<cityName>.*?)\|(?<cityCode>\d{6})/ + /#(?<districtName>.*?)\|(?<districtCode>\d{6})#(?<address>.*)/
        def matcher = address =~ reg
        if (matcher.find()) {
            def map = [
                    'provinceName': matcher.group('provinceName'),
                    'province': matcher.group('provinceCode'),
                    'cityName'    : matcher.group('cityName'),
                    'city'    : matcher.group('cityCode'),
                    'districtName': matcher.group('districtName'),
                    'district': matcher.group('districtCode'),
                    'address'     : matcher.group('address')
            ]
            return map
        }
    }
}

/**
 * 计算两个时间间隔的日和月数量
 * @param start
 * @param end
 * @return
 */
static def timeInterval(start, end) {
    def startDate = start.length() == 10 ? LocalDate.parse(start as String) : LocalDateTime.parse(start as String, DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss'))
    def endDate = start.length() == 10 ? LocalDate.parse(end as String) : LocalDateTime.parse(end as String, DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss'))
    def dayDiff = ChronoUnit.DAYS.between(startDate, endDate)
    def monthDiff = ChronoUnit.MONTHS.between(startDate, endDate)
    [
            dayDiff  : dayDiff,
            monthDiff: monthDiff
    ]
}

/**
 * 时间格式转换
 * @param input
 * @param inputPattern
 * @param outputPattern
 * @return
 */
static def timeFormat(input, inputPattern, outputPattern) {
    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(inputPattern as String)
    def local
    def output
    def _DATE_PATTERN = [
            'yyyy/MM/dd',
            'yyyy-MM-dd',
            'yyyyMMdd'
    ]
    def _DATETIME_PATTERN = [
            'yyyy-MM-dd HH:mm:ss.SSS',
            'yyyy-MM-dd HH:mm:ss',
            'yyyy-MM-dd HH:mm:ss.SSS \'CST\'',
            'yyyy-MM-dd HH:mm',
            'yyyyMMddHHmm'
    ]
    if (_DATE_PATTERN.contains(inputPattern)) {
        local = LocalDate.parse(input as String, dateTimeFormatter)

    }
    if (_DATETIME_PATTERN.contains(inputPattern)) {
        local = LocalDateTime.parse(input as String, dateTimeFormatter)
    }
    if (_DATE_PATTERN.contains(outputPattern)) {
        if (local instanceof LocalDate) {
            output = local.format(DateTimeFormatter.ofPattern(outputPattern as String))
        } else if (local instanceof LocalDateTime) {
            output = local.toLocalDate().format(DateTimeFormatter.ofPattern(outputPattern as String))
        }
    }
    if (_DATETIME_PATTERN.contains(outputPattern)) {
        if (local instanceof LocalDate) {
            LocalDateTime time = local.atTime(0, 0, 0)
            output = time.format(DateTimeFormatter.ofPattern(outputPattern as String))
        } else if (local instanceof LocalDateTime) {
            output = local.format(DateTimeFormatter.ofPattern(outputPattern as String))
        }
    }
    output
}

/**
 * 传入时间加减年日，月
 * @param sDate
 * @param dayPlus
 * @param yearPlus
 * @return
 */
static def arithmeticDay(sDate, dayPlus, monthPlus, yearPlus) {
    def date = LocalDate.parse(sDate)
    date.plusYears(yearPlus).plusMonths(monthPlus).plusDays(dayPlus).toString()
}


/**
 * 根据身份证获取性别 1 男， 2 女
 * @param idCardNumber
 * @return
 */
static def getSex(idCardNumber) {
    if ((idCardNumber as String).length() != 18)
        throw new InsReturnException('无法获取当前关系人性别')
    def sexNum = idCardNumber.substring(16, 17) as int
    return sexNum % 2 == 0 ? 2 : 1
}

/**
 * 根据身份证获取年龄
 * @param idCardNumber
 * @return
 */
static def getAge(idCardNumber) {
    if ((idCardNumber as String).length() != 18)
        throw new InsReturnException('无法获取当前关系人年龄')
    def birthday = idCardNumber.substring(6, 14)
    return LocalDate.parse(birthday as String, DateTimeFormatter.ofPattern('yyyyMMdd')).until(LocalDate.now()).years
}

/**
 * 根据身份证获取生日
 * @param idCardNumber
 * @return
 */
static def getBirth(idCardNumber, String pattern = 'yyyyMMdd') {
    if ((idCardNumber as String).length() != 18) {
        throw new InsReturnException('无法获取当前关系人出生日期')
    }
    def birthday = idCardNumber.substring(6, 14)
    return LocalDate.parse(birthday as String, DateTimeFormatter.ofPattern(pattern)).toString()
}

/**
 * 获取共享保额对应主险的保额
 * @param enquiry
 * @return
 */
static def shareAmount(enquiry) {
    def suiteList = []
    if (enquiry instanceof Map) {
        suiteList = enquiry?.baseSuiteInfo?.bizSuiteInfo?.suites

    } else {
        suiteList = JSONArray.parseArray(JSON.toJSONString(Lists.newArrayList((enquiry as Enquiry).order.suiteInfo.bizSuiteInfo.suites.values())))
    }
    def share = suiteList.findAll { suite ->
        suite?.share
    }.collect { suite ->
        suite.code - 'NIHC'
    }
    if (share) {
        def nihc = suiteList.findAll { suite ->
            share.contains(suite.code)
        }.collect { suite ->
            [(suite.code): suite.amount]
        }.collectEntries()
        return nihc
    }
    return null
}

/**
 * 文件包含保单，发票等上传obs结果处理
 * @param enquiry
 * @param sourceType efc,biz,nonMotor,efcInvoice,bizInvoice,nonMotorInvoice
 * @param byteArr
 * @param companyId
 * @param businessId 车险保单号，车险投保单号，非车险保单号，非车险投保单号
 * @return
 */
static def uploadOBS(enquiry, sourceType, byteArr, companyId, businessId) {
    def fileName = UUID.randomUUID().toString() + '.pdf'
    def city = enquiry instanceof Map ? enquiry['insArea']['city'] : enquiry.order.insureArea.city
    def now = LocalDate.now().toString()
    def filePath = 'river_attachment/' + companyId + '/' + sourceType + '/' + city + '/' + now + '/' + businessId + '_' + fileName
    def assetResult = SDASUtils.assetOccupancy('application/pdf', fileName as String, filePath as String)
    if (!assetResult || assetResult.code != 0)
        throw new InsReturnException(InsReturnException.Others, '上传资产库占位失败')
    def byteArrayEntity = new ByteArrayEntity(byteArr as byte[])
    def uploadResult = SDASUtils.uploadPutAsset(fileName, assetResult, byteArrayEntity)
    if (uploadResult['code'] == '-9999')
        throw new InsReturnException(InsReturnException.Others, '文件上传资产中心失败')
    def sourceId = (uploadResult as JSONObject).getString('id')
    def obj = SDASUtils.getAsset(sourceId)
    def asset = (JSONObject) obj.getJSONObject("payload").getJSONArray("assets").get(0)
    def id = sourceType + 'Id'
    if (enquiry instanceof Map) {
        enquiry['SQ'][(id)] = sourceId
        if (sourceType.contains('Invoice')) {
            def sourceUrl = sourceType + 'SourceUrl'
            enquiry['SQ'][(sourceUrl)] = asset.getString("location")
        }
    } else {
        enquiry.misc[(id)] = sourceId
    }
}

/**
 * 当前方法不考虑低速货车三轮汽车
 * 车辆实际价值
 * @param autoTask
 */

static def carActualValue(autoTask) {
    def enquiry = autoTask.taskEntity
    def price
    def firstRegDate
    def seatCount
    def plateNum
    def carModelName
    def useProps
    def start
    if (enquiry instanceof Map) {
        def map = enquiry['enquiry'] as Map
        price = map['carInfo']['price'] as BigDecimal
        firstRegDate = LocalDateTime.parse(map['carInfo']['firstRegDate'] as String, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        seatCount = map['carInfo']['seatCnt']
        plateNum = map['carInfo']['plateNum']
        carModelName = map['carInfo']['carModelName']
        useProps = map['carInfo']['useProps']
        start = LocalDateTime.parse((map?.baseSuiteInfo?.bizSuiteInfo?.start ?: map?.baseSuiteInfo?.efcSuiteInfo?.start) as String, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
    } else {
        price = (enquiry as Enquiry).order.carInfo.price
        firstRegDate = Instant.ofEpochMilli((enquiry as Enquiry).order.carInfo.firstRegDate.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime()
        seatCount = (enquiry as Enquiry).order.carInfo.seatCnt
        plateNum = (enquiry as Enquiry).order.carInfo.plateNum
        carModelName = (enquiry as Enquiry).order.carInfo.carModelName
        useProps = (enquiry as Enquiry).order.carInfo.useProps
        start = LocalDateTime.parse(((enquiry as Enquiry).order?.suiteInfo?.bizSuiteInfo?.start ?: (enquiry as Enquiry).order?.suiteInfo?.efcSuiteInfo?.start), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
    }
    Assert.isTrue((Objects.nonNull(price) && (price as BigDecimal).doubleValue() > 0), '新车购置价为空不能计算车辆实际价值')
    def interval = ChronoUnit.MONTHS.between(firstRegDate, start)
    def isEnergy = checkEnergyType(carModelName, plateNum)
    //自定义类型
    def customType
    //家用及非营业
    if ([1, 8, 9, 10, 11, 12].contains(useProps)) {
        if ('0' == isEnergy) {
            customType = seatCount < 10 ? 'nineSeat' : 'tenSeat'
        } else {
            if ('3' == isEnergy) {
                customType = 'fcv'
            } else {
                def priceRange = Math.floor(((price as BigDecimal).doubleValue() - 1) / 100000)
                switch (priceRange) {
                    case 0:
                        customType = 'price1'
                        break
                    case 1:
                        customType = 'price2'
                        break
                    case 2:
                        customType = 'price3'
                        break
                    default:
                        customType = 'price4'
                        break
                }
            }
        }
    }

    //出租
    if ([2, 17].contains(useProps))
        customType = 'taxi'
    //其他
    if (!customType) {
        if (useProps == 6) {
            customType = 'businessFreight'
        } else {
            customType = 'businessPassenger'
        }
    }
    customType = customType ?: 'default'
    //系数
    def _FACTOR_MAPPING = [
            'price1'           : 0.0082, //新能源10万及以下
            'price2'           : 0.0077, //新能源10-20万
            'price3'           : 0.0072, //新能源20-30万及以下
            'price4'           : 0.0068, //新能源30万及以上
            'fcv'              : 0.0063, //插电混动
            'nineSeat'         : 0.006, //10座以下
            'taxi'             : 0.011,  //出租
            'tenSeat'          : 0.009, //10座及以上
            'businessFreight'  : 0.011, //其他货车
            'businessPassenger': 0.009,//其他客车
            'default'          : 0.011 //默认
    ]

    def factor = _FACTOR_MAPPING[(customType)]
    def useMonth = '0' == isEnergy ? interval : interval + 2
    useMonth = useMonth > 132 ? 132 : useMonth
    //折旧率 最高80%
    def depreciation = useMonth * factor > 0.8 ? 0.8 : useMonth * factor
    return (price - (price * depreciation) as BigDecimal).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()

}

/**
 * 关系人手机号码，邮箱，地址 补充信息的获取，传进来的key 如果可以取到 就用取到的 没取到 按照这个顺序找applicant->owner->insured
 * @param autoTask
 * @param supplyParamKey
 * @return
 */

static def getPersonSupply(autoTask, supplyParamKey) {
    def infoPrefix = [
            'applicant',
            'owner',
            'insured'
    ]
    def applyJson = JSON.parse(autoTask.applyJson as String)
    def supplyParam = applyJson['supplyParam']
    def result = supplyParam.find {
        it['itemcode'] == supplyParamKey
    }
    if (!result) {
        def curPrefix = infoPrefix.find { (supplyParamKey as String).startsWith(it) }
        def endString = supplyParamKey - curPrefix
        infoPrefix -= curPrefix
        result = supplyParam.find { it['itemcode'] == infoPrefix[0] + endString } ?: supplyParam.find { it['itemcode'] == infoPrefix[1] + endString }
    }
    if (result) {
        if (supplyParamKey.contains('Address')) {
            def address = result['itemvalue']
            def matcher = address =~ /\|\d+#/
            return [
                    'province': matcher ? (matcher[0] - '|' - '#') : '',
                    'city'    : matcher ? (matcher[1] - '|' - '#') : '',
                    'district': matcher ? (matcher[2] - '|' - '#') : '',
                    'detail'  : address.replaceAll('\\|\\d{6}+#', '')
            ]

        } else {
            return result['itemvalue']
        }
    }
    return null
}

static def getMobile() {
    def telPrefix = [134, 135, 136, 137, 138, 139, 150, 151, 152, 157, 158, 159, 130, 131, 132, 155, 156, 133, 153, 170, 162, 185, 178, 187, 167, 171, 186, 182]
    def first = telPrefix[new Random().nextInt(telPrefix.size())] + ''
    def second = new Random().nextInt(900) + 100 + ''
    def third = new Random().nextInt(9100) + 10000 + ''
    return first + second + third
}

static def dateToString(date, pattern) {
    def instant = date.toInstant()
    def datetime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime()
    return datetime.format(DateTimeFormatter.ofPattern(pattern))
}

/**
 * 地址校验
 * @param address
 */
static def checkAddress(address) {
    if (!address.contains('#') || !address.contains('|')) {
        throw new InsReturnException('地址需传省市区编码')
    }
}

/**
 * 行业车型编码转换为精友编码
 * @param processType 任务类型edi,robot 必传
 * @param platModelCode 行业车型编码 必传
 * @param carModelName 车型名称 必传
 * @param companyCode 需要使用哪个保司的查车能力 必传
 * @param searchCompanyCode 需要查哪个保司对应的精友编码 必传
 * @param vin 车架号
 * @return
 */
static def platModelCodeToJyCode(processType, platModelCode, carModelName, companyCode, searchCompanyCode, vin) {
    if (StringUtils.isAnyBlank(processType, platModelCode, carModelName, companyCode, searchCompanyCode))
        return null
    def env = System.getProperty('spring.profiles.active')
    def urlPrefix = env == 'production' ? 'https://auto.api.chetimes.com/jyCode' : 'https://auto.itg.api.chetimes.com/jyCode'
    def url = urlPrefix + "?processType=" + processType + "&platVehicleCode=" + platModelCode + "&carModelName=" + carModelName + "&vinCode=" + vin + "&companyCode=" + companyCode + "&searchCompanyCode=" + searchCompanyCode
    def result = HttpSender.doGet(url as String, null) as String
    if (result.contains('carModelId')) {
        def arr = JSON.parseObject(result).getJSONObject("data").getJSONArray("data")
        return arr.collect { it['code'] }
    }
    return null
}

/**
 * 是否跳过新车备案
 */
static def skipNewCarRegistration(enquiry) {
    if ('新车未上牌' != enquiry?.carInfo?.plateNum) {
        return true
    }
    return !(needRegistrationProvinces(enquiry?.insArea?.province) || needRegistrationCities(enquiry?.insArea?.city))
}

/**
 * 省或者直辖市
 * 北京，辽宁，新疆，湖南，陕西，内蒙，黑龙江，河北，吉林，河南，海南，云南，青海，广西，贵州，甘肃，山西省，重庆市，江西省，安徽省，湖北省，福建省新车需要备案
 */
@Memoized
static def needRegistrationProvinces(def province) {
    return ['110000', '210000', '430000', '650000', '610000', '150000', '230000', '130000', '220000', '410000',
            '460000', '530000', '630000', '450000', '520000', '620000','140000','500000', '360000', '340000',
            '420000', '350000'].contains(province)
}

/**
 * 城市
 * 宁波,新车需要备案
 */
@Memoized
static def needRegistrationCities(def city) {
    return ['330200'].contains(city)
}
