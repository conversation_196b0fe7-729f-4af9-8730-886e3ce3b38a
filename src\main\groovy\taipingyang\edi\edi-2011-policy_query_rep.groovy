package taipingyang.edi

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import groovy.transform.BaseScript
import groovy.transform.Field
import taipingyang.edi.module.script.BaseScript_Map_2011

@BaseScript BaseScript_Map_2011 _

@Field private static final List<String> INVALID_POLICY_STATUSES = ['0', '2']

@Field private static final edi_2011_ask_charge_util util = new edi_2011_ask_charge_util()
/**
 * 交强险保单信息
 */
@Field private JSONObject trafficPolicy = null

/**
 * 商业险/新能源
 */
@Field private JSONObject businessPolicy = null
/**
 * 非车险
 */
@Field private JSONArray accidentPolicyList = null

check { result ->
    if (result?.getString('responseCode') != "0000") {
        fail("太保保单状态查询出错：" + result?.getString('responseMsg'))
    }

    def queryAuapPolicyStatusRes = result?.getJSONObject('queryAuapPolicyStatusRes')

    assertNotNull('太保保单状态查询出错：未返回保单状态信息', queryAuapPolicyStatusRes)

    if (queryAuapPolicyStatusRes.getJSONObject('payInfo')?.getString('payStatus') == '0') {
        fail('太保保单状态查询出错：未支付')
    }

    //交强险
    trafficPolicy = queryAuapPolicyStatusRes.getJSONObject('trafficPolicy')
    //商业/新能源
    if (queryAuapPolicyStatusRes.containsKey('newEnergyPolicy')) {
        businessPolicy = queryAuapPolicyStatusRes.getJSONObject('newEnergyPolicy')
        tempValues.NEFlag = '1'
    } else {
        businessPolicy = queryAuapPolicyStatusRes.getJSONObject('businessPolicy')
    }

    if ([trafficPolicy, businessPolicy].any { policyInfo -> policyInfo?.getString('policyStatus') in INVALID_POLICY_STATUSES }) {
        fail("太保保单状态查询出错：保单状态为未生效或者已删除")
    }

    accidentPolicyList = queryAuapPolicyStatusRes.getJSONArray('accidentPolicyList')

}

def SQ = enquiry['SQ'] = enquiry['SQ'] as Map ?: [:]

def totalCharge = BigDecimal.ZERO

enquiry?.SQ?.totalCharge = totalCharge

def baseSuiteInfo = enquiry['baseSuiteInfo'] as Map

handleEfcData(baseSuiteInfo, SQ, totalCharge)

handleBusinessData(baseSuiteInfo, SQ)

handleNonMotorData(SQ)

private void handleEfcData(Map baseSuiteInfo, Map SQ, BigDecimal totalCharge) {
    def efcSuiteInfo = baseSuiteInfo['efcSuiteInfo'] as Map ?: [:]
    def taxSuiteInfo = baseSuiteInfo['taxSuiteInfo'] as Map ?: [:]
    if (!trafficPolicy) {
        if (!efcSuiteInfo.isEmpty()) {
            baseSuiteInfo.remove('efcSuiteInfo')
        }
        if (!taxSuiteInfo.isEmpty()) {
            baseSuiteInfo.remove('taxSuiteInfo')
        }
        return
    }
    //投保单号
    SQ.efcProposeNum = trafficPolicy.getString('applicationNo')
    //保单号
    SQ.efcPolicyCode = trafficPolicy.getString('policyNo')
    //投保单确认时间
    SQ.policyEffectTime = util.TPYDateTimeToBC(trafficPolicy.getString('insuranceConfirmDate'))

    baseSuiteInfo['efcSuiteInfo'] = efcSuiteInfo
    baseSuiteInfo['taxSuiteInfo'] = taxSuiteInfo
    efcSuiteInfo.start = util.TPYDateTimeToBC(trafficPolicy.getString('startDate'))
    efcSuiteInfo.end = util.TPYDateTimeToBC(trafficPolicy.getString('endDate'))

    util.chargeEfcSuit(enquiry, trafficPolicy, totalCharge)
}

private void handleBusinessData(Map baseSuiteInfo, Map SQ) {
    def bizSuiteInfo = baseSuiteInfo['bizSuiteInfo'] as Map ?: [:]
    if (!businessPolicy) {
        if (!bizSuiteInfo.isEmpty()) {
            baseSuiteInfo.remove('bizSuiteInfo')
        }
        return
    }

    SQ.bizProposeNum = businessPolicy.getString('applicationNo')
    SQ.bizPolicyCode = businessPolicy.getString('policyNo')
    SQ.policyEffectTime = util.TPYDateTimeToBC(businessPolicy.getString('insuranceConfirmDate'))

    baseSuiteInfo['bizSuiteInfo'] = bizSuiteInfo
    bizSuiteInfo.start = util.TPYDateTimeToBC(businessPolicy.getString('startDate'))
    bizSuiteInfo.end = util.TPYDateTimeToBC(businessPolicy.getString('endDate'))

    util.chargeBizSuit(enquiry, businessPolicy, tempValues)
}

private void handleNonMotorData(Map SQ) {
    if (!accidentPolicyList) {
        return
    }
    def accidentPolicy = accidentPolicyList.getJSONObject(0)
    SQ.nonMotor?.accidentPolicyCode = accidentPolicy?.accPolicyNo
    SQ.nonMotor?.productCode = accidentPolicy?.accidentPlan
    SQ.nonMotor?.discountCharge = accidentPolicy?.accAmount

    util.chargeNonMotor(enquiry, accidentPolicy, null)
}