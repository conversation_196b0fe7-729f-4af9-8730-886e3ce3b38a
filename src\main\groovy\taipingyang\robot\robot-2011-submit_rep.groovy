package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.dama.Dama2Web
import com.cheche365.bc.utils.sender.HttpSender
import org.apache.http.impl.client.CloseableHttpClient
import taipingyang.robot.module.Robot2011Util


AutoTask autoTask = autoTask
def TbUtil = new common_2011()
Enquiry entity = (Enquiry) autoTask?.taskEntity;

boolean ExistBI = null != autoTask.tempValues.ExistBI ? autoTask.tempValues.ExistBI : null != entity.order.suiteInfo.bizSuiteInfo;
boolean ExistCI = null != autoTask.tempValues.ExistCI ? autoTask.tempValues.ExistCI : null != entity.order.suiteInfo.efcSuiteInfo;

Map header = new HashMap();
header.put("Content-Type", "application/json;charset=utf-8")

String submitInsureInfoResultStr = (String) autoTask.backRoot
submitInsureInfoResultStr = Robot2011Util.decodeBody(autoTask.tempValues, submitInsureInfoResultStr)
//提交核保
if (!submitInsureInfoResultStr.startsWith("{"))
    throw new InsReturnException("提交核保失败,平台提示为----" + submitInsureInfoResultStr)

JSONObject submitInsureInfoResultObj = JSON.parseObject(submitInsureInfoResultStr);
String flag = TbUtil.getFromJson(submitInsureInfoResultObj, "message.code").toString();
String errMsg = TbUtil.getFromJson(submitInsureInfoResultObj, "message.message");
if (errMsg.contains("核保保费和二次报价保费不一致") && !"true".equals(autoTask.tempValues.quote4submit)) {
    if (null != autoTask.configs.calculateTimeLimit) {
        Thread.sleep(60000);
    }
    //清楚报价完成的标记
    autoTask.tempValues.remove("calculateParamJson")
    autoTask.tempValues.remove("finishCalculate");
    autoTask.tempValues.remove("finishPTserch");
    //再次报价标记
    autoTask.tempValues.quote4submit = "true"
    InsReturnException errSumitException = new InsReturnException(InsReturnException.AllowRepeat, "核保提示重新计算保费")
    errSumitException.setStep(4)
    throw errSumitException
}

if ("failed".equals(flag)) {
    throw new InsReturnException("平台返回提示:" + TbUtil.getFromJson(submitInsureInfoResultObj, "message.message").toString())
}

//校验转保验证码
if (StringUtil.isNoEmpty(TbUtil.getFromJson(submitInsureInfoResultObj, "result.checkInfoVo.checkCode"))) {
    String submitCheckCode = TbUtil.getFromJson(submitInsureInfoResultObj, "result.checkInfoVo.checkCode");
    submitCheckCode = Dama2Web.getAuth(submitCheckCode, 1)
    JSONObject checkInfoVo = submitInsureInfoResultObj.getJSONObject("result").getJSONObject("checkInfoVo").clone()
    checkInfoVo.put("questionAnswer", submitCheckCode)
    JSONObject submitInsureInfoParam = JSON.parseObject(autoTask.tempValues.get("submitInsureInfoParam"))
    submitInsureInfoParam.getJSONObject("redata").put("checkInfoVo", checkInfoVo)
    autoTask.tempValues.put("submitInsureInfoParam", submitInsureInfoParam.toJSONString())
    throw new InsReturnException(InsReturnException.AllowRepeat, "提交核保转保验证码:" + submitCheckCode)
}

autoTask.tempValues.finishSubmit = "true"

String autoInsureMsg = TbUtil.getFromJson(submitInsureInfoResultObj, "result.checkInfoVo.questionAnswer").toString();
if (StringUtil.isNoEmpty(autoInsureMsg)) {
    autoTask.tempValues.put("autoInsureMsg", autoInsureMsg);
}

String insuredNoBI = TbUtil.getFromJson(submitInsureInfoResultObj, "result.commercialInsuransVo.insuredNo").toString();
String insuredNoCI = TbUtil.getFromJson(submitInsureInfoResultObj, "result.compulsoryInsuransVo.insuredNo").toString();

if (ExistBI && !ExistCI) {
    entity.setBizProposeNum(insuredNoBI)
} else if (ExistCI && !ExistBI) {
    entity.setEfcProposeNum(insuredNoCI)
} else if (ExistCI && ExistBI) {
    entity.setBizProposeNum(insuredNoBI)
    entity.setEfcProposeNum(insuredNoCI)
}
autoTask.taskEntity = entity;

//二次提交核保
String insureUnderInfoURL = "https://issue.cpic.com.cn/ecar/insure/insureUnderInfo";
JSONObject insureUnderInfoParam = JSON.parse(TbUtil.getBaseParam());
insureUnderInfoParam.put("redata", submitInsureInfoResultObj.getJSONObject("result"));
if (autoTask.tempValues.productCode) {
    autoTask.tempValues.insureUnderInfoParam = insureUnderInfoParam.toJSONString()
} else if ("北京".equals(autoTask.configs?.areaComCode)) {
    autoTask.tempValues.insureUnderInfoParam = insureUnderInfoParam.toJSONString()
} else {
    autoTask.tempValues.insureUnderInfoParam = StringUtil.chinesetoUnicode(insureUnderInfoParam.toJSONString())
}
def reqHeader = Robot2011Util.getDefaultHead(autoTask.tempValues, autoTask.configs)
def reqBody = Robot2011Util.genBody(autoTask.tempValues, autoTask.tempValues.insureUnderInfoParam as String)
def insureUnderInfoResult = HttpSender.doPostWithRetry(5, autoTask?.httpClient as CloseableHttpClient, true, insureUnderInfoURL, reqBody, null, reqHeader, "UTF-8", null, "");
insureUnderInfoResult = Robot2011Util.decodeBody(autoTask.tempValues, insureUnderInfoResult)

boolean insureUnderInfoFailed = false;
if (insureUnderInfoResult.startsWith("{"))
    insureUnderInfoFailed = true;
JSONObject insureUnderInfoResultObj = JSON.parseObject(insureUnderInfoResult);
if (!"success".equals(TbUtil.getFromJson(insureUnderInfoResultObj, "message.code")))
    insureUnderInfoFailed = true;
if (StringUtil.isNoEmpty((String) TbUtil.getFromJson(insureUnderInfoResultObj, "result.checkInfoVo.questionAnswer")))
    insureUnderInfoFailed = true;
