package taipingyang.robot

import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotDict_2011

/**
 * 本模版通过精友编码查询车型信息
 * 目前只从续期查询结果取值
 */
@BaseScript BaseScript_Http_Enq _

head Robot2011Util.getDefaultHead(tempValues, config)

json Robot2011Util.serializer(tempValues), {
    RobotDict_2011.makeBaseReqBody {
        [
                //精友车型编码
                'moldCharacterCode': tempValues?.queryQuickOfferByPlate?.ecarvo?.moldCharacterCode
        ]
    }
}