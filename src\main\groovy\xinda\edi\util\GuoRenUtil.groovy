package xinda.edi.util

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.alibaba.fastjson.serializer.SerializerFeature
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.tools.BigDecimalUtil
import com.cheche365.bc.utils.sdas.SDASUtils
import com.cheche365.bc.utils.sender.HttpSender
import common.common_all
import org.apache.commons.lang3.StringUtils
import org.apache.http.HttpEntity
import org.apache.http.client.methods.HttpPost
import org.apache.http.entity.ByteArrayEntity
import org.apache.http.entity.mime.MultipartEntityBuilder
import org.apache.http.impl.client.CloseableHttpClient
import org.apache.http.impl.client.HttpClients
import xinda.edi.config.GuoRenMap

import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class GuoRenUtil {

    /**
     * 统一请求方法
     * @return
     */
    static request(body, intefaceCode, config, tempValues) {
        //流水号
        if(!tempValues.BUSINESS_UUID) {
            tempValues.BUSINESS_UUID = UUID.randomUUID().toString()
        }
        def lastParams = [
                'PACKAGE': [
                        'HEAD': [
                                'CLIENT_ENCODE'      : config.CLIENT_ENCODE,
                                'SERVE_CODE'         : config.SERVE_CODE,
                                'CHANNEL_CODE'       : config.CHANNEL_CODE,
                                'INTERFACE_CODE'     : intefaceCode,
                                'INTERFACE_USER_CODE': config.INTERFACE_USER_CODE,
                                'INTERFACE_PWD'      : config.INTERFACE_PWD,
                                'REQUEST_TIME'       : LocalDateTime.now().format(DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss:SSSS')),
                                'XML_LIST_SUFFIX'    : '',
                                'IFTEST'             : config.IFTEST,
                                'BUSINESS_UUID'      : tempValues.BUSINESS_UUID,

                        ],
                        'BODY': body
                ]
        ]
        JSONObject.toJSONString(lastParams, SerializerFeature.DisableCircularReferenceDetect)
    }

    /**
     * 根据车辆列表 匹配车，先匹配jycode，没有则选取车价最低
     * @param jycode
     * @param list
     * @return
     */
    static chooseCar(jycode, list) {
        if(list.size() == 0) {
            throw new InsReturnException("没有匹配到车辆！")
        }
        def car = list.find { (it.codeCode == jycode || it.modelCodeBC == jycode) }
        if(!car) {
            def newList = list.findAll{StringUtils.isNotBlank(it?.priceShow as String)}?.sort { a, b ->
                return new BigDecimal(a.priceShow as String) - new BigDecimal(b.priceShow as String)
            }
            car = newList.get(0)
        }
        //如果座位数 有小数点，去掉
        def seatCount = car?.ext?.seatCountlb ?: getNumber(car?.seatCountBC)
        //整备质量的值
        def tonCount = car?.tonCount ?: car?.ext?.tonCount
        return [
                'codeCode'              : car?.codeCode ?: car?.modelCodeBC,
                'seatCount'             : Integer.valueOf(seatCount),//核定载客数
                'tonCount'              : tonCount,//总质量
                'tonCountlb'            : tonCount,//类比总质量
                'exhaustScale'          : car?.ext?.exhaustScale ?: car?.exHaustScale ?: 0,//排量
                'exhaustScalelb'        : car?.ext?.exhaustScalelb ?: car?.exHaustScale ?: 0,//类比排量
                'completekerbmass'      : car?.ext?.completekerbmass ?: car?.completeKerbMass,//整备质量
                'purchasePrice'         : car?.ext?.purchasePrice ?: car?.priceShow,//新车购置价
                'modelCodeBC'           : car?.modelCodeBC, //
                'fuelType'              : car?.fuelType ?: car?.ext?.fuelType,
                'vehicleSize'           : car?.vehicleSize
        ]
    }

    /**
     * 获取补充数据项信息
     * @param supplyParams
     */
    static getSupplyParamMap(supplyParams) {
        def supplyParamMap = [:]
        if (supplyParams) {
            supplyParams.each {
                if (it.itemcode.contains('Gender')) {
                    it.itemvalue = (Integer.valueOf(it.itemvalue as String) - 1).toString()
                }
                supplyParamMap << [(it.itemcode): it.itemvalue]
            }
        }
        supplyParamMap
    }

    /**
     * 去除地址无用信息
     */
    static splitAddress(addressAll) {

        def addressInfo = '' as String
        if (addressAll) {
            if(addressAll.contains('#') && addressAll.contains('|')) {
                def addressAllList = addressAll.split('\\|').size() > 1 ? addressAll.split('\\|') : [] as List
                addressAllList.each {
                    if (it.contains('#')) {
                        addressInfo = addressInfo + it?.split('#')[1]
                    } else {
                        addressInfo = addressInfo + it
                    }
                }
            } else {
                return addressAll
            }
        }
        addressInfo
    }

    /**
     * 获取图片对象
     * @param imgUrl
     * @return
     */
    static getFile(imgUrl) {
        def file
        try {
             file = new URL(imgUrl as String).withInputStream { is ->
                File.createTempFile('XDCX-upload-image', '.jpg').with { tmpFile ->
                    tmpFile.withOutputStream { os ->
                        os << is
                    }
                    tmpFile
                }
            }
        } catch (Exception e) {
            throw new InsReturnException('获取影像件失败:' + e.getMessage())
        }
        return file
    }

    /**
     * 查询参数
     */
    static queryParam(bizProposeNum,efcProposeNum) {
        def proposalNos = []
        if (bizProposeNum){
            proposalNos << bizProposeNum
        }
        if (efcProposeNum){
            proposalNos << efcProposeNum
        }
        return proposalNos
    }

    /**
     * 对返回结果进行处理
     * @param root
     */
    static getResponse(root, enquiry) {
        if (root['PLATFORM_STS'] != '0'){
            dealErrorMessage(enquiry, root?.PLATFORM_MSG)
        }
        if (root['RESPONSE_BODY'] instanceof String) {
            dealErrorMessage(enquiry, root['RESPONSE_BODY'] )
        }
        if (root['RESPONSE_BODY']['resultCode'] == 0 || root['RESPONSE_BODY']['code'] in ['0000', 0] ) {
            if(root['RESPONSE_BODY']['msg'] && root['RESPONSE_BODY']['msg'] != '处理成功！') {
                dealErrorMessage(enquiry, root['RESPONSE_BODY']['msg'])
            }
            //查看是否转保
            if(root.interface == 'quote' && (root['RESPONSE_BODY']['data']?.cicheckCode || root['RESPONSE_BODY']['data']?.bicheckCode)) {
                return '转保'
            }
            //查看是否有商业重复投保提示
            if (root.interface == 'quote' && root['RESPONSE_BODY']['data']?.bireCoverMsg) {
                def bireCoverMsg =  root['RESPONSE_BODY']['data']?.bireCoverMsg as String
                if (bireCoverMsg.contains('-')) {
                    return bireCoverMsg
                } else {
                    dealErrorMessage(enquiry, bireCoverMsg)
                }
            }
            //查看重复投保提示
            if (root.interface == 'quote' && root['RESPONSE_BODY']?.resultMsg) {
                if(root['RESPONSE_BODY']?.resultMsg.contains('重复投保')) {
                    return root['RESPONSE_BODY']?.resultMsg as String
                }
            }
            return
        }
        def resultMsg = root['RESPONSE_BODY']?.resultMsg as String
        if(root['RESPONSE_BODY']['data'] == null && root['RESPONSE_BODY']['resultMsg'] == null) {
            dealErrorMessage(enquiry, JSONObject.toJSONString(root['RESPONSE_BODY']))
        }
        if(resultMsg.contains('重复投保')) {
            if (resultMsg.contains('起保日期')) {
                return resultMsg
            } else {
                dealErrorMessage(enquiry, resultMsg)
            }
        }
        //特约需要特殊处理
        if (resultMsg.contains('没有查询到数据') || resultMsg.contains('录入的校验码有误') || resultMsg.contains('该车险不存在绑定的非车投保单')) {
            return resultMsg
        }
        dealErrorMessage(enquiry, root['RESPONSE_BODY']['resultMsg'] ?: root['RESPONSE_BODY']['message'] ?: '')
    }

    /**
     * 在流程失败时 仍需要返回接口反参的信息，使用此方法 拼写错误信息 抛出异常
     * @param enquiry
     * @param msg
     */
    static dealErrorMessage (enquiry, msg) {
        //10396 【百川】【国任】抓取【签单折扣预期赔付率2】字段值需求优化
        if(enquiry?.definition) {
            msg = "${msg};【签单折扣预期赔付率2：${enquiry?.definition['application.expectMixedRatioTag']}】"
        }
        throw new InsReturnException(msg)
    }

    /**
     *  保单上传
     */
    static uploadPolicy(enquiry, type ,policyNo, data){
        def url = type == 'nonMotor' ? data : GuoRenMap.getEnvCode().policyNoUrl + data as String
        byte[] b = HttpSender.doGet(null, url, null, 'UTF-8', null, true)
        if (b) {
            String fileHeader = new common_all().bytesToHexString(b)
            if (StringUtils.isNotBlank(fileHeader) && fileHeader.length() > 14) {
                String upperCase = fileHeader.substring(0, 14).toUpperCase()
                if ("255044462D312E".equals(upperCase)) {
                    if (type == 'efc') {
                        byte[] flag_b = HttpSender.doGet(null, GuoRenMap.getEnvCode().policyNoFlagUrl + data as String, null, 'UTF-8', null, true)
                        //合并PDF
                        if (flag_b.length > 100000) {
                            try {
                                b = new common_all().mergePDF(b, flag_b)
                            } catch (Exception e) {
                                throw new InsReturnException('2096edi保单下载异常')
                            }
                        }
                    }
                    def fileName = "river_attachment/2096/${policyNo}/${LocalDate.now().toString()}/${policyNo}${type}_${System.currentTimeMillis()}.pdf"
                    //占位
                    def assetResult = SDASUtils.assetOccupancy('application/pdf', fileName, fileName)
                    if (!assetResult || assetResult.code != 0) {
                        throw new InsReturnException('2096--PDF占位异常')
                    }
                    //上传
                    ByteArrayEntity byteArrayEntity = new ByteArrayEntity(b)
                    def uploadResult = SDASUtils.uploadPutAsset(fileName, assetResult, byteArrayEntity)
                    if (!uploadResult) {
                        throw new InsReturnException(InsReturnException.Others, "2096上传资产库失败")
                    }
                    //回写ID
                    enquiry?.SQ << [(type + 'Id'): uploadResult.id]
                } else {
                    throw new InsReturnException(InsReturnException.Others, "保司文件异常请稍后重")
                }
            }
        } else {
            throw new InsReturnException(InsReturnException.Others, "保司返回文件为空")
        }
    }

    /**
     * 回写 报价信息/投保单明细查询信息
     * type 1-报价回写 2-投保单明细查询信息 3-承保回写
     */
    static writeBackInfo(data, enquiry, type, isNewEnergy, nonMotorPremium) {
        boolean bizFlag = enquiry.baseSuiteInfo.bizSuiteInfo ? true : false
        boolean efcFlag = enquiry.baseSuiteInfo.efcSuiteInfo ? true : false
        //回写报价结果========
        //sq信息
        def SQ = enquiry?.SQ
        if (!SQ) {
            SQ = new HashMap()
            enquiry << ['SQ', SQ]
        }
        def totalCharge = '0' as String
        //回写商业险========
        if (bizFlag) {
            def bizSuiteInfo = enquiry.baseSuiteInfo.bizSuiteInfo
            //总折扣 discountRate
            def discountRate
            if (type == 1) {
                discountRate = data?.signDiscount ?: '1' as String
            } else if (type == 2) {
                discountRate = data.prpTmainList.find { it['proposalNo'] == enquiry.SQ.bizProposeNum }?.signDiscount as String
            } else {
                discountRate = data.prpCmainList.find { it['policyNo'] == enquiry.SQ?.bizPolicyCode ?: enquiry.SQ?.bizPolicyNo }?.signDiscount as String
            }
            discountRate = discountRate ?: 1
            //
            bizSuiteInfo.discountRate = new BigDecimal(discountRate)
            //原始保费 sumPrem
            def sumPrem
            if (type == 1) {
                sumPrem = data.sumPremiumBI
            } else if (type == 2) {
                sumPrem = data.prpTmainList.find { it['proposalNo'] == enquiry.SQ.bizProposeNum }?.sumPremium
            } else {
                sumPrem = data.prpCmainList.find { it['policyNo'] == enquiry.SQ?.bizPolicyCode ?: enquiry.SQ?.bizPolicyNo }?.sumPremium
            }
            bizSuiteInfo.orgCharge = new BigDecimal(sumPrem as String).divide(new BigDecimal(discountRate), 2, BigDecimal.ROUND_HALF_UP)
            //折后保费========
            bizSuiteInfo.discountCharge = new BigDecimal(sumPrem as String).setScale(2, RoundingMode.HALF_UP)
            //回写商业险各个保费========
            def itemVOList
            def itemCode = 'kindCode'
            def amountCode = 'unitAmount'
            if (type == 1) {
                itemVOList = data['itemVOList'] as JSONArray
            } else if (type == 2) {
                itemVOList =  data['prpTitemkindListTotal'].getAt(0) as JSONArray
            } else {
                itemVOList =  data['prpCitemkindListTotal'].getAt(0) as JSONArray
                itemCode = 'kindcode'
                amountCode = 'unitamount'
            }
            def bizStart, bizEnd
            def itemVOInfoList = []
            itemVOList.each {
                if(it['kindCode'] == 'BZ' || it['kindcode'] == 'BZ') {
                    return
                }
                bizStart = it['startdate'] ?: it['startDate'] + ' 00:00:00'
                bizEnd = it['enddate'] ?: it['endDate'] + ' 23:59:59'
                //保司承保返回格式不正确 重新拼接
                if (bizEnd.contains('00:00:00') && it.endhour) {
                    bizEnd = bizEnd.replace(' 00'," ${it.endhour}")
                }
                //险种编码
                def code
                if (isNewEnergy) {
                    code = GuoRenMap.getReturnRiskCodeNewEnergy(it[(itemCode)])
                } else {
                    code = GuoRenMap.getReturnRiskCode(it[(itemCode)])
                }
                def itemVOInfo = [
                        'discountRate' : new BigDecimal(discountRate),
                        //折扣保费
                        'discountCharge' : new BigDecimal(it['premium'] as String),
                        //原始保费
                        'orgCharge' : new BigDecimal(it['standardPremuim'] ?: it['standardpremuim'] ?: '0' as String),
                        //保额
                        'amount' : new BigDecimal(it[(amountCode)] ?: it['amount']),
                        //
                        'code': code[0],
                        //
                        'name': code[1]
                ]
                //回写增值服务次数 RoadsideService VehicleInspection SendForInspection DesignatedDriving
                if(code[0] == 'RoadsideService') {
                    itemVOInfo['amount'] = new BigDecimal(it[('quantity')])
                }
                itemVOInfoList << itemVOInfo
            }
            enquiry.baseSuiteInfo.bizSuiteInfo.suites = itemVOInfoList
            //
            totalCharge = BigDecimalUtil.add(totalCharge, bizSuiteInfo.discountCharge as String)
            SQ << [
                    'bussDiscountRate': enquiry.baseSuiteInfo.bizSuiteInfo.discountRate,
                    'bizCharge'       : enquiry.baseSuiteInfo.bizSuiteInfo.discountCharge,
            ]
            //回写商业险日期
            if(data?.biImmeValidStartDate) {
                bizStart = data?.biImmeValidStartDate
            }
            if(data?.biImmeValidEndDate) {
                bizEnd = data?.biImmeValidEndDate
            }
            enquiry.baseSuiteInfo.bizSuiteInfo.start = bizStart ?: enquiry.baseSuiteInfo.bizSuiteInfo.start
            enquiry.baseSuiteInfo.bizSuiteInfo.end = bizEnd ?: enquiry.baseSuiteInfo.bizSuiteInfo.end
        }
        //回写交强险车船税========
        if (efcFlag) {
            def efcInfo
            def index = 0
            if(bizFlag) {
                index = 1
            }
            if (type == 1) {
                efcInfo = data['itemVOList'].find { item -> item.kindCode == 'BZ' }
            } else if (type == 2) {
                efcInfo = data['prpTitemkindListTotal'].getAt(index).getAt(0)
            } else {
                efcInfo = data['prpCitemkindListTotal'].getAt(index).getAt(0)
            }
            def efcSuiteInfo = enquiry.baseSuiteInfo.efcSuiteInfo
            efcSuiteInfo.orgCharge = new BigDecimal(efcInfo['premium'] as String)
            efcSuiteInfo.discountCharge = new BigDecimal(efcInfo['premium'] as String)
            efcSuiteInfo.discountRate = efcInfo?.disCount ?: efcInfo?.discount
            totalCharge = BigDecimalUtil.add(totalCharge, efcSuiteInfo?.discountCharge as String)
            //车船税
            if (!enquiry.baseSuiteInfo.taxSuiteInfo) {
                enquiry.baseSuiteInfo.taxSuiteInfo = [:]
            }
            def taxSuiteInfo
            if (type == 1) {
                def charge = data?.carShipTaxRespVO?.sumPayTax ?: data?.carShipTaxRespVO?.taxActual
                enquiry.baseSuiteInfo.taxSuiteInfo.charge = new BigDecimal(charge as String)
            } else if (type == 2) {
                enquiry.baseSuiteInfo.taxSuiteInfo.charge = new BigDecimal(data?.prptcarshiptax?.sumPayTax ?: 0 as String)
            } else {
                enquiry.baseSuiteInfo.taxSuiteInfo.charge = new BigDecimal(data?.prpccarshiptax?.sumpaytax ?: 0 as String)
            }
            totalCharge = BigDecimalUtil.add(totalCharge, enquiry.baseSuiteInfo.taxSuiteInfo.charge as String)
            SQ << [
                    'taxCharge'          : enquiry.baseSuiteInfo.taxSuiteInfo.charge,
                    'trafficDiscountRate': enquiry.baseSuiteInfo.efcSuiteInfo.discountRate,
                    'efcCharge'          : enquiry.baseSuiteInfo.efcSuiteInfo.discountCharge
            ]
            //回写交强时间
            //回写险日期
            enquiry.baseSuiteInfo.efcSuiteInfo.start = efcInfo?.startdate ?: efcInfo?.startDate + ' 00:00:00' ?: enquiry.baseSuiteInfo.efcSuiteInfo.start
            def EfcEnd = efcInfo?.enddate ?: efcInfo?.endDate + ' 23:59:59' ?: enquiry.baseSuiteInfo.efcSuiteInfo.end
            //保司承保返回格式不正确 重新拼接
            if (EfcEnd.contains('00:00:00') && efcInfo.endhour) {
                EfcEnd = EfcEnd.replace(' 00'," ${efcInfo.endhour}")
            }
            enquiry.baseSuiteInfo.efcSuiteInfo.end = EfcEnd
            if(data?.ciImmeValidStartDate) {
                enquiry.baseSuiteInfo.efcSuiteInfo.start = data?.ciImmeValidStartDate
            }
            if(data?.ciImmeValidEndDate) {
                enquiry.baseSuiteInfo.efcSuiteInfo.end = data?.ciImmeValidEndDate
            }
         }
        //回写非车险
        if (enquiry?.SQ?.nonMotor) {
            if (nonMotorPremium) {
                def premium = nonMotorPremium
                String nonMotorPrem = new BigDecimal(premium as String)
                enquiry?.SQ?.nonMotor?.discountCharge = new BigDecimal(premium as String)
                totalCharge = BigDecimalUtil.add(totalCharge, nonMotorPrem as String)
            }
        }
        SQ << ['totalCharge': new BigDecimal(totalCharge)]
        //自主定价系数 等
        if (data?.selfAdjustRate) {
            def definition  = ['selfRate' : data?.selfAdjustRate]
            enquiry << ['definition': definition]
        }
        //bug 27586 回写车辆信息，保司结果覆盖查询参数
        def carInfoInsureBack = [:]
        if (data.prptitemcar) {
            carInfoInsureBack = data.prptitemcar
        }
        if (data.prpCitemcar) {
            carInfoInsureBack = data.prpCitemcar
        }
        if (carInfoInsureBack) {
            enquiry.carInfo << [
                    'carModelName': carInfoInsureBack.brandName ?: carInfoInsureBack.brandname,
                    'vin'         : carInfoInsureBack.frameNo ?: carInfoInsureBack.frameno,
                    'engineNum'   : carInfoInsureBack.engineNo ?: carInfoInsureBack.engineno,
                    'firstRegDate': carInfoInsureBack.enrolldate ?: (carInfoInsureBack.enrollDate  + ' 00:00:00'),
                    //0 非 1 过
                    'isTransfer'  : (carInfoInsureBack.chgOwnerFlag ?: carInfoInsureBack.chgownerflag) == '0' ? false : true,
                    'modelLoad'   : (new BigDecimal((carInfoInsureBack.tonCount ?: carInfoInsureBack.toncount) as String) * 1000).intValue(),
                    'fullLoad'    : carInfoInsureBack.completeKerbMass as Integer ?:  enquiry.carInfo.fullLoad,
                    'displacement': carInfoInsureBack.exhaustScale ?: carInfoInsureBack.exhaustscale,
                    'seatCnt'     : carInfoInsureBack.seatCount ?: carInfoInsureBack.seatcount,
                    'useProps'    : GuoRenMap.getUsePropsWriteBack(carInfoInsureBack.useNatureCode ?: carInfoInsureBack.usenaturecode, carInfoInsureBack.maincarkindcode ?: carInfoInsureBack.maincarKindCode),
                    'carUserType' : (carInfoInsureBack.carOwnerIdentifyType ?: carInfoInsureBack.carowneridentifytype) in ['74', '10'] ? 1 : 0,
                    'plateNum'    : carInfoInsureBack.licenseno ?: carInfoInsureBack.licenseNo ?: enquiry.carInfo.plateNum
            ]
        }
        //回写关系人信息 bug 30700 【生产环境】国任edi核保查询，车主、被保人、投保人名字没有回写磐石
        if (data?.prptitemcar?.carOwner || data?.prpCitemcar?.carowner) {
            enquiry['carOwnerInfo']['name'] = data?.prptitemcar?.carOwner ?: data?.prpCitemcar?.carowner
        }
        def insuredPerson = ''
        def applicantPerson = ''
        if(data?.prpTinsuredList) {
            //被保人
            insuredPerson = data?.prpTinsuredList.find{ it -> it.insuredflag == '1'}
            //投保人
            applicantPerson = data?.prpTinsuredList.find{ it -> it.insuredflag == '2'}
        }
        if(data?.prpCinsuredList) {
            //被保人
            insuredPerson = data?.prpCinsuredList.find{ it -> it.insuredflag == '1'}
            //投保人
            applicantPerson = data?.prpCinsuredList.find{ it -> it.insuredflag == '2'}
        }
        if(insuredPerson) {
            enquiry['insuredPersonList'][0]['name'] = insuredPerson['insuredname']
        }
        if(applicantPerson) {
            enquiry['insurePerson']['name'] = applicantPerson['insuredname']
        }
    }

    /**
     * 影像上传
     */
    static uploadImage(enquiry, tempValues) {

        //影像上传
        def uploadUrl = GuoRenMap.getEnvCode().imageUrl
        def imageFlag = false
        enquiry?.imgAddress?.each { key, value ->
            File file = getFile(value.toString())
            if (file.isFile()) {
                def httpPost
                def response
                try (CloseableHttpClient client = HttpSender.buildHttpClient()){
                    httpPost = new HttpPost(uploadUrl)
                    MultipartEntityBuilder builder = MultipartEntityBuilder.create()
                    builder.addTextBody("businessNo", tempValues['proposalNoCI'] ?: tempValues['proposalNoBI'])//交商同保传交强
                    builder.addTextBody("operatorCode", tempValues['operatorCode'] as String)
                    builder.addTextBody("comCode", tempValues.baseInfo?.prptmain?.comCode as String)
                    builder.addTextBody("fileGroup", 'DZ')
                    builder.addBinaryBody("file",file)
                    HttpEntity entity = builder.build()
                    httpPost.setEntity(entity)
                    response = client.execute(httpPost)
                    if (response.getStatusLine().statusCode != 200) {
                        imageFlag = false
                    } else {
                        imageFlag = true
                    }
                } catch (e) {
                    throw new InsReturnException("传递2096edi上传影像异常")
                } finally {
                    file.delete()
                    httpPost?.releaseConnection()
                    response?.close()
                    if (file.exists()) {
                        file.delete()
                    }
                }
            } else {
                throw new InsReturnException("获取2096edi上传影像异常")
            }
        }
        def SQ = enquiry?.SQ
        if (!SQ) {
            SQ = new HashMap()
            enquiry << ['SQ', SQ]
        }
        SQ << ['imageFlag': imageFlag]
    }

    /**
     * 替换验证码字符
     * @param code
     * @return
     */
    static transformationCode(code) {
        if (code) {
            code = code.replace('0', 'O').replace('l', "1")
        } else {
            throw new InsReturnException("效验码为空")
        }
    }

    /**
     * 不规则字符串数字 转换 数字
     */
    static getNumber(value) {
        if (value.contains('.')) {
            value = value.split('\\.').getAt(0)
        }
        return value
    }

    static getNonAutoAddress(address, config) {
        def matcher1 = '#' + address =~ /#\D+\|/
        def matcher2 = address =~ /#[^#]*\u0024/
        return [addrProvince: matcher1 ? (matcher1[0] - '|' - '#') : config?.addrProvince ?: '',
                addrCity    : matcher1 ? (matcher1[1] - '|' - '#') : config?.addrCity ?: '',
                addrCounty  : matcher1 ? (matcher1[2] - '|' - '#') : config?.addrCounty ?: '',
                addrStreet  : matcher2 ? matcher2[0] - '#' : config?.addrStreet ?: '',
                fullAddr    : address ? address.replaceAll('\\|\\d{6}+#', '') : config?.fullAddr ?: '']
    }
}

