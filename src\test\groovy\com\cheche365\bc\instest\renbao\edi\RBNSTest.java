package com.cheche365.bc.instest.renbao.edi;

import com.cheche365.bc.SingleCompanyTest;
import com.cheche365.bc.message.TaskType;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.junit.Test;

/**
 * Created by Administrator on 2017/6/13.
 */
public class RBNSTest extends SingleCompanyTest {

    // 测试环境
    protected static final String TEST_URL = "http://acceptance.mypicc.com.cn/newmerchandise/insure";

    // 生产环境
    protected static final String PROD_URL = "https://www.epicc.com.cn/newmerchandise/insure";

    private static final CloseableHttpClient httpClient;

    static {
        httpClient = HttpSender.buildHttpClient();
    }

    private AutoTask t;

    protected void init() {
        this.comId = "2005";
        this.cityCode = "440100";
        this.dataType = "map";
    }

    private void initAutoTask(String taskType) throws Exception {
        t = this.getMockDataFromFile();
        t.setTaskType(taskType);
        t.setHttpClient(httpClient);
    }

    private void doQuote(String url) throws Exception {
        this.singleInterface("edi_2005_ns101100", url, t, "获取报价", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_ns101105", url, t, "补充报价", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_ns101106", url, t, "保费计算", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("edi_2005_ns101110", url, t, "修改报价", HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void quote() throws Exception {
        initAutoTask(TaskType.QUOTE.code);
        doQuote(PROD_URL);
    }

    private void doAutoInsure(String url) throws Exception {
        this.singleInterface("edi_2005_ns101115", url, t, "提交核保", HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void autoInsure() throws Exception {
        initAutoTask(TaskType.QUOTE.code);
        doAutoInsure(PROD_URL);
    }

    private void doIdentifyVerify(String url) throws Exception {
        this.singleInterface("edi_2005_ns315", url, t, "北京身份采集", HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void identifyVerify() throws Exception {
        initAutoTask(TaskType.IDENTIFY_VERIFY.code);
        doIdentifyVerify(PROD_URL);
    }

    private void doGetSms(String url) throws Exception {
        this.singleInterface("edi_2005_ns340", url, t, "江苏投保人身份确认", HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void getSms() throws Exception {
        initAutoTask(TaskType.SMS_FOR_PAY.code);
        doGetSms(PROD_URL);
    }
}
