package renbao.robot

import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript

import static renbao.robot.common_market.header
import static renbao.robot.common_market.hostPrefix


@BaseScript BaseScript_Http_Enq _

def areaAbbreviation = config['areaAbbreviation']
assertNotNull('未配置地区简称，请检查（areaAbbreviation）配置项', areaAbbreviation)

head(header(autoTask))

def downPrepare = hostPrefix(autoTask) + "/khyx/newFront/tool/epdownload/prepare.do" as String
tempValues['downPrepare'] = downPrepare

