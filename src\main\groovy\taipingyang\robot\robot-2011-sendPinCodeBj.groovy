package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.sender.HttpSender
import org.apache.http.impl.client.CloseableHttpClient
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotConstant_2011

def entity = enquiry
Map tempValues = null != entity.get("tempValues") ? entity.get("tempValues") : tempValues
Map postParameters = postParameters
Map reqHeaders = reqHeaders

Map header = new HashMap();
header.put("Content-Type", tempValues?.needSM4Flag ? 'text/plain' : "application/json;charset=utf-8")
JSONObject param = new JSONObject()

String insuredNo = entity?.SQ?.bizProposeNum;
if (StringUtil.isEmpty(insuredNo))
    insuredNo = entity?.SQ?.efcProposeNum;
if (StringUtil.isEmpty(insuredNo))
    throw new InsReturnException("查询用投保单号为空，请确认查询数据正确性")
if (StringUtil.isEmpty(tempValues?.verificationCode))
    throw new InsReturnException("发送用验证码为空，请确认查询数据正确性")
param = JSON.parseObject("{\"meta\":{\"pageSize\":8},\"redata\":{\"quotationNo\":\"\",\"insuredNo\":\"ABEJ920Y1417F004371L\",\"policyHolder\":\"\",\"partyName\":\"\",\"insuredStartDate\":\"\",\"insuredEndDate\":\"\"}}");
param.getJSONObject("redata").put("insuredNo", insuredNo)
def reqBody = param.toJSONString()
reqBody = Robot2011Util.genBody(tempValues, reqBody)
def queryPaymentResult = HttpSender.doPostWithRetry(5, httpClient as CloseableHttpClient, true,
        RobotConstant_2011.URL.QUERY_PAYMENT,
        reqBody, null,
        header,
        "UTF-8", null, "");
queryPaymentResult = Robot2011Util.decodeBody(tempValues, queryPaymentResult)
JSONObject queryPaymentResultObj = null
if (queryPaymentResult instanceof JSONObject)
    queryPaymentResultObj = (JSONObject) queryPaymentResult
else
    queryPaymentResultObj = JSON.parseObject(queryPaymentResult)


if (null == queryPaymentResultObj.getJSONArray("result") || queryPaymentResultObj.getJSONArray("result").size() == 0)
    throw new InsReturnException("按投保单号" + insuredNo + "查无记录")
String quotationNo = (String) queryPaymentResultObj.getJSONArray("result").getJSONObject(0).get("quotationNo");
if (StringUtil.isEmpty(quotationNo))
    throw new InsReturnException("报价单号为空")

def verificationCode = tempValues?.verificationCode
tempValues.put("quotationNo", quotationNo)

param = JSON.parseObject("{\"meta\":{},\"redata\":{\"bjIdentifyCodeVos\":[{\"quotationNo\":\"QBEJ920Y1417F004898I\",\"bjIdentifyCode\":\"HJ4BAQ\"}]}}")
param.getJSONObject("redata").getJSONArray("bjIdentifyCodeVos").getJSONObject(0).put("quotationNo", quotationNo)
param.getJSONObject("redata").getJSONArray("bjIdentifyCodeVos").getJSONObject(0).put("bjIdentifyCode", verificationCode)


reqHeaders.put("Content-Type", tempValues?.needSM4Flag ? 'text/plain' : "application/json;charset=utf-8")
postParameters.clear()
entity.put("tempValues", tempValues)

String nullifyJson = param.toJSONString()
Robot2011Util.genBody(tempValues, nullifyJson)
