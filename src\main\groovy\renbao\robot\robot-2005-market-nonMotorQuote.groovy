package renbao.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.utils.sender.HttpSender
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import org.apache.http.impl.client.CloseableHttpClient
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import static common.common_all.arithmeticDay
import static common.common_all.getAge
import static renbao.common.renbao_dict.useNatureCode
import static renbao.robot.common_market.hostPrefix



@BaseScript BaseScript_Http_Enq _


skip('无非车，跳过当前模板') {
    !entity?.misc?.containsKey("nonMotor")
}

def commonMarket = new common_market()
def header = commonMarket.header(autoTask)
def appInfo = autoTask?.tempValues['applicantInfo']
def insuredInfo = entity.order.insurePerson.idCard == entity.order.insuredPersons[0].idCard ? appInfo : autoTask?.tempValues['insuredInfo']
def qtComCode = autoTask?.configs['opconfComCode']
def monopolyCode = autoTask?.configs['monopolyCode'] ?: ''
def isGetRiskByVehicle = autoTask?.configs['isGetRiskByVehicle'] ?: 'false'
def carInfo = autoTask?.tempValues?.carInfo
def brandIDNew = carInfo['brandIDNew']
def saleAreaLevel = autoTask?.configs['saleAreaLevel'] ?: '02'
def serviceGroupTypeCode = autoTask?.configs['serviceGroupTypeCode'] ?: '05'
def productCode = entity?.misc?.nonMotor?.productCode as String
def queryUrl = hostPrefix(autoTask) + "/khyx/newFront/common/queryEADPlans.do?riskCode=EBS&qtComCode=${qtComCode}&serviceGroupTypeCode=${serviceGroupTypeCode}&brandIDNew=${brandIDNew}&isGetRiskByVehicle=${isGetRiskByVehicle}&saleAreaLevel=${saleAreaLevel}&monopolyCode=${monopolyCode}&pageNo=1&pageSize=10&plandData=${productCode}" as String
def result = HttpSender.doGet(autoTask.httpClient as CloseableHttpClient, queryUrl, header as Map<String, String>, null, "UTF-8",null, false)
def resultObj = JSONObject.parseObject(result)
Logger log = LoggerFactory.getLogger("营销系统非车险信息查询")
log.info("任务：{}，营销系统非车险信息查询url：{}， 返回数据为：{}", autoTask.tempValues['businessId'], queryUrl, result)
if ('Success' == resultObj['statusText'] && result.toString().contains(productCode)) {
    queryUrl = hostPrefix(autoTask) + "/khyx/newFront/common/queryQtEADPlanObj.do?planCode=${productCode}" as String
    def productInfoStr = HttpSender.doGet(autoTask.httpClient as CloseableHttpClient, queryUrl, header as Map<String, String>,null, "UTF-8",null, false)
    resultObj =  JSON.parseObject(productInfoStr as String)
    def items = resultObj['data']['items'] as JSONArray
    def eadUnitAmount = new BigDecimal(0)
    def eadUnitPremium = new BigDecimal(0)
    def clauseKindInfos = [:]
    items.eachWithIndex { item, index ->
        def serialNo = index == 0 ? '' : (index + '')
        def clauseKindInfo = [
                ('jointSalesNonvBaseInfoList[0].clauseKindInfo[' + index  + '].serialno') : serialNo,
                ('jointSalesNonvBaseInfoList[0].clauseKindInfo[' + index  + '].clausecode') : item['clauseCode'],
                ('jointSalesNonvBaseInfoList[0].clauseKindInfo[' + index  + '].kindcode') : item['kindCode'],
                ('jointSalesNonvBaseInfoList[0].clauseKindInfo[' + index  + '].unitamount') : item['itemAmount'],
                ('jointSalesNonvBaseInfoList[0].clauseKindInfo[' + index  + '].unitpremium') : item['itemPremium']
        ]
        clauseKindInfos.putAll(clauseKindInfo)
        eadUnitAmount = eadUnitAmount.add(new BigDecimal(item['itemAmount'] as String))
        eadUnitPremium = eadUnitPremium.add(new BigDecimal(item['itemPremium'] as String))
    }
    def useNatureCode = useNatureCode(entity?.order?.carInfo?.useProps)
    def quoteData = autoTask.tempValues.quoteData
    def appAge = [6, 8, 9, 10].contains(entity.order.insurePerson.idCardType) ? '' : getAge(entity.order.insurePerson.idCard) + ''
    def insuredAge = [6, 8, 9, 10].contains(entity.order.insuredPersons[0].idCardType) ? '' : getAge(entity.order.insuredPersons[0].idCard) + ''
    def start = entity.order.suiteInfo?.bizSuiteInfo?.start ?: entity.order.suiteInfo?.efcSuiteInfo?.start
    def sDate = start.substring(0, 10)
    def startDate = arithmeticDay(sDate, 0, 0,0)
    def endDate = arithmeticDay(sDate, -1, 0,1)
    def param = [
            "jointSalesNonvBaseInfoList[0].isAccredit"                                       : '1',
            "jointSalesNonvBaseInfoList[0].carKindCode"                                      :  carInfo['vehicleClassPicc'],
            "jointSalesNonvBaseInfoList[0].useNatureCode"                                    : useNatureCode,
            "jointSalesNonvBaseInfoList[0].licenseNo"                                        : entity.order.carInfo.plateNum,
            "jointSalesNonvBaseInfoList[0].engineNo"                                         : entity.order.carInfo.engineNum,
            "jointSalesNonvBaseInfoList[0].vinNo"                                            : entity.order.carInfo.vin,
            "jointSalesNonvBaseInfoList[0].brandName"                                        : carInfo['vehicleName'],
            "jointSalesNonvBaseInfoList[0].seatCount"                                        : entity.order.carInfo.seatCnt + '',
            "jointSalesNonvBaseInfoList[0].riskCode"                                         : 'EBS',
            "jointSalesNonvBaseInfoList[0].source"                                           : '1',
            "jointSalesNonvBaseInfoList[0].comId"                                            : entity.order.insureArea.province + '00',
            "jointSalesNonvBaseInfoList[0].eadUnitAmount"                                    : eadUnitAmount.intValue() + '',
            "jointSalesNonvBaseInfoList[0].eadUnitPremium"                                   : eadUnitPremium.doubleValue() + '',
            "jointSalesNonvBaseInfoList[0].emailNonv"                                        : appInfo['email'] ?: autoTask?.configs['insureEmails'],
            "jointSalesNonvBaseInfoList[0].emailNonvEncryption"                              : appInfo['emailEncryption'] ?: '',
            "jointSalesNonvBaseInfoList[0].elecPolicyNonv"                                   : '1',
            "jointSalesNonvBaseInfoList[0].projectCode"                                      : autoTask?.configs?.projectCode ?: '',
            "jointSalesNonvBaseInfoList[0].sameAsCarQuoteConfig"                             : '1',
            "jointSalesNonvBaseInfoList[0].quotationId"                                      : quoteData['quotationId'] ?: '',
            "jointSalesNonvBaseInfoList[0].userMonopolyNov"                                  : monopolyCode,
            "jointSalesNonvBaseInfoList[0].invoiceTitleSelectNonv"                           : '1',
            "jointSalesNonvBaseInfoList[0].serviceManager"                                   : autoTask?.configs?.serviceManager ?: '',
            "jointSalesNonvBaseInfoList[0].servicemanagername"                               : autoTask?.configs?.serviceManagerName ?: '',
            "jointSalesNonvBaseInfoList[0].servicegrouptype"                                 : '',
            "jointSalesNonvBaseInfoList[0].isGetRiskByVehicle"                               : '',
            "jointSalesNonvBaseInfoList[0].saleAreaLevel"                                    : saleAreaLevel,
            "jointSalesNonvBaseInfoList[0].servicebelongcode"                                : qtComCode,
            "jointSalesNonvBaseInfoList[0].makeCom"                                          : qtComCode,
            "jointSalesNonvBaseInfoList[0].selectedOperateConfigId"                          : autoTask?.tempValues?.selectedOperateConfigId,
            "jointSalesNonvBaseInfoList[0].salesCode"                                        : '',
            "jointSalesNonvBaseInfoList[0].sellerName"                                       : '',
            "jointSalesNonvBaseInfoList[0].sellerNo"                                         : '',
            "jointSalesNonvBaseInfoList[0].sellAgentCode"                                    : autoTask?.configs?.agentCode,
            "jointSalesNonvBaseInfoList[0].invoiceTypeNonv"                                  : '1',
            "jointSalesNonvBaseInfoList[0].invoiceTitleNonv"                                 : appInfo['insuredName'],
            "jointSalesNonvBaseInfoList[0].elecPolicyPhoneNonv"                              : appInfo['mobile'],
            "jointSalesNonvBaseInfoList[0].isElecInvoice"                                    : '1',
            "jointSalesNonvBaseInfoList[0].invoiceTitleType"                                 : '1',
            "jointSalesNonvBaseInfoList[0].invoiceTitleContent"                              : appInfo['insuredName'],
            "jointSalesNonvBaseInfoList[0].invoicePhoneNumber"                               : appInfo['mobile'],
            "jointSalesNonvBaseInfoList[0].invoiceMail"                                      : appInfo['email'],
            "jointSalesNonvBaseInfoList[0].invoiceMailEncryption"                            : appInfo['invoiceMailEncryption'] ?: '',
            "jointSalesNonvBaseInfoList[0].msgFlag"                                          : '',
            "jointSalesNonvBaseInfoList[0].electronicTFlag"                                  : appInfo['insuredType'] == '2' ? '0' : '1',
            "jointSalesNonvBaseInfoList[0].comCode"                                          : qtComCode,
            "jointSalesNonvBaseInfoList[0].jointSalesMain.riskCode"                          : 'EBS',
            "jointSalesNonvBaseInfoList[0].jointSalesMain.startDate"                         : startDate,
            "jointSalesNonvBaseInfoList[0].jointSalesMain.startHour"                         : '0',
            "jointSalesNonvBaseInfoList[0].jointSalesMain.endDate"                           : endDate,
            "jointSalesNonvBaseInfoList[0].jointSalesMain.endHour"                           : '24',
            "jointSalesNonvBaseInfoList[0].jointSalesMain.startMinute"                       : '0',
            "jointSalesNonvBaseInfoList[0].jointSalesMain.endMinute"                         : '0',
            "jointSalesNonvBaseInfoList[0].jointSalesMain.planCode"                          : productCode,
            "jointSalesNonvBaseInfoList[0].jointSalesMain.planCName"                         : resultObj['data']['PlanName'],
            "jointSalesNonvBaseInfoList[0].jointSalesMain.quantity"                          : entity.misc.nonMotor.count + '',
            "jointSalesNonvBaseInfoList[0].jointSalesMain.newContinue"                       : '3',
            "jointSalesNonvBaseInfoList[0].jointSalesMain.insuredQuantity"                   : entity.order.carInfo.seatCnt + '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].insuredFlag"             : insuredInfo['insuredType'] == '2' ? '01000010' : '01000000',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].countryCode"             : 'CHN',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].resident"                : 'A',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].versionNo"               : insuredInfo['versionNo'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].educationCode"           : '20',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].marriage"                : '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].selfYearIncome"          : '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].province"                : insuredInfo['province'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].city"                    : insuredInfo['city'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].area"                    : insuredInfo['area'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].isPersonConsistentWithID": '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].dateValid"               : insuredInfo['dateValid'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].startDateValid"          : insuredInfo['startDateValid'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].insuredName"             : insuredInfo['insuredName'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].insuredEnName"           : '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].identifyType"            : insuredInfo['insuredType'] == '2' ? '31' : insuredInfo['identifyType'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].mobile"                  : insuredInfo['mobile'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].phoneNumber"             : insuredInfo['phonenumber'] ?: '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].sex"                     : insuredInfo['sex'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].age"                     : insuredAge,
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].birthday"                : insuredInfo['birthday'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].insuredCode"             : insuredInfo['insuredCode'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].customerCodeAll"         : insuredInfo['customerCodeAll'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].source"                  : '1',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].insuredAddress"          : insuredInfo['insuredAddress'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].insuredAddressSeqNo"     : '1',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].identifyNumber"          : insuredInfo['insuredType'] == '2' ? insuredInfo['identifyNumber'] : entity.order.insuredPersons[0].idCard,
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].email"                   : insuredInfo['email'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].emailEncryption"         : insuredInfo['emailEncryption'] ?: '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].unitType"                : insuredInfo['insuredType'] == '2' ? '300' : '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].unifiedSocialCreditCode" : insuredInfo['unifiedSocialCreditCode'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].insuredType"             : insuredInfo['insuredType'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].contactPersonName"       : insuredInfo['contactPersonName'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].insuredNature"           : insuredInfo['insuredType'] == '2' ? '2' : '1',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].signCode"                : insuredInfo.signMessage?.signCode ?: insuredInfo['signCode'] ?: '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].processDataPrimaryKey"   : insuredInfo?.processDataPrimaryKey ?: insuredInfo?.signMessage?.processDataPrimaryKey ?: '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].postCode"                : insuredInfo['postCode'] ?: '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].mobilePhoneProperties"   : '01',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[0].phoneProperties"         : '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].insuredFlag"             : appInfo['insuredType'] == '2' ? '10000010' : '10000000',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].countryCode"             : 'CHN',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].resident"                : 'A',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].versionNo"               : appInfo['versionNo'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].educationCode"           : '20',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].marriage"                : '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].selfYearIncome"          : '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].province"                : appInfo['province'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].city"                    : appInfo['city'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].area"                    : appInfo['area'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].isPersonConsistentWithID": '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].dateValid"               : appInfo['dateValid'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].startDateValid"          : appInfo['startDateValid'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].insuredName"             : appInfo['insuredName'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].insuredEnName"           : '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].identifyType"            : appInfo['insuredType'] == '2' ? '31' : appInfo['identifyType'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].mobile"                  : appInfo['mobile'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].phoneNumber"             : appInfo['phonenumber'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].sex"                     : appInfo['sex'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].age"                     : appAge,
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].birthday"                : appInfo['birthday'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].insuredCode"             : appInfo['insuredCode'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].customerCodeAll"         : appInfo['customerCodeAll'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].source"                  : '1',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].insuredAddress"          : appInfo['insuredAddress'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].insuredAddressSeqNo"     : "1",
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].identifyNumber"          : appInfo['insuredType'] == '2' ? appInfo['identifyNumber'] : entity?.order?.insurePerson?.idCard,
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].email"                   : appInfo['email'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].emailEncryption"         : appInfo['emailEncryption'] ?: '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].unitType"                : appInfo['insuredType'] == '2' ? '300' : '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].unifiedSocialCreditCode" : appInfo['unifiedSocialCreditCode'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].insuredType"             : appInfo['insuredType'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].contactPersonName"       : appInfo['contactPersonName'],
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].insuredNature"           : appInfo['insuredType'] == '2' ? '2' : '1',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].signCode"                : appInfo.signMessage?.signCode ?: appInfo['signCode'] ?: '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].processDataPrimaryKey"   : appInfo?.processDataPrimaryKey ?: appInfo?.signMessage?.processDataPrimaryKey ?: '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].postCode"                : appInfo['postCode'] ?: '',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].mobilePhoneProperties"   : '01',
            "jointSalesNonvBaseInfoList[0].jointSalesInsuredList[1].phoneProperties"         : '',
            "jointSalesNonvBaseInfoList[0].flag"                                             : '0',
            "jointSalesNonvBaseInfoList[0].policyType"                                       : '03'
    ]
    if (appInfo['insuredType'] == '2') {
        param['jointSalesNonvBaseInfoList[0].taxpayerNumberNonv'] = ''
        param['jointSalesNonvBaseInfoList[0].invoiceTaxpayerNumber'] = ''
    }
    if (autoTask.tempValues['nonMotorProposalNo'] && (autoTask.tempValues['nonMotorProposalNo'] as String).startsWith('FEBS')) {
        param['jointSalesNonvBaseInfoList[0].flag'] = '1'
    }
    param.putAll(clauseKindInfos)
    def paramAll = param.clone().collect {
        k, v -> "${k}:${v}"
    }.join('&')
    param['jointSalesNonvBaseInfoList[0].jointSalesInsureData'] = paramAll + '&'
    def jointSalesQuote = hostPrefix(autoTask) + "/khyx/newFront/qth/price/dealJointSalesQuoteNew.do"
    autoTask.reqHeaders = header
    autoTask?.tempValues['jointSalesQuote'] = jointSalesQuote
    autoTask.params = param
} else {
    throw new InsReturnException('未获取到相关非车产品')
}


