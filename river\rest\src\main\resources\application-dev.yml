server:
  port: 8081

spring:
  datasource:
    driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
    url: jdbc:shardingsphere:classpath:sharding-jdbc.yml?placeholder-type=environment
  #  datasource:
  #    dynamic:
  #      datasource:
  #        bcs:
  #          username: river_zzb
  #          password: Prfc65_fkzqU
  #          url: ****************************************************************************************************************************************************************
  data:
    mongodb:
      database: river_zzb
      host: *************
      port: 27017
      username: admin
      password: admin123
    redis:
      host: *************
      port: 6379
      password: cheche
      database: 0

web:
  groovy-files: D:\workspace2024\river_templates\src\main\groovy

export:
  file-path: /data/tmp

#api-interface
api:
  sign-val: 8f0e85d2-b128-6aa3-a377-dcf0f879078c


bi:
  rolling-file-appender:
    file: build/logs/BiLog/autoTaskBi.log
    fileNamePattern: build/logs/BiLog/autoTaskBi.%d{yyyy-MM-dd}.%i.log
    maxFileSize: 100MB

clue:
  guoshou:
    aesKey: 1234567812345678
