package renbao.robot


import com.cheche365.bc.exception.InsReturnException
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript

import static renbao.robot.ConstantsFourth.FOURTH_NON_MOTOR_PLAN
import static renbao.robot.common_fourth.motorHeader



@BaseScript BaseScript_Http_Enq _

    skip('无非车，跳过当前模板') {
        !entity?.misc?.containsKey("nonMotor") || (entity?.misc?.nonMotor?.productCode as String).contains('LCO')
    }

    def planCode = entity.misc.nonMotor.productCode
    def riskCode = ["EAD", "YEL", "EBS"].find {planCode.toString().indexOf(it) > -1}
    if (!riskCode) {
        throw new InsReturnException("当前非车险种代码:${planCode}并未适配")
    }

    head(motorHeader(autoTask))
    autoTask?.tempValues?.queryUrl = FOURTH_NON_MOTOR_PLAN

    jsonBody{
        [
                "unionRiskCode": riskCode,
                "planCode": planCode,
                "comCode": config.comCode,
                "seatingCapacity" : entity?.order?.carInfo?.seatCnt,
                "userclassificationCode" : config?.userclassificationCode ?: "0602"
        ]
    }
