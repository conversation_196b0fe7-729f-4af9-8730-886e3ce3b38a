package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import taipingyang.robot.module.Robot2011Util

def TbUtil = new common_2011()
Map entity = enquiry
Map tempValues = null != entity.get("tempValues") ? entity.get("tempValues") : tempValues
def getPayInfoResult = Robot2011Util.decodeBody(tempValues, (String) root)
JSONObject j = JSON.parseObject(getPayInfoResult)
if ("failed".equals(TbUtil.getFromJson(j, "message.code")))
    throw new InsReturnException("撤销已有支付信息失败，平台提示:" + TbUtil.getFromJson(j, "message.message"))
