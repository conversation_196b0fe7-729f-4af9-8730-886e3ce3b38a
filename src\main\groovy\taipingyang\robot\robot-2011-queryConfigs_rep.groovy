package taipingyang.robot

import com.alibaba.fastjson.JSONObject
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import groovy.transform.Field
import taipingyang.robot.module.Robot2011Util

@BaseScript BaseScript_Http_Enq _

@Field JSONObject resp = null

check Robot2011Util.serializer(tempValues), {
    resp = it
    assertNotNull('查询登录信息失败', resp)
    assertTrue('查询登录信息失败', 'success' == resp?.message?.code)
}

output {
    def result = resp.getJSONObject('result')
    def constConfigVos = result?.getJSONArray('constConfigVos')
    def configs = [:]
    if (constConfigVos) {
        for (int i = 0; i < constConfigVos.size(); i++) {
            def config = constConfigVos.getJSONObject(i)
            String configKey = config.getString('configKey')
            String configValues = config.getString('configValue')
            if (configKey == 'page_decrypted_trans' && configValues == '1') {
                configs['needDecrypted'] = '1'
            }
        }
    }

    //当前登录人员信息
    def currentUser = result.getJSONObject('currentUser')
    [
            'currentUser': currentUser
    ]
}
