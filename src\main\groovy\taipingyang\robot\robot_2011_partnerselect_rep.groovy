package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.cheche365.bc.utils.RedisUtil
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import org.apache.http.impl.client.BasicCookieStore
import org.apache.http.impl.client.CloseableHttpClient

/**
 * 太平洋精灵 - 代理点、终端及经办人选择响应模板
 */
@BaseScript BaseScript_Http_Enq _

String resultStr = autoTask.backRoot.toString()
if (resultStr.startsWith('{')) {
    def result = JSON.parseObject(resultStr)
    def authentication = result.getString('authentication')
    if (authentication != 'true') {
        fail(result.getString('errMsg'))
    }
} else if (!resultStr.contains('退出')) {
    fail('登录失败')
}

def cookieStr = getCookies()
def j_username = config['login'] ?: config['username']
RedisUtil.set('cookieStrKey:2011:' + j_username, cookieStr as String, 12 * 60 * 60)

private String getCookies() {
    def httpClient = autoTask.httpClient as CloseableHttpClient
    def cookieStore = httpClient.class.getDeclaredField("cookieStore")
    cookieStore.setAccessible(true)
    def cookies = (cookieStore.get(httpClient) as BasicCookieStore).getCookies()
    String cookieStr = ''
    cookies.each({ cookie ->
        if (cookie.getName() in ['route', 'JSESSIONID']) {
            cookieStr += (cookie.getName() + '=' + cookie.getValue() + '; ')
        }
    })
    return cookieStr
}