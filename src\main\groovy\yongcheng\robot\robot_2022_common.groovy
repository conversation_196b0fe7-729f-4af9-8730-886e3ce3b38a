package yongcheng.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.model.car.CarInfo
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.model.car.Order
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.sdas.SDASUtils
import com.cheche365.bc.utils.sender.HttpSender
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.MultiFormatWriter
import com.google.zxing.client.j2se.MatrixToImageWriter
import com.google.zxing.common.BitMatrix
import common.common_all
import org.apache.commons.collections4.map.ListOrderedMap
import org.apache.http.conn.socket.LayeredConnectionSocketFactory
import org.apache.http.entity.ByteArrayEntity
import org.apache.http.impl.client.BasicCookieStore
import org.apache.http.impl.client.HttpClients
import org.apache.http.impl.cookie.BasicClientCookie

import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.util.regex.Pattern
import java.util.stream.Stream

import static com.cheche365.bc.exception.InsReturnException.Others
import static java.util.Calendar.instance

/**
 *  组装车型搜索页面需要的参数 动态传code,
 */
def getCarModelDW_DATA(def modelName, def vin, def jycode, def dwid) {

    def DW_DATA = "<data><dataObjs type=\"ONE_SELECT\"  dwName=\"sys.common.new_vehicle_DW\" dwid=\"" + dwid + "\" pageCount=\"1\" pageNo=\"1\" pageSize=\"20\" rsCount=\"0\"/><filters colsInOneRow=\"3\" dwName=\"sys.common.new_vehicle_DW\"><filter isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"true\" name=\"null\" width=\"150\"/><filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"" + modelName + "\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isNullable=\"true\" isRowBegin=\"false\" isRowEnd=\"false\" name=\"VehicleName\" operator=\"*\" rows=\"1\" tableName=\"\" title=\"车型名称/别名\" type=\"input\" width=\"190\" issExtValue=\"" + modelName + "\" /><filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowEnd=\"true\" name=\"VehicleId\" operator=\"*\" rows=\"1\" tableName=\"\" title=\"车辆标识\" type=\"input\" width=\"190\" issExtValue=\"\"/><filter isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"true\" name=\"null\" width=\"150\"/><filter checkType=\"NumberText\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isNullable=\"true\" isRowBegin=\"false\" isRowEnd=\"false\" name=\"GxVehiclePriceBegin\" operator=\"*\" rows=\"1\" tableName=\"\" title=\"新车购置价（万）\" type=\"input\" width=\"190\" issExtValue=\"\"/><filter checkType=\"NumberText\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isNullable=\"true\" isRowEnd=\"true\" name=\"GxVehiclePriceEnd\" operator=\"*\" rows=\"1\" tableName=\"\" title=\"到\" type=\"input\" width=\"190\" issExtValue=\"\"/><filter isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"true\" name=\"null\" width=\"150\"/><filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"false\" isRowEnd=\"false\" name=\"SearchCode\" onchange=\"UpperCase()\" operator=\"*\" rows=\"1\" tableName=\"\" title=\"拼音简码\" type=\"input\" width=\"190\" issExtValue=\"\"/><filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"false\" isRowEnd=\"false\" name=\"BrandName\" operator=\"*\" rows=\"1\" tableName=\"\" title=\"品牌\" type=\"input\" width=\"190\" issExtValue=\"\"/><filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\"  isRowBegin=\"false\" isRowEnd=\"false\" name=\"CModelCde\" operator=\"*\" rows=\"1\" tableName=\"\" title=\"车型代码\" type=\"input\" width=\"190\" issExtValue=\"\" /><filter isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"true\" name=\"null\" width=\"150\"/><filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"" + vin + "\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isReadOnly=\"false\" isRowBegin=\"false\" isRowEnd=\"false\" name=\"VinCode\" operator=\"*\" rows=\"1\" tableName=\"\" title=\"VIN（前8位）\" type=\"input\" width=\"190\" issExtValue=\"" + vin + "\"/><filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowEnd=\"true\" name=\"FamilyName\" operator=\"*\" rows=\"1\" tableName=\"\" title=\"车型系列\" type=\"input\" width=\"190\" issExtValue=\"\"/><filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"false\" isRowEnd=\"false\" name=\"VehicleMaker\" operator=\"*\" rows=\"1\" tableName=\"\" title=\"生产厂商\" type=\"input\" width=\"190\" issExtValue=\"\"/><filter isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"false\" isRowEnd=\"false\" name=\"\" width=\"150\"/><filter isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowEnd=\"true\" name=\"\" width=\"150\"/></filters></data>"
    DW_DATA = URLEncoder.encode(DW_DATA, "utf-8");
    return DW_DATA
}

// 核保查询/承保查询 参数组装
def getQueryQuotedata(AutoTask autoTask, Order order, String code, TAppTmStart, TAppTmEnd, page, pagecount) {
    def config = autoTask.getConfigs();
    def BaseCDptCde = config?.insureOrgCode ?: config?.organizationCode;
    def showOrganizationCode = config?.showInsureOrgCode ?: config?.showOrganizationCode;
    String CAppNo = ""
    String policyNo = ""
    def CAppStatus = "请选择"
    def CAppCode = ""

    def enquiry = autoTask.taskEntity
    if (!autoTask.taskType.contains("policyquery")) {
        def policyFlag = ""
        if (autoTask.taskType.contains("Policy") || autoTask.taskType.contains("Download")) { //保单抓取
            CAppStatus = "已出保单"
            CAppCode = "5"
            if (autoTask.taskType.contains("Download")) {
                def bizpolicy = JSON.parseObject(autoTask.applyJson)?.sq?.bizPolicyNo ?: JSON.parseObject(autoTask.applyJson)?.sq?.sypolicyno
                def efcpolicy = JSON.parseObject(autoTask.applyJson)?.sq?.efcPolicyNo ?: JSON.parseObject(autoTask.applyJson)?.sq?.jqpolicyno
                policyNo = efcpolicy ?: bizpolicy
                autoTask.tempValues.bizpolicy = bizpolicy
                autoTask.tempValues.efcpolicy = efcpolicy
                autoTask.tempValues.policyNo = policyNo
            }
        }
        if ("efc".equals(code)) {
            CAppNo = getRealValue(enquiry?.efcProposeNum);
            policyFlag = CAppNo.substring(5, 9)
            if (!"0326".equals(policyFlag) && "0364".equals(policyFlag)) {
                throw new InsReturnException("该投保单号为商业险单号,请核实后输入正确的交强险投保单号:" + policyNo)
            }
        } else if ("biz".equals(code)) {
            CAppNo = getRealValue(enquiry?.bizProposeNum);
            policyFlag = CAppNo.substring(5, 9)
            if ("0326".equals(policyFlag) && !"0364".equals(policyFlag)) {
                throw new InsReturnException("该投保单号为交强险单号,请核实后输入正确的商业险投保单号:" + policyNo)
            }
        }
    } else {
        Map<String, String> policynoMap = (Map<String, String>) autoTask.tempValues.policyno
        if ("efc".equals(code)) {
            policyNo = policynoMap.get("efcProposeNum")
            if (StringUtil.isNoEmpty(policyNo)) {
                def policyFlag = policyNo.substring(5, 9);
                if (!"0326".equals(policyFlag) && "0364".equals(policyFlag)) {
                    throw new InsReturnException("该保单号为商业险保单号,请核实后输入正确的交强险保单号:" + policyNo)
                }
            }
        } else if ("biz".equals(code)) {
            policyNo = policynoMap.get("bizProposeNum")
            if (StringUtil.isNoEmpty(policyNo)) {
                def policyFlag = policyNo.substring(5, 9);
                if ("0326".equals(policyFlag) && !"0364".equals(policyFlag)) {
                    throw new InsReturnException("该保单号为交强险保单号,请核实后输入正确的商业险保单号:" + policyNo)
                }
            }
        }
    }
    String DW_DATA = "<data><dataObjs type=\"ONE_SELECT\"  dwid=\"dwid0.18537766702177516\" rsCount=\"1\" pageSize=\"20\" pageNo=\"" + page + "\" pageCount=\"" + pagecount + "\" dwName=\"policy.app_auditing_query_DW\" difFlag=\"false\"></dataObjs>" +
            "<filters colsInOneRow=\"2\" dwName=\"policy.app_auditing_query_DW\">" +
            "<filter isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"true\" name=\"null\" width=\"150\"/>" +
            "<filter checkType=\"Text\" class=\"input250 mustinput read\" codeKind=\"codeKind\" codelistname=\"dataDptSet\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"" + BaseCDptCde + "\" isGroupBegin=\"true\" isGroupEnd=\"false\" isHidden=\"false\" isNullable=\"false\" isReadOnly=\"false\" isRowBegin=\"false\" isRowEnd=\"false\" name=\"CDptCde\" operator=\"2\" rows=\"1\" tableName=\"\" title=\"机构\" type=\"issSelect\" width=\"250\" initVal=\"" + BaseCDptCde + "\" issExtValue=\"" + showOrganizationCode + "\"/>" +
            "<filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"1\" isGroupBegin=\"false\" isGroupEnd=\"true\" isHidden=\"false\" name=\"LoadSub\" operator=\"2\" rows=\"1\" tableName=\"\" title=\"包含下级\" type=\"checkbox\" width=\"70\" initVal=\"1\" issExtValue=\"1\"/>" +
            "<filter checkType=\"Text\" codeKind=\"codeKind\" codelistname=\"AppState_List\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"" + CAppCode + "\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowEnd=\"true\" name=\"CAppStatus\" operator=\"*\" rows=\"1\" title=\"状态\" type=\"issSelect\" width=\"250\" initVal=\"\" issExtValue=\"" + CAppStatus + "\"/>" +
            "<filter isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"true\" name=\"null\" width=\"150\"/>" +
            "<filter checkType=\"Text\" codeKind=\"codeKind\" codelistname=\"NewDataPermProd_List\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"false\" isRowEnd=\"false\" name=\"CProdNo\" operator=\"*\" rows=\"1\" title=\"产品\" type=\"issSelect\" width=\"250\" initVal=\"\" issExtValue=\"请选择\"/>" +
            "<filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"" + policyNo + "\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowEnd=\"true\" maxLength=\"100\" name=\"CPlyNo\" operator=\"2\" rows=\"1\" tableName=\"\" title=\"保单号\" type=\"input\" width=\"250\" initVal=\"" + policyNo + "\" issExtValue=\"" + policyNo + "\"/>" +
            "<filter isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"true\" name=\"null\" width=\"150\"/>" +
            "<filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"" + CAppNo + "\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"false\" isRowEnd=\"false\" maxLength=\"100\" name=\"CAppNo\" operator=\"2\" rows=\"1\" tableName=\"\" title=\"申请单号\" type=\"input\" width=\"250\" initVal=\"" + CAppNo + "\" issExtValue=\"" + CAppNo + "\"/>" +
            "<filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowEnd=\"true\" maxLength=\"100\" name=\"CInsuredNme\" operator=\"8\" rows=\"1\" tableName=\"\" title=\"被保险人\" type=\"input\" width=\"250\" initVal=\"\" issExtValue=\"\"/>" +
            "<filter isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"true\" name=\"null\" width=\"150\"/>" +
            "<filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"false\" isRowEnd=\"false\" maxLength=\"100\" name=\"CPlateNo\" operator=\"2\" rows=\"1\" tableName=\"\" title=\"车牌号码\" type=\"input\" width=\"250\" initVal=\"\" issExtValue=\"\"/>" +
            "<filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowEnd=\"true\" maxLength=\"100\" name=\"CFrmNo\" operator=\"2\" rows=\"1\" tableName=\"\" title=\"车架号\" type=\"input\" width=\"250\" initVal=\"\" issExtValue=\"\"/>" +
            "<filter isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"true\" name=\"null\" width=\"150\"/>" +
            "<filter checkType=\"Text\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"false\" isRowEnd=\"false\" maxLength=\"100\" name=\"CEngNo\" operator=\"2\" rows=\"1\" tableName=\"\" title=\"发动机号\" type=\"input\" width=\"250\" initVal=\"\" issExtValue=\"\"/>" +
            "<filter checkType=\"Date\" cols=\"1\" dataType=\"DATE\" dateFormat=\"yyyy-MM-dd\" defaultValue=\"" + TAppTmStart + "\" isGroupBegin=\"true\" isGroupEnd=\"false\" isHidden=\"false\" isNullable=\"false\" label=\"投保日期\" name=\"TAppTmStart\" onblur=\"compareFilterDate(this, getFilterByObj(this, 'TAppTmEnd'), this)\" operator=\"3\" rows=\"1\" tableName=\"\" title=\"投保日期\" type=\"input\" width=\"115\" issExtValue=\"" + TAppTmStart + "\" initVal=\"" + TAppTmStart + "\"/>" +
            "<filter checkType=\"Date\" cols=\"1\" dataType=\"DATE\" dateFormat=\"yyyy-MM-dd\" defaultValue=\"" + TAppTmEnd + "\" isGroupBegin=\"false\" isGroupEnd=\"true\" isHidden=\"false\" isNullable=\"false\" isRowEnd=\"true\" name=\"TAppTmEnd\" onblur=\"compareFilterDate(getFilterByObj(this, 'TAppTmStart'), this, this)\" operator=\"1\" rows=\"1\" tableName=\"\" title=\"-\" type=\"input\" width=\"115\" issExtValue=\"" + TAppTmEnd + "\" initVal=\"" + TAppTmEnd + "\"/>" +
            "<filter isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"true\" name=\"null\" width=\"150\"/>" +
            "<filter checkType=\"Text\" codeKind=\"codeKind\" codelistname=\"QuerySlsPay_List\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowBegin=\"false\" isRowEnd=\"false\" name=\"CSlsId\" operator=\"8\" rows=\"1\" tableName=\"\" title=\"业务员\" type=\"issSelect\" width=\"250\" initVal=\"\" issExtValue=\"请选择\"/>" +
            "<filter checkType=\"Text\" codeKind=\"codeKind\" codelistname=\"CChaCde_All_list\" cols=\"1\" dataType=\"STRING\" dateFormat=\"yyyy-MM-dd HH:mm\" defaultValue=\"\" isGroupBegin=\"true\" isGroupEnd=\"true\" isHidden=\"false\" isRowEnd=\"true\" name=\"CChaCde\" operator=\"*\" rows=\"1\" title=\"代理人\" type=\"issSelect\" width=\"250\" initVal=\"\" issExtValue=\"请选择\"/></filters></data>";
    DW_DATA = URLEncoder.encode(DW_DATA, "utf-8");
    autoTask.tempValues.query_quote_DW_DATA = DW_DATA;
    return DW_DATA;
}

def paramData(data) {
    def param = [:]
    param << [
            ACTION_HANDLE     : "perform",
            BEAN_HANDLE       : "baseAction",
            BIZ_SYNCH_CONTINUE: "false",
            DW_DATA           : data,
            HELPCONTROLMETHOD : "common",
            SCENE             : "UNDEFINED",
            SERVICE_TYPE      : "ACTION_SERVIC"

    ]
    param
}
//投保单明细查询 参数组装 DW_DATA
def getDetailDW_DATA(AutoTask autoTask) {
    def CDptCde = autoTask.tempValues.get("CDptCde");
    def CProdNo = autoTask.tempValues.get("CProdNo");
    def dwDataJSONArray = new JSONArray()

    def PAY_DW_JSONObject = getJSONOByDwName("prodDef.vhl.PAY_DW", "2")
    def Base_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Base_DW", "2")
    def Vhl_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Vhl_DW", "2")
    def Cvrg_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Cvrg_DW", "2")
    def PrmCoef_DW_JSONObject = getJSONOByDwName("prodDef.vhl.PrmCoef_DW", "2")
    def Applicant_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Applicant_DW", "2")
    def Insured_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Insured_DW", "2")
    def Vhlowner_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Vhlowner_DW", "2")
    def PAY_DW_JSONObject2 = getJSONOByDwName("prodDef.vhl.PAY_DW", "2")
    def VhlDrv_DW_JSONObject = getJSONOByDwName("prodDef.vhl.VhlDrv_DW", "2")
    def Acctinfo_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Acctinfo_DW", "2")
    def edr_base_DW_JSONObject = getJSONOByDwName("prodDef.vhl.edr_base_DW", "2")
    def edr_rsn_DW_JSONObject = getJSONOByDwName("prodDef.vhl.edr_rsn_DW", "2")
    def edr_cmp_item_DW_JSONObject = getJSONOByDwName("prodDef.vhl.edr_cmp_item_DW", "2")
    def Bnfc_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Bnfc_DW", "2")
    def Taxation_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Taxation_DW", "2")
    def VsTax_DW_JSONObject = getJSONOByDwName("prodDef.vhl.VsTax_DW", "2")
    def VhlInsurance_DW_JSONObject = getJSONOByDwName("prodDef.vhl.VhlInsurance_DW", "2")
    //prodDef.vhl.PAY_DW 支付信息
    JSONArray dataObjVoListArray = getDataObjVoListByDwName("PAY_DW", "insert")
    PAY_DW_JSONObject.put("dataObjVoList", dataObjVoListArray)

    //VhlInsurance_DW_JSONObject非车险信息
    dataObjVoListArray = getDataObjVoListByDwName("VhlInsurance_DW", "insert")
    VhlInsurance_DW_JSONObject.put("dataObjVoList", dataObjVoListArray)

    //prodDef.vhl.Base_DW
    dataObjVoListArray = getDataObjVoListByDwName("Base_DW", "insert", CProdNo, CDptCde)
    Base_DW_JSONObject.put("dataObjVoList", dataObjVoListArray)

    //prodDef.vhl.Vhl_DW
    dataObjVoListArray = getDataObjVoListByDwName("Vhl_DW", "insert")
    Vhl_DW_JSONObject.put("dataObjVoList", dataObjVoListArray)

    //prodDef.vhl.Cvrg_DW
    Cvrg_DW_JSONObject.put("dataObjVoList", new JSONArray())

    //prodDef.vhl.PrmCoef_DW
    dataObjVoListArray = getDataObjVoListByDwName("PrmCoef_DW", "insert")
    PrmCoef_DW_JSONObject.put("dataObjVoList", dataObjVoListArray)

    //prodDef.vhl.Applicant_DW
    dataObjVoListArray = getDataObjVoListByDwName("Applicant_DW", "insert")
    Applicant_DW_JSONObject.put("dataObjVoList", dataObjVoListArray)

    //prodDef.vhl.Insured_DW 被保人信息
    dataObjVoListArray = getDataObjVoListByDwName("Insured_DW", "insert")
    Insured_DW_JSONObject.put("dataObjVoList", dataObjVoListArray)

    //prodDef.vhl.Vhlowner_DW 车主信息
    dataObjVoListArray = getDataObjVoListByDwName("Vhlowner_DW", "insert")
    Vhlowner_DW_JSONObject.put("dataObjVoList", dataObjVoListArray)

    //prodDef.vhl.PAY_DW2 支付信息
    dataObjVoListArray = getDataObjVoListByDwName("PAY_DW2", "insert")
    PAY_DW_JSONObject2.put("dataObjVoList", dataObjVoListArray)

    //prodDef.vhl.VhlDrv_DW
    VhlDrv_DW_JSONObject.put("dataObjVoList", new JSONArray())

    //prodDef.vhl.Acctinfo_DW
    Acctinfo_DW_JSONObject.put("dataObjVoList", getAcctinfo_DW(autoTask, "no"))

    //prodDef.vhl.edr_base_DW
    dataObjVoListArray = getDataObjVoListByDwName("edr_base_DW", "insert")
    edr_base_DW_JSONObject.put("dataObjVoList", dataObjVoListArray)

    //prodDef.vhl.edr_rsn_DW
    edr_rsn_DW_JSONObject.put("dataObjVoList", new JSONArray())

    //prodDef.vhl.edr_cmp_item_DW
    edr_cmp_item_DW_JSONObject.put("dataObjVoList", new JSONArray())

    //prodDef.vhl.Bnfc_DW 受益人信息
    dataObjVoListArray = getDataObjVoListByDwName("Bnfc_DW", "insert")
    Bnfc_DW_JSONObject.put("dataObjVoList", dataObjVoListArray)

    //prodDef.vhl.Taxation_DW 税务信息
    dataObjVoListArray = getDataObjVoListByDwName("Taxation_DW", "insert")
    Taxation_DW_JSONObject.put("dataObjVoList", dataObjVoListArray)

    //prodDef.vhl.Mechine_DW 设备信息
    VsTax_DW_JSONObject.put("dataObjVoList", new JSONArray())

    dwDataJSONArray.add(0, PAY_DW_JSONObject)
    dwDataJSONArray.add(1, Base_DW_JSONObject)
    dwDataJSONArray.add(2, Vhl_DW_JSONObject)
    dwDataJSONArray.add(3, Cvrg_DW_JSONObject)
    dwDataJSONArray.add(4, PrmCoef_DW_JSONObject)
    dwDataJSONArray.add(5, Applicant_DW_JSONObject)
    dwDataJSONArray.add(6, Insured_DW_JSONObject)
    dwDataJSONArray.add(7, Vhlowner_DW_JSONObject)
    dwDataJSONArray.add(8, PAY_DW_JSONObject2)
    dwDataJSONArray.add(9, VhlDrv_DW_JSONObject)
    dwDataJSONArray.add(10, Acctinfo_DW_JSONObject)
    dwDataJSONArray.add(11, edr_base_DW_JSONObject)
    dwDataJSONArray.add(12, edr_rsn_DW_JSONObject)
    dwDataJSONArray.add(13, edr_cmp_item_DW_JSONObject)
    dwDataJSONArray.add(14, Bnfc_DW_JSONObject)
    dwDataJSONArray.add(15, Taxation_DW_JSONObject)
    dwDataJSONArray.add(16, VsTax_DW_JSONObject)
    dwDataJSONArray.add(17, VhlInsurance_DW_JSONObject)
    return dwDataJSONArray.toJSONString()
}


//新增atrributeVoListArray  工具
def getDataObjVoListByDwName(String dwName, String status, Object... args) {
    def script = new robot_2022_assemble_data()
    JSONArray attributeVoListArray = new JSONArray()
    switch (dwName) {
        case "Base_DW": attributeVoListArray = script.getBaseDataJsonArray(); break
        case "Vhl_DW": attributeVoListArray = script.getVhlDataJsonArray("quote"); break
        case "PrmCoef_DW": attributeVoListArray = script.getQueryData_PrmCoef_DW(); break
        case "Applicant_DW": attributeVoListArray = script.getQueryData_Applicant_DW(); break
        case "Insured_DW": attributeVoListArray = script.getQueryData_Insured_DW(); break
        case "Bnfc_DW": attributeVoListArray = script.getBnfc_DW(); break
        case "Vhlowner_DW": attributeVoListArray = script.getQueryData_Vhlowner_DW(); break
        case "Taxation_DW": attributeVoListArray = script.getQueryData_Taxation_DW(); break
        case "VhlInsurance_DW": attributeVoListArray = script.getQueryData_VhlInsurance_DW(); break

    }

    for (int i = 0; i < attributeVoListArray.size(); i++) {
        JSONObject jsonObject = attributeVoListArray.getJSONObject(i);
        String name = jsonObject.get("name").toString();
        if ("Base_DW".equals(dwName)) {
            switch (name) {
                case "Base.CProdNo": jsonObject.put("newValue", args[0]); break
                case "JQ_Base.NAmt": jsonObject.put("newValue", "0.00"); break
                case "JQ_Base.NPrm": jsonObject.put("newValue", "0.00"); break
                case "SY_Base.NAmt": jsonObject.put("newValue", "0.00"); break
                case "SY_Base.NIrrRatio": jsonObject.put("newValue", "1.0"); break
                case "SY_Base.NPrm": jsonObject.put("newValue", "0.00"); break
                case "SY_Base.NBefTaxPrm": jsonObject.put("newValue", "0.00"); break
                case "SY_Base.NAppTaxAmt": jsonObject.put("newValue", "0.00"); break
                case "JQ_Base.NBefTaxPrm": jsonObject.put("newValue", "0.00"); break
                case "JQ_Base.NAppTaxAmt": jsonObject.put("newValue", "0.00"); break
                case "Base.CCommonFlag": jsonObject.put("newValue", "0"); break
                case "SY_Base.NBasePrm": jsonObject.put("newValue", "0.00"); break
                case "JQ_Base.NBasePrm": jsonObject.put("newValue", "0.00"); break
                case "SY_Base.CUnfixSpcChange": jsonObject.put("newValue", "0"); break
                case "JQ_Base.CUnfixSpcChange": jsonObject.put("newValue", "0"); break
                case "Base.CTransMrk": jsonObject.put("newValue", "0"); break
                case "Base.CDptCde": jsonObject.put("newValue", args[1]); break
                case "Base.CExceptionTyp": jsonObject.put("newValue", "Typ004"); break
                case "Base.CRatioTyp": jsonObject.put("newValue", "340001"); break
                case "Base.CPdfFlag": jsonObject.put("newValue", "1"); break
                case "Base.CDisptSttlCde": jsonObject.put("newValue", "007001"); break
                case "Base.CResvTxt4": jsonObject.put("newValue", "536001"); break
                case "Base.CFinTyp": jsonObject.put("newValue", "002001"); break
                case "Base.CDataSrc": jsonObject.put("newValue", "V7"); break
                case "Base.CPdfAppFlag": jsonObject.put("newValue", "1"); break
                case "Base.CRiskLevel": jsonObject.put("newValue", "00"); break
                case "Base.CPrmCur": jsonObject.put("newValue", "01"); break
                case "Base.CAmtCur": jsonObject.put("newValue", "01"); break
                case "Base.NPrmRmbExch": jsonObject.put("newValue", "1.000000"); break
                case "JQ_Base.NExtPrm": jsonObject.put("newValue", "0.00"); break
                case "SY_Base.NExtPrm": jsonObject.put("newValue", "0.00"); break
                case "Base.CAgriMrk": jsonObject.put("newValue", "0"); break
            }
        } else if ("Vhl_DW".equals(dwName)) {
            switch (name) {
                case "Vhl.CGlassTyp": jsonObject.put("newValue", "303011001"); break
                case "SY_Vhl.NPoWeight": jsonObject.put("newValue", "0.0"); break
                case "JQ_Vhl.CBusType": jsonObject.put("newValue", "0"); break
                case "SY_Vhl.CBusType": jsonObject.put("newValue", "0"); break
                case "SY_Vhl.CDevice4Mrk": jsonObject.put("newValue", "0"); break
                case "SY_Vhl.CFullEdrMrk": jsonObject.put("newValue", "0"); break
                case "Vhl.CUsageCde": jsonObject.put("newValue", "336001"); break
                case "Vhl.CBizMrk": jsonObject.put("newValue", "338001"); break
                case "Vhl.NNewPurchaseValue": jsonObject.put("newValue", "0.00"); break
                case "Vhl.NSeatNum": jsonObject.put("newValue", "0"); break
                case "Vhl.NResvNum1": jsonObject.put("newValue", "5"); break
                case "Vhl.CPlateColor": jsonObject.put("newValue", "0"); break
                case "Vhl.CDisplacementLvl": jsonObject.put("newValue", "0"); break
                case "Vhl.CPlateTyp": jsonObject.put("newValue", "02"); break
                case "Vhl.CProdPlace": jsonObject.put("newValue", "0"); break
                case "Vhl.CEcdemicMrk": jsonObject.put("newValue", "0"); break
                case "Vhl.CNewMrk": jsonObject.put("newValue", "1"); break
                case "Vhl.CChgOwnerFlag": jsonObject.put("newValue", "0"); break
                case "Vhl.CMortgageMrk": jsonObject.put("newValue", "0"); break
                case "SY_Vhl.CDevice8Mrk": jsonObject.put("newValue", "0"); break
                case "Vhl.CHangDep": jsonObject.put("newValue", "0"); break
                case "Vhl.CUploadMsg": jsonObject.put("newValue", "0"); break
                case "Vhl.CInspectionCde": jsonObject.put("newValue", "305005003"); break
                case "Vhl.CFleetMrk": jsonObject.put("newValue", "0"); break
            }
        } else if ("PrmCoef_DW".equals(dwName)) {
            switch (name) {
                case "JQ_PrmCoef.NAgoVioProFloat": jsonObject.put("newValue", "1"); break
                case "JQ_PrmCoef.NAgoClaProFloat": jsonObject.put("newValue", "1"); break
                case "JQ_PrmCoef.CDruDrinNum": jsonObject.put("newValue", "1"); break
                case "JQ_PrmCoef.CDruDrvNum": jsonObject.put("newValue", "1"); break
                case "JQ_PrmCoef.CProDruDrinNum": jsonObject.put("newValue", "1"); break
                case "JQ_PrmCoef.CUnproDrinDrvNum": jsonObject.put("newValue", "1"); break
                case "JQ_PrmCoef.CProDruDrvNum": jsonObject.put("newValue", "1"); break
                case "JQ_PrmCoef.CUnproDruDrvNum": jsonObject.put("newValue", "1"); break
                case "JQ_PrmCoef.NTotDisc": jsonObject.put("newValue", "1.0"); break
                case "SY_PrmCoef.NAutonomyAdjust": jsonObject.put("newValue", "1"); jsonObject.put("bakValue", "1"); break
                case "SY_PrmCoef.NChannelAdjust": jsonObject.put("newValue", "1"); jsonObject.put("bakValue", "1"); break
                case "SY_PrmCoef.NAgoClmRecQuick": jsonObject.put("newValue", "1"); break
                case "SY_PrmCoef.NSafeDev": jsonObject.put("newValue", "1"); break
                case "SY_PrmCoef.NResvNum8": jsonObject.put("newValue", "1"); break
                case "SY_PrmCoef.NTotDisc": jsonObject.put("newValue", "1"); break
                case "SY_PrmCoef.NAddiInsLow": jsonObject.put("newValue", "1"); break
            }
        } else if ("Applicant_DW".equals(dwName)) {
            switch (name) {
                case "Applicant.CClntMrk": jsonObject.put("newValue", "1"); break
                case "Applicant.CCertfCls": jsonObject.put("newValue", "120001"); break
                case "Applicant.IsLongTimeChecked": jsonObject.put("newValue", "0"); break
                case "Applicant.CClntAddr": jsonObject.put("newValue", "*"); break
                case "appCheckButton": jsonObject.put("newValue", "点击获取投保人身份证信息"); break
                case "appInfoCollectButton": jsonObject.put("newValue", "点击采集投保、告知单信息"); break
                case "Applicant.CIsIncola": jsonObject.put("newValue", "1"); break
                case "Applicant.CDptRelation": jsonObject.put("newValue", "*"); break
                case "Applicant.IsLegalCertfClsLongTimeChecked": jsonObject.put("newValue", "0"); break
                case "Applicant.IsShareHolderCertfClsLongTimeChecked": jsonObject.put("newValue", "0"); break
                case "Applicant.IsChargePerCertfClsLongTimeChecked": jsonObject.put("newValue", "0"); break
                case "Applicant.IsLicensingPersonCertfClsLongTimeChecked": jsonObject.put("newValue", "0"); break
                case "Applicant.CInsuredSameApplicant": jsonObject.put("newValue", "1"); break
                case "Applicant.CVhlownerSameApplicant": jsonObject.put("newValue", "1"); break
                case "Applicant.CVhlownerSameInsured": jsonObject.put("newValue", "0"); break
                case "Applicant.CBnfcChecked": jsonObject.put("newValue", "0"); break
                case "Applicant.CBnfcSameAsApplicant": jsonObject.put("newValue", "0"); break
                case "Applicant.CBnfcSameInsured": jsonObject.put("newValue", "0"); break
            }
        } else if ("Insured_DW".equals(dwName)) {
            switch (name) {
                case "Insured.CClntMrk": jsonObject.put("newValue", "1"); break
                case "Insured.CCertfCls": jsonObject.put("newValue", "120001"); break
                case "Insured.IsLongTimeChecked": jsonObject.put("newValue", "0"); break
                case "Insured.CClntAddr": jsonObject.put("newValue", "*"); break
                case "inrCheckButton": jsonObject.put("newValue", "点击获取被保人身份证信息"); break
                case "Insured.IsLegalCertfClsLongTimeChecked": jsonObject.put("newValue", "0"); break
                case "Insured.IsShareHolderCertfClsLongTimeChecked": jsonObject.put("newValue", "0"); break
                case "Insured.IsChargePerCertfClsLongTimeChecked": jsonObject.put("newValue", "0"); break
                case "Insured.IsLicensingPersonCertfClsLongTimeChecked": jsonObject.put("newValue", "0"); break
                case "Insured.CIsIncola": jsonObject.put("newValue", "1"); break
            }
        } else if ("Vhlowner_DW".equals(dwName)) {
            switch (name) {
                case "Vhlowner.COwnerCls": jsonObject.put("newValue", "1"); break
                case "Vhlowner.CCertfCls": jsonObject.put("newValue", "120001"); break
                case "Vhlowner.CClntAddr": jsonObject.put("newValue", "*"); break
            }
        }
    }
    def dataObjVoListArray = getDefaultDataObjVoListArray(status)
    dataObjVoListArray.getJSONObject(0).put("attributeVoList", attributeVoListArray)
    return dataObjVoListArray
}


/**
 * 快速续保 请求参数组装
 * @param autoTask
 * @param order
 * @return
 */
def getRenewalQueryData(AutoTask autoTask, Order order) {
    String organizationCode = autoTask.getConfigs().insureOrgCode ?: autoTask.getConfigs().organizationCode
    String biz = getRealValue(autoTask.tempValues.get("renewalCPlyNobiz"));
    String efc = getRealValue(autoTask.tempValues.get("renewalCPlyNoefc"));

    String cust_data = "scene=PLY_APP_STRICT_RENEW_SCENE###prodNoIsOt=0364_0326###dptCdeIsOt=" + organizationCode + "###syAppNo=null###jqAppNo=null###syPlyNo=" + biz + "###jqPlyNo=" + efc + "###taskId=null###updTm=null###appFromTmpl=null###appNo=null###relAppNo=null###isCopy=null###relPlyNo=null###subSidiary=05###renewMrk=###edrType=null###edrRsnOrBundle=null###qteId=###syEdrNo=null###jqEdrNo=null###edrNo=null"

    autoTask.tempValues.renewalQuerycust_data = cust_data;
    //使用循环组装请求参数(此时组装的参数值为空,部分参数需要赋值)
    String renewalQuerydw_data = getQueryRenewlDatas(autoTask)
    JSONArray renewJsonArrayData = JSONArray.parseArray(renewalQuerydw_data)

    JSONObject jsonObj = null
    JSONArray dataVoList = null
    JSONArray attributeVoList = null
    JSONObject attributeJsonObj = null
    ListOrderedMap map = null
    String name = ""
    for (int index = 0; index < renewJsonArrayData.size(); index++) {
        jsonObj = renewJsonArrayData.getJSONObject(index)
        //调整顺序
        String dwType = jsonObj.getString("dwType")
        String dwName = jsonObj.getString("dwName")
        jsonObj.remove("isFilter")
        jsonObj.remove("dwType")
        jsonObj.remove("dwName")
        jsonObj.remove("rsCount")
        jsonObj.remove("pageSize")
        jsonObj.remove("pageNo")
        jsonObj.remove("maxCount")
        jsonObj.remove("filterMapList")
        map = JSON.parseObject(jsonObj.toString(), ListOrderedMap.class)
        map.put(0, "isFilter", "false")
        map.put(1, "dwType", dwType)
        map.put(2, "dwName", dwName)
        map.put(3, "rsCount", "1")
        map.put(4, "pageSize", "10")
        map.put(5, "pageNo", "1")
        map.put(7, "maxCount", "1000")
        map.put(9, "filterMapList", new JSONArray())
        jsonObj = new JSONObject(map)
        renewJsonArrayData.fluentRemove(index).fluentAdd(index, jsonObj);

        dataVoList = jsonObj.getJSONArray("dataObjVoList")
        if (dataVoList.size() > 0) {
            //调整status的顺序
            JSONObject jsonObject = dataVoList.getJSONObject(0)
            jsonObject.remove("status")
            map = JSON.parseObject(jsonObject.toString(), ListOrderedMap.class);
            map.put(2, "status", "INSERTED");
            jsonObject = new JSONObject(map);
            dataVoList.fluentRemove(0).fluentAdd(0, jsonObject);
            attributeVoList = jsonObject.getJSONArray("attributeVoList")

            for (int i = 0; i < attributeVoList.size(); i++) {
                attributeJsonObj = attributeVoList.getJSONObject(i)
                name = attributeJsonObj.getString("name")
                switch (name) {
                //prodDef.vhl.Base_DW 部分
                    case "Base.CDptCde": attributeJsonObj.put("newValue", organizationCode); break
                    case "Base.CProdNo": attributeJsonObj.put("newValue", "0364_0326"); break
                    case "JQ_Base.NAmt": attributeJsonObj.put("newValue", "0.00"); break
                    case "JQ_Base.NPrm": attributeJsonObj.put("newValue", "0.00"); break
                    case "SY_Base.NAmt": attributeJsonObj.put("newValue", "0.00"); break
                    case "SY_Base.NIrrRatio": attributeJsonObj.put("newValue", "1.0"); break
                    case "SY_Base.NPrm": attributeJsonObj.put("newValue", "0.00"); break
                    case "SY_Base.NBefTaxPrm": attributeJsonObj.put("newValue", "0.00"); break
                    case "SY_Base.NAppTaxAmt": attributeJsonObj.put("newValue", "0.00"); break
                    case "JQ_Base.NBefTaxPrm": attributeJsonObj.put("newValue", "0.00"); break
                    case "JQ_Base.NAppTaxAmt": attributeJsonObj.put("newValue", "0.00"); break
                    case "Base.CCommonFlag": attributeJsonObj.put("newValue", "0"); break
                    case "SY_Base.NBasePrm": attributeJsonObj.put("newValue", "0.00"); break
                    case "JQ_Base.NBasePrm": attributeJsonObj.put("newValue", "0.00"); break
                    case "SY_Base.CUnfixSpcChange": attributeJsonObj.put("newValue", "0"); break
                    case "JQ_Base.CUnfixSpcChange": attributeJsonObj.put("newValue", "0"); break
                    case "Base.CTransMrk": attributeJsonObj.put("newValue", "0"); break
                    case "Base.CRatioTyp": attributeJsonObj.put("newValue", "340001"); break
                    case "Base.CPdfFlag": attributeJsonObj.put("newValue", "1"); break
                    case "Base.CDisptSttlCde": attributeJsonObj.put("newValue", "007001"); break
                    case "Base.CResvTxt4": attributeJsonObj.put("newValue", "536001"); break
                    case "Base.CFinTyp": attributeJsonObj.put("newValue", "002001"); break
                    case "Base.CDataSrc": attributeJsonObj.put("newValue", "V7"); break
                    case "Base.CPrmCur": attributeJsonObj.put("newValue", "01"); break
                    case "Base.CAmtCur": attributeJsonObj.put("newValue", "01"); break
                    case "Base.NPrmRmbExch": attributeJsonObj.put("newValue", "1.000000"); break
                    case "JQ_Base.NExtPrm": attributeJsonObj.put("newValue", "0.00"); break
                    case "SY_Base.NExtPrm": attributeJsonObj.put("newValue", "0.00"); break
                    case "Base.CAgriMrk": attributeJsonObj.put("newValue", "0"); break
                        //prodDef.vhl.Vhl_DW 部分
                    case "SY_Vhl.NPoWeight": attributeJsonObj.put("newValue", "0.0"); break
                    case "JQ_Vhl.CBusType": attributeJsonObj.put("newValue", "0"); break
                    case "SY_Vhl.CBusType": attributeJsonObj.put("newValue", "0."); break
                    case "SY_Vhl.CDevice4Mrk": attributeJsonObj.put("newValue", "0"); break
                    case "SY_Vhl.CFullEdrMrk": attributeJsonObj.put("newValue", "0"); break
                    case "Vhl.NNewPurchaseValue": attributeJsonObj.put("newValue", "0.00"); break
                    case "Vhl.CNewMrk": attributeJsonObj.put("newValue", "1"); break
                    case "Vhl.CChgOwnerFlag": attributeJsonObj.put("newValue", "0"); break
                    case "Vhl.CMortgageMrk": attributeJsonObj.put("newValue", "0"); break
                    case "SY_Vhl.CDevice8Mrk": attributeJsonObj.put("newValue", "0"); break
                    case "Vhl.CHangDep": attributeJsonObj.put("newValue", "0"); break
                    case "Vhl.CUploadMsg": attributeJsonObj.put("newValue", "0"); break
                    case "Vhl.CInspectionCde": attributeJsonObj.put("newValue", "305005003"); break
                    case "Vhl.CFleetMrk": attributeJsonObj.put("newValue", "0"); break
                        //prodDef.vhl.PrmCoef_DW 部分
                    case "JQ_PrmCoef.NAgoVioProFloat": attributeJsonObj.put("newValue", "1"); break
                    case "JQ_PrmCoef.NAgoClaProFloat": attributeJsonObj.put("newValue", "1"); break
                    case "JQ_PrmCoef.CDruDrinNum": attributeJsonObj.put("newValue", "1"); break
                    case "JQ_PrmCoef.CDruDrvNum": attributeJsonObj.put("newValue", "1"); break
                    case "JQ_PrmCoef.CProDruDrinNum": attributeJsonObj.put("newValue", "1"); break
                    case "JQ_PrmCoef.CUnproDrinDrvNum": attributeJsonObj.put("newValue", "1"); break
                    case "JQ_PrmCoef.CProDruDrvNum": attributeJsonObj.put("newValue", "1"); break
                    case "JQ_PrmCoef.CUnproDruDrvNum": attributeJsonObj.put("newValue", "1"); break
                    case "JQ_PrmCoef.NTotDisc": attributeJsonObj.put("newValue", "1.0"); break
                    case "SY_PrmCoef.NAutonomyAdjust": attributeJsonObj.put("newValue", "1"); break
                    case "SY_PrmCoef.NChannelAdjust": attributeJsonObj.put("newValue", "1"); break
                    case "SY_PrmCoef.NAgoClmRecQuick": attributeJsonObj.put("newValue", "1"); break
                    case "SY_PrmCoef.NSafeDev": attributeJsonObj.put("newValue", "1"); break
                    case "SY_PrmCoef.NResvNum8": attributeJsonObj.put("newValue", "1"); break
                    case "SY_PrmCoef.NTotDisc": attributeJsonObj.put("newValue", "1"); break
                    case "SY_PrmCoef.NAddiInsLow": attributeJsonObj.put("newValue", "1"); break
                        //prodDef.vhl.Applicant_DW 部分
                    case "Applicant.CClntMrk": attributeJsonObj.put("newValue", "1"); break
                    case "Applicant.CCertfCls": attributeJsonObj.put("newValue", "120001"); break
                    case "Applicant.IsLongTimeChecked": attributeJsonObj.put("newValue", "0"); break
                    case "Applicant.CClntAddr": attributeJsonObj.put("newValue", "*"); break
                    case "appCheckButton": attributeJsonObj.put("newValue", "点击获取投保人身份证信息"); break
                    case "Applicant.CIsIncola": attributeJsonObj.put("newValue", "1"); break
                    case "Applicant.CDptRelation": attributeJsonObj.put("newValue", "*"); break
                    case "Applicant.CInsuredSameApplicant": attributeJsonObj.put("newValue", "1"); break
                    case "Applicant.CVhlownerSameApplicant": attributeJsonObj.put("newValue", "0"); break
                    case "Applicant.CVhlownerSameInsured": attributeJsonObj.put("newValue", "0"); break
                    case "Applicant.CBnfcChecked": attributeJsonObj.put("newValue", "0"); break
                    case "Applicant.CBnfcSameAsApplicant": attributeJsonObj.put("newValue", "0"); break
                    case "Applicant.CBnfcSameInsured": attributeJsonObj.put("newValue", "0"); break
                        //prodDef.vhl.Insured_DW 部分
                    case "Insured.CClntMrk": attributeJsonObj.put("newValue", "1"); break
                    case "Insured.CCertfCls": attributeJsonObj.put("newValue", "120001"); break
                    case "Insured.IsLongTimeChecked": attributeJsonObj.put("newValue", "0"); break
                    case "Insured.CClntAddr": attributeJsonObj.put("newValue", "*"); break
                    case "inrCheckButton": attributeJsonObj.put("newValue", "点击获取被保人身份证信息"); break
                    case "Insured.CIsIncola": attributeJsonObj.put("newValue", "1"); break
                        //prodDef.vhl.Vhlowner_DW 部分
                    case "Vhlowner.COwnerCls": attributeJsonObj.put("newValue", "1"); break
                    case "Vhlowner.CCertfCls": attributeJsonObj.put("newValue", "120001"); break
                    case "Vhlowner.CClntAddr": attributeJsonObj.put("newValue", "*"); break
                        //prodDef.vhl.Bnfc_DW 部分
                    case "Bnfc.IsLongTimeChecked": attributeJsonObj.put("newValue", "0"); break
                        //prodDef.vhl.VsTax_DW 部分
                    case "VsTax.CCollectionProject": attributeJsonObj.put("newValue", "10114"); break
                    case "VsTax.CBizMrk": attributeJsonObj.put("newValue", "338001"); break
                    case "VsTax.CPayersNation": attributeJsonObj.put("newValue", "156"); break
                    case "VsTax.NCurbWt": attributeJsonObj.put("newValue", "0"); break
                    case "VsTax.CTaxableQuantity": attributeJsonObj.put("newValue", "1.00"); break
                }
                //解决顺序错乱的问题
                attributeJsonObj.remove("name", name);
                map = JSON.parseObject(attributeJsonObj.toString(), ListOrderedMap.class);
                map.put(0, "name", name);
                attributeJsonObj = new JSONObject(map);
                attributeVoList = attributeVoList.fluentRemove(i).fluentAdd(i, attributeJsonObj)
            }

        }
    }
    //保存续保查询所需的dw_data
    autoTask.tempValues.renewalQuerydw_data = renewalQuerydw_data
}

//======保费计算组装数据start======
/**
 * @param autoTask
 * @param order
 * @param code 通过code识别交强商业  efcbiz  混保  efc 单交  biz 单商
 * @return
 */
def getChargeData(AutoTask autoTask, Order order, String code) {
    String flag = "quote"
    def script = new robot_2022_assemble_data()
    def dwDataJSONArray = new JSONArray();
    def Applicant_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Applicant_DW", "1")
    def Base_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Base_DW", "1")
    def Vhl_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Vhl_DW", "2")
    def Cvrg_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Cvrg_DW", "2")
    def PrmCoef_DW_JSONObject = getJSONOByDwName("prodDef.vhl.PrmCoef_DW", "1")
    def Fee_DW_JSONObject = getJSONOByDwName("prodDef.common.Fee_DW", "2")//todo 修改
    def PAY_DW_JSONObject = getJSONOByDwName("prodDef.vhl.PAY_DW", "2")
    def Insured_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Insured_DW", "1")
    def Vhlowner_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Vhlowner_DW", "2")
    def VhlDrv_DW_JSONObject = getJSONOByDwName("prodDef.vhl.VhlDrv_DW", "1")
    def Bnfc_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Bnfc_DW", "2")
    def Taxation_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Taxation_DW", "2")
    def VsTax_DW_JSONObject = getJSONOByDwName("prodDef.vhl.VsTax_DW", "2")
    def JqFixSpec_DW_JSONObject = getJSONOByDwName("prodDef.vhl.JqFixSpec_DW", "2")
    def SyFixSpec_DW_JSONObject = getJSONOByDwName("prodDef.vhl.SyFixSpec_DW", "2")
    def Mechine_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Mechine_DW", "1")

    //prodDef.vhl.Applicant_DW
    Applicant_DW_JSONObject.put("dataObjVoList", getApplican_DW(autoTask, order))
    //prodDef.vhl.Base_DW
    Base_DW_JSONObject.put("dataObjVoList", getBase_DW(autoTask, order))
    //prodDef.vhl.Vhl_DW
    Vhl_DW_JSONObject.put("dataObjVoList", getVhl_DW(flag, autoTask, order))
    //prodDef.vhl.Cvrg_DW
    Cvrg_DW_JSONObject.put("dataObjVoList", getJQSYData(code, autoTask, order))
    //prodDef.vhl.PrmCoef_DW
    PrmCoef_DW_JSONObject.put("dataObjVoList", getPrmCoef_DW(autoTask, order))
    //prodDef.common.Fee_DW 费率信息
    Fee_DW_JSONObject.put("dataObjVoList", getFee_DW(autoTask))
    //prodDef.vhl.PAY_DW 支付信息
    def dataVoListArray = getDefaultDataObjVoListArray("insert")
    dataVoListArray.getJSONObject(0).put("attributeVoList", new JSONArray())
    PAY_DW_JSONObject.put("dataObjVoList", dataVoListArray)
    //prodDef.vhl.Insured_DW 被保人信息
    Insured_DW_JSONObject.put("dataObjVoList", getInsured_DW(autoTask, order))
    //prodDef.vhl.Vhlowner_DW 车主信息
    Vhlowner_DW_JSONObject.put("dataObjVoList", getVhlowner_DW(autoTask, order))
    //prodDef.vhl.VhlDrv_DW
    VhlDrv_DW_JSONObject.put("dataObjVoList", new JSONArray())
    //prodDef.vhl.Bnfc_DW 受益人信息
    Bnfc_DW_JSONObject.put("dataObjVoList", getBnfc_DW())
    //prodDef.vhl.Taxation_DW 税务信息
    Taxation_DW_JSONObject.put("dataObjVoList", getTaxation_DW(autoTask, order))
    //prodDef.vhl.VsTax_DW 车船税
    VsTax_DW_JSONObject.put("dataObjVoList", getVsTax_DW(autoTask, order, flag))
    //prodDef.vhl.JqFixSpec_DW 交强特约
    JqFixSpec_DW_JSONObject.put("dataObjVoList", script.jqFixSpec_DW(autoTask))
    //prodDef.vhl.SyFixSpec_DW 商业险特约
    SyFixSpec_DW_JSONObject.put("dataObjVoList", script.syFixSpec_DW(autoTask))
    //prodDef.vhl.Mechine_DW 设备信息
    Mechine_DW_JSONObject.put("dataObjVoList", new JSONArray())

    dwDataJSONArray.add(0, Applicant_DW_JSONObject)
    dwDataJSONArray.add(1, Base_DW_JSONObject)
    dwDataJSONArray.add(2, Vhl_DW_JSONObject)
    dwDataJSONArray.add(3, Cvrg_DW_JSONObject)
    dwDataJSONArray.add(4, PrmCoef_DW_JSONObject)
    dwDataJSONArray.add(5, Fee_DW_JSONObject)
    dwDataJSONArray.add(6, PAY_DW_JSONObject)
    dwDataJSONArray.add(7, Insured_DW_JSONObject)
    dwDataJSONArray.add(8, Vhlowner_DW_JSONObject)
    dwDataJSONArray.add(9, VhlDrv_DW_JSONObject)
    dwDataJSONArray.add(10, Bnfc_DW_JSONObject)
    dwDataJSONArray.add(11, Taxation_DW_JSONObject)
    if ("efc".equals(code)) {//单交
        dwDataJSONArray.add(12, VsTax_DW_JSONObject)
        dwDataJSONArray.add(13, JqFixSpec_DW_JSONObject)
    } else if ("efcbiz".equals(code)) {//混保
        dwDataJSONArray.add(12, VsTax_DW_JSONObject)
        dwDataJSONArray.add(13, JqFixSpec_DW_JSONObject)
        dwDataJSONArray.add(14, SyFixSpec_DW_JSONObject)
        dwDataJSONArray.add(15, Mechine_DW_JSONObject)
    } else {//单商
        dwDataJSONArray.add(12, SyFixSpec_DW_JSONObject)
        dwDataJSONArray.add(13, Mechine_DW_JSONObject)
    }
    return dwDataJSONArray.toJSONString()
}

//组装投保人信息
def getApplican_DW(AutoTask autoTask, Order order) {
    def script = new robot_2022_assemble_data()
    def attributeVoListArray = script?.getApplicationData(autoTask, order)
    def dataObjVoListArray = getDefaultDataObjVoListArray("update")
    dataObjVoListArray.getJSONObject(0).put("attributeVoList", attributeVoListArray)
    return dataObjVoListArray
}
//获取机构信息/基本信息
def getBase_DW(AutoTask autoTask, Order order) {
    def script = new robot_2022_assemble_data()
    def attributeVoListArray = script?.getBaseData(autoTask, order)
    def dataObjVoListArray = getDefaultDataObjVoListArray("update")
    dataObjVoListArray.getJSONObject(0).put("attributeVoList", attributeVoListArray)
    return dataObjVoListArray
}
//组装车辆信息
def getVhl_DW(String flag, AutoTask autoTask, Order order) {
    def script = new robot_2022_assemble_data();
    def attributeVoListArray = script?.getVhl_DW(flag, autoTask, order)
    def dataObjVoListArray = getDefaultDataObjVoListArray("insert")
    dataObjVoListArray.getJSONObject(0).put("attributeVoList", attributeVoListArray)
    return dataObjVoListArray
}
//组装商业险,交强险数据
def getJQSYData(String code, AutoTask autoTask, Order order) {//efcbiz混保  efc交强  biz商业
    def script = new robot_2022_assemble_data()
    String data = ""
    def dataObjVoListArray = new JSONArray()
    if ("efcbiz".equals(code)) {
        dataObjVoListArray = script?.getbizCvrg_DW(autoTask, order) as JSONArray
        dataObjVoListArray.add(dataObjVoListArray.size(), script?.getefcCvrg_DW(autoTask, order) as JSONObject)
    } else if ("efc".equals(code)) {
        dataObjVoListArray.add(dataObjVoListArray.size(), script?.getefcCvrg_DW(autoTask, order) as JSONObject)
    } else {
        dataObjVoListArray = script?.getbizCvrg_DW(autoTask, order) as JSONArray
    }
    return dataObjVoListArray
}
//商业险系数信息
def getPrmCoef_DW(AutoTask autoTask, Order order) {
    def script = new robot_2022_assemble_data()
    def attributeVoListArray = script?.getPrmCoef_DW(autoTask, order)
    def dataObjVoListArray = getDefaultDataObjVoListArray("update")
    dataObjVoListArray.getJSONObject(0).put("attributeVoList", attributeVoListArray)
    return dataObjVoListArray

}
//费率系数信息
def getFee_DW(AutoTask autoTask) {
    def script = new robot_2022_assemble_data()
    return script?.getFee_DW(autoTask) as JSONArray
}
//组装被保人数据
def getInsured_DW(AutoTask autoTask, Order order) {
    def script = new robot_2022_assemble_data();
    def attributeVoListArray = script?.getInsured_DW(autoTask, order)
    def dataObjVoListArray = getDefaultDataObjVoListArray("update")
    dataObjVoListArray.getJSONObject(0).put("attributeVoList", attributeVoListArray)
    return dataObjVoListArray
}
//车主信息
def getVhlowner_DW(AutoTask autoTask, Order order) {
    def script = new robot_2022_assemble_data()
    def attributeVoListArray = script?.getVhlowner_DW(autoTask, order)
    def dataObjVoListArray = getDefaultDataObjVoListArray("insert")
    dataObjVoListArray.getJSONObject(0).put("attributeVoList", attributeVoListArray)
    return dataObjVoListArray
}
//受益人信息
def getBnfc_DW() {
    def script = new robot_2022_assemble_data()
    def attributeVoListArray = script?.getBnfc_DW()
    def dataObjVoListArray = getDefaultDataObjVoListArray("insert")
    dataObjVoListArray.getJSONObject(0).put("attributeVoList", attributeVoListArray)
    return dataObjVoListArray
}
//税务信息
def getTaxation_DW(AutoTask autoTask, Order order) {
    def script = new robot_2022_assemble_data();
    def attributeVoListArray = script?.getTaxation_DW(autoTask, order)
    def dataObjVoListArray = getDefaultDataObjVoListArray("insert")
    dataObjVoListArray.getJSONObject(0).put("attributeVoList", attributeVoListArray)
    return dataObjVoListArray
}
//组装车船税
def getVsTax_DW(AutoTask autoTask, Order order, String flag) {
    def script = new robot_2022_assemble_data();
    def attributeVoListArray = script?.getVsTax_DW(autoTask, order, flag)
    def dataObjVoListArray = getDefaultDataObjVoListArray("insert")
    dataObjVoListArray.getJSONObject(0).put("attributeVoList", attributeVoListArray)
    return dataObjVoListArray
}

//======保费计算组装数据end======

//======核保,暂存组装数据start======
def getSavePremiumCalculation(String code, AutoTask autoTask, Order order) {
    def script = new robot_2022_assemble_data()
    def save_DWDataJSONArray = new JSONArray()
    //通过code识别交强商业  efcbiz  混保  efc 单交  biz 单商
    String flag = "save"
    String kingData = ""
    //todo 更改数据组装
    def base_Dw_JSONObject = script?.getSaveFee_DW(autoTask, "saveBase_DW", "update")
    def vhl_Dw_JSONObject = script?.getSaveFee_DW(autoTask, "saveVhl_DW", "update")
    def applicant_DW_JSONObject = script?.getSaveFee_DW(autoTask, "saveApplican_DW")
    def insured_DW_JSONObject = script?.getSaveFee_DW(autoTask, "saveInsured_DW")
    def vhlowner_DW_JSONObject = script?.getSaveFee_DW(autoTask, "saveVhlowner_DW")
    def fee_DW_JSONObject = script?.getSaveFee_DW(autoTask, "saveFee_DW")
    def cvrg_DW_JSONObject = script?.getSaveFee_DW(autoTask, "saveCvrg_DW", "update")
    def prmCoef_DW_JSONObject = script?.getSaveFee_DW(autoTask, "savePrmCoef_DW", "update")
    def PAY_DW_JSONObject = script?.getSaveFee_DW(autoTask, "savePAY_DW")
    def Acctinfo_DW_JSONObject = getJSONOByDwName("prodDef.vhl.Acctinfo_DW", "2")
    Acctinfo_DW_JSONObject.put("dataObjVoList", getAcctinfo_DW(autoTask))
    def vhlDrv_DW_JSONObject = script?.getSaveFee_DW(autoTask, "saveVhlDrv_DW")
    def bnfc_DW_JSONObject = script?.getSaveFee_DW(autoTask, "saveBnfc_DW")
    def taxation_DW_JSONObject = script?.getSaveFee_DW(autoTask, "saveTaxation_DW", "update")
    def vsTax_DW_JSONObject = script?.getSaveFee_DW(autoTask, "saveVsTax_DW", "update")
    def jqFixSpec_DW = script?.getSaveFee_DW(autoTask, "saveJqFixSpec_DW")
    def syFixSpec_DW_JSONObject = script?.getSaveFee_DW(autoTask, "saveSyFixSpec_DW", "update")
    def mechine_DW_JSONObject = script?.getSaveFee_DW(autoTask, "saveMechine_DW")
    def VhlInsurance_DW_JSONObject = script?.getSaveFee_DW(autoTask, "saveVhlInsurance_DW", "inserted")//非车险

    save_DWDataJSONArray.add(0, base_Dw_JSONObject)
    save_DWDataJSONArray.add(1, vhl_Dw_JSONObject)
    save_DWDataJSONArray.add(2, applicant_DW_JSONObject)
    save_DWDataJSONArray.add(3, insured_DW_JSONObject)
    save_DWDataJSONArray.add(4, vhlowner_DW_JSONObject)
    save_DWDataJSONArray.add(5, fee_DW_JSONObject)
    save_DWDataJSONArray.add(6, cvrg_DW_JSONObject)
    save_DWDataJSONArray.add(7, prmCoef_DW_JSONObject)
    save_DWDataJSONArray.add(8, PAY_DW_JSONObject)
    save_DWDataJSONArray.add(9, Acctinfo_DW_JSONObject)
    save_DWDataJSONArray.add(10, vhlDrv_DW_JSONObject)
    save_DWDataJSONArray.add(11, bnfc_DW_JSONObject)
    save_DWDataJSONArray.add(12, taxation_DW_JSONObject)
    save_DWDataJSONArray.add(13, VhlInsurance_DW_JSONObject)
    //单交强
    if ("efc".equals(code)) {//单交
        save_DWDataJSONArray.add(14, vsTax_DW_JSONObject)
        save_DWDataJSONArray.add(15, jqFixSpec_DW)
    } else if ("efcbiz".equals(code)) {
        save_DWDataJSONArray.add(14, vsTax_DW_JSONObject)
        save_DWDataJSONArray.add(15, jqFixSpec_DW)
        save_DWDataJSONArray.add(16, syFixSpec_DW_JSONObject)
        save_DWDataJSONArray.add(17, mechine_DW_JSONObject)

    } else {//单商业
        save_DWDataJSONArray.add(14, syFixSpec_DW_JSONObject)
        save_DWDataJSONArray.add(15, mechine_DW_JSONObject)
    }

    autoTask.tempValues.saveDW_DATA = save_DWDataJSONArray.toJSONString()
}

def getAcctinfo_DW(AutoTask autoTask, String needCAcctNme = "yes") {
    def script = new robot_2022_assemble_data()
    def attributeVoListArray = script?.getAcctinfoData(autoTask, needCAcctNme)
    def dataObjVoListArray = getDefaultDataObjVoListArray("insert")
    dataObjVoListArray.getJSONObject(0).put("attributeVoList", attributeVoListArray)
    return dataObjVoListArray
}
//======核保,暂存组装数据end======

//*************快速续保组装数据start*************
def getQueryRenewlDatas(AutoTask autoTask) {
    List dWNameList = new ArrayList<>()
    dWNameList.add("prodDef.vhl.PAY_DW")
    dWNameList.add("prodDef.vhl.Base_DW")
    dWNameList.add("prodDef.vhl.Vhl_DW")
    dWNameList.add("prodDef.vhl.Cvrg_DW")
    dWNameList.add("prodDef.vhl.PrmCoef_DW")
    dWNameList.add("prodDef.vhl.Applicant_DW")
    dWNameList.add("prodDef.vhl.Insured_DW")
    dWNameList.add("prodDef.vhl.Vhlowner_DW")
    dWNameList.add("prodDef.vhl.VhlDrv_DW")
    dWNameList.add("prodDef.vhl.Acctinfo_DW")
    dWNameList.add("prodDef.vhl.edr_base_DW")
    dWNameList.add("prodDef.vhl.edr_rsn_DW")
    dWNameList.add("prodDef.vhl.edr_cmp_item_DW")
    dWNameList.add("prodDef.vhl.Bnfc_DW")
    dWNameList.add("prodDef.vhl.Taxation_DW")
    dWNameList.add("prodDef.vhl.VsTax_DW")
    dWNameList.add("prodDef.vhl.Mechine_DW")

    StringBuilder sb = new StringBuilder()
    sb.append("[");
    for (String dWName : dWNameList) {
        switch (dWName) {
            case "prodDef.vhl.PAY_DW": assembleAttributeVoListData(sb, "[]", "ONLY_DATA", dWName); break
            case "prodDef.vhl.Base_DW": assembleAttributeVoListData(sb, getAttributeVolistData(dWName), "ONLY_DATA", dWName); break
            case "prodDef.vhl.Vhl_DW": assembleAttributeVoListData(sb, getAttributeVolistData(dWName), "ONLY_DATA", dWName); break
            case "prodDef.vhl.Cvrg_DW": assembleAttributeVoListData(sb, "[]", "GRID_CVRG", dWName); break;
            case "prodDef.vhl.PrmCoef_DW": assembleAttributeVoListData(sb, getAttributeVolistData(dWName), "ONLY_DATA", dWName); break;
            case "prodDef.vhl.Applicant_DW": assembleAttributeVoListData(sb, getAttributeVolistData(dWName), "ONLY_DATA", dWName); break;
            case "prodDef.vhl.Insured_DW": assembleAttributeVoListData(sb, getAttributeVolistData(dWName), "ONLY_DATA", dWName); break;
            case "prodDef.vhl.Vhlowner_DW": assembleAttributeVoListData(sb, getAttributeVolistData(dWName), "ONLY_DATA", dWName); break;
            case "prodDef.vhl.VhlDrv_DW": assembleAttributeVoListData(sb, "[]", "GRID_DATA", dWName); break;
            case "prodDef.vhl.Acctinfo_DW": assembleAttributeVoListData(sb, getAttributeVolistData(dWName), "ONLY_DATA", dWName); break;
            case "prodDef.vhl.edr_base_DW": assembleAttributeVoListData(sb, "[]", "ONLY_DATA", dWName); break;
            case "prodDef.vhl.edr_rsn_DW": assembleAttributeVoListData(sb, "[]", "GRID_DATA", dWName); break;
            case "prodDef.vhl.edr_cmp_item_DW": assembleAttributeVoListData(sb, "[]", "GRID_DATA", dWName); break;
            case "prodDef.vhl.Bnfc_DW": assembleAttributeVoListData(sb, getAttributeVolistData(dWName), "ONLY_DATA", dWName); break;
            case "prodDef.vhl.Taxation_DW": assembleAttributeVoListData(sb, getAttributeVolistData(dWName), "ONLY_DATA", dWName); break;
            case "prodDef.vhl.VsTax_DW": assembleAttributeVoListData(sb, getAttributeVolistData(dWName), "ONLY_DATA", dWName); break;
            case "prodDef.vhl.Mechine_DW": assembleAttributeVoListData(sb, "[]", "GRID_DATA", dWName); break;
        }
    }
    return sb.substring(0, sb.length() - 1) + "]"
}

def assembleAttributeVoListData(StringBuilder sb, String attributeVoListData, String dwType, String dwName) {
    List<String> ignoreNames = Arrays.asList("prodDef.vhl.Cvrg_DW", "prodDef.vhl.VhlDrv_DW", "prodDef.vhl.edr_rsn_DW", "prodDef.vhl.edr_cmp_item_DW", "prodDef.vhl.Mechine_DW")
    String dataObjVoList = ""
    if (!ignoreNames.contains(dwName)) {
        dataObjVoList = "{\"index\":\"1\", \"selected\":\"true\",\"status\":\"INSERTED\",\"attributeVoList\":" + attributeVoListData + "}"
    }
    String baseData = "{\"isFilter\":\"false\",\"dwType\":\"" + dwType + "\",\"dwName\":\"" + dwName + "\",\"rsCount\":\"1\",\"pageSize\":\"10\",\"pageNo\":\"1\",\"pageCount\":\"0\",\"maxCount\":\"1000\",\"toAddFlag\":\"false\",\"filterMapList\":[],\"dataObjVoList\":[" + dataObjVoList + "]},"
    return sb.append(baseData)
}
//*************快速续保组装数据end*************


//===========回写相关start================

//保费计算数据回写
def getchargeBackData(AutoTask autoTask, Order order, JSONArray WEB_DATA) {
    def script = new robot_2022_writeback_data()
    script?.getchargeBackData(autoTask, order, WEB_DATA)
    //如果是核保 或 自核任务 ,报价 则完毕后,保存返回信息. 便于后续使用
    if (autoTask.taskType.endsWith("insure")) {
        script?.saveChargeBackData(autoTask, WEB_DATA)
    }
}
//核保暂存数据回写
def getAuditBackData(AutoTask autoTask, Order order, JSONArray web_data) {
    def script = new robot_2022_writeback_data()
    script?.getAuditBackData(autoTask, order, web_data)
}
//保单，投保单查询回写
def getDetail(AutoTask autoTask, Order order, String html) {
    def script = new robot_2022_writeback_data()
    def flag = getefcbizflag(order)
    script?.getApplicanData(autoTask, order, html)//投保人
    script?.getInsuredData(autoTask, order, html)//被保人
    script?.getCarownerData(autoTask, order, html)//车主
    script?.getCarInfo(autoTask, order, html)//车信息
    script?.getRiskData(autoTask, order, html)//险别
    if ("true".equals(getRealValue(autoTask.tempValues.efcquote)) || "true".equals(getRealValue(autoTask.tempValues.efcapproved))) {
        script.getTaxData(autoTask, order, html)//车船税
    }
    script?.getTimeData(autoTask, order, html, flag)//与日期有关回写
}

//快速续保结果回写
def assembleRenewalData(AutoTask autoTask, Order order, JSONArray web_data) {
    def script = new robot_2022_writeback_data()
    script?.assembleRenewalData(autoTask, order, web_data)
}
//=============回写相关END==============


//通过order获得当前交强商业标志
def getefcbizflag(Order order) {
    if (order.isBiz() && order.isEfc()) {//混保
        return "efcbiz";
    } else if (order.isEfc()) {//单交
        return "efc";
    } else {//单商
        return "biz"
    }
}
//获取当前单险种状态：单商，单交，混保 4用于Base_DW  单交0326混保0364_0326单商0364
def getbizAndefcStatus(order) {
    if (order?.suiteInfo?.bizSuiteInfo) {//商业险
        if (order?.suiteInfo?.efcSuiteInfo) {//混保
            return "0364_0326";
        } else {//单商
            return "0364";
        }
    } else {//单交
        return "0326";
    }
}

/**
 * 获取折旧率
 * @param carInfo
 * @return
 */
def getVhlNActualRate(carInfo) {
    def rate = 0.006;
    def carModelName = carInfo?.carModelName?.toString();
    def userProps = getUseNature(carInfo?.useProps);
    def seatCount = carInfo?.seatCnt;
    if ("02".equals(userProps)) {//非营运
        if (seatCount > 9 || carInfo?.useProps == 12) {
            rate = 0.009;
        }
    } else if ("01".equals(userProps)) {
        def fullload = 0;
        if (carInfo?.fullLoad != null) {
            fullload = new BigDecimal(new DecimalFormat("0.0000").format(carInfo?.fullLoad / 1000));
        }
        if (carInfo?.useProps == 6 && carModelName.indexOf("运输") <= 0) {
            if (carInfo.syvehicletypecode.toString() == "NA") {
                rate = 0.014
            } else if (fullload < 2) {
                rate = 0.011;
            } else {
                rate = 0.009;
            }
        } else {
            rate = 0.009;
        }
    } else {
        rate = 0.009;
    }
    return rate;
}
/**
 * 得到折旧价  永诚不支持起保日期不一致的混保的单，如果不一样，只能分开提单
 * @param rate
 * @param carInfo
 */
def getVhlNActualValue(AutoTask autoTask, rate, Order order, String start) {
    def common_all = new common_all()
    DecimalFormat df = new DecimalFormat("######0.0000");
    def definePrice = df.format(0);
    if (StringUtil.isEmpty(start)) {
        start = getCurrentStart(order);
    }
    def firstRegDatebw = order?.carInfo?.firstRegDate;//得到当前初登  //Wed Jun 03 00:00:00 CST 2015
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
    def firstRegDate = sdf.format(firstRegDatebw);
    firstRegDate = firstRegDate + " 00:00:00"

    /**
     * Days表示初登日期 与 起保日期 天数的差值(例:初登日期:2017.8.7 保单开始日期:2018.9.10) 那么Days = 3
     * Months表示两者总月份的差值 同上 Months= 12 + 1 = 13
     * MonthDay:表示本月天数
     *
     */
    def diff = common_all.timeInterval(firstRegDate, start)
    def monthDiff = diff.monthDiff.toInteger()
    def days = diff.dayDiff.toInteger()
    PPSetPTMsg(autoTask, PlatformKey.carAge, monthDiff.toString());//车辆使用月数
    Calendar cal = Calendar.getInstance();
    cal.setTime(new Date())
    int MonthDay = cal.getMaximum(Calendar.DAY_OF_MONTH)
    //获取当前车价
    def price = getPrice(order.carInfo);

    if (monthDiff <= 1) { //1个月内为新车
        definePrice = df.format(price);
    } else {
        /**判断剩余天数大于上个月总共天数*/
        if (days > MonthDay) {
            Months = monthDiff + 1;
        }
        //月折旧率
        rate = rate * monthDiff;
        if (rate > 0.8) {
            rate = 0.8;
        }
        order.carInfo.misc.monthRate = rate.toString();
        /***** 计算(1-折扣率*月份)*车价 ****/
        price = (1 - rate) * price;
        definePrice = df.format(price);
    }
    return definePrice;
}
/**
 * 获取当前车型的价格
 */
def getPrice(CarInfo carInfo) {
    if (carInfo == null) {
        throw new RuntimeException("robot_2022_common_getPrice() : carInfo不能为null");
    } else {
        Double price;
        if (carInfo.getCarPriceType() == 2) {
            if (carInfo?.plateNum.contains("未上牌")) {
                price = carInfo.getPrice().doubleValue();
            } else {
                price = carInfo.getDefinedCarPrice().doubleValue();
            }

        } else {
            price = carInfo.getPrice().doubleValue();

        }
        return price;
    }
}

/**
 * 获取当前单的起保日期
 */
def getCurrentStart(order) {
    if (order?.suiteInfo) {
        def suiteInfo = order.suiteInfo;
        def ret = StringUtil.isNoEmpty(suiteInfo.bizSuiteInfo?.start) ? suiteInfo.bizSuiteInfo?.start : suiteInfo.efcSuiteInfo?.start;
        return ret;
    }
}
/**
 * 判断当前有车是否是新车
 * @param order
 */
def getisNewCar(Order order) {
    def common_all = new common_all()
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    def start = getCurrentStart(order);
    def firstRegDate = order?.carInfo?.firstRegDate;//得到当前初登
    firstRegDate = sdf.format(firstRegDate);
    return common_all.timeInterval(firstRegDate, start).monthDiff.toInteger();
}


//判断自核的结果 START
def checkAutoInsureStatus(AutoTask autoTask, String syIsAutoPass, String jqIsAutoPass, String msg) {
    if ("2".equals(syIsAutoPass) && "2".equals(jqIsAutoPass)) {
        autoTask.setTaskStatus("32")
    } else if ("1".equals(syIsAutoPass) && "1".equals(jqIsAutoPass)) {
        autoTask.setTaskStatus("33")
        throw new InsReturnException("商业单,交强单自核均未通过,需转人工核保,详细信息如下 ： \r\n" + msg);
    } else if ("1".equals(jqIsAutoPass) && "2".equals(syIsAutoPass)) {
        autoTask.setTaskStatus("33")
        throw new InsReturnException("商业单自核成功,交强单自核未通过,需转人工核保,详细信息如下 ： \r\n" + msg);
    } else if ("1".equals(syIsAutoPass) && "2".equals(jqIsAutoPass)) {
        autoTask.setTaskStatus("33")
        throw new InsReturnException("交强单自核成功,商业单自核未通过,需转人工核保,详细信息如下 ： \r\n" + msg);
    } else {
        throw new InsReturnException("永诚自核失败,原因如下 ：\n\r" + msg);
    }
}

def checkAutoInsureStatus(AutoTask autoTask, String isAutoPass, String msg) {
    if (!"1,2".contains(isAutoPass)) {
        throw new InsReturnException("永诚自核失败,原因如下 ：\n\r" + msg);
    } else if ("1".equals(isAutoPass)) {
        autoTask.setTaskStatus("33")
        throw new InsReturnException("该单自核未通过,需转人工核保,详细信息如下 ： \r\n" + msg);
    } else if ("2".equals(isAutoPass)) {
        autoTask.setTaskStatus("32")
    }
}
//判断自核的结果 END


/**
 *  todo  快速续保的参数还需要与出单系统核对一下
 *  组装attributeVoList数据
 * @param dwName
 * @return
 */
def getAttributeVolistData(String dwName) {

    def script = new robot_2022_assemble_data()
    String attributeVolist = ""
    //系数信息
    String prmCoef_DW = "JQ_PrmCoef.CTraAccFloRate,JQ_PrmCoef.NVhlAge,JQ_PrmCoef.NTaxRedFactor,JQ_PrmCoef.CRegFactor,JQ_PrmCoef.NAgoVioProFloat,JQ_PrmCoef.CTraSafFloRate,JQ_PrmCoef.NAgoClaProFloat,JQ_PrmCoef.CTraAccFloCoe,JQ_PrmCoef.CDruDrinNum,JQ_PrmCoef.CDruDrvNum,JQ_PrmCoef.CProDruDrinNum,JQ_PrmCoef.CUnproDrinDrvNum,JQ_PrmCoef.CProDruDrvNum,JQ_PrmCoef.CUnproDruDrvNum,JQ_PrmCoef.NProvInsurePro,JQ_PrmCoef.NTotDisc,SY_PrmCoef.CAppNo,SY_PrmCoef.CPlyNo,PrmCoef.CCrtCde,PrmCoef.TCrtTm,PrmCoef.CUpdCde,PrmCoef.TUpdTm,SY_PrmCoef.NAutonomyAdjust,SY_PrmCoef.NChannelAdjust,SY_PrmCoef.NAgoClmRecQuick,SY_PrmCoef.CAgoClmRec,SY_PrmCoef.CNoClaimRes,SY_PrmCoef.NSafeDev,SY_PrmCoef.NResvNum8,SY_PrmCoef.CResvTxt5,SY_PrmCoef.CResvTxt8,SY_PrmCoef.NWeDiscount,SY_PrmCoef.NTotDisc,SY_PrmCoef.NResvNum3,SY_PrmCoef.NAddiInsLow,SY_PrmCoef.NResvNum5,SY_PrmCoef.NDiscountPurchase,SY_PrmCoef.CResvTxt1,SY_PrmCoef.NResvNum4"
    //投保人信息
    String applicant_DW = "Applicant.CAppNo,Applicant.CCrtCde,Applicant.TCrtTm,Applicant.CUpdCde,Applicant.TUpdTm,Applicant.CNation,Applicant.CIssuedAt,Applicant.TEffectedDate,Applicant.TExpiredDate,Applicant.CAppNme,Applicant.CClntMrk,Applicant.CCertfCls,Applicant.CCertfCde,Applicant.TCertfClsValiDate,Applicant.IsLongTimeChecked,Applicant.CSex,Applicant.TBirthday,Applicant.CMobile,Applicant.CProvince,Applicant.CCity,Applicant.CCounty,Applicant.CTrdCde,Applicant.CWorkDpt,Applicant.CCntrNme,Applicant.CClntAddr,Applicant.CPaperCheck,Applicant.CRelCde,Applicant.CRelCdedesc,Applicant.CAreaCde,Applicant.CEmail,Applicant.CZipCde,Applicant.CMachineCode,Applicant.CAppCusName,Applicant.CTel,appCheckButton,Applicant.CIncome,Applicant.CAppCde,,Applicant.CIsIncola,Applicant.CPartCde,Applicant.CDptRelation,Applicant.CLegalNme,Applicant.COccupCde,Applicant.CParClntCde,Applicant.CSubTrdCde,Applicant.CParTrdCde,Applicant.CParSubTrdCde,Applicant.CAppSchoolName,Applicant.CAppClntQQ,Applicant.CCompAddr,Applicant.COccupation,Applicant.CStayAddr,Applicant.CChargePerName,Applicant.CLicensingPerson,Applicant.CLegalCertfCls,Applicant.CLegalCertfCde,Applicant.TLegalCertfClsValiDate,Applicant.CInsuredSameApplicant,Applicant.CVhlownerSameApplicant,Applicant.CVhlownerSameInsured,Applicant.CBnfcChecked,btn_applicant_query,btn_applicant_querypar,Applicant.CBnfcSameAsApplicant,Applicant.CBnfcSameInsured"
    //被保人信息
    String insured_DW = "SY_Insured.CPkId,JQ_Insured.CPkId,Insured.CInsuredCde,Insured.CCrtCde,Insured.TCrtTm,Insured.CUpdCde,Insured.TUpdTm,Insured.CResvTxt3,Insured.CResvTxt2,Insured.CResvTxt4,Insured.CNation,Insured.CIssuedAt,Insured.TEffectedDate,Insured.TExpiredDate,Insured.CInsuredNme,Insured.CClntMrk,Insured.CCertfCls,Insured.CCertfCde,Insured.TCertfClsValiDate,Applicant.IsLongTimeChecked,Insured.CSex,Insured.TBirthday,Insured.CMobile,Insured.CProvince,Insured.CCity,Insured.CCounty,Insured.CClntAddr,Insured.CWorkDpt,Insured.CMrgCde,Insured.CEmail,Insured.CZipCde,Insured.CTel,Insured.CPaperCheck,Insured.CIncome,inrCheckButton,Insured.CAreaCde,Insured.CRelCdes,Insured.CRelCdesdesc,,Insured.CCntrNme,Insured.CLegalNme,Insured.COccupCde,Insured.CParClntCde,Insured.CTrdCde,Insured.CSubTrdCde,Insured.CParTrdCde,Insured.CParSubTrdCde,Insured.CInsuredSchoolName,Insured.CInsuredClntQQ,Insured.CCompAddr,Insured.COccupation,Insured.CStayAddr,Insured.CChargePerName,Insured.CLicensingPerson,Insured.CLegalCertfCls,Insured.CLegalCertfCde,Insured.TLegalCertfClsValiDate,Insured.CIsIncola,btn_insured_query,btn_insured_querypar"
    //车主信息
    String vhlowner_DW = "Vhlowner.COwnerCde,Vhlowner.COwnerNme,Vhlowner.COwnerCls,Vhlowner.CCertfCls,Vhlowner.CCertfCde,Vhlowner.CClntAddr,Vhlowner.CZipCde,Vhlowner.CWorkdptKind,Vhlowner.TUpdTm,Vhlowner.CUpdCde,Vhlowner.TCrtTm,Vhlowner.CCrtCde,Vhlowner.CSex,Vhlowner.TBirthday";

    String acctinfo_DW = "Acctinfo.CBankCde,Acctinfo.CRhtFlag,Acctinfo.CCrtCde,Acctinfo.TCrtTm,Acctinfo.CUpdCde,Acctinfo.TUpdTm,,Acctinfo.CAcctNme,Acctinfo.CAcctNo,btn_acctinfo_get,Acctinfo.CBankName,Acctinfo.COpenBankName,btn_open_bank_search,Acctinfo.CBankArea,Acctinfo.CCpayMode,Acctinfo.CCustType,Acctinfo.CCardType,Acctinfo.CEmergencyType"
    //税务信息
    String taxation_DW = "Taxation.CAppNo,Taxation.CCrtCde,Taxation.TCrtTm,Taxation.CUpdCde,Taxation.TUpdTm,Taxation.CTaxType,Taxation.CTaxNme,Taxation.CClntMrk,Taxation.CTaxPayerComId,Taxation.CBankNme,Taxation.CAcctNo,Taxation.CMobile,Taxation.CClntAddr"
    switch (dwName) {
    //复用报价时的base_dw , 有三个参数值需要单独加上去:buttonOa,btn_searchPkgBusiness 值均为空串
        case "prodDef.vhl.Base_DW": attributeVolist = script.getBaseDataJsonArray(); break;
        case "prodDef.vhl.Vhl_DW": attributeVolist = script.getVhlDataJsonArray("quote"); break;
        case "prodDef.vhl.PrmCoef_DW":
            attributeVolist = script.assembleJsonArrayUtil(prmCoef_DW.split(",")).toJSONString()
            break;
        case "prodDef.vhl.Applicant_DW":
            attributeVolist = script.assembleJsonArrayUtil(applicant_DW.split(",")).toJSONString()
            break;
        case "prodDef.vhl.Insured_DW":
            attributeVolist = script.assembleJsonArrayUtil(insured_DW.split(",")).toJSONString()
            break;
        case "prodDef.vhl.Vhlowner_DW":
            attributeVolist = script.assembleJsonArrayUtil(vhlowner_DW.split(",")).toJSONString()
            break;
        case "prodDef.vhl.Acctinfo_DW":
            attributeVolist = script.assembleJsonArrayUtil(acctinfo_DW.split(",")).toJSONString()
            break;
        case "prodDef.vhl.Bnfc_DW": attributeVolist = script.getBnfc_DW(); break;
        case "prodDef.vhl.Taxation_DW":
            attributeVolist = script.assembleJsonArrayUtil(taxation_DW.split(",")).toJSONString()
            break;
        case "prodDef.vhl.VsTax_DW": attributeVolist = script.getVsTaxDataJsonArray("quote"); break;
    }
    return attributeVolist;
}

/**
 * 根据dwname 获取相应JSON对象
 * @param dwname
 */
def getJSONOByDwName(String dwname, String type) {
    def linkedHashMap = new LinkedHashMap<>()
    if ("1".equals(type)) {
        linkedHashMap.put("isFilter", "undefined")
        if ("prodDef.vhl.Mechine_DW".equals(dwname)) {
            linkedHashMap.put("dwType", "GRID_DATA")
        } else {
            linkedHashMap.put("dwType", "ONLY_DATA")
        }
        linkedHashMap.put("dwName", dwname)
        linkedHashMap.put("rsCount", "5")
        linkedHashMap.put("pageSize", "8")
        linkedHashMap.put("pageNo", "1")
        linkedHashMap.put("pageCount", "1")
        linkedHashMap.put("maxCount", "undefined")
        linkedHashMap.put("toAddFlag", "undefined")
        linkedHashMap.put("filterMapList", new JSONArray())
    } else {
        linkedHashMap.put("isFilter", "false")
        switch (dwname) {
            case "prodDef.vhl.Vhl_DW":
            case "prodDef.vhl.PAY_DW":
            case "prodDef.vhl.Vhlowner_DW":
            case "prodDef.vhl.Bnfc_DW":
            case "prodDef.vhl.Taxation_DW":
            case "prodDef.vhl.VsTax_DW":
            case "prodDef.vhl.VhlInsurance_DW":
            case "prodDef.vhl.Acctinfo_DW":
                linkedHashMap.put("dwType", "ONLY_DATA")
                break;
            case "prodDef.vhl.Cvrg_DW":
                linkedHashMap.put("dwType", "GRID_CVRG")
                break;
            case "prodDef.common.Fee_DW":
                linkedHashMap.put("dwType", "GRID_EDIT")
                break;
            case "prodDef.vhl.VhlDrv_DW":
            case "prodDef.vhl.JqFixSpec_DW":
            case "prodDef.vhl.SyFixSpec_DW":
            case "prodDef.vhl.edr_rsn_DW":
            case "prodDef.vhl.edr_cmp_item_DW":
            case "prodDef.vhl.Mechine_DW":
                linkedHashMap.put("dwType", "GRID_DATA")
                break;
            default: linkedHashMap.put("dwType", "ONLY_DATA")
        }
        linkedHashMap.put("dwName", dwname)
        linkedHashMap.put("rsCount", "1")
        linkedHashMap.put("pageSize", "10")
        linkedHashMap.put("pageNo", "1")
        linkedHashMap.put("pageCount", "0")
        linkedHashMap.put("maxCount", "1000")
        linkedHashMap.put("toAddFlag", "false")
        linkedHashMap.put("filterMapList", new JSONArray())
    }
    return new JSONObject(linkedHashMap)
//    return  JSONObject.parse()getCarModelDW_DATA
}

def getDefaultDataObjVoListArray(String status) {
    def jsonArray = new JSONArray()
    def linkenHashMap = new LinkedHashMap()
    linkenHashMap.put("index", "1")
    if ("update".equals(status)) {
        linkenHashMap.put("selected", "false")
        linkenHashMap.put("status", "UPDATED")
    } else if ("insert".equals(status)) {
        linkenHashMap.put("selected", "true")
        linkenHashMap.put("status", "INSERTED")
    } else {
        throw new RuntimeException("调用getDefaultDataObjVoListArray()传入status值有误!")
    }
    jsonArray.add(new JSONObject(linkenHashMap))
    return jsonArray
}


//============处理重复投保相关方法 START===========

//商业与交强险种同时需要校正重复投保的日期
def adjustBizEfcQuoteDate(AutoTask autoTask, JSONObject dataJSONObj) {
    String RESULT_MSG = dataJSONObj.getString("RESULT_MSG")
    JSONArray WEB_DATA = dataJSONObj.getJSONArray("WEB_DATA")
    String bizDate, efcDate = ""
    for (int i = 0; i < WEB_DATA.size(); i++) {
        if (WEB_DATA.getJSONObject(i).getString("dwName").equals("prodDef.vhl.Base_DW")) {
            JSONObject attributeVoList = WEB_DATA.getJSONObject(i).getJSONArray("dataObjVoList").getJSONObject(0).getJSONObject("attributeVoList");
            bizDate = attributeVoList.getJSONObject("SY_Base.TLastStartDate").getString("value"); //上年商业保险的起期
            efcDate = attributeVoList.getJSONObject("JQ_Base.TLastStartDate").getString("value");//上年交强保险的起期
        }
    }

    Enquiry enquiry = (Enquiry) autoTask.taskEntity;
    Order order = enquiry?.order;
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Calendar cal = getInstance();
    int bizIndex = 0;
    int efcIndex = 0;
    String bizEndDate = "";
    String efcEndDate = "";
    String bizStartTime = ""
    String efcStartTime = ""
    checkRepeatInsure(bizDate, RESULT_MSG)
    checkRepeatInsure(efcDate, RESULT_MSG)

    if (StringUtil.isNoEmpty(bizDate)) {
        bizStartTime = bizDate
        bizDate = getTimeByPattern(bizDate, "yyyy-MM-dd HH:mm:ss", Calendar.YEAR, 1);
        if (isLessThanCorrectDate(bizDate)) {
            bizDate = getTimeByPattern(new Date().format('yyyy-MM-dd HH:mm:ss'), "yyyy-MM-dd HH:mm:ss", Calendar.DATE, 1);
            bizDate = setHMS2Zero(bizDate)
        }
    }
    if (StringUtil.isNoEmpty(efcDate)) {
        efcStartTime = efcDate
        efcDate = getTimeByPattern(efcDate, "yyyy-MM-dd HH:mm:ss", Calendar.YEAR, 1);
        if (isLessThanCorrectDate(efcDate)) {
            efcDate = getTimeByPattern(new Date().format('yyyy-MM-dd HH:mm:ss'), "yyyy-MM-dd HH:mm:ss", Calendar.DATE, 1);
            efcDate = setHMS2Zero(efcDate)
        }
    }
    if (RESULT_MSG.contains("保险止期")) { // 包含保险止期
        bizIndex = RESULT_MSG.lastIndexOf("保险起期是");
        efcIndex = RESULT_MSG.indexOf("保险起期是");
        if (StringUtil.isEmpty(bizDate)) {  //当永诚未在WEB_DATA中返回截止时间,或者返回的时间有误时
            bizDate = getDate(autoTask, bizDate, RESULT_MSG, bizIndex, sdf, cal, 1);
            bizStartTime = bizDate
        }
        if (StringUtil.isEmpty(efcDate)) {
            efcDate = getDate(autoTask, efcDate, RESULT_MSG, efcIndex, sdf, cal, 1);
            efcStartTime = efcDate
        }
    } else {
        efcIndex = RESULT_MSG.indexOf("起保日期");
        String tempValue = RESULT_MSG.substring(RESULT_MSG.indexOf("商业险提示信息"));
        bizIndex = tempValue.indexOf("起保日期");
        if (StringUtil.isEmpty(bizDate)) {
            bizDate = getDate(autoTask, bizDate, tempValue, bizIndex, sdf, cal, 3);
            bizStartTime = getTimeByPattern(bizDate, "yyyy-MM-dd HH:mm:ss", Calendar.YEAR, -1);
        }
        if (StringUtil.isEmpty(efcDate)) {
            efcDate = getDate(autoTask, efcDate, RESULT_MSG, efcIndex, sdf, cal, 2);
            efcStartTime = getTimeByPattern(efcDate, "yyyy-MM-dd HH:mm:ss", Calendar.YEAR, -1);
        }
    }
    repeateInsurePTMsgWB(autoTask, "biz", bizStartTime, RESULT_MSG);
    repeateInsurePTMsgWB(autoTask, "efc", efcStartTime, RESULT_MSG);

    bizEndDate = getEndOrBeginDate(autoTask, bizDate, sdf, cal, "getEndDate");
    efcEndDate = getEndOrBeginDate(autoTask, efcDate, sdf, cal, "getEndDate");

    //检查矫正后的起保日期距离今日 是否大于配置中规定的时间 一般90天
    //可提前投保天数
    int repeatDay = Integer.parseInt(autoTask.configs.repeatDay);
    int bizDays = differentDaysByMillisecond(bizDate, sdf.format(new Date()))
    int efcDays = differentDaysByMillisecond(efcDate, sdf.format(new Date()))
    if (bizDays > repeatDay) {
        throw new InsReturnException(InsReturnException.RepeatInsure, "双纠正后的商业险的起保日期不在允许的" + repeatDay + "天之内,请核对： \n\r" + "商业险起保日期 : " + bizDate);
    }
    if (efcDays > repeatDay) {
        throw new InsReturnException(InsReturnException.RepeatInsure, "双纠正后的交强险的起保日期不在允许的" + repeatDay + "天之内,请核对： \n\r" + "交强险起保日期 : " + efcDate);
    }
    //yc_csj_001
    String bizStartDate = order.suiteInfo.bizSuiteInfo.start
    if (!bizStartDate.equals(bizDate)) {
        recalculateVhlNActualValue(autoTask, bizDate)
    }
    order.suiteInfo.bizSuiteInfo.start = bizDate;
    order.suiteInfo.bizSuiteInfo.end = bizEndDate;
    order.suiteInfo.efcSuiteInfo.start = efcDate;
    order.suiteInfo.efcSuiteInfo.end = efcEndDate;
}

//当只提示商业或交强出现重复投保时,进行校正
def adjustQuoteDate(AutoTask autoTask, JSONObject dataJSONObj, String flag) {
    Enquiry enquiry = (Enquiry) autoTask.taskEntity;
    Order order = enquiry?.order;
    String RESULT_MSG = dataJSONObj.getString("RESULT_MSG")
    JSONArray WEB_DATA = dataJSONObj.getJSONArray("WEB_DATA")
    String date = ""

    if (RESULT_MSG != ~/.*其他保险公司保单信息.*/) {
        for (int i = 0; i < WEB_DATA.size(); i++) {
            if (WEB_DATA.getJSONObject(i).getString("dwName").equals("prodDef.vhl.Base_DW")) {
                JSONObject attributeVoList = WEB_DATA.getJSONObject(i).getJSONArray("dataObjVoList").getJSONObject(0).getJSONObject("attributeVoList");
                if ("biz".equals(flag)) {
                    date = attributeVoList.getJSONObject("SY_Base.TLastStartDate").getString("value"); //上年商业保险的起期
                } else {
                    date = attributeVoList.getJSONObject("JQ_Base.TLastStartDate").getString("value");//上年交强保险的起期
                }
            }
        }
    }

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Calendar cal = getInstance();
    int index = 0;
    String endDate = "";
    checkRepeatInsure(date, RESULT_MSG)
    if (StringUtil.isNoEmpty(date)) {
        date = getTimeByPattern(date, "yyyy-MM-dd HH:mm:ss", Calendar.YEAR, 1);
        if (isLessThanCorrectDate(date)) {
            date = getTimeByPattern(sdf.format(new Date()), "yyyy-MM-dd HH:mm:ss", Calendar.DATE, 0);
            //date = setHMS2Zero(date)
        }
    }
    if (RESULT_MSG.contains("保险起期是")) {
        index = RESULT_MSG.indexOf("保险起期是");
        if (StringUtil.isEmpty(date)) {
            date = getDate(autoTask, date, RESULT_MSG, index, sdf, cal, 1);
        }
    } else if (RESULT_MSG.contains("终保日期")) {
        index = RESULT_MSG.indexOf("终保日期");
        if ("efc".equals(flag)) { // 处理交强重复投保
            date = getDate(autoTask, date, RESULT_MSG, index, sdf, cal, 2);
        } else if ("biz".equals(flag)) { // 处理单商重复投保
            date = getDate(autoTask, date, RESULT_MSG, index, sdf, cal, 3);
        } else {
            throw new RuntimeException("重复投保校正起保日期时,传入的flag有误  flag : " + flag);
        }
    } else {
        throw new InsReturnException('投保日期校正失败！')
    }
    repeateInsurePTMsgWB(autoTask, flag, date, RESULT_MSG);
    //将起保日期 设置为为 2017-11-17 00:00:00 防止 2017-11-17 01:10:00这种情况
//    cal.setTime(sdf.parse(date))
//    cal.set(Calendar.HOUR_OF_DAY, 0)
//    cal.set(Calendar.MINUTE, 0)
//    cal.set(Calendar.SECOND, 0)
//    if (cal.getTime().getTime() < sdf.parse(date).getTime()) {
//        cal.add(Calendar.DATE, 1)
//    }
//    date = sdf.format(cal.getTime())
    int repeatDay = Integer.parseInt(autoTask.configs.repeatDay);
    int days = differentDaysByMillisecond(date, sdf.format(new Date()))
    if (days > repeatDay) {
        throw new InsReturnException(InsReturnException.RepeatInsure, "单纠正后的" + flag + "险的起保日期不在允许的" + repeatDay + "天之内,请核对： \n\r" + "交强险起保日期 : " + days);
    }
    endDate = getEndOrBeginDate(autoTask, date, sdf, cal, "getEndDate");
    //校验起保日期与终保日期不同
    if ("efc".equals(flag)) {
        order.suiteInfo.efcSuiteInfo.start = date;
        order.suiteInfo.efcSuiteInfo.end = endDate;
    } else if ("biz".equals(flag)) {
        //yc_csj_001
        String bizStartDate = order.suiteInfo.bizSuiteInfo.start
        if (!bizStartDate.equals(date)) {
            recalculateVhlNActualValue(autoTask, date)
        }
        order.suiteInfo.bizSuiteInfo.start = date;
        order.suiteInfo.bizSuiteInfo.end = endDate;
    }
}

//如果保险公司返回的起保日期大于当前日期,表示该单已经出现重复投保
def checkRepeatInsure(String date, String RESULT_MSG) {
    if (StringUtil.isNoEmpty(date) && !isLessThanCorrectDate(date)) {
        throw new InsReturnException(InsReturnException.RepeatInsure, "重复投保,保险公司返回起保日期大于当前日期,已出保单： \n\r" + RESULT_MSG);
    }
}

//获取RESULT_MSG中包含的重复投保的开始时间
def getDate(AutoTask autoTask, String date, String RESULT_MSG, int index, SimpleDateFormat sdf, Calendar cal, int flag) {
    SimpleDateFormat sdd = new SimpleDateFormat("yyyy-MM-dd");
    if (flag == 1) { // 返回标准的时间格式可用
        date = RESULT_MSG.substring(index + 6, index + 17);
    } else if (flag == 2) { // 交强险重复投保时可用
        date = RESULT_MSG.substring(index + 5, index + 21);
    } else if (flag == 3) {  // 商业险重复投保时可用
        date = RESULT_MSG.substring(index + 5, index + 21);
        sdd = new SimpleDateFormat("yyyyMMddHHmm");
    }
    // 校正前的起保日期如果晚于当前日期 认定为重复投保
    if (StringUtil.isNoEmpty(date)) {
        String pattern = ""
        date = date.trim()
        if (date.contains("-")) {
            if (date.length() == 19) {
                pattern = "yyyy-MM-dd HH:mm:ss"
            } else if (date.length() == 10) {
                pattern = "yyyy-MM-dd"
            } else if (date.length() == 13) {
                pattern = "yyyy-MM-dd HH"
            } else if (date.length() == 16) {
                pattern = "yyyy-MM-dd HH:mm"
            }
        } else {
            if (date.length() == 14) {
                pattern = "yyyyMMddHHmmss"
            } else if (date.length() == 12) {
                pattern = "yyyyMMddHHmm"
            } else if (date.length() == 10) {
                pattern = "yyyyMMddHH"
            } else if (date.length() == 8) {
                pattern = "yyyyMMdd"
            } else {
                pattern = "yyyyMMddHHmm"
            }
        }
        if (StringUtil.isEmpty(pattern)) {
            throw new InsReturnException("重复投保校正日期,获取pattern出错,返回的date:" + date + "长度:" + date.length())
        }
        sdd = new SimpleDateFormat(pattern)
    }
    Date beginDate = sdd.parse(date);
//    if (beginDate.getTime() - new Date().getTime() > 0) {
//        throw new InsReturnException(InsReturnException.RepeatInsure, "重复投保,起保日期大于当前日期,已出保单： \n\r" + RESULT_MSG);
//    }
    cal.setTime(beginDate);
//    cal.add(Calendar.SECOND,1);
//    cal.add(Calendar.YEAR, 1);
    return sdf.format(cal.getTime());
}

//根据起保日期获取终保日期 或 根据终保日期获得起保日期
def getEndOrBeginDate(AutoTask autoTask, String date, SimpleDateFormat sdf, Calendar cal, String flag) {
    cal.setTime(sdf.parse(date));
    if ("getEndDate".equals(flag)) {
        //将终保日期 设置为为 2017-11-17 00:00:00 防止 2017-11-17 01:10:00这种情况
        cal.add(Calendar.YEAR, 1);
//        cal.set(Calendar.HOUR_OF_DAY, 0)
//        cal.set(Calendar.MINUTE, 0)
//        cal.set(Calendar.SECOND, 0)
        cal.add(Calendar.SECOND, -1);
    } else {
        //cal.add(Calendar.SECOND, 1)
        cal.add(Calendar.YEAR, -1);
    }

    String endDate = sdf.format(cal.getTime());
    if ("getEndDate".equals(flag)) {
    } else {
    }
    return endDate;
}

/**
 * 出现重复投保时,重新计算车辆实际价值(折旧价)
 * @param autoTask
 * 2017-12-11 11:28 yc_csj_001
 */
def recalculateVhlNActualValue(AutoTask autoTask, String bizStartDate) {
    Order order = ((Enquiry) autoTask.taskEntity).order;
    def rate = autoTask.tempValues.VhlNDespRate
    def newprice = getVhlNActualValue(autoTask, rate, order, bizStartDate)
    def realPrice = Math.round(new Double(newprice.toString()));
    def oldVhlNActualValue = autoTask.tempValues.VhlNActualValue
    autoTask.tempValues.VhlNNewPurchaseValue = realPrice;
    autoTask.tempValues.VhlNActualValue = realPrice;
}

//重复投保时将部分平台信息回写
def repeateInsurePTMsgWB(AutoTask autoTask, String flag, String beginTime, String html) {
    String endTime = beginTime
    beginTime = getTimeByPattern(beginTime, "yyyy-MM-dd HH:mm:ss", Calendar.YEAR, -1)
    String value = ""

    //测试环境用
    /*SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm")
    SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")*/
    //
    if ("efc".equals(flag)) {
        value = findhtmlvalue("保单号 ", ";", html);
        //测试环境
        /*endTime = findhtmlvalue("终保日期 ",";",html)
        sdf = new SimpleDateFormat("yyyy-MM-dd HH")
        endTime = sdf2.format(sdf.parse(endTime))*/
        //
    } else if ("biz".equals(flag)) {
        value = findhtmlvalue("保险公司名称：", "\n", html);
        //测试环境
        /*endTime = findhtmlvalue("终保日期：","险种代码",html).trim()
        endTime = sdf2.format(sdf.parse(endTime))*/
        //
    } else {
        throw new RuntimeException("传入的flag不符合规则,flag :" + flag)
    }
    PTMsgWB(autoTask, beginTime, endTime, value, flag)
    //PPSetPTMsg(autoTask, PlatformKey.application_lastInsureCo, value)
    if (autoTask.taskEntity.order.platformInfo == null) {
        autoTask.taskEntity.order.platformInfo = new HashMap<String, Object>();
    }
    ((Map) autoTask.taskEntity.order.platformInfo).put(PlatformKey.application_lastInsureCo, value)

}


//============处理重复投保相关方法 END===========


//============映射相关方法 START===========

/**
 * 使用性质
 * @param code
 * @return
 */
def getUseProps(code) {
    switch (code) {
        case [1, 10, 11, 12, 16]: "336001"; break;//非营运
        case [5, 6, 15]: "336002"; break;//营运
        case [2, 17]: "336002001"; break;//营业出租,租赁
            /*case 3:"336002002";break;
            case 4:"336002003";break;*/
        default: "336001"
    }
}
/**
 * 所属性质
 * @param carUserType
 */
def getCarUserType(carUserType) {
    switch (carUserType) {
        case 0: "338001"; break;
        case 1: "338003"; break; //企业
        case 2: "338002"; break;
        default: "338001";
    }
}
/**
 * 车辆类型 车辆种类
 * @param code
 */
def getsyvehicletypecode(carInfo) {
    String useProp = carInfo?.useProps
    if (useProp in ["1", "2", "3", "4", "5", "10", "11", "17"]) {
        def seatCnt = Integer.valueOf(carInfo?.seatCnt)
        if (seatCnt < 6) return "337001"//六座以下客车
        if (seatCnt < 10) return "337002"//六座至十座以下客车
        if (seatCnt < 20) return "337003"//十座至二十座以下客车
        if (seatCnt < 36) return "337004"//二十座至三十六座以下客车
        return "337005" //三十六座及三十六座以上客车
    } else if (useProp in ["6", "12"]) {
        def modelLoad = Double.valueOf(carInfo?.modelLoad)
        if (modelLoad < 2000) return "337006"//二吨以下货车
        if (modelLoad < 5000) return "337007"//二吨至五吨以下货车
        if (modelLoad < 10000) return "337008"//五吨至十吨以下货车
        return "337009"//十吨以上货车
    } else if (useProp in ["15", "16"]) {
        def modelLoad = Double.valueOf(carInfo?.modelLoad)
        if (modelLoad < 2000) return "337023"//2吨以下挂车
        if (modelLoad < 5000) return "337024"//二吨至五吨以下挂车
        if (modelLoad < 10000) return "337025"//五吨至十吨以下挂车
        return "337026"//十吨以上挂车
    } else {
        def carKindCode = carInfo.syvehicletypecode ?: ""
        switch (carKindCode) {
            case "KA": "337001"; break;//六座以下客车
            case "KB": "337002"; break;//六座至十座以下客车
            case "KC": "337003"; break;//十座至二十座以下客车
            case "KD": "337004"; break;//二十座至三十六座以下客车
            case "KE": "337005"; break;//三十六座及三十六座以上客车
            case "HA": "337006"; break;//二吨以下货车
            case "HB": "337007"; break;//二吨至五吨以下货车
            case "HC": "337008"; break;//五吨至十吨以下货车
            case "HD": "337009"; break;//十吨以上货车
            case "NA": "337036"; break;//低速货车
            case "GA": "337023"; break;//2吨以下挂车
            case "GB": "337024"; break;//二吨至五吨以下挂车
            case "GC": "337025"; break;//五吨至十吨以下挂车
            case "GD": "337026"; break;//十吨以上挂车
            case "NB": "337028"; break;//三轮汽车
            default: "337001";
        }
    }
}


/**
 * 车辆核定载客数 车辆种类转换
 * @param code
 */
def getSyvehicletypecode(code) {
    switch (code) {
        case [0, 1, 2, 3, 4, 5]: "337001"; break;//六座以下客车
        case [6, 7, 8, 9]: "337002"; break;//六座至十座以下客车
        default: "337001";
    }
}
/**
 * 车辆类型
 * @param code
 */
def getsyvehicletypecode4bw(code) {
    switch (code) {
        case "337001": "KA"; break;//六座以下客车
        case "337002": "KB"; break;//六座至十座以下客车
        case "337003": "KC"; break;//十座至二十座以下客车
        case "337004": "KD"; break;//二十座至三十六座以下客车
        case "337005": "KE"; break;//三十六座及三十六座以上客车
        case "337006": "HA"; break;//二吨以下货车
        case "337007": "HB"; break;//二吨至五吨以下货车
        case "337008": "HC"; break;//五吨至十吨以下货车
        case "337009": "HD"; break;//十吨以上货车
        case "337036": "NA"; break;//低速货车
        case "337023": "GA"; break;//2吨以下挂车
        case "337024": "GB"; break;//二吨至五吨以下挂车
        case "337025": "GC"; break;//五吨至十吨以下挂车
        case "337026": "GD"; break;//十吨以上挂车
        default: "KA";
    }
}
//获得车辆分类  客车，货车，摩托车，免税车，减免税车
def getcarType(order) {
    def useProps = order?.carInfo?.useProps ?: 1;
    switch (useProps) {
        case [1, 2, 3, 4, 5, 10, 11]: "349001001"; break;
        case [6, 12]: "349001002"; break;
        default: "349001001";
    }
}
//车辆细分
def getRealCarType(order) {
    //def types = order?.insuredPersons?.get(0)?.idCardType
    def type = getcarType(order).toString();
    def seatcount = order?.carInfo?.seatCnt;
    if ("349001001".equals(type)) {
        if (seatcount < 10) {
            return "3490010013";
        } else if (seatcount < 20) {
            return "3490010012";
        } else if (seatcount >= 20) {
            return "3490010011";
        }
    } else if ("349001002".equals(type)) {
        return "3490010021";
    } else {
        return "3490010026";
    }
}

/**
 * 使用性质  此方法在计算折旧率时使用
 * @param code
 */
def getUseNature(code) {
    switch (code) {
        case [2, 3, 4, 5, 6, 15]: "01"; break;//营运
        case [1, 10, 11, 12, 16]: "02"; break;//非营运
        default: "02";
    }
}
/**
 * 交管车辆类型
 * @param code
 * @return
 */
def getJgVehicleType(code) {
    //def jgVehicleType = carInfo?.JgVehicleType?:"13";//默认 k33
    switch (code) {
        case 13: "K33"; break;
        case 14: "K32"; break;
        case 19: "K31"; break;
        case 18: "K22"; break;
        case 17: "K11"; break;
        case 31: "H31"; break;
        case 33: "H21"; break;
        case 32: "H11"; break;
        case [0, 1, 2, 3, 4]: "X99"; break;
        default: "K33";
    }
}
/**
 * 交管车辆类型
 * @param code
 * @return
 */
def getJgVehicleType4bw(code) {
    //def jgVehicleType = carInfo?.JgVehicleType?:"13";//默认 k33
    switch (code) {
        case "K33": 13; break;
        case "K31": 19; break;
        case "K22": 18; break;
        case "K11": 17; break;
        case "H31": 31; break;
        case "H21": 33; break;
        case "H11": 32; break;
        case "X99": 0; break;
        default: 13;
    }
}
/**
 * 号牌颜色
 * @param carInfo
 * @return
 */
def getPlateColor(carInfo) {
    def plateColor = carInfo?.plateColor;
    switch (plateColor) {
        case 1: "0"; break;//蓝
        case 0: "1"; break;//黄
        case 3: "2"; break;//黑
        case 2: "3"; break//白
        default: "0";
    }
}
/**
 * 号牌颜色
 * @param carInfo
 * @return
 */
def getPlateColor4bw(String plateColor) {
    //def plateColor = carInfo?.plateColor;
    switch (plateColor) {
        case "0": 1; break;//蓝
        case "1": 0; break;//黄
        case "2": 3; break;//黑
        case "3": 2; break//白
        default: 1;
    }
}
/**
 * 号牌种类
 * @param code
 * @return
 */
def getPlateType(code) {
    switch (code) {
        case 0: "01"; break;//大型汽车号牌
        case 1: "02"; break;//小型汽车号牌
        case 2: "03"; break;//使馆汽车号牌
        case 3: "04"; break;//领馆汽车号牌
        case 4: "05"; break;//境外汽车号牌
        case 5: "06"; break;//外籍汽车号牌
        case 6: "07"; break;//两、三轮摩托车号牌
        case 7: "08"; break;//轻便摩托车号牌
        case 8: "09"; break;//使馆摩托车号牌
        case 9: "10"; break;//领馆摩托车号牌
        case 10: "11"; break;//境外摩托车号牌
        case 11: "12"; break;//外籍摩托车号牌
        case 12: "13"; break;//农用运输车号牌
        case 13: "14"; break;//拖拉机号牌
        case 14: "15"; break;//挂车号牌
        case 15: "16"; break;//教练汽车号牌
        case 16: "17"; break;//教练摩托车号牌
        case 17: "18"; break;//试验汽车号牌
        case 18: "19"; break;//试验摩托车号牌
        case 19: "20"; break;//临时入境汽车号牌
        case 20: "21"; break;//临时入境摩托车号牌
        case 21: "22"; break;//临时行驶车号牌
        case 22: "23"; break;//公安警车号牌
        case 23: "24"; break;//公安警用摩托车
        case 24: "81"; break;//武警号牌
        case 25: "28"; break;//军队号牌
        case 26: "99"; break;//其它车型
        default: "02";

    }
}
/**
 * 号牌种类
 * @param code
 * @return
 */
def getPlateType4bw(code) {
    switch (code) {
        case "01": 0; break;
        case "02": 1; break;
        case "15": 14; break;
        default: 1;
    }
}

//证件类型
def getCardType(code) {
    switch (code) {
        case 0: "120001"; break;//身份证
        case 1: "120006"; break;//户口薄
        case 2: "120005"; break;//驾照
        case 3: "120002"; break;//军官证
        case 4: "120004"; break;//护照
        case 5: "120007"; break;//120007港澳通行证
        case 6: "110003"; break;//机构组织代码
        case 7: "110099"; break;//其它
        case 8: "110004"; break;//统一社会信用代码
        case 9: "110005"; break;//税务登记证
        case 10: "110001"; break;//营业执照
        case 11: "120007"; break;//香港身份证 -> 港澳通行证
        case 12: "120008"; break;//台胞证 -> 台湾通行证
        default: "120001";
    }
}
//证件类型
def getCardType4bw(code) {
    switch (code) {
        case "120001": 0; break;//身份证
        case "120006": 1; break;//户口薄
        case "120005": 2; break;//驾照
        case "120002": 3; break;//军官证
        case "120004": 4; break;//护照
        case "120007": 5; break;//120007港澳通行证 , 还需进一步判断是5(港澳回乡证) 还是11(香港身份证)
        case "120008": 12; break;//台湾通行证 -> 台胞证
        case "110003": 6; break;//机构组织代码
        case "110099": 7; break;//其它
        case "110004": 8; break;//统一社会信用代码
        case "110005": 9; break;//税务登记证
        case "110001": 10; break;//营业执照
        default: 0;
    }
}
//性别转换
def getJender(code) {
    switch (code) {
        case 0: "106001"; break;
        case 1: "106002"; break;
        default: "106001";
    }
}
//险别代码转换
def getkindCode4ancheng(code) {
    switch (code) {
        case "VehicleDamage": "030006"; break;//车辆损失险
        case "ThirdParty": "030018"; break;//第三者责任险
        case "Driver": "030101"; break;//车上人员责任险（驾驶人）
        case "Passenger": "030102"; break;//车上人员责任险（乘客）
        case "Scratch": "030108"; break;//车身划痕损失险
        case "SpecifyingPlant": "030202"; break;//指定修理厂险
        case "ExtraDevice": "030117"; break;//新增加设备损失险
        case "GoodsOnVehicle": "030119"; break;//车上货物责任险
        case "CompensationDuringRepair": "030151"; break;//修理期间费用补偿险 0364/0361
        case "HolidayDouble": "030019"; break;//三者节假日翻倍险
        case "Wheel": "030205"; break;//车轮单独
        case "EngineDamageExclude": "030206"; break;//发动机涉水除外
        case "CFMDThirdParty": "030207"; break;//精神损害抚慰金（三者）
        case "CFMDDriver": "030217"; break;//附加精神损害抚慰金责任险（车上人员责任保险（司机））
        case "CFMDPassenger": "030218"; break;//附加精神损害抚慰金责任险（车上人员责任保险（乘客））
        case "NIHCThirdParty": "030208"; break;//附加医保外用药（三者）
        case "NIHCDriver": "030219"; break;//附加医保外用药责任险（车上人员责任保险（司机））
        case "NIHCPassenger": "030220"; break;//附加医保外用药责任险（车上人员责任保险（乘客））
        case "RoadsideService": "030209"; break;//道路救援服务特约条款
        case "VehicleInspection": "030210"; break;//车辆安全检测
        case "DesignatedDriving": "030211"; break;//代为驾驶服务特约条款
        case "SendForInspection": "030212"; break;//代为送检

        case "ANCVehicleDamage": "030213"; break;//附加绝对免赔率特约条款(机动车损失保险)
        case "ANCThirdParty": "030214"; break;//附加绝对免赔率特约条款(机动车第三者责任保险)
        case "ANCDriver": "030215"; break;//附加绝对免赔率特约条款(机动车车上人员责任保险(司机))
        case "ANCPassenger": "030216"; break;//附加绝对免赔率特约条款(机动车车上人员责任保险(乘客))
    }
}
//险别代码转换
def getkindCode4baowang(code) {
    switch (code) {
        case "030006": "VehicleDamage"; break;//车辆损失险
        case "030018": "ThirdParty"; break;//第三者责任险
        case "030101": "Driver"; break;//车上人员责任险（驾驶人）
        case "030102": "Passenger"; break;//车上人员责任险（乘客）
        case "030108": "Scratch"; break;//车身划痕损失险
        case "030202": "SpecifyingPlant"; break;//指定修理厂险
        case "030117": "ExtraDevice"; break;//新增加设备损失险
        case "030119": "GoodsOnVehicle"; break;//车上货物责任险
        case "030151": "CompensationDuringRepair"; break;//修理期间费用补偿险 0364/0361
        case "030019": "HolidayDouble"; break;//三者节假日翻倍险
        case "030205": "Wheel"; break;//车轮单独损失
        case "030206": "EngineDamageExclude"; break;//发动机除外
        case "030207": "CFMDThirdParty"; break;//精神损害抚慰金（三者）
        case "030217": "CFMDDriver"; break;//附加精神损害抚慰金责任险（车上人员责任保险（司机））
        case "030218": "CFMDPassenger"; break;//附加精神损害抚慰金责任险（车上人员责任保险（乘客））
        case "030208": "NIHCThirdParty"; break;//附加医保外用药（三者）
        case "030219": "NIHCDriver"; break;//附加医保外用药责任险（车上人员责任保险（司机））
        case "030220": "NIHCPassenger"; break;//附加医保外用药责任险（车上人员责任保险（乘客））
        case "030209": "RoadsideService"; break;//道路救援服务特约条款
        case "030210": "VehicleInspection"; break;//车辆安全检测
        case "030211": "DesignatedDriving"; break;//代为驾驶服务特约条款
        case "030212": "SendForInspection"; break;//代为送检
        case "030213": "ANCVehicleDamage"; break;//附加绝对免赔率特约条款(机动车损失保险)
        case "030214": "ANCThirdParty"; break;//附加绝对免赔率特约条款(机动车第三者责任保险)
        case "030215": "ANCDriver"; break;//附加绝对免赔率特约条款(机动车车上人员责任保险(司机))
        case "030216": "ANCPassenger"; break;//附加绝对免赔率特约条款(机动车车上人员责任保险(乘客))
    }
}
//获得险别编号
def getCvrgRow(code) {
    switch (code) {
        case "030006": "0"; break;//车辆损失险
        case "030018": "1"; break;//第三者责任险
        case "030101": "3"; break;//车上人员责任险（驾驶人）
        case "030102": "4"; break;//车上人员责任险（乘客）
        case "030108": "6"; break;//车身划痕损失险
        case "030206": "10"; break;//发动机涉水损失险
        case "030202": "15"; break;//指定修理厂险
        case "030117": "11"; break;//新增加设备损失险
        case "030119": "12"; break;//车上货物责任险
        case "030151": "29"; break;//修理期间费用补偿险 0364/0361
        case "030121": "13"; break;//交通事故精神损害赔偿险
        case "030050": "25"; break;//机动车交通事故强制责任险 todo 更改
        case "030205": "39"; break;//车轮单独损失险
        case "030019": "35"; break;//节假日翻倍
        case "030207": "41"; break;//精神抚慰金（三者）
        case "030208": "42"; break;//医保外用药（三者）
        case "030209": "43"; break;//道路救援
        case "030210": "44"; break;//车辆安全检测
        case "030211": "45"; break;//代为驾驶
        case "030212": "46"; break;//代为送检
        case "030217": "51"; break;//附加精神损害抚慰金责任险（车上人员责任保险（司机））
        case "030218": "52"; break;//附加精神损害抚慰金责任险（车上人员责任保险（乘客））
        case "030219": "53"; break;//附加医保外用药责任险（车上人员责任保险（司机））
        case "030220": "54"; break;//附加医保外用药责任险（车上人员责任保险（乘客））
        case "030213": "47"; break;//附加绝对免赔率特约条款(机动车损失保险)
        case "030214": "48"; break;//附加绝对免赔率特约条款(机动车第三者责任保险)
        case "030215": "49"; break;//附加绝对免赔率特约条款(机动车车上人员责任保险(司机))
        case "030216": "50"; break;//附加绝对免赔率特约条款(机动车车上人员责任保险(乘客))
    }
}
//获得险别名称
def getCvrgCCustCvrgNme(code) {
    switch (code) {
        case "030006": "机动车损失保险"; break;//车辆损失险
        case "030018": "第三者责任险"; break;//第三者责任险
        case "030101": "车上人员责任险（驾驶人）"; break;//车上人员责任险（驾驶人）
        case "030102": "车上人员责任险（乘客）"; break;//车上人员责任险（乘客）
        case "030108": "车身划痕损失险"; break;//车身划痕损失险
        case "030206": "发动机涉水损失险"; break;//发动机涉水损失险
        case "030202": "指定修理厂险"; break;//指定修理厂险
        case "030117": "新增加设备损失险"; break;//新增加设备损失险
        case "030119": "车上货物责任险"; break;//车上货物责任险
        case "030151": "修理期间费用补偿险"; break;//修理期间费用补偿险 0364/0361
        case "030121": "交通事故精神损害赔偿险"; break;//交通事故精神损害赔偿险
        case "030019": "节假日翻倍险"; break;//节假日翻倍险
        case "030050": "机动车交通事故强制责任险"; break;//机动车交通事故强制责任险 todo 更改
        case "030205": "车轮单独损失险"; break;//车轮单独损失险
        case "030019": "节假日翻倍"; break;//节假日翻倍
        case "030207": "精神抚慰金（三者）"; break;//精神抚慰金（三者）
        case "030208": "医保外用药（三者）"; break;//医保外用药（三者）
        case "030209": "道路救援"; break;//道路救援
        case "030210": "车辆安全检测"; break;//车辆安全检测
        case "030211": "代为驾驶"; break;//代为驾驶
        case "030212": "代为送检"; break;//代为送检
        case "030217": "附加精神损害抚慰金责任险（车上人员责任保险（司机））"; break;//附加精神损害抚慰金责任险（车上人员责任保险（司机））
        case "030218": "附加精神损害抚慰金责任险（车上人员责任保险（乘客））"; break;//附加精神损害抚慰金责任险（车上人员责任保险（乘客））
        case "030219": "附加医保外用药责任险（车上人员责任保险（司机））"; break;//附加医保外用药责任险（车上人员责任保险（司机））
        case "030220": "附加医保外用药责任险（车上人员责任保险（乘客））"; break;//附加医保外用药责任险（车上人员责任保险（乘客））
    }
}

/**
 * 根据配置参数中的BaseExceptionType值 转换为单系统 基本信息中的通融类型的值
 * @param type BaseExceptionType值
 * @return
 */
def getExceptionType(String type) {
    switch (type) {
        case "1": "Typ001"; break//风险类业务
        case "2": "Typ002"; break//谨慎类业务
        case "3": "Typ003"; break//特殊业务
        case "4": "Typ004"; break//非通融
        default: "Typ004";
    }
}


/**
 * 快速续保查询时,根据机构编码获取参数subSidiary的值
 * @param orgCode
 * @return
 */
def getSubSidiaryByOrgcode(String orgCode) {
    String subSidiary = ""
    switch (orgCode) {
        case "1237000000": subSidiary = "09"; break //山东机构
        case "1221000000": subSidiary = "11"; break //辽宁机构
        case "1244500000": subSidiary = "12"; break //深圳机构
        case "1234000000": subSidiary = "18"; break //安徽机构
        case "1233300000": subSidiary = "32"; break //宁波机构
        default: subSidiary = "05";//默认与四川机构相同
    }
    return subSidiary;
}

//============映射相关方法 END===========


//============平台信息回写相关方法 START===========
//平台信息回写
def PTMsgWB(AutoTask autoTask, String beginTime, String endTime, String value, String flag) {
    String key = "";
    String valueKey = ""
    if ("biz".equals(flag)) {
        key = "bizPolicies";
        valueKey = "insCorpName"
    } else if ("efc".equals(flag)) {
        key = "efcPolicies";
        valueKey = "policyId"
    } else {
        throw new RuntimeException("传入的flag有误,flag : " + flag)
    }
    JSONObject policies = new JSONObject(true);
    JSONArray policiesArray = new JSONArray();
    /*起保时间*/
    policies.put(PlatformKey.policyStartTime, beginTime);
    /*终保时间*/
    policies.put(PlatformKey.policyEndTime, endTime);
    /*保险公司或者保单号*/
    policies.put(valueKey, value);
    policiesArray.add(policies);
    PPSetPTMsg(autoTask, key, policiesArray.toJSONString());
}

def PPSetPTMsg(AutoTask autoTask, String key, String value) {
    JSONObject platformBack = GetBaseMap4JSONObject(autoTask, PlatformKey.platformBack);
    String ls_JsonArrayFlag = "," + PlatformKey.bizPolicies + "," + PlatformKey.efcPolicies + "," + PlatformKey.bizClaims + "," + PlatformKey.efcClaims + ",";
    if (ls_JsonArrayFlag.indexOf("," + key + ",") > -1) {
        platformBack.put(key, JSON.parseArray(value));
    } else {
        platformBack.put(key, value);
    }
    autoTask?.tempValues?.put(PlatformKey.platformBack, platformBack);
}

def GetBaseMap4JSONObject(AutoTask autoTask, String key) throws Exception {
    Object myobj = null;
    if (autoTask?.tempValues.containsKey(key)) {
        myobj = autoTask?.tempValues.get(key);
    }
    JSONObject ljobj_return = new JSONObject(true);
    if (null == myobj) {
    } else {
        ljobj_return = (JSONObject) myobj;
    }
    return ljobj_return;
}


//============平台信息回写相关方法 END===========


//============工具方法类 START===========
def findhtmlvalue(String findkey1, String findkey2, String html) {
    int a, b, c;
    a = html.indexOf(findkey1);
    c = findkey1.length();
    if (StringUtil.isEmpty(findkey2)) {
        b = html.length();
    } else {
        b = html.indexOf(findkey2, html.indexOf(findkey1) + c);
    }
    if (a < 0 || b <= 0 || (a + c == b)) {
        if (a < 0) {
        } else if (b <= 0) {
        } else {
        }
        return "";
    }
    return html.substring(a + findkey1.length(), b);
}

//自定义时间格式与偏移量,并将结果返回
def getTimeByPattern(String time, String pattern, int field, int amount) {
    SimpleDateFormat sdf = new SimpleDateFormat(pattern);
    Calendar cal = getInstance();
    Date date = sdf.parse(time);
    cal.setTime(date);
    cal.add(field, amount);
    date = cal.getTime();
    sdf.format(date)
}

//将yyyy-MM-dd HH:mm:ss 这种格式的日期时分秒设置为0
def setHMS2Zero(String date) {
    def format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    def time = format.parse(date)
    Calendar calendar = getInstance()
    calendar.setTime(time)
    calendar.set(Calendar.HOUR_OF_DAY, 0)
    calendar.set(Calendar.MINUTE, 0)
    calendar.set(Calendar.SECOND, 0)
    format.format(calendar.getTime())
}

//计算两个日期之间的天数
def differentDaysByMillisecond(String time1, String time2) {
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date date1 = format.parse(time1)
    Date date2 = format.parse(time2)
    int days = (int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24));
    Math.abs(days)
}

//判断传入日期是否早于当前日期 是返回true  反之false
def isLessThanCorrectDate(String date) {
    SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date oldDate = sd.parse(date);
    Date nowDate = new Date();
    oldDate.getTime() - nowDate.getTime() < 0;
}

//此方法一般用于当取autoTask.tempValues下面的值不存在时可以返回默认值空串
def getRealValue(value) {
    if (value == null || value == "null") {
        return "";
    } else {
        return value;
    }
}

//判断sub 在 str中出现的次数 (用于判断回答问题时,问题的个数)
def getCount(str, sub) {
    def index = 0;
    def count = 0;
    while ((index = str.indexOf(sub)) != -1) {
        str = str.substring(index + sub.length());
        count++;
    }
    return count;
}

//根据传入参数获得相应年份第一天 传入参数应为整数，如果是0即为当前年份，如果是-1，即为上年
def getBeginMonth(flag) {
    SimpleDateFormat sdft = new SimpleDateFormat("yyyy-MM-dd");
    def date = sdft.format(new Date()).substring(0, 4).toString();
    BigDecimal year = new BigDecimal(date).add(new BigDecimal(flag));
    def result = year + "-01-01";
    return result;
}
//根据传入参数获得相应年份最后一天
def getEndMonth(flag) {
    SimpleDateFormat sdft = new SimpleDateFormat("yyyy-MM-dd");
    def date = sdft.format(new Date()).substring(0, 4).toString();
    BigDecimal year = new BigDecimal(date).add(new BigDecimal(flag));
    def result = year + "-12-31";
    return result;
}

//获得当前关系人的生日  此处个人只考虑身份证  自定义一个标志flag:0投保人1被保人2车主
def getbirthday(order, flag) {
    def carUserType = order?.carInfo?.carUserType?.toString() ?: "0";
    SimpleDateFormat sf1 = new SimpleDateFormat("yyyyMMdd");
    if ("0".equals(carUserType)) {//个人
        def idcard = sf1.format(new Date())?.toString();
        if ("0".equals(flag)) {//投保人
            def type = order?.insurePerson?.idCardType?.toString();
            def idCard = order?.insurePerson?.idCard?.toString() ?: idcard;
            idCard = getIdCard(type, idCard);
            return idCard;
        } else if ("1".equals(flag)) {//被保人
            def type = order?.insuredPersons?.get(0)?.idCardType?.toString() ?: "";
            def idCard = order?.insuredPersons?.get(0)?.idCard?.toString() ?: idcard;
            idCard = getIdCard(type, idCard);
            return idCard;
        } else if ("2".equals(flag)) {//车主
            def type = order?.carOwnerInfo?.idCardType?.toString() ?: ""
            def idCard = order?.carOwnerInfo?.idCard?.toString() ?: idcard
            idCard = getIdCard(type, idCard);
            return idCard;
        }
    } else {
        //0个人1企业2机关
        return "";
    }
}

//getbirthday方法中抽取公共方法  获得具体关系人生日
def getIdCard(type, idCard) {
    SimpleDateFormat sf1 = new SimpleDateFormat("yyyyMMdd");
    SimpleDateFormat sf2 = new SimpleDateFormat("yyyy-MM-dd");
    if ("0".equals(type)) {//身份证
        idCard = sf2.format(sf1.parse(idCard.substring(6, 14)))
        return idCard;
    } else {
        return "";
    }
}

//根据身份证号获得生日  例：50022719850324162X
def getBirthday(code) {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd")
    SimpleDateFormat sdfs = new SimpleDateFormat("yyyy-MM-dd")
    if (StringUtil.isNoEmpty(code)) {
        def birthday = sdfs.format(sdf.parse(code.substring(6, 14)));
        return birthday;
    } else {
        return "";
    }
}
//获得玻璃险国产或进口区别
def getGlassType4yc(Order order) {
    def vinNo = order.carInfo.vin
    if (!StringUtil.isNoEmpty(vinNo) || vinNo.length() < 17 || !vinNo.startsWith("L") && !vinNo.startsWith("l")) {
        return "303011002";//进口
    } else {
        return "303011001";//国产
    }
}

/**
 * 根据车辆种类编码获取汉字，回给平台
 * @param code
 * @return
 */

def carTypeByCode(code) {
    switch (code) {
        case "337001": "六座以下客车"; break
        case "337002": "六座至十座以下客车"; break
        case "337003": "十座至二十座以下客车"; break
        case "337004": "二十座至三十六座以下客车"; break
        case "337005": "三十六座及三十六座以上客车"; break
        case "337006": "二吨以下货车"; break
        case "337007": "二吨至五吨以下货车"; break
        case "337009": "十吨以上货车"; break
        case "337036": "低速货车"; break
        case "337023": "2吨以下挂车"; break
        case "337024": "二吨至五吨以下挂车"; break
        case "337025": "五吨至十吨以下挂车"; break
        case "337026": "十吨以上挂车"; break
        case "337028": "三轮汽车"; break
        default: "六座以下客车"
    }
}

/**
 * 根据链接创建二维码，并返回base64编码格式
 * @param url
 */
def createQrcode(String url) {
    Map<EncodeHintType, String> hints = new HashMap<>()
    hints.put(EncodeHintType.CHARACTER_SET, "UTF-8")
    BitMatrix bitMatrix = new MultiFormatWriter().encode(url, BarcodeFormat.QR_CODE, 400, 400, hints)
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream()
    MatrixToImageWriter.writeToStream(bitMatrix, "PNG", outputStream)
    Base64.Encoder encoder = Base64.getEncoder()

    String text = encoder.encodeToString(outputStream.toByteArray())
    return text
}

//使用正则获取承保查询中的batch参数
def findBatch(root, batchParam) {
    def reg = "${batchParam}=\"([\\S\\s\\n]*?)\""
    def matcher = Pattern.compile(reg).matcher(root)
    if (matcher.find()) {
        println matcher.group(1)
    }

}


//将电子保单字节流转换位zip压缩包保存在项目相对路径下
def convertByteArrayToZipFile(byte[] certFile, File zipFile) {
    try {
        def out = new FileOutputStream(zipFile)
        out.write(certFile)
        out.flush()
        out.close()
    } catch (Exception e) {
        throw new IOException(e)
    }
}

def deleteDirectoryStream(Path path, log) {
    try {
        Files.delete(path)
        log.info("删除文件成功：{}", path.toString())
    } catch (IOException e) {
        log.info("无法删除的路径：{},失败异常：{}", path.toString(), e)
    }
}

def deleteFile(targetPath, policyno, log) {
    Path path = Paths.get(targetPath)

    try {
        Stream<Path> walk = Files.walk(path)
        walk.sorted(Comparator.reverseOrder())
                .each {
                    deleteDirectoryStream(it, log)
                }
        log.info("保单号:{}，删除文件成功：{}", policyno, path)
    } catch (IOException e) {
        log.info("保单号:{}，删除文件失败:{} 失败异常:{}", policyno, path, e)
    }
}


def loginParam(configs, lt) {
    def param = [
            username   : configs.username ?: configs.login,
            password   : configs.password.toString(),
            _eventId   : "submit",
            Submit     : "",
            mac        : "B0:7B:25:26:04:7D",
            ip         : "************",
            forwardedIp: "************",
            smscode    : "",
            lt         : lt
    ]
    param
}

def headerParam() {
    def header = [:]
    header."User-Agent" = "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET4.0E)"
    header."Accept" = "text/html, application/xhtml+xml, image/jxr, */*"
    header."Accept-Encoding" = "gzip, deflate"
    header.Host = "www.95552.cc:8005"
    header."Accept-Language" = "zh-CN"
    header.'Content-Type' = 'application/x-www-form-urlencoded'
    header
}

static def photoType(code) {
    switch (code) {
        case ["3", "4"]: [["UW01007": "行驶证"], ["UW02007": "行驶证"]]; break
        case ["0", "1", "28", "29", "25", "26", "43", "44", "16", "107", "108"]: ["UW01008": "身份证"]; break
        case "20": [["UW01010": "合格证"], ["UW02010": "合格证"]]; break
        case "14": [["UW01009", "购车发票"], ["UW02009", "购车发票"]]; break
        case ["2", "56", "19", "48", "30", "57", "27", "58", "46", "45", "22", "21", "70"]: [["UW01017": "其他"], ["UW02017": "其他"]]; break
        case ["100", "54", "55", "52", "53", "24", "61", "71", "72", "73", "74", "101", "102", "103", "104", "105", "109", "110"]: ["UW03001": "自定义"]; break
        case ["17", "18"]: [["UW01003": "保单"], ["UW02003": "保单"]]; break
        case "49": ["FGZD": "浮动告知单"]; break
        case "50": ["T": "投保单"]; break
        case "51": ["R": "发票"]; break
        case ["52", "31", "32", "33", "36", "37", "35", "13", "38", "39", "40", "41"]: ["VhlList": "车辆清单"]; break
        case ["34", "12"]: ["B", "内置标志"]; break
        case "106": ["UW02008": "营业执照"]; break
        case ["5", "6", "7", "8", "9", "10", "11", "15", "59", "60"]: [["UW01012": "验车照片"], ["UW02012": "验车照片"]]; break
    }
}

static def volidParam(code, type) {
    if (photoType(code).size() < 2) {
        return photoType(code)//如果该表达式为true说明是通用单证多一层节点//[0]是个人[1]是企业
    } else {
        return "1" == type ? photoType(code)[0] : photoType(code)[1]
    }
}

def nodeExist(nodeKey, nodeId, nodeName, page_code, page_desc, pageId) {
    def node1 = nodeKey.elements().find { it -> it.attribute("NAME").value == nodeName }
    if (!node1) {
        node1 = nodeKey.addElement("NODE").addAttribute("ID", nodeId).addAttribute("NAME", nodeName)
    }
    if (node1.elements().find { it -> it.attribute("NAME").value == page_desc } == null) {
        node1.addElement("NODE").addAttribute("ID", page_code).addAttribute("NAME", page_desc)
    }
    node1.elements().find { it -> it.attribute("NAME").value == page_desc }.addElement("LEAF").setText(pageId)
}

//电子保单下载
def sdas(autoTask, common_all, img, proposalno) {
    //数字资产占位
    def data = common_all.makeSDASDataRobot(autoTask, proposalno)
    def fileName = "river_attachment/2022/${System.currentTimeMillis()}.pdf"
    def assetResult = SDASUtils.assetOccupancy("application/pdf", fileName, data)
    if (!assetResult || assetResult.code != 0) {
        throw new InsReturnException(Others, "2107PDF占位异常")
    }
    //上传
    SDASUtils.uploadPutAsset(fileName.toString(), assetResult, new ByteArrayEntity(img))
}

//检查cookie是否生效
def checkLogin(html, config, autoTask) {
    if (html.contains("获取验证码")) {
        autoTask.httpClient = buildHttpClientWithCookie(config)
        def response = HttpSender.doGet(autoTask.httpClient, autoTask.tempValues['url'] as String, null, 'UTF-8')
        if (response.contains("获取验证码")) throw new InsReturnException(Others, "登录失败,请检查cookie是否有效")
    }
}

def buildHttpClientWithCookie(config) {
    def cookieStr = config['cookie'] as String
    def cookieStore = new BasicCookieStore()
    def cookieArray = cookieStr.split("; ")
    cookieArray.each({ item ->
        def split = item.split('=')
        def cookie = new BasicClientCookie(split[0], split[1])
        cookie.setDomain('www.95552.cc')
        cookie.setPath('/cas')
        cookieStore.addCookie(cookie)
    })
    LayeredConnectionSocketFactory sslSocketFactory2 = HttpSender.getSSlSocketFactory("TLSv1.2", "TLSv1.2")
    return HttpClients.custom().setSSLSocketFactory(sslSocketFactory2).setDefaultCookieStore(cookieStore).build()
}


//============工具方法类 END===========

