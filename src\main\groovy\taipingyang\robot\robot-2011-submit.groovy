package taipingyang.robot


import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import taipingyang.robot.module.Robot2011Util

@BaseScript BaseScript_Http_Enq _

head Robot2011Util.getDefaultHead(tempValues, config)

autoTask.params.clear()
String submitInsureInfoParam = (String) autoTask.tempValues.get("submitInsureInfoParam")
Robot2011Util.genBody(tempValues, submitInsureInfoParam)
