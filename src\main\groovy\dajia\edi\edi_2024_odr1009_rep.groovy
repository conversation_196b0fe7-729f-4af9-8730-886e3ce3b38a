package dajia.edi

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.model.RuleInfoKey
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.utils.PlatformUtil
import com.cheche365.bc.utils.RuleUtil
import static common.common_all.*
import static dajia.common.dajia_dict.*
import static dajia.edi.edi_common_2024.*
import org.apache.commons.lang3.StringUtils

import java.util.regex.Pattern



def responseStr = root?.toString()
def result = JSONObject.parseObject(responseStr)

if (result['code'] == '2000') {
    if (('0000' == result?.body?.status && StringUtils.isBlank(result?.body?.result?.reInsuredInfo as String)) || ('0000' == result?.body?.status && tempValues?.reInsuredHadDeal)) {
        def SQ = enquiry['SQ'] = enquiry['SQ'] ?: [:]
        SQ['totalCharge'] = 0.0
        def basePart = result['body']['result']['basePart']
        def riskRatio = result['body']['result']['riskRatio']
        //回写信息
        def jqCommissionRate = basePart['jqCommissionRate']   //交强险手续费
        def billCTPELR = riskRatio['billCTPELR'] //交强险预期赔付率
        def billCOMELR = riskRatio['billCOMELR'] //商业险预期赔付率
        def billELR = riskRatio['billELR'] //整单预期赔付率
        def jqPubScores = riskRatio['jqPubScores'] //交强险大家分
        def syPubScores = riskRatio['syPubScores'] //商业险大家分
        def syResvNum2 = riskRatio['syResvNum2'] //【商业险】无赔款优待系数
        def pricingAdjustValue = riskRatio['pricingAdjustValue'] //自主定价系数
        def platformInfo = tempValues['platformInfo'] = tempValues['platformInfo'] ?: [:]
        def platformBack = tempValues[PlatformKey.platformBack] = tempValues[PlatformKey.platformBack] as Map ?: [:]
        def definition = platformBack['definition'] = platformBack['definition'] as Map ?: [:]
        platformInfo['definition'] = definition
        enquiry['definition'] = definition
        platformBack[PlatformKey.application_expectMixedRatio] = billELR
        platformBack[PlatformKey.noClaimDiscountCoefficient] = syResvNum2
        platformBack[PlatformKey.selfRate] = pricingAdjustValue
        definition[PlatformKey.application_expectMixedRatio] = billELR
        definition[PlatformKey.noClaimDiscountCoefficient] = syResvNum2
        definition[PlatformKey.selfRate] = pricingAdjustValue
        // 需求13813 新续保标志
        def jqRenewMrk = basePart['jqRenewMrk'] as String
        def syRenewMrk = basePart['syRenewMrk'] as String
        definition[PlatformKey.application_loyalty] = enquiry?.baseSuiteInfo?.bizSuiteInfo?.start ? syRenewMrk : jqRenewMrk
        if (enquiry?.baseSuiteInfo?.efcSuiteInfo?.start && result?.body?.result?.jqAppStatus == '1') {
            def vehicleTaxation = result['body']['result']['vehicleTaxation']
            enquiry?.baseSuiteInfo?.taxSuiteInfo = [:]
            enquiry?.baseSuiteInfo?.taxSuiteInfo?.charge = vehicleTaxation['taxAggTax']
            SQ['taxCharge'] = vehicleTaxation['taxAggTax']
            enquiry?.baseSuiteInfo?.efcSuiteInfo?.orgCharge = basePart['jqPremium']
            enquiry?.baseSuiteInfo?.efcSuiteInfo?.discountCharge = basePart['jqPremium']
            enquiry?.baseSuiteInfo?.efcSuiteInfo?.discountRate = 1
            SQ['efcCharge'] = basePart['jqPremium']
            SQ['totalCharge'] = basePart['insurancePremium']
            SQ['efcProposeNum'] = basePart['jqProposalNo']
            if (result['body']['result']['jqSpecialEngageList']) {
                tempValues?.jqSpecialEngageList = result['body']['result']['jqSpecialEngageList']
            }
            platformBack[PlatformKey.efcBrokerageRate] = jqCommissionRate
            platformBack[PlatformKey.application_expectTrafficLossRatio] = billCTPELR
            platformBack[PlatformKey.application_trafficRate] = jqPubScores
            definition[PlatformKey.efcBrokerageRate] = jqCommissionRate
            definition[PlatformKey.application_expectTrafficLossRatio] = billCTPELR
            definition[PlatformKey.application_trafficRate] = jqPubScores
            definition['efcDajiaRate'] = jqPubScores
        }
        if (enquiry?.baseSuiteInfo?.bizSuiteInfo?.start && result?.body?.result?.syAppStatus == '1') {

            if (result['body']['result']['sySpecialEngageList']) {
                tempValues?.sySpecialEngageList = result['body']['result']['sySpecialEngageList']
            }
            SQ['bizCharge'] = basePart['syPremium']
            SQ['totalCharge'] = basePart['insurancePremium']
            SQ['bizProposeNum'] = basePart['syProposalNo']
            def coverageList = result['body']['result']['coverageList'] as JSONArray
            def syDiscount = 0.0
            def energyType = checkEnergyType(enquiry?.carInfo?.carModelName, enquiry?.carInfo?.plateNum)

            enquiry?.baseSuiteInfo?.bizSuiteInfo?.suites?.each { suite ->
                def insCode = energyType == '0' ? simpleEN(suite['code']) : newEnergyEN(suite['code'])
                def kindInfo = coverageList.find { coverage ->
                    coverage['cvrgNo'] == insCode
                }
                if (kindInfo) {
                    suite['discountRate'] = kindInfo['discountValue']
                    syDiscount = kindInfo['discountValue']
                    suite['orgCharge'] = kindInfo['basePremium']
                    suite['discountCharge'] = kindInfo['premium']
                    if (['RoadsideService', 'VehicleInspection', 'DesignatedDriving', 'SendForInspection'].contains(suite['code'])) {
                        suite['orgCharge'] = 0
                        suite['discountCharge'] = 0
                    } else if (['Passenger', 'NIHCPassenger'].contains(suite['code'])) {

                    } else {
                        suite['amount'] = kindInfo['amt']
                    }
                }
            }
            enquiry?.baseSuiteInfo?.bizSuiteInfo?.discountRate = syDiscount
            enquiry?.baseSuiteInfo?.bizSuiteInfo?.discountCharge = basePart['syPremium']
            enquiry?.baseSuiteInfo?.bizSuiteInfo?.orgCharge = ((basePart['syPremium'] as BigDecimal) / (syDiscount as BigDecimal)).setScale(2, BigDecimal.ROUND_HALF_UP)
            platformBack[PlatformKey.application_expectLossRatio] = billCOMELR
            platformBack[PlatformKey.application_bizRate] = syPubScores
            definition[PlatformKey.application_expectLossRatio] = billCOMELR
            definition[PlatformKey.application_bizRate] = syPubScores
            definition['bizDajiaRate'] = syPubScores
        }
        if (result['body']['result']['nvhPolicyList']) {
            tempValues['nvhPolicyList'] = result['body']['result']['nvhPolicyList']
            SQ['totalCharge'] = (SQ['totalCharge'] as BigDecimal).add(enquiry.SQ.nonMotor?.discountCharge as BigDecimal).setScale(2, BigDecimal.ROUND_HALF_UP)
        }
        PlatformUtil.doBackPlatformInfo(autoTask as AutoTask, enquiry as Map, tempValues as Map)
        if (!tempValues?.adjustDiscount) {
            tempValues.adjustDiscount = true
            RuleUtil.doRuleInfo(autoTask as AutoTask, enquiry as Map, tempValues as Map, '2024', '', '')
            if (tempValues['ruleInfo'] && tempValues['ruleInfo'][RuleInfoKey.geniusItem_policyDiscount]) {
                throw new InsReturnException(InsReturnException.AllowRepeat, "调整折扣重新报价")
            }
        }

    } else {
        if (result?.body?.result?.jqAppStatus == '2' || result?.body?.result?.syAppStatus == '2') {
            def vehicleInfo = result?.body?.result?.carModelList?.get(0)
            if (vehicleInfo) {
                def vehicleInfoBack = [
                       'vehlcleCode' : vehicleInfo['vehlcleCode'], //行业车型编码
                       'vehicleStyleUniqueId' : vehicleInfo['vehicleStyleUniqueId'], //车型唯一ID, 单交,单商，交商混保传值
                       'vehicleIndustryModelCode' : vehicleInfo['industryModelCode'] //交强行业车型编码
                ]
                tempValues['vehicleInfo'] = vehicleInfoBack
                throw new InsReturnException(InsReturnException.AllowRepeat, "精准场景再次报价")
            }
        }
        def errorMsg = result['body']['message']
        def jqAppStatus = result?.body?.result?.jqAppStatus
        if (jqAppStatus && jqAppStatus != '1') {
            if (result?.body?.result?.jqAppStatusMessage) {
                errorMsg = result['body']['result']['jqAppStatusMessage']
            }
            if (errorMsg && errorMsg.contains('重复投保')) {
                def dateTimeReg = /\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?/
                def reg = /.*?保单号 (?<policyNo>.*);起保日期 (?<start>${dateTimeReg});终保日期 (?<end>${dateTimeReg});.*/
                def matcher = errorMsg =~ reg
                def lastEnd
                if (matcher.find()) { // 交强险重复投保
                    lastEnd = matcher.group('end')
                    def newEfcTime = getEfcDate(lastEnd)
                    enquiry?.baseSuiteInfo?.efcSuiteInfo?.start = newEfcTime['start']
                    enquiry?.baseSuiteInfo?.efcSuiteInfo?.end = newEfcTime['end']
                    throw new InsReturnException(InsReturnException.AllowRepeat, "交强险重复投保，终保日期：${lastEnd}，再次报价")
                }
            }
        }
        def syAppStatus = result?.body?.result?.syAppStatus
        def reInsuredInfo = result?.body?.result?.reInsuredInfo
        if ((syAppStatus && syAppStatus != 1) || StringUtils.isNotBlank(reInsuredInfo)) {
            if (result?.body?.result?.syAppStatusMessage) {
                errorMsg += result['body']['result']['syAppStatusMessage']
            }
            if (reInsuredInfo && (reInsuredInfo.contains('重复投保') || reInsuredInfo.contains("终保日期"))) {
                def reInsuredInfoFragment = reInsuredInfo.contains('发动机号') ? reInsuredInfo.substring(0, reInsuredInfo.indexOf('发动机号')) : reInsuredInfo
                def dateArr = []
                def p = Pattern.compile("(\\d{12})")
                def m = p.matcher(reInsuredInfoFragment as String)
                while (m.find()) {
                    dateArr.add(m.group())
                }
                if (dateArr) {
                    def newBizTime = getBizDate(dateArr[dateArr.size() - 1])
                    enquiry?.baseSuiteInfo?.bizSuiteInfo?.start = newBizTime['start']
                    enquiry?.baseSuiteInfo?.bizSuiteInfo?.end = newBizTime['end']
                    tempValues?.reInsuredHadDeal = true
                    throw new InsReturnException(InsReturnException.AllowRepeat, "商业险重复投保，终保日期：${dateArr[dateArr.size() - 1]}，再次报价")
                }
            }

        }
        if (errorMsg.contains('新车备案') && !tempValues?.newCarRecord) {
            tempValues?.newCarRecord = true
            def ex =  new InsReturnException(InsReturnException.AllowRepeat, "平台需要新车备案")
            ex.setStep(2)
            throw ex
        }
        throw new InsReturnException(errorMsg as String)
    }

} else {
    throw new InsReturnException(result['message'] as String)
}
