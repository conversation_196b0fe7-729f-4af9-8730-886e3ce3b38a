package guoshou.edi

import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import groovy.transform.BaseScript
import guoshou.script.BaseScript_2002

import static guoshou.edi.edi_2002_common_new.checkResultStatus

@BaseScript BaseScript_2002 _

JSONObject jsonResult = root

checkResultStatus(jsonResult, true)

def carJson = jsonResult.businessData?.find { it -> carInfo.jyCode == it.rbCode}

if (!carJson && tempValues.localFlag) {
    throw new InsReturnException(InsReturnException.Others, "vin码查询与加入localFlag标识均无法返回正确车型！")
}

if (!carJson) {
    tempValues.localFlag = true
    throw new InsReturnException(InsReturnException.AllowRepeat, "无法匹配到指定车型，加入localFlag重试！")
}

tempValues.carMessResult = carJson
tempValues.vehicleStyleUniqueId = carJson?.vehicleStyleUniqueId