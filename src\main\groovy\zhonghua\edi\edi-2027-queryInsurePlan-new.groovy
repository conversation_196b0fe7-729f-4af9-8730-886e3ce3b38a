package zhonghua.edi

import common.common_all
import org.apache.commons.lang3.StringUtils
import org.dom4j.Document
import org.dom4j.DocumentHelper
import org.dom4j.Element

import java.text.SimpleDateFormat
import java.time.LocalDate

def common = new edi_2027_common_new()
def xmlString=queryInsurePlanService(common)

//车险承保方案信息接口
def queryInsurePlanService(common){
    XmlBuilder xmlBuilder=new XmlBuilder()
    xmlBuilder.setHeadAndMain(config,common.getBusinessCode(enquiry),
            common.getTransrNo("queryInsurePlan")[0],tempValues.serialdecimal)
    .addElement("BASE").setElementValue("C_DPT_CDE",common.getCity(enquiry))

    def bizSuiteInfo=enquiry?.baseSuiteInfo?.bizSuiteInfo
    def efcSuiteInfo=enquiry?.baseSuiteInfo?.efcSuiteInfo
    if(bizSuiteInfo && efcSuiteInfo){
        xmlBuilder.setElementValue("T_INSRNC_BGN_TM",bizSuiteInfo?.start)
    }else{
        xmlBuilder.setElementValue("T_INSRNC_BGN_TM",efcSuiteInfo==null?bizSuiteInfo?.start:efcSuiteInfo?.start)
    }
    xmlBuilder.setElementValueReturnRoot("T_OPER_DATE",LocalDate.now().toString())

    def carMess=tempValues.carMess
    xmlBuilder.addElement("VHL")
        .setElementValue("C_LCN_NO",enquiry?.carInfo?.plateNum.equals("新车未上牌")?"*-*":enquiry?.carInfo?.plateNum)
        .setElementValue("BUY_DATE", enquiry?.carInfo?.plateNum?.equals("新车未上牌") ? enquiry?.carInfo?.firstRegDate : "")
        .setElementValue("C_NEW_VHL",enquiry?.carInfo?.isNew?"1":"0")
        .setElementValue("C_DRV_OWNER",enquiry?.carOwnerInfo?.name)
        .setElementValue("C_ENG_NO",enquiry?.carInfo?.engineNum)
        .setElementValue("C_VHL_FRM",enquiry?.carInfo?.vin)
        .setElementValue("C_FST_REG_DTE",enquiry?.carInfo.firstRegDate.substring(0,10))
        .setElementValue("C_VEHICLE_CODE",carMess.C_VEHICLE_CODE.toString())
        .setElementValue("C_VEHICLE_NAME",carMess.C_VEHICLE_NAME.toString())
        .setElementValue("C_VEHICLE_BRAND",carMess.C_VEHICLE_BRAND.toString())
        .setElementValue("N_VEHICLE_PRICE",carMess.N_VEHICLE_PRICE.toString())
        .setElementValue("N_LIMIT_LOAD_PERSON",enquiry?.carInfo?.seatCnt.toString())
        .setElementValue("C_EXT_MSR",enquiry?.carInfo?.displacement.toString())
        .setElementValue("C_VEHICLE_FAMILY",carMess.C_VEHICLE_FAMILY.toString())
        .setElementValue("C_AGE_LEVEL","0".equals(enquiry.carOwnerInfo.idCardType)?
                getAgeLevel(LocalDate.now().getYear()- Integer.parseInt(enquiry.carOwnerInfo.idCard.substring(6,10))):"341071")
        .setElementValue("C_SEX", [6, 8, 9, 10].contains(enquiry?.carOwnerInfo?.idCardType) ? '' : common.getSex(enquiry?.carOwnerInfo?.sex)[0])

    if(!enquiry?.carInfo?.plateNum.equals("新车未上牌") && common.getProvince(enquiry).equals("110000")){
        xmlBuilder.setElementValue("C_CHECK_NO",tempValues.baseChange?.cCheckNo.toString())
        .setElementValue("C_CHECK_CODE",tempValues.baseChange?.cCheckCode)
    }
    xmlBuilder.setElementValue("T_ISSUE_DATE",enquiry?.carInfo.firstRegDate.substring(0,10))
    .setElementValue("C_OWNER_TEL",enquiry?.carOwnerInfo?.mobile ?: getRandomMobileNo())
    .setElementValue("C_IDENT_NO",enquiry.carOwnerInfo.idCard)
    .setElementValue("C_IDENT_TYPE",common.getIdCardType(enquiry.carOwnerInfo.idCardType,common.getProvince(enquiry))[0])
    .setElementValue("C_NEW_ENERGY",common.getIsNewEnergy(enquiry,null))
    .setElementValue("C_HY_MODEL_CDE",carMess.C_HY_MODEL_CDE.toString())

    return xmlBuilder.getDocumentString()
}

def getAgeLevel(param){
    def ageLevel=[
            "341060":"0-18",
            "341061":"18-20",
            "341062":"20-25",
            "341063":"25-30",
            "341064":"30-35",
            "341065":"35-40",
            "341066":"40-45",
            "341067":"45-50",
            "341068":"50-55",
            "341069":"55-60",
            "341070":"60"]
    def result
    for(e in ageLevel){
        def tempAges=e.value.split("-")
        if(tempAges.length>1){
            if(Integer.parseInt(tempAges[0])<=param && Integer.parseInt(tempAges[1])>param){
                result=e.key
                break
            }
        }else{
            if(Integer.parseInt(tempAges[0])<=param){
                result=e.key
                break
            }
        }
    }
    return  StringUtils.isEmpty(result)?"341071":result
}

def getRandomMobileNo(){
    //各运营商号段
    def mobileStart = [
            "134", "135", "136", "137", "138", "139", "150", "151", "152", "157", "158", "159",
            "182", "183", "184", "187", "188", "178", "147", "172", "198","130", "131", "132",
            "145", "155", "156", "166", "171", "175", "176", "185", "186","133", "149",
            "153", "173", "177", "180", "181", "189", "199"
    ]
    Random random=new Random()
    StringBuffer sb=new StringBuffer()
    sb = sb.append(mobileStart.get(random.nextInt(mobileStart.size()))).
            append(random.nextInt(9999).toString()).append(random.nextInt(9999).toString())
    if(sb.length()<11){
        def temp=11-sb.length()
        for(int a=0;a<temp;a++){
            sb.append(random.nextInt(10))
        }
    }
    return sb.toString()
}

xmlString
