package tianan.robot

import cn.hutool.core.util.ObjectUtil
import cn.hutool.crypto.Mode
import cn.hutool.crypto.Padding
import cn.hutool.crypto.symmetric.AES
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.constants.Constants
import com.cheche365.bc.enums.InsCompanyEnum
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.model.RuleInfoKey
import com.cheche365.bc.model.car.CarOwnerInfo
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.model.car.InsurePerson
import com.cheche365.bc.model.car.TaxSuiteInfo
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.tools.QRCodeUtil
import com.cheche365.bc.utils.PlatformUtil
import com.cheche365.bc.utils.RedisUtil
import com.cheche365.bc.utils.RuleUtil
import com.cheche365.bc.utils.TaskUtil
import com.cheche365.bc.utils.VehicleUtil
import com.cheche365.bc.utils.sdas.SDASUtils
import com.cheche365.bc.utils.sender.HttpSender
import common.common_all
import org.apache.commons.codec.binary.Base64
import org.apache.commons.lang3.StringUtils
import org.apache.http.client.config.RequestConfig
import org.apache.http.entity.ByteArrayEntity
import org.apache.http.impl.client.BasicCookieStore
import org.apache.http.impl.client.CloseableHttpClient
import org.apache.http.impl.cookie.BasicClientCookie
import org.slf4j.LoggerFactory

import javax.crypto.Cipher
import java.security.KeyFactory
import java.security.interfaces.RSAPublicKey
import java.security.spec.X509EncodedKeySpec
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.LocalDate
import java.time.Period
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.regex.Pattern

import static com.alibaba.fastjson.JSON.parse
import static com.cheche365.bc.exception.InsReturnException.Others
import static com.cheche365.bc.utils.sender.HttpSender.doPostWithRetry
import static common.common_all.getToken

def static getHeader() {
    [
            'Host'            : "netauto.95505.cn",
            'Accept'          : "application/json, text/plain, */*",
            'Accept-Encoding' : "gzip, deflate, br",
            'Accept-Language' : "zh-CN,zh;q=0.9",
            'Content-Type'    : "application/json;charset=UTF-8",
            'Origin'          : "https://netauto.95505.cn",
            'User-Agent'      : "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36",
            'sec-ch-ua'       : '"Chromium";v="86", "\"Not\\A;Brand";v="99", "Google Chrome";v="86"',
            'sec-ch-ua-mobile': '?0',
            'Sec-Fetch-Dest'  : 'empty',
            'Sec-Fetch-Mode'  : 'cors',
            'Sec-Fetch-Site'  : 'same-origin',
            'Connection'      : "keep-alive",
            'Referer'         : "https://netauto.95505.cn/",
    ]
}
/**
 * Base64转换
 * @return
 */
static def base64(String str) {
    new cn.hutool.core.codec.Base64().encode(str.getBytes("utf-8")).replaceAll("\n", "").replaceAll("\r", "")
}

static def carResultMap(Object loginResult, Enquiry entity) {
    def rbCode = entity.order.carInfo?.rbCode
    def vehicles = (loginResult.data?.vehicles ?: loginResult.data) as List
    def result = vehicles.find {
        it.modelCode == rbCode
    } ?: vehicles.sort {
        Integer.valueOf(it.purchasePrice as String)
    }.first()
    result
}

static def localCheck(plateNum, province) {
    if (province?.length() < 4) {
        return false
    }
    String provinceFirstName = VehicleUtil.getAlias(province)
    if (plateNum?.contains("未上牌")) {
        return true
    }
    return plateNum.startsWith(provinceFirstName)
}

def populateCarInfo(autoTask, getCarInfo, requestConfig, isMotorcycle) {
    def util = new robot_2045_util()
    CloseableHttpClient httpClient = autoTask.httpClient as CloseableHttpClient
    def config = autoTask.getConfigs()
    def enquiry = autoTask.getTaskEntity()
    def carInfo = enquiry.order.carInfo
//    def efcSuiteInfo = enquiry.order.suiteInfo?.efcSuiteInfo
//    def bizSuiteInfo = enquiry.order.suiteInfo?.bizSuiteInfo
    def newBuydate = dateFormatter(carInfo?.firstRegDate)
    def isNew = { newBuydate1 ->
        def days = LocalDate.now().toEpochDay() - LocalDate.parse(newBuydate1).toEpochDay()
        return days < 270
    }
    def vehicledto = [
            colorcode          : "02",
            evalPriceChe300    : "",
            evalPriceChe       : "",
            runMiles           : "1",                              //运行公里数
            tonCount           : new BigDecimal(getCarInfo?.limitLoad ?: carInfo?.modelLoad).toInteger(),            //核定载质量
            completekerbmass   : carInfo.fullLoad.intValue() ?: getCarInfo?.completeKerbMass ?: 0,     //整备质量
            sendRepairCode     : config.GuMainSendRepairCode,
            frameno            : carInfo?.vin ?: getCarInfo?.vinNo,                //车架号
            arbitBoardname     : "",
            argueSolution      : "2",                              //合同争议解决方式
            printbrandname     : getCarInfo?.printBrandName,
            series             : carInfo?.familyName,
            ecdemicVehicle     : localCheck(carInfo?.plateNum, enquiry?.order?.insureArea?.province) ? "0" : "1",   //外地是1本地是0
            newVehicle         : isNew(newBuydate) ? "1" : "0",     //是否为新车 1是新车0是旧车
            brandname          : getCarInfo?.brandName,
            sindouflag         : "",
            chgdate            : carInfo.isTransfer ? carInfo?.transferDate?.format("yyyy-MM-dd") : "",                                //过户车转移日期
            firstBeneficiary   : "",
            vehicleloss        : "01",
            modelName          : getCarInfo?.modelcname,
            hymodelcode        : getCarInfo?.ciModelCode,
            userYear           : getCarInfo?.realUseYear,           //车辆使用年限 起保日期-购买日期
            carbuydate         : newBuydate,                        //车辆购置日期和初等日期一样
            enrollDate         : newBuydate,                        //车辆初等日期
            certificateNo      : "",
            purchaseprice      : getCarInfo?.purchasePrice,         //新车购置价

            ciNoticeType       : getCarInfo?.ciNoticeType,          //公告型号
            runareacode        : "02",
            licensetypecode    : isMotorcycle == 1 ? "07" : util.parsePlateType(carInfo?.plateType ?: 0),   //号牌种类
            exhaustScale       : (carInfo.displacement > 100 ? carInfo.displacement?.setScale(0) : (carInfo.displacement * 1000).setScale(0)) ?: getCarInfo?.exhaustScale,          //排量
            power              : getCarInfo?.power != null ? getCarInfo?.power?.contains(".") ? getCarInfo?.power?.substring(0, getCarInfo?.power?.indexOf(".") + 2) : getCarInfo?.power : "0",          //功率
            seatcount          : carInfo.seatCnt ?: getCarInfo?.seatCount,             //载客量
            vehicleweight      : getCarInfo?.vehicleweight,
            chgownerflag       : carInfo.isTransfer ? "1" : "0",    //是否为过户车 0:非过户；1：过户
            category           : "1",
            identifytype       : util.parseIdentifyType(enquiry.order?.carOwnerInfo?.idCardType),
            certificatedate    : newBuydate, //发证日期
            carname            : getCarInfo?.categoryname,          //车款名称
            categoryname       : getCarInfo?.categoryname,          //车款名称
            loanvehicleflag    : "0",
            specialcarflag     : carInfo.isTransfer ? "1" : "0",    //交强险特殊标识0：没过户 1：过户
            carCheckStatus     : "2",                               //验车情况暂时写死
            uncheckReasonCode  : "2",
            carchecker         : "",
            carchecktime       : "",
            saleCompany        : "",
            saleAreaCode       : "",
            sale4SFlag         : "",
            agencytype         : "1",
            crossfirstoutinsure: "0",
            carownercategory   : "1",
            certifytype        : util.parseIdentifyType(enquiry.order?.carOwnerInfo?.idCardType),
            certificateType    : "01",
            sex                : "1",
            checked            : false,
            carRegiste         : "0",
            tonnage            : new BigDecimal(carInfo?.modelLoad ?: getCarInfo?.limitLoad ?: "0").toInteger(),                                  //核定载质量
            pmUseNatureCode    : "",
            vehiclecategory    : isMotorcycle == 1 ? "M21" : util.parseJgVehicleType(carInfo?.jgVehicleType ?: ""),
            haulage            : "0",
            vehiclestyledesc   : getCarInfo?.categoryname,
            powertypecode      : util.fuelType(getCarInfo?.powertype),
            factoryid          : getCarInfo?.factoryid,
            vehicleclass       : getCarInfo?.vehicleclass,
            importFlag         : "L" == carInfo?.vin?.substring(0, 1) ? "国产车" : "进口车",
            espurchaseprice    : getCarInfo?.purchasePrice,
            cardCollectFlag    : "1",
            cartypecode        : "01",
            madeFactory        : getCarInfo?.factoryid,
            fullWeight         : carInfo.fullLoad.intValue() ?: getCarInfo?.completeKerbMass ?: 0,
            cicarname          : getCarInfo?.ciCarName,
            ytflag             : "fail",
            calculatemode      : "1",
            insuredName        : enquiry.order?.carOwnerInfo?.name,
            taxpayercerticode  : enquiry.order?.carOwnerInfo?.idCard,
            taxPayerNo         : enquiry.order?.carOwnerInfo?.idCard,
            carOwner           : enquiry.order?.carOwnerInfo?.name,
            phone              : "13928374755",
            maxDesignSpeed     : isMotorcycle == 1 ? "52" : null
    ]

    //报价前进行车型校验如果校验失败更新车辆重新报价
    def checkCarUrl = "https://netauto.95505.cn/enetauto/newcalculate/checkCarModel.htmls"
    def checkParam = [
            licenseno       : "新车未上牌" == carInfo.plateNum ? "暂未上牌" : carInfo.plateNum,                //车牌号
            licenseType     : isMotorcycle == 1 ? "07" : util.parsePlateType(carInfo?.plateType ?: 0), //号牌种类
            engineno        : carInfo?.engineNum ?: getCarInfo?.engineNo,  //发动机号
            vin             : carInfo.vin ?: getCarInfo?.vinNo,
            usenaturecode   : util.parseUseProps(carInfo?.useProps),// 车辆使用性质
            newBuydate      : newBuydate,
            attachnature    : util.parseCarUserType(carInfo?.carUserType), // 所属性质
            carusetype      : util.carUseType(carInfo?.useProps),//车辆用途
            carkindcode     : isMotorcycle == 1 ? "400" : util.getMotorType(carInfo?.jgVehicleType),//机动车种类
            purchaseprice   : getCarInfo?.purchasePrice,         //新车购置价
            cimodelcode     : getCarInfo?.ciModelCode,
            modelCode       : getCarInfo?.modelCode ?: carInfo.jyCode,
            companyCode     : enquiry?.config?.configMap?.companyCode ?: config.GuMainCompanyCode, //业务来源
            effectiveDate   : LocalDate.now().plusDays(1),
            newVehicle      : isNew(newBuydate) ? "1" : "0",
            carkindcodeshow : getCarInfo?.carKindCodeShow ?: util.getCarKindCodeShow(carInfo?.syvehicletypecode), //车辆种类
            completeKerbMass: carInfo.fullLoad.intValue() ?: getCarInfo?.completeKerbMass ?: 0,
            seatcount       : carInfo.seatCnt ?: getCarInfo?.seatCount
    ]
    if (carInfo?.familyName?.toString()?.contains("电动") && config?.carkindType == "true") {
        vehicledto.carkindcodeshow = "75"
    }
    if (getIsNewEnergy(getCarInfo)) {
        checkParam << ["powertypecode": getFuleType(getCarInfo?.powertype)[0]]
        vehicledto << ["isNewEnergy": "1"]
    }
    vehicledto << checkParam
    vehicledto.licenseno = "新车未上牌".equals(carInfo.plateNum) ? "0" : carInfo.plateNum ?: getCarInfo?.licenseNo
    //如果是新车未上牌，checkCarModel是暂未上牌，报价接口是0
    def checkCarResult = parse(doPostWithRetry(5, httpClient, true, checkCarUrl, JSONObject.toJSONString(checkParam), null, autoTask?.reqHeaders, "utf-8", requestConfig, ""))
    def vehicle = [:]
    if (checkCarResult.code == "0000") {

        def checkDates = checkCarResult?.data?.vehicleDTOList
        if (checkDates != null) {
            def checkDate = checkDates.sort { a, b -> Double.valueOf(a.purchasepricenottax) - Double.valueOf(b.purchasepricenottax)
            }.first()
            vehicle << [
                    modelCode       : checkDate?.modelCode,
                    hymodelcode     : checkDate?.ciModelCode,
                    cicarname       : checkDate?.ciCarName,
                    carname         : checkDate?.ciCarName,
                    categoryname    : checkDate?.ciCarName,
                    espurchaseprice : checkDate?.purchasepricenottax,
                    purchaseprice   : checkDate?.purchasepricenottax,
                    cimodelcode     : checkDate?.ciModelCode,
                    vehiclestyledesc: checkDate?.ciCarName,
                    vehiclebrand    : checkDate?.modelcname,
                    modelname       : checkDate?.modelcname,
                    caryear         : checkDate?.caryear,
                    brandname       : checkDate?.brandName
            ]
            autoTask.tempValues.vehicledto?.purchasePrice = checkDate?.purchasepricenottax ?: checkDate?.purchasePrice
        }
        //todo 转包车逻辑
        if (checkCarResult.data?.sameFlag != "1") {

        }
    } else {
        throw new InsReturnException(Others, checkCarResult.message)
    }
    vehicledto << vehicle
}

//时间格式转换
static def dateFormatter(Object date) {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
    return sdf.format(date)
}

//通过身份证号获取
static def getBirthday(idCard) {
    if (idCard?.length() == 18) {
        def year = idCard.substring(6, 10)
        def month = idCard.substring(10, 12)
        def day = idCard.substring(12, 14)
        return year + "-" + month + "-" + day
    }
    return ""
}
//通过生日计算年龄
static def getAge(date) {
    def now = LocalDate.now()
    return Period.between(date, now).getYears()
}
//是否及时起保
def effectflag(suiteInfo) {
    def effectDate = [:]
    effectDate.effectflag = suiteInfo?.start?.length() < 11 ? "0" : "00".equals(suiteInfo?.start?.substring(11, 13)) ? "0" : "1"
    effectDate.date = suiteInfo?.start
    effectDate.enddate = suiteInfo?.end
    effectDate
}
// 处理投保日期
static def startDate(suiteInfo) {
    def startDate = suiteInfo?.start?.split(" ")[0]
    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    def start = LocalDate.parse(startDate, dateTimeFormatter)
    start
}

static def endDate(suiteInfo) {
    def endDate = suiteInfo?.end?.split(" ")[0]
    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    def end = LocalDate.parse(endDate, dateTimeFormatter)
    end
}

//处理商业险
def populatKindItems(suites, autoTask) {
    def util = new robot_2045_util()
    def tempValues = autoTask?.tempValues
    def carInfo = autoTask.getTaskEntity().order.carInfo ?: [:]
    def seatNum = ((tempValues?.vehicledto?.seatCnt ?: carInfo.seatCnt) as BigDecimal) - 1
    def kindList = []
    suites?.each { key, suite ->
        def contrast = util.getKindName2020(key)
        if (contrast) {
            def (code, name) = contrast
            def amount = suite?.amount?.toString() ?: ""
            def kind = [
                    kindcode: code,
                    kindname: name,
                    kindind : "2",
                    kindflag: 0,
                    riskcode: getIsNewEnergy(tempValues?.vehicledto) ? "0891" : "0888",
                    amount  : amount
            ]
            if (["01", "85", "606"].contains(code)) { //机动车损失险
                amount = tempValues?.vehicledto?.actualValue ?: tempValues?.vehicledto?.adjustRealValue
                kind << ["amount": amount]
                if ("01".contains(code)) {
                    kind << ["deductible": "0"]
                }
            }
            if ("89" == code) {
                if (getIsNewEnergy(tempValues?.vehicledto)) {
                    kind << [
                            shareMIAmountFlag: "0",
                            unitamount       : "",
                            count            : ""
                    ]
                } else {
                    kind << ["unitamount": amount]
                    amount = new BigDecimal(amount) * seatNum
                    kind << ["count": seatNum]
                    kind << ["amount": amount]
                }
            }
            if ("044" == code) {// 乘客
                kind << ["unitamount": amount]
                amount = new BigDecimal(amount) * seatNum
                kind << ["count": seatNum]
                kind << ["amount": amount]
            }
            if ("041" == code) {
                kind << ["count": "1"]
                kind << ["unitamount": new BigDecimal(amount)]
            }
            if ("19" == code) { // 机动车第三者责任保险附加法定节假日限额翻倍险
                amount = suites.find { it.value.code == "ThirdParty" }?.value?.amount
                kind << ["amount": amount]
            }
            if (["01", "02", "041", "044"].contains(code)) { //如果是主险则kindind是1 其他都是2
                def kindind = "1"
                kind << ["kindind": kindind]
            } else if (["601", "602", "604", "605"].contains(code)) { //如果是附加绝对免赔率
                kind.remove("amount")
                def deductiblerate = new BigDecimal(amount).multiply(100)
                kind << ["deductiblerate": deductiblerate]
            } else if (["79", "80", "81", "82"].contains(code)) { //如果是特约条款
                if (code == "79" && !["0", "2", "7", "12", "17", "22"].contains(amount.toString().split("\\.")[0])) {
                    throw new InsReturnException(Others, "投保道路救援服务特约条款时，服务次数必须为0、2、7、12、17、22中的一个,amoumt为:" + amount)
                }
                def servicetimes = new BigDecimal(suite?.amount?.toString() ?: "").setScale(0)
                kind << [servicetimes: servicetimes]
                kind << [amount: "0"]
            } else if (["607", "609"].contains(code)) {//充电桩损失保险 附加自用充电桩责任保险

                kind << ["amount": "0"]
                def config = autoTask.getConfigs()
                def miscParam = autoTask.getTaskEntity().getMisc()
                def supplyParams = [:]
                if (miscParam?.supplyParam) {
                    miscParam.supplyParam.each { it -> supplyParams << [(it.key): it.value]
                    }
                }
                def chargingMess = [
                        "chargerNo"            : 1,
                        "chargingPostType"     : supplyParams?.neChargerModel ?: config?.NEChargerModel,
                        "chargingPostCode"     : supplyParams?.neChargerNo ?: config?.NEChargerNo,
                        "chargingPostAddress"  : supplyParams?.neChargerAddress ?: config?.NEChargerAddress,
                        "chargingPostAddressEl": "1" == (supplyParams?.neChargerLocationType ?: config?.NEChargerLocationType) ? "1" : "2",
                        "chargingPostKind"     : supplyParams?.neChargerType ?: config?.NEChargerType,
                        "amount"               : amount
                ]
                if ("607" == code) {
                    chargingMess << ["chargingPostUseAge": supplyParams?.neChargerWarranty ?: config?.NEChargerWarranty]
                }
                kind << ["chargingPostInfoList": [chargingMess]]
            }
            if (suite?.share) {
                kind << [
                        shareMIAmountFlag: "1",
                        amount           : "0"
                ]
            }
            kindList << kind
        } else {
            throw new InsReturnException("没有此险别的对应信息：[" + key + "]")
        }
    }
    kindList
}

//将字符串20200101转换为2020-01-01
static def dateFormatter1(date) {
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
    Date format = simpleDateFormat.parse(date)
    dateToLocal(format)
}
//将date类型转换为LocalDate
static def dateToLocal(date) {
    try {
        Instant instant = date.toInstant()
        ZoneId zoneId = ZoneId.systemDefault()
        LocalDate localDate = instant.atZone(zoneId).toLocalDate()
        localDate
    } catch (Exception e) {
        throw new InsReturnException(Others, "证件号码和投被保人类型不一致")
    }
}
//将字符串时间格式2020-01-01转换成LocalDate
static def dateFormat(date) {
    def formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    def localDate = LocalDate.parse(date, formatter)
    localDate
}
//核保查询信息回写
def QuotationPolicyResult(autoTask, type, continueResult) {
    def util = new robot_2045_util()
    def entity = autoTask.getTaskEntity()
    //回写车和人的信息
    returnCarPersons(autoTask, entity, continueResult)
    entity.totalCharge = new BigDecimal(0)
    def baseSuiteInfo = entity.order.suiteInfo
    def bizSuiteInfo = type != null ? ("1" == type || "3" == type) ? baseSuiteInfo.bizSuiteInfo : [:] : baseSuiteInfo.bizSuiteInfo
//商业险
    def efcSuiteInfo = type != null ? ("2" == type || "3" == type) ? baseSuiteInfo.efcSuiteInfo : [:] : baseSuiteInfo.efcSuiteInfo
//交强险
    def kindList = continueResult.kindList
    def riskList = continueResult.riskList
    def taxSuiteInfo = baseSuiteInfo?.taxSuiteInfo ?: new TaxSuiteInfo()
    def accidentProposeCode
    if (Objects.nonNull(entity.misc?.nonMotor)) {
        def rcld = continueResult.personAuto[0]
        def nonCharge
        def productCode = entity.misc?.nonMotor?.productCode?.toString()
        if (productCode.split("-")[0] == "JYXCOMBO") {
            nonCharge = continueResult.policyMsgDTO?.jypremium ?: 0
            accidentProposeCode = rcld?.jyProposalno
        } else {
            nonCharge = continueResult.policyMsgDTO?.personautopremium ?: rcld?.premium
            accidentProposeCode = rcld?.rcldProposalno
        }
        if (ObjectUtil.isNull(nonCharge)) {
            throw new InsReturnException(Others, "未获取到非车价格")
        }
        entity.misc?.nonMotor << [
                accidentProposeCode: accidentProposeCode,
                discountCharge     : nonCharge
        ]
        entity.totalCharge += new BigDecimal(nonCharge)
    }
    if (efcSuiteInfo) {
        taxSuiteInfo?.discountCharge = new BigDecimal(continueResult.policyMsgDTO?.sumtax ?: 0).setScale(2, BigDecimal.ROUND_HALF_UP)
        taxSuiteInfo?.orgCharge = new BigDecimal(continueResult.policyMsgDTO?.sumtax ?: 0).setScale(2, BigDecimal.ROUND_HALF_UP)
//    taxSuiteInfo?.discountCharge = new BigDecimal(efcActualRoot.plcCarShipTax.sumTax ?: 0).setScale(2, BigDecimal.ROUND_HALF_UP)
//    if (taxSuiteInfo?.discountCharge) totalCharge = totalCharge.add(taxSuiteInfo?.discountCharge)
        kindList.forEach {
            if ("0881" == it.riskcode.toString()) {
                def discountCharge = new BigDecimal(it.premium ?: 0).setScale(2, BigDecimal.ROUND_HALF_UP)
                def discount
                def count = new BigDecimal(it.discount)
                discount = count <=> 10 == -1 ? count : count.divide(100 as BigDecimal).setScale(4, BigDecimal.ROUND_HALF_UP)
                efcSuiteInfo.orgCharge = discountCharge.divide(count).setScale(2, BigDecimal.ROUND_HALF_UP)
                efcSuiteInfo.discountCharge = discountCharge
                efcSuiteInfo.discountRate = discount
                efcSuiteInfo.amount = new BigDecimal(it.amount ?: 0).setScale(2, BigDecimal.ROUND_HALF_UP)
            }
        }
        def jtcieffectflag = continueResult?.policyMsgDTO?.jtcieffectflag
        def start1 = continueResult?.policyMsgDTO?.jtcistartdate?.substring(0, 10) ?: LocalDate.now().plusDays(1).toString()
        def end1 = dateFormat(start1.substring(0, 10)).plusYears(1).minusDays(1)
        efcSuiteInfo.start = "1" == jtcieffectflag ? continueResult?.policyMsgDTO?.jtcistartdate : start1 + " 00:00:00"
        efcSuiteInfo.end = "1" == jtcieffectflag ? continueResult?.policyMsgDTO?.jtcienddate : end1.toString() + " 23:59:59"
        entity.order.suiteInfo.efcSuiteInfo = efcSuiteInfo
        entity.order.suiteInfo.taxSuiteInfo = taxSuiteInfo
        entity.totalCharge += (taxSuiteInfo?.orgCharge + (efcSuiteInfo.discountCharge ?: new BigDecimal( 0).setScale(2, BigDecimal.ROUND_HALF_UP)))
    }
    if (bizSuiteInfo) {
        def orgCharge = new BigDecimal(0)
        def discount
        def suites = [:]
        def premium = 0
        kindList.each {
            if ("0881" != it.riskcode.toString()) {
                premium = new BigDecimal(it.premium?.toString()).setScale(2, BigDecimal.ROUND_HALF_UP)
                def bizDis = new BigDecimal(it.discount)
                discount = bizDis <=> 10 == -1 ? bizDis : bizDis.divide(100 as BigDecimal)

                def item = [:]
                def amount = it.amount
                if (["79", "80", "81", "82"].contains(it.kindcode)) {
                    amount = it.servicetimes
                } else if (["607", "609"].contains(it.kindcode)) {
                    def chargingPostInfoList = it?.chargingPostInfoList
                    amount = chargingPostInfoList[0].amount
                }
                if (["044", "87"].contains(it.kindcode) || ("89" == it.kindcode && !getIsNewEnergy(autoTask?.tempValues?.vehicledto))) {
                    amount = new BigDecimal(it.unitamount as String)
                }
                def discountCharge = new BigDecimal(premium ?: 0)
                def basepremium = discountCharge.divide(discount, 2).setScale(2, BigDecimal.ROUND_HALF_UP)
                if ("" != it.kindcode || "" != it.kindname) {
                    def itemKey = "" == it.kindcode ? util.riskCodeMap(it.kindname) : util.outKindName2020(it.kindcode)[0]
                    item << [(itemKey): [
                            orgCharge     : basepremium,
                            code          : it.kindcode ? util.outKindName2020(it.kindcode)[0] : util.riskCodeMap(it.kindname),
                            name          : it.kindcode ? util.outKindName2020(it.kindcode)[1] : it.kindname,
                            amount        : amount,
                            discountCharge: discountCharge,
                            discountRate  : discount,
                            share         : "1" == it.shareMIAmountFlag ? "true" : "false"
                    ]]
                }
                orgCharge += basepremium
                suites << item
            }
        }
        def bizRisk = riskList.find { ("0881" != it.riskcode) }
        def bzTotalCharge = new BigDecimal(bizRisk?.sumPremium) ?: new BigDecimal(continueResult?.policyMsgDTO?.bsumPremium) ?: new BigDecimal(0)
        bizSuiteInfo?.discountRate = discount ?: (bzTotalCharge / orgCharge).setScale(4, BigDecimal.ROUND_HALF_UP)
        bizSuiteInfo?.orgCharge = orgCharge  //原始保费
        bizSuiteInfo?.suites = suites
        bizSuiteInfo?.discountCharge = bzTotalCharge //折后保费
        def bvcieffectflag = continueResult?.policyMsgDTO?.bvcieffectflag
        def start2 = continueResult?.policyMsgDTO?.bvcistartdate ?: LocalDate.now().plusDays(1).toString()
        def end2 = dateFormat(start2.substring(0, 10)).plusYears(1).minusDays(1)
        bizSuiteInfo?.start = bvcieffectflag == "1" ? continueResult?.policyMsgDTO?.bvcistartdate : start2 + " 00:00:00"
        bizSuiteInfo?.end = bvcieffectflag == "1" ? continueResult?.policyMsgDTO?.bvcienddate : end2.toString() + " 23:59:59"
        entity.order.suiteInfo.bizSuiteInfo = bizSuiteInfo
        entity.totalCharge += bizSuiteInfo?.discountCharge
    }
}

/*
* @Description 回写人车信息
*/

static returnCarPersons(autoTask, entity, continueResult) {
    def util = new robot_2045_util()
    //回写车牌号
    def carInfo = entity?.order?.carInfo
    def getCarInfo = continueResult.vehicledto
    entity?.order?.carInfo = (carInfo.properties << [
            plateNum         : getCarInfo?.licenseno,//车牌号
            plateType        : Integer.valueOf(util.plateTypeParse(getCarInfo?.licenseType)),//好牌种类
            engineNum        : getCarInfo?.engineno,//发动机号
            vin              : getCarInfo?.frameno,//车架号
            seatCnt          : Integer.valueOf(getCarInfo?.seatcount),
            fullLoad         : new BigDecimal(getCarInfo?.fullWeight ?: 0),
            syvehicletypecode: util.carKindCodePase(getCarInfo?.carkindcodeshow).get(0), //车辆种类代码
            syvehicletypename: util.carKindCodePase(getCarInfo?.carkindcodeshow).get(1), //车辆种类名称
            displacement     : new BigDecimal(getCarInfo?.exhaustScale) / 1000, //排气量
            carModelName     : getCarInfo.brandname ?: getCarInfo.series ?: "",  //车型名称
            isTransfer       : getCarInfo?.chgownerflag != "0", //是否为过户车1：是过户车0：非过户车
            price            : new BigDecimal(getCarInfo?.purchaseprice),
            carPriceType     : 0,
            firstRegDate     : new SimpleDateFormat("yyyy-MM-dd").parse(getCarInfo?.carbuydate),
            useProps         : Integer.valueOf(util.getParseUseProps(getCarInfo.usenaturecode, carInfo?.useProps ?: "210")),
            isNew            : getCarInfo.newVehicle == "1",
            modelLoad        : new BigDecimal(getCarInfo.tonnage),
            familyName       : getCarInfo.brandname ?: getCarInfo.series ?: "",
            carUserType      : util.getParseCarUserType(getCarInfo.attachnature),
            jgVehicleType    : Integer.valueOf(util.getParseJgVehicleType(getCarInfo.vehiclecategory))
    ]).findAll { it.key != "class" }
    autoTask.plateNo = getCarInfo?.licenseno   //修改日志表面的车牌

    //车主
    def gsCarOwners = continueResult.personList.find { it.type == "3" }
    def carOwner = entity.order.carOwnerInfo ?: new CarOwnerInfo()
    if (gsCarOwners) {
        entity.order.carOwnerInfo = returnPerson(gsCarOwners, carOwner)
    }
    //投保人
    def gsApplicantPerson = continueResult.personList.find { it.type == "1" }
    def applicantPerson = entity.order.insurePerson ?: new InsurePerson()
    if (gsApplicantPerson) {
        entity.order.insurePerson = returnPerson(gsApplicantPerson as Map, applicantPerson)
    }
    //被保人
    //回写投被保人
    def insuredPerson = new InsurePerson()
    def gsInsuredPerson = continueResult.personList.find { it.type == "2" }
    if (gsInsuredPerson) {
        insuredPerson = returnPerson(gsInsuredPerson as Map, insuredPerson)
        if (entity.order.insuredPersons != null) {
            entity.order.insuredPersons.clear()
        } else {
            entity.order.insuredPersons = []
        }
        entity.order.insuredPersons += insuredPerson //往数组中添加元素
    }
}

static def returnPerson(gsPerson, personInfo) {
    def util = new robot_2045_util()
    (personInfo.properties << [
            name      : gsPerson?.name,
            idCardType: Integer.valueOf(util.getIdentifyType(gsPerson?.identifytype as String) ?: 0),
            idCard    : personInfo?.idCard ?: decryptData(gsPerson.identifyno?.toString()) ?: "",
            mobile    : personInfo?.mobile ?: decryptData(gsPerson.mobile?.toString()) ?: "",
            address   : gsPerson.homeaddr ?: "",
            email     : personInfo?.email ?: decryptData(gsPerson.email?.toString()) ?: ""
    ]).findAll { it.key != "class" }
}

static def decryptData(String data) {
    try {
        AES aes = new AES(Mode.ECB, Padding.PKCS5Padding, 'cGhvbmVNYXNrCDYH'.bytes)
        return aes.decryptStr(data)
    } catch (Exception e) {

    }
    return data
}

//将[[key:value],[key1:value]]转换为[key:value,key1:value]
static def getPersonInfo(supplyParam) {
    supplyParam.inject([:]) { result, param ->
        def itemCode = param?.key
        def itemValue = param?.value
        /*  def itemCode = param?.itemcode?.toString()
          def itemValue = param?.itemvalue?.toString() ?: ""*/
        if (itemCode.contains("Address")) {
            itemValue = getAddInfoName(itemValue)
        }
        result << [(itemCode): itemValue]
    }
}

static def getAddInfoName(address) {
    if (address) {
        address.replaceAll("\\|\\d{6}#", "")
    }
}


/**
 * 生成11位随机手机号码*/
static def getRandomNum() {
    String[] str = "130,131,132,133,134,135,136,137,138,139,150,151,152,153,155,156,157,158,159,186".split(",")
    int index = getNum(0, str.length - 1)
    String first = str[index]
    String second = String.valueOf(getNum(1, 888) + 10000).substring(1)
    String thrid = String.valueOf(getNum(1, 9100) + 10000).substring(1)
    return first + second + thrid
}

static getNum(int start, int end) {
    return (int) (Math.random() * (end - start + 1) + start)
}

//拉起支付
def static doGetPay(String qrCodeUrl, String payCodeSum, autoTask) {
    def applyJSON = JSONObject.parseObject(autoTask.getApplyJson())
    def payCodeUrl = QRCodeUtil.encodeBase64QRCode(qrCodeUrl) //生成二维码
    if (payCodeUrl) {
        applyJSON.taskStatus = 6
        applyJSON.payCodeSum = payCodeSum
        applyJSON.payCodeIsSuccess = "true"
        applyJSON.payCodeUrl = payCodeUrl
        autoTask.applyJson = applyJSON.toJSONString()
    } else {
        throw new InsReturnException("生成二维码失败！")
    }
}

static def saveCalculateno(list) {
    //将查询出来的报价单号进行倒序排序
    def calculate = list.sort { a, b -> new BigDecimal(b.calculateno.substring(1)) - new BigDecimal(a.calculateno.substring(1))
    }
    def calculatenoResult = calculate.get(0) //取第一条数据
    return calculatenoResult
}

//核保查询所需参数拼装
static def findParamParse(startDate, endDate, start, status, documentNo, calculateno) {
    def param = [
            calculateno: calculateno,
            start      : start, //起始日期
            end        : 10,    //终止日期
            status     : status,
            licenseno  : "",
            certno     : "",
            begindate  : startDate ?: LocalDate.now().plusDays(1).minusMonths(1),
            finishdate : endDate ?: LocalDate.now().plusDays(1),
            historyflag: "1",
            frameNo    : "",
            documentNo : documentNo,
    ]
    //JSONObject.toJSONString(["jsonKey": base64(JSON.toJSONString(param))])
    JSON.toJSONString(param)
}

//============平台信息回写相关方法 START===========
//平台信息回写
static def PTMsgWB(AutoTask autoTask, String beginTime, String value, String endTime, String flag) {
    def key
    def valueKey
    if ("biz" == flag) {
        key = "bizPolicies"
        valueKey = "insCorpName"
    } else if ("efc" == flag) {
        key = "efcPolicies"
        valueKey = "insCorpName"
    } else {
        throw new RuntimeException("传入的flag有误,flag : " + flag)
    }
    JSONObject policies = new JSONObject(true)
    JSONArray policiesArray = new JSONArray()
    /*起保时间*/
    policies.put(PlatformKey.policyStartTime, beginTime)
    /*终保时间*/
    policies.put(PlatformKey.policyEndTime, endTime)
    policiesArray.add(policies)
    policies.put(valueKey, value)
    PPSetPTMsg(autoTask, key, policiesArray.toJSONString())
}

static def PPSetPTMsg(AutoTask autoTask, String key, String value) {
    JSONObject platformBack = GetBaseMap4JSONObject(autoTask, PlatformKey.platformBack)
    String ls_JsonArrayFlag = "," + PlatformKey.bizPolicies + "," + PlatformKey.efcPolicies + "," + PlatformKey.bizClaims + "," + PlatformKey.efcClaims + ","
    if (ls_JsonArrayFlag.indexOf("," + key + ",") > -1) {
        platformBack.put(key, JSON.parseArray(value))
    } else {
        platformBack.put(key, value)
    }
    autoTask?.tempValues?.put(PlatformKey.platformBack, platformBack)
}

static def GetBaseMap4JSONObject(AutoTask autoTask, String key) throws Exception {
    Object myobj = null
    if (autoTask?.tempValues?.containsKey(key)) {
        myobj = autoTask?.tempValues?.get(key)
    }
    JSONObject ljobj_return = new JSONObject(true)
    if (null == myobj) {
    } else {
        ljobj_return = (JSONObject) myobj
    }
    return ljobj_return
}

/**
 * 规则交互通过规则获取期望折扣
 * @param autoTask
 * @param businessInsurance
 */
def exceptDiscount(AutoTask autoTask) {
    if (!autoTask?.tempValues?.ruleFlag) {
        //规则交互
        RuleUtil.doRuleInfo(autoTask, autoTask?.configs?.LoginUsername, "", "")
        JSONObject ruleResultObj = null != (JSONObject) autoTask.tempValues.ruleInfo ? (JSONObject) autoTask.tempValues.ruleInfo : new JSONObject()
        String discount = null != ruleResultObj.get(RuleInfoKey.geniusItem_policyDiscount) ? ruleResultObj.get(RuleInfoKey.geniusItem_policyDiscount) : ""
        if (discount) {
            autoTask?.tempValues?.expDisCoef = discount
            autoTask?.tempValues?.ruleFlag = true
            discount
            throw new InsReturnException(InsReturnException.AllowRepeat, "根据规则传入折扣：" + discount + "开始报价")
        }
    }
}

def platInfoResult(autoTask, data) {
    def util = new robot_2045_util()
    def enquiry = autoTask.getTaskEntity()
    def carkindcode = data?.vehicledto?.carkindcode ?: ""
    def tianan_threeTranche = data?.vehicleChdDTO?.threeTranche ?: "" //三者分档
    def tianan_totalTranche = data?.vehicleChdDTO?.totalTranche ?: "" //三者+分档
    def tianan_vehicleDamageTranche = data?.vehicleChdDTO?.vehicleDamageTranche ?: "" //车损分档分档
    def tianAn_TMIndex = data.cPlatformVO?.tianMaIndexBasicPrice ?: data.trcPlatformVO?.tianMaIndexBasicPrice ?: ""
    def noClaimDiscountCoefficient = data.cPlatformVO?.commericBastPlat?.noAmountAdjustRatio ?: ""
    def selfRate = data.cPlatformVO?.commericBastPlat?.premfactorPrice ?: "" ////自主定价系数
    def bizContinuityInsureYears = data.cPlatformVO?.commericBastPlat?.insureYears ?: ""
    def bwCommercialClaimTimes = data.cPlatformVO?.commericBastPlat?.claimTimes ?: ""
    def commercialClaimTimes = data.cPlatformVO?.commericBastPlat?.claimTimes ?: ""
    def loyalty = data.policyMsgDTO.insuredStatus ?: ""//需求15170客户忠诚度
    def bizRate = carkindcode == "200" ? data?.vehicleChdDTO?.carloadTranche ?: "" : data?.vehicledto?.selfVehcType ?: ""
    def tianan_csxForecastScore = data?.vehicleChdDTO?.damageTranche ?: ""//车损分档预测
    def tianan_szxForecastScore = data?.vehicleChdDTO?.cdThreeTranche ?: ""//三者分档预测
    def tianan_csxszxForecastScore = data?.vehicleChdDTO?.threeDamageTranche ?: ""//三者+车损分档预测
    def tianan_jqxForecastScore = data?.vehicleChdDTO?.cdCompulsoryTranche ?: ""//交强险分档预测
    def tianan_jqxszxForecastScore = data?.vehicleChdDTO?.compulsoryThreeTranche ?: ""//交强+三者分档预测
    def trafficViolationRiskLevel = data?.vehicledto?.violationRiskLevel ?: ""
    def beginTime, endTime
    def insCorpName
    def loyaltyReasons = data?.cPlatformVO?.commericBastPlat?.noFloatCause ?: ""//商业险不浮动原因
    def bwCompulsoryClaimTimes//交强险理赔次数
    def platformInfo = [:]
    PPSetPTMsg(autoTask, PlatformKey.noClaimDiscountCoefficient, noClaimDiscountCoefficient)
    //自主定价系数
    PPSetPTMsg(autoTask, PlatformKey.selfRate, selfRate)
    //连续承保年数
    PPSetPTMsg(autoTask, PlatformKey.bizContinuityInsureYears, bizContinuityInsureYears)
    //连续承保次数
    PPSetPTMsg(autoTask, PlatformKey.bwCommercialClaimTimes, bwCommercialClaimTimes)
    PPSetPTMsg(autoTask, RuleInfoKey.application_commercialClaimTimes, commercialClaimTimes)
    //三者分档预测
    PPSetPTMsg(autoTask, PlatformKey.application_expectLossRatio, tianan_threeTranche)
    //车损分档
    PPSetPTMsg(autoTask, "application.expectLossRatioTag", tianan_vehicleDamageTranche)
    PPSetPTMsg(autoTask, PlatformKey.loyaltyReasons, loyaltyReasons)

    PPSetPTMsg(autoTask, PlatformKey.application_expectMixedRatio, data.cPlatformVO?.wholePrePayRateBasicPrice ?: "")

    insCorpName = autoTask.tempValues?.insName ?: ""
    PPSetPTMsg(autoTask, PlatformKey.application_lastInsureCo, insCorpName as String)
    //获取上年商业险保险信息
    if (data.cPlatformVO?.lastPolicyPlat) {
        beginTime = data.cPlatformVO?.lastPolicyPlat?.lastPolicyEffectiveDate
        endTime = data.cPlatformVO?.lastPolicyPlat?.lastPolicyExpireDate
        PTMsgWB(autoTask, beginTime, insCorpName, endTime, "biz")
    }

    if (data.trcPlatformVO?.bzBastPlat) {
        beginTime = data.trcPlatformVO.bzBastPlat.lastStartDate          //交强险上年投保日期
        endTime = data.trcPlatformVO.bzBastPlat.lastEndDate              //交强险上年投保结束日期
        def bwCompulsoryClaims = data.trcPlatformVO.bzClaimPlat

        //交强险理赔
        if (bwCompulsoryClaims) {
            bwCompulsoryClaimTimes = bwCompulsoryClaims.size()       //交强险理赔次数是个数组
            def efcclaimAmount = 0.0                                         //交强险理赔金额
            bwCompulsoryClaims.each { bwCompulsoryClaim -> efcclaimAmount += bwCompulsoryClaim.claimAmount }
            PPSetPTMsg(autoTask, PlatformKey.bwCompulsoryClaimTimes, bwCompulsoryClaimTimes >= 2 ? "上年有两次及以上理赔" : "新保或上年发生一次有责任不涉及死亡理赔")
            PPSetPTMsg(autoTask, PlatformKey.bwLastCompulsoryClaimSum, efcclaimAmount.toString()) //交强险理赔金额
        } else {
            PPSetPTMsg(autoTask, PlatformKey.bwCompulsoryClaimTimes, "上年没有理赔")
        }
        PTMsgWB(autoTask, beginTime, null, endTime, "efc")
    }
    //0888：商业险险种code 0891：新能源商业险险种code 0881：交强险险种code
    if (data.riskList?.find { it.riskcode == "0888" || it.riskcode == "0891" }) {
        PPSetPTMsg(autoTask, PlatformKey.application_bizRate, bizRate.toString())
        PPSetPTMsg(autoTask, PlatformKey.bizScore, tianAn_TMIndex) //天马指数_标费
    } else {
        PPSetPTMsg(autoTask, PlatformKey.application_trafficRate, bizRate.toString())
        PPSetPTMsg(autoTask, PlatformKey.TRAFFIC_SCORE, tianAn_TMIndex) //天马指数_标费
    }
    //获取上年交强险投保信息
    def renewalLastYearClaimTimes = data?.lastclaimcount != null ? data?.lastclaimcount + "" : ""
    def renewalLastYearClaimAmount = data?.lastclaimloss != null ? data?.lastclaimloss + "" : ""
    PPSetPTMsg(autoTask, "application.renewalLastYearClaimTimes", renewalLastYearClaimTimes)
    PPSetPTMsg(autoTask, "application.renewalLastYearClaimAmount ", renewalLastYearClaimAmount)
    PPSetPTMsg(autoTask, PlatformKey.application_loyalty, loyalty) // 新/续保标志: 首年投保:0,续保:1,转保:2
    PPSetPTMsg(autoTask, "application.trafficViolationRiskLevel", trafficViolationRiskLevel) //交通违章风险评级
    def definitionMap = autoTask?.tempValues?.platformBack ?: [:]
    definitionMap.each { k, v -> platformInfo << [(k): v] }
    enquiry.order.platformInfo = platformInfo
    PlatformUtil.doBackPlatformInfo(autoTask)
    ///调完doBackPlatformInfo之后回写不到definition中所以放doBackPlatformInfo后边，只是为了在回写的definition中有值不需要调平台
    if (!enquiry.order.platformInfo?.definition) {
        enquiry.order.platformInfo?.definition = [:]
    }
    enquiry.order.platformInfo?.definition << [
            csxszxScore                       : tianan_totalTranche,
            tianAn_TMIndex                    : tianAn_TMIndex,
            tianan_threeTranche               : tianan_threeTranche,
            tianan_totalTranche               : tianan_totalTranche,
            tianan_vehicleDamageTranche       : tianan_vehicleDamageTranche,
            csxForecastScore                  : tianan_csxForecastScore,
            szxForecastScore                  : tianan_szxForecastScore,
            csxszxForecastScore               : tianan_csxszxForecastScore,
            jqxForecastScore                  : tianan_jqxForecastScore,
            jqxszxForecastScore               : tianan_jqxszxForecastScore,
            tianan_csxForecastScore           : tianan_csxForecastScore,
            tianan_szxForecastScore           : tianan_szxForecastScore,
            tianan_csxszxForecastScore        : tianan_csxszxForecastScore,
            tianan_jqxForecastScore           : tianan_jqxForecastScore,
            tianan_jqxszxForecastScore        : tianan_jqxszxForecastScore,
            tianan_selfVehcType               : data?.vehicledto?.selfVehcType,      //车系分类
            tianan_carloadTranche             : data?.vehicleChdDTO?.carloadTranche,  //货车整车评级
            tianan_InsureType                 : loyalty,
            "application.compulsoryClaimTimes": bwCompulsoryClaimTimes,    //交强险理赔次数
            (PlatformKey.application_loyalty) : loyalty,
            selfRate                          : selfRate,
            noClaimDiscountCoefficient        : noClaimDiscountCoefficient,
            "application.renewalLastYearClaimTimes"  : renewalLastYearClaimTimes, // 续保上年出险次数
            "application.renewalLastYearClaimAmount" : renewalLastYearClaimAmount, // 续保上年出险金额
            "renewalLastYearClaimTimes": renewalLastYearClaimTimes,
            "renewalLastYearClaimAmount": renewalLastYearClaimAmount,
            "syClaimTimes": commercialClaimTimes,
            "application.trafficViolationRiskLevel":trafficViolationRiskLevel
    ]
}

//人车联动销售信息
def getrcldScxlSaleInfoDTO(saleOrgInfo, autoTask) {
    def enquiry = autoTask.getTaskEntity()
    def config = enquiry?.config?.configMap
    def productCode = enquiry.misc?.nonMotor?.productCode?.toString()
    def util = new robot_2045_util()
    def agencyno = saleOrgInfo?.agencyno ?: config.agentCode
    def intermediarycode = config?.agencyName + "_" + agencyno
    def getrcldScxlSaleInfo = []
    def rcldscxlflag = productCode.split("-")[0] == "JYXCOMBO" ? "3" : "1"

    def queryAgencyUrl = "https://netauto.95505.cn/enetauto/calculate/queryAgencyMsg.htmls"
    def queryAgencyParam = ["jsonKey": base64(JSON.toJSONString([
            agencyno        : config?.fcagentCode ?: config.agentCode,
            rcldScxlSaleFlag: 1
    ]))]
    def queryAgencyResult = httpPost(autoTask, queryAgencyUrl, JSONObject.toJSONString(queryAgencyParam))
    def saleOrg = queryAgencyResult.data?.saleOrg

    getrcldScxlSaleInfo << [
            businessmode     : saleOrg?.agencytype ?: saleOrgInfo?.agencytype ?: "2",
            salesmanname     : saleOrg?.belsalername ?: config.salesmanName ?: saleOrgInfo?.belsalername,
            companycode      : saleOrg?.belorgname ?: saleOrgInfo?.belorgname ?: saleOrgInfo?.orgname,
            businesssource   : saleOrg?.agencyclass ?: saleOrgInfo?.agencyclass ?: util.businessNatureCode(config.businessSourceName),
            solutioncode     : saleOrg?.teamno ?: saleOrgInfo?.teamno ?: saleOrgInfo?.teamNo,
            channeldetailcode: saleOrg?.belchannel ?: saleOrgInfo?.belchannel ?: "01",
            agreementno      : saleOrg?.agreementno ?: saleOrgInfo?.agreementno ?: config.agentNo,
            intermediarycode : intermediarycode,
            intersalesmancode: "",
            intersalesmanname: "",
            checked          : "0",
            beldepartno      : saleOrg?.beldepartno ?: saleOrgInfo?.beldepartno,
            agencyno         : saleOrg?.agencyno ?: "",
            agencyName       : saleOrg?.agencyname ?: "",
            salesmancode     : saleOrg?.belsalerno ?: config.salesmanCode ?: saleOrgInfo?.belsalerno,
            rcldscxlflag     : rcldscxlflag
    ]
    getrcldScxlSaleInfo
}

//人车联动及驾意险
def getpersonAuto(autoTask, enquiry) {
    def util = new robot_2045_util()
    def carInfo = JSON.parseObject(autoTask.applyJson)?.carInfo
    def personAuto = []
    def productCode = enquiry.misc?.nonMotor?.productCode?.toString()
    if (productCode.split("-")[0] == "JYXCOMBO") {
        def fuelType = util.fuelType(autoTask.getTempValues()?.vehicledto?.powertype)
        def riskcode = ["D6", "D8", "D14", "D12"].contains(fuelType) ? "0896" : "0895"
        personAuto << [
                kindDTOS     : util.getNonPlan(productCode.split("-")[1], riskcode),
                rcldRiskCode : riskcode,
                rcldflag     : "3",
                rcldplancount: carInfo?.seatCnt ?: autoTask.getTempValues()?.vehicledto?.seatCnt
        ]
    } else {
        CloseableHttpClient httpClient = autoTask.httpClient as CloseableHttpClient
        RequestConfig requestConfig = TaskUtil.buildReqConfig(autoTask)
        def getrcldScxlUrl = "https://netauto.95505.cn/enetauto/calculate/rcldfaxi.htmls"
        def getrcldScxlParam = JSONObject.toJSONString([
                channelcode  : "01",
                companycode  : autoTask.tempValues.userorg,
                riskcode     : enquiry.order.suiteInfo.bizSuiteInfo ? "" : "0881",
                isCarFlag    : "1",
                rcldplancount: "1",
                seatcount    : carInfo?.seatCnt,
                carusetype   : util.carUseType(carInfo?.useProps),
                carkindcode  : util.getMotorType(carInfo.jgVehicleType),
                usenaturecode: util.parseUseProps(carInfo?.useProps),
                fueltype    : util.fuelType(carInfo?.fuelType),
                isVehicleDamageInsured : enquiry.order.suiteInfo?.bizSuiteInfo?.suites?.find {key, suite -> key == 'VehicleDamage'} ? true : false,
                powertypecode : autoTask?.tempValues?.vehicledto?.powertypecode ?: '',
                tonCount : carInfo?.modelLoad
        ])
        def result = parse(doPostWithRetry(5, httpClient, true, getrcldScxlUrl, getrcldScxlParam, null, autoTask?.reqHeaders, "utf-8", requestConfig, ""))
        if ("0000" != result.code) throw new InsReturnException(InsReturnException.Others, "adfafa" + result.message as String)
        def rcldplan = productCode?.split("&")[0]
        def rcld = result.data?.rcldGyxiVOS?.find {
            it.rcldplan == rcldplan
        }
        if (Objects.isNull(rcld)) throw new InsReturnException(Others, "未查出该非车险信息")
        personAuto << [
                rcldflag       : "1",
                rcldplan       : rcldplan?.split("&")[0],
                rcldname       : rcld?.rcldname,
                rcldplancount  : enquiry.misc?.nonMotor?.count, //方案份数
                rcldRiskCode   : rcldplan?.split("&")[0],
                sumamount      : rcld?.sumamount,
                premium        : rcld?.sumpremium,
                countType      : rcld?.countType,
                prepaidLossRate: rcld?.prepaidLossRate
        ]
    }
}

//电子保单下载获取流
def getImg(autoTask, proposalno, getElementUrl, isFlag, requestConfig, bigProposalNo) {
    def common_all = new common_all()
    def getImgParam = [
            "proposalno"   : proposalno,
            "isFlag"       : isFlag,
            "bigProposalNo": bigProposalNo
    ]

    if (isFlag == 0) {
        getImgParam << [printType: "AE"]
    }
    def img = HttpSender.doGet(autoTask?.httpClient as CloseableHttpClient, getElementUrl, null, getImgParam, "UTF-8", requestConfig, true)
    if (img?.contains("9999")) throw new InsReturnException("电子保单获取失败")
    //下载电子交强险标志
//    if ("0881" == proposalno.substring(7, 11)) {
//        def markUrl = "https://netauto.95505.cn/enetauto/policyPrint/getElePolicyMark.htmls"
//        def markParam = ["proposalno": proposalno]
//        def markImg = HttpSender.doGet(autoTask?.httpClient as CloseableHttpClient, markUrl, null, markParam, "UTF-8", requestConfig, true)
//        if (markImg?.contains("9999")) throw new InsReturnException("电子保单获取失败")
//        try {
//            img = common_all.mergePDF(img, markImg)
//        } catch (Exception e) {
//            throw new InsReturnException(Others, "交强险电子保单和标志合并异常")
//        }
//    }
    //数字资产占位
    def data = common_all.makeSDASDataRobot(autoTask, proposalno)
    def fileName = "river_attachment/2045/${System.currentTimeMillis()}.pdf"
    def assetResult = SDASUtils.assetOccupancy("application/pdf", fileName, data)
    if (!assetResult || assetResult.code != 0) {
        throw new InsReturnException(Others, "2107PDF占位异常")
    }
    //上传
    def uploadResult = SDASUtils.uploadPutAsset(fileName.toString(), assetResult, new ByteArrayEntity(img))
    //回写ID flag为0是车险保单，为1是非车险
    if (isFlag == 0) {
        if (proposalno.contains("0881")) {
            autoTask?.taskEntity?.misc?.put("efcId", uploadResult.id)
        } else {
            autoTask?.taskEntity?.misc?.put("bizId", uploadResult.id)
        }
    } else {
        autoTask?.taskEntity?.misc?.put("nonMotorId", uploadResult.id)
    }
}

//是否新能源
def getIsNewEnergy(getCarInfo) {
    def newEnergyList = ["电动", "燃料电池", "插电式混合动力", "太阳能", "D6", "D8", "D12", "D14"]
    newEnergyList.contains(getCarInfo?.powertype)
}

//能源类型
def getFuleType(param) {
    switch (param) {
        case "汽油": ["D1", "汽油"]; break
        case "混合油": ["D13", "混合油"]; break
        case "柴油": ["D2", "柴油"]; break
        case "电动": ["D6", "电动"]; break
        case "燃料电池": ["D8", "燃料电池"]; break
        case "插电式混合动力": ["D12", "插电式混合动力"]; break
//        case"":["D3", "天然气(NG/CNG/LNG)"];break
//        case"":["D4", "液化石油气(LPG)"];break
        case "太阳能": ["D14", "太阳能"]; break
//        case"":["D7", "两用燃料"];break
        case "混合动力": ["D5", "混合动力"]; break
//        case"":["D9", "甲醇"];break
//        case"":["D11", "其它"];break
//        case"":["无", "汽油"];break
//        case"":["D15", "无"];break
        default: ["D11", "其它"]
    }
}

//报价时传preventFlag字段为rsa加密字符串
def encrypt(str, publicKey) throws Exception {
    //base64编码的公钥
    byte[] decoded = Base64.decodeBase64(publicKey)
    RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded))
    //RSA加密
    Cipher cipher = Cipher.getInstance("RSA")
    cipher.init(Cipher.ENCRYPT_MODE, pubKey)
    Base64.encodeBase64String(cipher.doFinal(str.getBytes("UTF-8")))
}

//如果flag为true取编码，否则取name
static def getAddress(insuredAddress) {
    def productFillinResult = [:]
    if (StringUtils.isNotEmpty(insuredAddress)) {
        if (!insuredAddress.contains("#")) {
            throw new InsReturnException("请检查关系人地址是否配置正确！")
        }
        def addresses = insuredAddress?.replaceAll("\\|", ":")?.replaceAll("#", ":")?.split(":")
        productFillinResult << [
                provinceCode: addresses[1],
                provinceName: addresses[0],
                cityCode    : addresses[3],
                cityName    : addresses[2],
                districtCode: addresses[5],
                districtName: addresses[4],
                situation   : addresses[6]
        ]
    }
    productFillinResult
}

static def getplan(amount1, amount2, amount3, amount4, riskcode) {
    def plan = { amount, kindCode, kindName ->
        def kindDTO = [
                "amount"  : amount,
                "kindcode": kindCode,
                "kindname": kindName,
                "riskcode": riskcode
        ]
        if (amount == "-1") {
            kindDTO << [
                    amount           : "0",
                    shareMIAmountFlag: "1"
            ]
        }
        kindDTO
    }
    def planAmount = []
    planAmount += plan(amount1, riskcode + "001", "驾乘人员意外伤害保险")
    planAmount += plan(amount2, riskcode + "002", "驾乘人员意外伤害医疗保险")
    if (amount3 != "0") {
        planAmount += plan(amount3, riskcode + "003", "附加住院津贴保险")
    }
    if (amount4 != "0") {
        planAmount += plan(amount4, riskcode + "004", "附加医保外医疗费用补偿")
    }
    planAmount
}

def httpPost(autoTask, String url, String param) {
    def log = LoggerFactory.getLogger("天安精灵post请求")
    def httpClient = autoTask.getHttpClient()
    log.info("post请求地址:{}请求参数:{}",url,param)
    def requestConfig = TaskUtil.buildReqConfig(autoTask)
    def result = parse(doPostWithRetry(5, httpClient as CloseableHttpClient, true, url, param, null, autoTask?.reqHeaders, "utf-8", requestConfig, ""))
    if (result?.code != "0000") {
        def message = result?.message as String
        throw new InsReturnException(Others, "请求失败，失败原因:" + message)
    }
    result
}

def getRemoteSale(autoTask, config, isMotorcycle) {
    def util = new robot_2045_util()
    def requestConfig = TaskUtil.buildReqConfig(autoTask)
    // 获取销售人员信息
    def remoteSaleUrl = 'https://netauto.95505.cn/enetauto/calculate/getRemoteSale.htmls'
    def remoteSaleResult = parse(doPostWithRetry(5, autoTask.getHttpClient() as CloseableHttpClient, true, remoteSaleUrl, null, null, autoTask?.reqHeaders, "utf-8", requestConfig, ""))
    def saleOrgInfo
    if (remoteSaleResult.code == "9999") {
        def queryAgencyUrl = "https://netauto.95505.cn/enetauto/calculate/queryAgencyMsg.htmls"
        def queryAgencyParam = ["jsonKey": base64(JSON.toJSONString([
                agencyno: config.agentCode
        ]))]
        def queryAgencyResult = httpPost(autoTask, queryAgencyUrl, JSONObject.toJSONString(queryAgencyParam))
        saleOrgInfo = queryAgencyResult.data?.saleOrg
    } else {
        saleOrgInfo = remoteSaleResult?.data?.saleOrgInfo
    }
    autoTask?.tempValues?.saleOrgInfo = saleOrgInfo
    //获取网点承保方案
    def final nbzplanUrl = 'https://netauto.95505.cn/enetauto/calculate/SaleNetMsgCb.htmls'
    def nbzplanParam = ["jsonKey": base64(JSON.toJSONString([
            servicecode: config.netServiceCode ?: (autoTask.tempValues.servicecode ?: "")
    ]))]
    def nbzplanResult = httpPost(autoTask, nbzplanUrl, JSONObject.toJSONString(nbzplanParam))
    def nbzplanDatas = nbzplanResult?.data
    def nbzplanData = nbzplanDatas.find { it.servicecode == config.netServiceCode || it.servicecode == autoTask.tempValues.servicecode}
    [
            producerCode         : "",
            salesmanname         : "",
            salesmanidno         : "",
            permitNo             : saleOrgInfo?.permitcode,
            companyno            : saleOrgInfo?.companyno,
            businessnature       : saleOrgInfo?.agencyclass ?: util.businessNatureCode(config.businessSourceName),
            businessNatureName   : util.businessNatureName(saleOrgInfo?.agencyclass) ?: config.businessSourceName,
            agreementno          : saleOrgInfo?.agreementno ?: config.agentNo,
            belchannel           : saleOrgInfo?.belchannel,
            beldepartname        : saleOrgInfo?.beldepartname,
            beldepartno          : saleOrgInfo?.beldepartno,
            teamNo               : saleOrgInfo?.teamno,
            teamName             : saleOrgInfo?.teamname,
            orgname              : saleOrgInfo?.belorgname,
            orgno                : saleOrgInfo?.belorgno ?: saleOrgInfo?.beldepartno,
            businessmod          : saleOrgInfo?.agencytype ?: "2",
            agencyName           : config?.agencyName,        //中介名称
            agencyno             : saleOrgInfo?.agencyno ?: config.agentCode,
            servicecode          : config.netServiceCode ?: (autoTask.tempValues.servicecode ?: ""),
            servicename          : config.netServiceName ?: "",
            belSalerName         : config.salesmanName ?: saleOrgInfo?.belsalername,
            belSalerNo           : config.salesmanCode ?: saleOrgInfo?.belsalerno,
            isdoubleagent        : "1",
            remoteName           : saleOrgInfo?.remotename,
            remoteCode           : saleOrgInfo?.remotecode,
            sendRepairCode       : config.GuMainSendRepairCode ?: "000000000000000000",
            isDirectSale         : "1",
            salesCommissionerCode: isMotorcycle == 1 ? "4" : "", //如果为摩托车时交强险为定额
            nbzplancode          : nbzplanData?.nbzplancode ?: "", //网点承保方案
            nbzplanname          : nbzplanData?.nbzplanname ?: "" //网点承保方案
    ]
}

def carTaxPlat(insurePersonList, carTaxPlat, config, tempValues, enquiry) {
    def util = new robot_2045_util()
    //如果车主地址包含乡，村，里，屯，组，队时计税分类为免税，如果为免税需填写机关代码，名称和完税凭证号
    def addr = tempValues.homoeaddr
    def payTaxWay = "N"
    def taxDepartment = ""
    def taxDepartmentDode = ""
    def isMotorcycle = tempValues?.vehicledto?.brandname?.contains("摩托")
    if (!Objects.isNull(addr) && isMotorcycle) {
        Pattern pattern = Pattern.compile("([乡村屯里队组])")
        def matcher = pattern.matcher(addr as CharSequence)
        if (matcher.find()) {
            payTaxWay = "E"
            taxDepartment = enquiry.misc?.supplyParam?.taxProofIssuingAuthorityName ?: config?.GuCarShipTaxTaxCompancyName
            taxDepartmentDode = enquiry.misc?.supplyParam?.taxProofIssuingAuthority ?: config?.GuCarShipTaxTaxCompancyCode
        }
    }
    [
            payTaxWay        : payTaxWay, //E为免税，N为正常纳税
            taxRegistryNumber: config?.taxRegistryNumber ?: "",  //税务登记号
            calculatemode    : "1",
            insuredName      : insurePersonList ? insurePersonList?.get(2)?.name : tempValues?.insuredName,
            taxpayercerticode: insurePersonList ? insurePersonList?.get(2)?.identifyno : tempValues?.identifyno, //税务证明码
            taxPayerNo       : insurePersonList ? insurePersonList?.get(2)?.identifyno : tempValues?.identifyno,  //身份证号
            phone            : insurePersonList ? insurePersonList?.get(2)?.mobile : tempValues?.mobile,
            payno            : isMotorcycle ? enquiry.misc?.supplyParam?.taxProofNo ?: config.payno : "",   //产品说摩托车需要单独完税凭证号
            taxpayercertitype: insurePersonList ? insurePersonList?.get(0)?.identifytype : tempValues?.identifytype,
            fuelType         : util.fuelType(tempValues?.vehicledto?.powertype),
            taxDepartment    : taxDepartment, //机关名称从（免税时填）
            taxDepartmentDode: taxDepartmentDode,   //机关代码（免税时填）
            taxStandard      : carTaxPlat?.taxStandard
    ]
}

static def setCookie(AutoTask autoTask) {
    def httpClient = autoTask.httpClient as CloseableHttpClient
    def cookieStore = httpClient.class.getDeclaredField("cookieStore")
    cookieStore.setAccessible(true)
    def cookies = (cookieStore.get(httpClient) as BasicCookieStore).getCookies()
    def cookieList = cookies?.collect{ cookie ->
        Date expiryDate = cookie.getExpiryDate()
//        String expiryStr = expiryDate ? new SimpleDateFormat("EEE, dd-MMM-yyyy HH:mm:ss z").format(expiryDate) : ''
        [name: cookie.getName(), value: cookie.getValue(), path: cookie.getPath(), domain: cookie.getDomain(), expiry: '']
    }
    if (cookieList?.size() > 0) {
        RedisUtil.set(cookieParamKey(autoTask), JSON.toJSONString(cookieList), 3600 * 8)
        def cookieStr = ''
        cookieList.eachWithIndex{ it, int i ->
            cookieStr += (it.name + '=' + it.value + (i == cookieList.size() - 1 ? '' : '; '))
        }
        cookieStr && RedisUtil.set(cookieKey(autoTask), cookieStr, 3600 * 8)
    }
}

static def cookieKey(AutoTask autoTask) {
    def configs = autoTask.configs
    def j_username = configs['login'] ?: configs['username']
    Constants.COOKIE_STRING_CACHE_KEY_PREFIX + InsCompanyEnum.TAIC.code.toString() + ':' + j_username
}

static def cookieParamKey(AutoTask autoTask) {
    cookieKey(autoTask) + ':param'
}


static def fillCookie(AutoTask autoTask) {
    if (autoTask?.tempValues?.hasLogin) {
        return
    }
    def log = LoggerFactory.getLogger("天安精灵post请求")
    def redisCookie = RedisUtil.get(cookieParamKey(autoTask))
    log.info('天安精灵-fillCookieParam:{}',redisCookie)
    def loginCookie = RedisUtil.get(cookieKey(autoTask))
    log.info('天安精灵-loginCookie:{}',loginCookie)
    if (!redisCookie) {
        if (loginCookie) {
            RedisUtil.get(cookieKey(autoTask))
            throw new InsReturnException(Others, "获取token失败，重试进行登录!")
        }
        return
    }
    def httpClient = autoTask.getHttpClient()
    def cookieStored = httpClient.class.getDeclaredField("cookieStore")
    cookieStored.setAccessible(true)
    def cookieList = JSON.parseArray(redisCookie)
    cookieList.each {
        BasicClientCookie cookie = new BasicClientCookie(it.name, it.value)
        cookie.setDomain(it.domain)
        cookie.setPath(it.path)
//        Date expiryDate = it.expiry ? new SimpleDateFormat("EEE, dd-MMM-yyyy HH:mm:ss z").parse(it.expiry) : null
//        cookie.setExpiryDate(expiryDate)
        ((BasicCookieStore) cookieStored.get(httpClient)).addCookie(cookie)
    }
}


def privilegeRequest(AutoTask autoTask, boolean isFromLoginPage = false) {

    //在login里面，使用验证码登录时，是否已经请求过该方法
    if (autoTask?.tempValues?.hasLogin) {
        return
    }
    def privilegeUrl = "https://netauto.95505.cn/enetauto/login/role/privilege.htmls"
    autoTask?.reqHeaders?.'Content-Type' = "application/json;charset=UTF-8"
    def privilegeParam = JSONObject.toJSONString(["username": autoTask.configs?.login])
    def privilegeResult = httpPost(autoTask, privilegeUrl, privilegeParam)
    if("0000" == privilegeResult.code){
        autoTask.tempValues.userorg = privilegeResult.data.user.userorg
        autoTask.tempValues.publicKey = privilegeResult.data.publicKey

        //#14526 【百川】申能精灵增加验证码登录方式，判断是否进行手机验证码校验
        def loginMobile = privilegeResult?.data?.user?.mobile ?: ""
        def servicecode = privilegeResult?.data?.user?.servicecode ?: ""
        //登录页面的话，如果需要再进行手机号+验证码的校验
        if (loginMobile && servicecode && autoTask.configs?.LoginVerificationCode && isFromLoginPage) {
            //手机验证码时，多出来的字段 mobile、certno、servicecode
            //报价和提交核保用到字段 saledto.servicecode报价
            autoTask.tempValues.servicecode = servicecode
            //验证码校验
            def loginCheckMessageUrl = "https://netauto.95505.cn/enetauto/login/loginCheckMessage.htmls"
            autoTask?.reqHeaders?.'Content-Type' = "application/json;charset=UTF-8"
            def checkMessageParam = ["telephone": loginMobile, "verificCode": autoTask.configs?.LoginVerificationCode ?: '', "fals": 1]
            def cmp = ["jsonKey": base64(JSON.toJSONString(checkMessageParam))]
            def checkMessageResult = httpPost(autoTask, loginCheckMessageUrl, JSON.toJSONString(cmp))
            if ("0000" == checkMessageResult?.code) {
                //验证成功，异常在common.httpPost里面处理了
            }
        }
        autoTask.tempValues.hasLogin = isFromLoginPage
    }
}