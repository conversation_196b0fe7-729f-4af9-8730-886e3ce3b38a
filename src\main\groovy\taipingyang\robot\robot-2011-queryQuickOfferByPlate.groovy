package taipingyang.robot

import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotDict_2011

/**
 * 查询车辆续保信息
 */
@BaseScript BaseScript_Http_Enq _

head Robot2011Util.getDefaultHead(tempValues, config)

body Robot2011Util.serializer(tempValues), {
    RobotDict_2011.makeBaseReqBody {
        [
                'ecarvo'     : [
                        'carVIN'     : '',
                        'plateColor' : '1',
                        'plateNo'    : order?.carInfo?.plateNum,
                        'quotationNo': '',
                ],
                'productFlag': RobotDict_2011.getProductFlag(autoTask)
        ]
    }
}