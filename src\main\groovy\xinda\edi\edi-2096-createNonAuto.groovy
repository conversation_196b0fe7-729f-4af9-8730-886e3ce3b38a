package xinda.edi

import com.cheche365.bc.exception.TempSkipException
import xinda.edi.config.GuoRenMap
import xinda.edi.util.GuoRenUtil
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

//如果不是非车，跳过此模版
if (!enquiry?.SQ?.nonMotor) {
    throw new TempSkipException(1, '未投非车险,跳过')
}
def supplyParamMap = GuoRenUtil.getSupplyParamMap(enquiry['supplyParam'])
//地址
def supplyAddress = supplyParamMap?.insuredAddress ?: supplyParamMap?.applicantAddress ?: supplyParamMap?.ownerAddress
def nonAutoAddress = GuoRenUtil.getNonAutoAddress(supplyAddress, enquiry['configInfo']['configMap'])
//bug 36617 【生产环境】国任EDI创建非车订单保期错误
def start = enquiry.baseSuiteInfo?.efcSuiteInfo?.start ?: enquiry.baseSuiteInfo?.bizSuiteInfo?.start
def dateTimeFormatter = DateTimeFormatter.ofPattern('yyyy-MM-dd')
def flag = LocalDate.now().isEqual(LocalDate.parse(start.substring(0, 10) as String, dateTimeFormatter))
if (flag) {
    start = LocalDate.now().plusDays(1).format(dateTimeFormatter) + ' 00:00:00'
}
if ('00:00:00' != start.substring(11)) {
    start = LocalDate.parse(start.substring(0, 10) as String, dateTimeFormatter).plusDays(1).format(dateTimeFormatter) + ' 00:00:00'
}
def end = LocalDateTime.parse(start as String, 'yyyy-MM-dd HH:mm:ss').plusYears(1).plusSeconds(-1).format('yyyy-MM-dd HH:mm:ss')
def baseInfo = tempValues['baseInfo']['prptmain']
def body = [
        'channelCode'  : enquiry['configInfo']['configMap']?.activityPlan,
        'data'         : [
                'orderList': [
                        [
                                'insuredList' : [
                                        [
                                                'certfNo'     : enquiry['insuredPersonList'].get(0)['idCard'],
                                                'certfType'   : GuoRenMap.getIdcardType(enquiry['insuredPersonList'].get(0)['idCardType'] as String),
                                                'countryName' : '中国',
                                                'insuredName' : enquiry['insuredPersonList'].get(0)['name'],
                                                'insuredType' : GuoRenMap.personnelType(enquiry['insuredPersonList'].get(0)['idCardType'] as String),
                                                'phoneNo'     : supplyParamMap['insuredMobile'] ?: enquiry['insuredPersonList'].get(0)['mobile'] ?: '13317781778',
                                                'planNo'      : '1',
                                                'addrProvince': nonAutoAddress?.addrProvince,
                                                'addrCity'    : nonAutoAddress?.addrCity,
                                                'addrCounty'  : nonAutoAddress?.addrCounty,
                                                'addrStreet'  : nonAutoAddress?.addrStreet,
                                                'fullAddr'    : nonAutoAddress?.fullAddr
                                        ]
                                ],
                                'orderAppl'   : [
                                        'applName'   : enquiry['insurePerson']['name'],
                                        'applType'   : GuoRenMap.personnelType(enquiry['insurePerson']['idCardType'] as String),
                                        'certfNo'    : enquiry['insurePerson']['idCard'],
                                        'certfType'  : GuoRenMap.getIdcardType(enquiry['insurePerson']['idCardType'] as String),
                                        'contactName': '',
                                        'phoneNo'    : supplyParamMap['applicantMobile'] ?: enquiry['insurePerson']['mobile'] ?: '13317781778'
                                ],
                                'objCarList'  : [
                                        [
                                                'carModels'    : enquiry?.carInfo?.carModelName,
                                                'carType'      : GuoRenMap.getCarType(enquiry.carInfo.jgVehicleType as String),// 车辆类型
                                                'carTypeCode'  : GuoRenMap.getUseProps(enquiry.carInfo.useProps as String, enquiry?.carOwnerInfo?.idCardType, enquiry.carInfo.carUserType as String),//使用性质代码
                                                'clnt'         : enquiry['carInfo']['isNew'] ? '1' : '2',
                                                'frameNo'      : enquiry?.carInfo?.vin,//同车架号,
                                                'licenseNo'    : enquiry.carInfo.plateNum == '新车未上牌' ? '新车' : enquiry.carInfo?.plateNum,
                                                'planNo'       : '1',
                                                'purchasePrice': tempValues.chooseCar?.purchasePrice,
                                                'seatNum'      : tempValues.chooseCar?.seatCount ?: enquiry?.carInfo?.seatCnt,//核定载客数,
                                                'toncount'     : tempValues.chooseCar?.tonCount ?: enquiry?.carInfo?.modelLoad,//总质量
                                                'carCategory'  : GuoRenMap.carCategory(enquiry?.carInfo?.syvehicletypecode)
                                        ]
                                ],
                                'orderMain'   : [
                                        'amount'    : tempValues.prodCodeInfo.amount * tempValues.nonMotorCount,//保额
                                        'othOrderNo': tempValues['carPrice']['inputvo'],//
                                        'renewalInd': 0,//新/续保标志 0：新保；1：续保
                                        'operatorCode': tempValues['operatorCode'],//出单员
                                        'premium'   : tempValues.nonMotorPremium,
                                        'uwCount'   : enquiry?.SQ?.nonMotor.count,//方案份数
                                        'prodCode'  : tempValues.prodCode,//产品代码
                                        'riskCode'  : tempValues.prodCodeInfo.riskCodeList,//险种代码
                                        'startDate' : start,
                                        'endDate'   : end,
                                        'goodsCode' : tempValues.goodsCode,//商品号
                                        'businessMode' : '6',
                                        'businessSource': enquiry['configInfo']['configMap']?.businessNature//见业务来源代码
                                ],
                                'salesmanList': [
                                        [
                                                'agentCode'   : baseInfo?.agentCode,
                                                'companyCode' : baseInfo?.comCode,
                                                'salesmanCode': baseInfo?.handlerCode,
                                                'salesmanFlag': 1,
                                                'salesmanName': baseInfo?.handler1Name,
                                                'serialNo'    : 1,
                                                //'teamCode'    : baseInfo?.channeltypeone,
                                                //'teamName'    : baseInfo?.channeltypeoneName,
                                                'agentCnrtNo' : tempValues['baseInfo']['prptmain']?.agreementNo
                                        ]
                                ],
                                'planList'    : [
                                        [
                                                'occupationCode' : '0500020',//职业代码
                                                'occupationGrade': '1',//occupationGrade
                                                'occupationName' : '其他',//occupationName
                                                'planNo'         : '1',//planNo
                                                'quantity'       : '1',//人数
                                                'socialSecInd'   : '01'//有无社保
                                        ]
                                ]
                        ]
                ]
        ],
        'othBusinessNo': tempValues['carPrice']['inputvo'],
        'requestTime'  : LocalDate.now(),
]
GuoRenUtil.request(body, 'IIIRBT00054', enquiry?.configInfo?.configMap as Map, tempValues as Map)