package guoshou.edi


import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import common.common_all
import org.apache.commons.lang3.StringUtils

import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.regex.Matcher
import java.util.regex.Pattern

def common = new edi_2002_common_new()
def commonAll = new common_all()
def jsonParams

if(tempValues.containsKey("repeatFlag") || tempValues.containsKey("repeatForPlcRation")){
    jsonParams=repeatPostForService()
}else{
    jsonParams=premiumCalculateService(common,commonAll)
}

//2.7.保费计算服务
def premiumCalculateService(common,commonAll){
    def isEnergy = commonAll.checkEnergyType(enquiry?.carInfo?.carModelName, enquiry?.carInfo?.plateNum)
    JSONObject commonData=common.setCommonDataForPost(config,tempValues.businessNumber,common.getTransactionType("premiumCalculateService")[0])

    def carJson=tempValues.carMessResult

    JSONObject actual=new JSONObject()
    def bizSuiteInfo=enquiry?.baseSuiteInfo?.bizSuiteInfo
    def efcSuiteInfo=enquiry?.baseSuiteInfo?.efcSuiteInfo
    actual.relationFlag = common.getRelationFlag(bizSuiteInfo, efcSuiteInfo)
    actual.newCarFlag = enquiry?.carInfo?.plateNum == "新车未上牌" ? "1" : "0"

    // 推荐修理厂
    actual.put("recommendGarageFlag",config?.recommendGarageFlag)
    if(config?.recommendGarageFlag == "1") {
        actual.put("garageCode", config?.garageCode)
    }

    if ("1" == config?.isTPreminum?.toString()) {
        actual.uniformPrice = "1"
    }

    JSONObject plcCar05=new JSONObject()
    plcCar05.licensePlateNo = enquiry?.carInfo?.plateNum == "新车未上牌" ? "" : enquiry?.carInfo?.plateNum
    plcCar05.engineNumber = enquiry?.carInfo?.engineNum
    plcCar05.frameNo = enquiry?.carInfo?.vin
    plcCar05.actualValue = ("新车未上牌" != enquiry.carInfo.plateNum && enquiry?.carInfo?.definedCarPrice) ? enquiry?.carInfo?.definedCarPrice : tempValues?.carPriceData?.actualvalue

    def plateType = enquiry?.carInfo?.plateType
    def licenseType = isEnergy == "0" ? common.getPlateType(plateType)[0] : common.getNewEnergyPlateType(plateType)[0]
    plcCar05.licenseType = licenseType
    plcCar05.licenseColorCode = common.transPlateColor(enquiry)
    plcCar05.enrollDate = enquiry?.carInfo?.firstRegDate?.substring(0,10)

    def carUserNatureCode = common.getUseProps(enquiry?.carInfo?.useProps as Integer)
    plcCar05.carUserNatureCode = carUserNatureCode
    plcCar05.carKindCode =  common.getCarKindCode(enquiry?.carInfo?.useProps as Integer)
    plcCar05.vehiclestyle = tempValues?.carMessForBeiJing?.vehicleStyle ?: common.getVehicleStyle(enquiry?.carInfo?.jgVehicleType as Integer)[1]
    plcCar05.rbCode = carJson.getString("rbCode")
    plcCar05.vehicleId = enquiry?.carInfo?.jyCode
    plcCar05.purchasePrice = carJson.getBigDecimal("purchasePrice")
    plcCar05.carpriceType =  enquiry?.carInfo?.plateNum == "新车未上牌" ? 2 : 1
    if (enquiry?.carInfo?.plateNum == "新车未上牌") {
        plcCar05.put("fairMarketValue",  enquiry?.carInfo?.price)
    }

    JSONObject extraCarInfo=new JSONObject()
    extraCarInfo.put("seatCount",enquiry?.carInfo?.seatCnt)
    extraCarInfo.put("passengersNumber",enquiry?.carInfo?.seatCnt)
    extraCarInfo.put("vehicleTonnage",common.getCarFiledValue(tempValues,"vehicleTonnage","String",enquiry,"modelLoad"))
    extraCarInfo.put("vehicleQuality",common.getCarFiledValue(tempValues,"wholeWeight","BigDecimal", enquiry, "fullLoad"))
    def exhaustsCale = common.getDisplacement(isEnergy, enquiry, tempValues)
    extraCarInfo.put("exhaustsCale", exhaustsCale)

    def energyTypesCode = common.getEnergyTypesCode(isEnergy, enquiry)
    extraCarInfo.put("energyTypesCode",energyTypesCode)
    extraCarInfo.put("modelCode", tempValues.repeatCarModel ?: carJson.getString("platModelCode"))
    extraCarInfo.put("platModelName",StringUtils.isNotBlank(carJson.getString("platModelName"))?carJson.getString("platModelName"):carJson.getString("standardName"))
    if(enquiry?.carInfo?.isTransfer){
        extraCarInfo.put("chgownerFlag","1")
        def sdf = new SimpleDateFormat("yyyy-MM-dd")
        def transferDate=sdf.format(sdf.parse(enquiry?.carInfo?.transferDate.toString()))
        extraCarInfo.put("transferDate",transferDate)
        extraCarInfo.put("secondhandcarflag","1")
        extraCarInfo.put("buyDate",transferDate)
        extraCarInfo.put("certifyDate",transferDate)
    }else{
        extraCarInfo.put("certifyDate",enquiry?.carInfo?.firstRegDate.substring(0,10))
    }

    // 新车备案报价接口补充
    if (enquiry?.carInfo?.plateNum == "新车未上牌") {
        def carCertificateNo = enquiry?.supplyParam?.find { it ->
            it["itemcode"] == "carOriginProofNo"
        }
        def certificateDate = enquiry?.supplyParam?.find { it ->
            it["itemcode"] == "carOriginProofDate"
        }
        extraCarInfo.carCertificateType = "01"
        extraCarInfo.carSequenceNo = tempValues.carSequenceNo
        extraCarInfo.carCertificateNo = carCertificateNo ? carCertificateNo["itemvalue"] : config?.carCertificateNo ?: ""
        extraCarInfo?.certificateDate = certificateDate ? certificateDate["itemvalue"] : LocalDate.now().toString()

    }

    extraCarInfo.put("speciesOriginCode",carJson.getString("importFlag"))
    extraCarInfo.put("foreignCarFlag", commonAll.nonLocalFlag(enquiry) ? "0" : "1") // 是否外地车1是0否
    extraCarInfo.put("modelEnergyType", isEnergy as String)
    plcCar05.put("extraCarInfo",extraCarInfo)
    plcCar05.put("specialVehicleTypesCode",enquiry?.carInfo?.isTransfer?1:0)//特殊车辆种类code 0正常车辆 1过户车
    // 存在车型唯一ID，补充进车辆信息
    if (tempValues?.vehicleStyleUniqueId) {
        plcCar05?.vehicleStyleUniqueId = tempValues?.vehicleStyleUniqueId
    }

    if(carUserNatureCode.equals("9A")){
        plcCar05.put("leaseType","1")
    } else if (carUserNatureCode == "9C" && enquiry?.insArea?.province == "230000") {
        plcCar05.put("leaseType","04")
    }else if(carUserNatureCode.equals("8B") && licenseType.equals("16")){
        plcCar05.put("leaseType","8B1")
    }
    actual.put("plcCar05",plcCar05)

    //投保人、被保人、车主地址
    def applicantAddress = common.getSupply(enquiry, "applicantAddress") ?: ""
    def insuredPersonAddress = common.getSupply(enquiry, "insuredAddress") ?: ""
    def carOwnerAddress = common.getSupply(enquiry, "ownerAddress") ?: ""
    def plcCarowner=common.setCustomerMess(enquiry,"owner", common.setAddressForCode(enquiry, carOwnerAddress, tempValues))
    actual.put("plcCarowner",plcCarowner)
//    投保人
    def plcApplicant=common.setCustomerMess(enquiry,"applicant", common.setAddressForCode(enquiry, applicantAddress, tempValues))
    actual.put("plcApplicant",plcApplicant)
    //被投保人
    def plcPublicInsurant=common.setCustomerMess(enquiry,"insured", common.setAddressForCode(enquiry, insuredPersonAddress, tempValues))
    plcPublicInsurant.put("carInsureRelation",enquiry?.insuredPersonList[0]?.idCard.equals(enquiry?.carOwnerInfo?.idCard)?1:2)
    actual.put("plcPublicInsurant",plcPublicInsurant)

    JSONObject plcSales=new JSONObject()
    plcSales.put("businessOffice",config?.comCode)
    plcSales.put("salesmanCode",config?.salesmanCode)
    plcSales.put("businessNatureCode",config?.businessNatureCode)
    plcSales.put("saleaman",config?.saleaman)
    plcSales.saleAgreementNo = config?.saleAgreementNo
    actual.put("plcSales",plcSales)
    actual.put("proposalform","1")
    actual.agreementNo = config?.agreementNo ?: ""
    actual.useAgrRateFlag = config?.useAgrRateFlag ?: ""
    if (config?.agreementNo && config?.useAgrRateFlag) {
        actual.overviewSerialNo = isEnergy == "0" ? "2" : "1"
    }

    JSONObject PlcCarCharger=new JSONObject()
    PlcCarCharger.put("lossSumLimit",0)
    PlcCarCharger.put("liabilitySumLimit",0)

    def plcCarChargersFlag=false
    if(bizSuiteInfo!=null && bizSuiteInfo.getJSONArray("suites").size()>0){
        for(JSONObject bizSuite:bizSuiteInfo.getJSONArray("suites")){

            if(bizSuite.getString("code").equals("NEChargerDamage")){
                PlcCarCharger.put("lossSumLimit",handleBigdecimalTypeParam(bizSuite.getString("amount")))
                plcCarChargersFlag=true
            }else if(bizSuite.getString("code").equals("NEChargerDuty")){
                PlcCarCharger.put("liabilitySumLimit",handleBigdecimalTypeParam(bizSuite.getString("amount")))
                plcCarChargersFlag=true
            }
        }
    }
    if(plcCarChargersFlag){

        PlcCarCharger.put("serialNo",commonAll.getSupplyParam(enquiry, "neChargerNo") ?: config?.NEChargerNo)
        PlcCarCharger.put("chargerTypeName",commonAll.getSupplyParam(enquiry, "neChargerModel") ?: config?.NEChargerModel)
        PlcCarCharger.put("chargerUsageCode",commonAll.getSupplyParam(enquiry, "neChargerType") ?: config?.NEChargerType)
        PlcCarCharger.put("installedAddressCode",commonAll.getSupplyParam(enquiry, "neChargerLocationType") ?: config?.NEChargerLocationType)
        PlcCarCharger.put("positionAddress",commonAll.getSupplyParam(enquiry, "neChargerAddress") ?: config?.NEChargerAddress)

        JSONArray plcCarChargers=new JSONArray()
        plcCarChargers.add(PlcCarCharger)
        actual.put("plcCarChargers",plcCarChargers)
    }

    JSONArray soleActual=new JSONArray()
    if(efcSuiteInfo!=null){
        JSONObject efcSoleActual=new JSONObject()
        efcSoleActual.put("productCode",common.getkindOfPlcicy("efcSuiteInfo")[0])
        efcSoleActual.put("startTime",common.getTimeByFormat(efcSuiteInfo?.start))
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        def efcEndTime= LocalDateTime.parse(efcSuiteInfo?.start, dtf).plusYears(1).format(dtf)
        efcSoleActual.put("endTime",common.getTimeByFormat(efcEndTime))
        if (efcSoleActual.startTime == "2024-02-29T00:00:00.000+0800") {
            efcSoleActual.endTime = "2025-03-01T00:00:00.000+0800"
        }

        JSONArray plcSolutions=new JSONArray()
        JSONObject plcSolution=new JSONObject()
        JSONArray plcClauses=new JSONArray()
        JSONObject plcClause=new JSONObject()
        plcClause.put("clauseCode","BZ")
        plcClause.put("clauseName","机动车交通事故责任强制险")
        plcClause.put("startTime",common.getTimeByFormat(efcSuiteInfo?.start))
        plcClause.put("endTime",common.getTimeByFormat(efcEndTime))
        if (plcClause.startTime == "2024-02-29T00:00:00.000+0800") {
            plcClause.endTime = "2025-03-01T00:00:00.000+0800"
        }

        plcClause.put("isDeductibles",0)
        plcClauses.add(plcClause)
        plcSolution.put("plcClauses",plcClauses)
        plcSolutions.add(plcSolution)
        efcSoleActual.put("plcSolutions",plcSolutions)

        JSONObject plcCarShipTax=new JSONObject()
        plcCarShipTax.put("taxTypeCode","1N")
        plcCarShipTax.put("taxPayerName",enquiry?.insurePerson?.name)
        plcCarShipTax.put("taxPayerNumber",enquiry?.insurePerson?.idCard)
        plcCarShipTax.ktaxComcode = tempValues?.taxAuthorityCode ?: ""
        plcCarShipTax.ktaxComname = tempValues?.taxAuthority ?: ""
        if ("310000" == enquiry?.insArea?.province) {
            // 上海地区
            def idCardTypeList = common.getIDCardType(enquiry?.insurePerson?.idCardType) as List
            plcCarShipTax.identifyType = idCardTypeList[0]
            plcCarShipTax.taxRegistrationNo = ["07", "11"].contains(idCardTypeList[0]) ? enquiry?.insurePerson?.idCard : ""
        }

        efcSoleActual.put("plcCarShipTax",plcCarShipTax)

        efcSoleActual.put("electronPolicyFlag",config?.electronPolicyFlag)
        soleActual.add(efcSoleActual)
    }

    if(bizSuiteInfo!=null && bizSuiteInfo.getJSONArray("suites").size()>0){
        JSONObject bizSoleActual=new JSONObject()
        //商业险只能报新能源与非新能源其中一种
        bizSoleActual.put("productCode","1".equals(carJson.getString("isNewEnergy"))?
                common.getkindOfPlcicy("newEnergySuiteInfo")[0]:common.getkindOfPlcicy("bizSuiteInfo")[0])
        bizSoleActual.put("startTime",common.getTimeByFormat(bizSuiteInfo?.start))
        bizSoleActual.put("endTime",common.getTimeByFormat(bizSuiteInfo?.end))

        JSONArray plcSolutions=new JSONArray()
        JSONObject plcSolution=new JSONObject()
        JSONArray plcClauses=new JSONArray()
        def masterPolicyExist=["VehicleDamage":false,"ThirdParty":false,"Driver":false,"Passenger":false]
        for(JSONObject bizSuite:bizSuiteInfo.getJSONArray("suites")){
            if(masterPolicyExist.containsKey(bizSuite.getString("code"))){
                masterPolicyExist<<[(bizSuite.getString("code")):true]//主险存在
            }
        }
        for(JSONObject bizSuite:bizSuiteInfo.getJSONArray("suites")){
            if((bizSuite.code == "GoodsOnVehicle" && carUserNatureCode != "9D") || (bizSuite.code == "HolidayDouble" && carUserNatureCode != "8A")){
                throw new InsReturnException(bizSuite.name?.toString() + "该险种不符合投保条件")
            }

            def clauseCodeForPost = common.getProductIdByKey(bizSuite.code?.toString(), carJson.isNewEnergy)[0]
            if(!clauseCodeForPost){
                throw new InsReturnException("当前险种存在不支持的险别:${bizSuite.name}无法投保")
            }

            def amountForHoliadyDouble
            if (bizSuite.code == "HolidayDouble") {
                amountForHoliadyDouble = bizSuiteInfo.suites.find { it -> it.code == "ThirdParty"}.amount?.toString()
            }

            JSONObject plcClause=new JSONObject()
            plcClause.clauseCode = clauseCodeForPost
            plcClause.clauseName = common.getProductIdByKey(bizSuite.code, carJson.isNewEnergy)[2]
            plcClause.startTime = common.getTimeByFormat(bizSuiteInfo?.start)
            plcClause.endTime = common.getTimeByFormat(bizSuiteInfo?.end)

            def clauseForANC=["ANCVehicleDamage","ANCThirdParty","ANCDriver","ANCPassenger"]
            def clauseForCount=["RoadsideService","VehicleInspection","DesignatedDriving","SendForInspection"]
            def clauseForAmount=["ThirdParty","Driver","Wheel","Scratch","GoodsOnVehicle","CFMDThirdParty","CFMDDriver",
                                 "CFMDPassenger","HolidayDouble","","NIHCThirdParty","NIHCDriver","NEChargerDamage","NEChargerDuty"]
            def clauseForUnitPrice=["Passenger","NIHCPassenger"]

            if(clauseForUnitPrice.contains(bizSuite.getString("code"))){
                plcClause.put("everyoneAmount",handleBigdecimalTypeParam(bizSuite.getString("amount")))
                plcClause.seatNumber = enquiry?.carInfo?.seatCnt -1
                if (bizSuite.code == "NIHCPassenger") {
                    plcClause.put("oriCurAmount",bizSuite?.getBigDecimal("amount")?.toString())
                    if (bizSuite.share) {
                        plcClause.typeFlag = "1"
                        def passengerSuit = bizSuiteInfo.suites.find { it -> it.code == "Passenger"}
                        plcClause.everyoneAmount = passengerSuit?.amount
                        plcClause.oriCurAmount = passengerSuit?.amount
                    }
                }

                if ("SH8" == config?.policySort && "Passenger" == bizSuite.code) {
                    // 惠军项目乘客险传 oriCurAmount = 每座保额 * 座位数
                    plcClause.oriCurAmount = bizSuite?.getBigDecimal("amount") * (enquiry?.carInfo?.seatCnt - 1)
                }

            } else if(clauseForANC.contains(bizSuite.getString("code"))){
                plcClause.put("deductibleRate",new BigDecimal(100).multiply(bizSuite.getBigDecimal("amount")).toString())

            }else if(clauseForCount.contains(bizSuite.getString("code"))){
                plcClause.put("compensationDays",handleBigdecimalTypeParam(bizSuite.getBigDecimal("amount").toString()))

            }else if(clauseForAmount.contains(bizSuite.getString("code"))){
                def oriCurAmount=amountForHoliadyDouble==null?bizSuite.getBigDecimal("amount").toString():amountForHoliadyDouble
                plcClause.put("oriCurAmount",handleBigdecimalTypeParam(oriCurAmount))
                if (bizSuite.share) {
                    if (carJson.isNewEnergy == "1") {
                        throw new InsReturnException("新能源车型暂不支持保额共享。")
                    }

                    plcClause.typeFlag = "1"
                    plcClause.oriCurAmount = bizSuiteInfo.suites.find { it -> it.code == bizSuite.code?.replace("NIHC", "")}?.amount
                }

            }else if(bizSuite.getString("code").equals("VehicleDamage")){
                if ('新车未上牌' == enquiry?.carInfo?.plateNum) {
                    plcClause.oriCurAmount = enquiry?.carInfo?.price?.toString()
                } else {
                    plcClause.oriCurAmount = enquiry?.carInfo?.definedCarPrice ?: tempValues?.carPriceData?.actualvalue
                }

                plcClause.put("deductible",0)
            }
            plcClause.put("isDeductibles",0)

            if(bizSuite.getString("code").equals("VehicleInspection")){
                def securityItem=["01":"发动机检测(机油、空滤、燃油、冷却等)",
                                  "02":"变速器检测",
                                  "03":"转向系统检测(含车轮定位测试、轮胎动平衡测试)",
                                  "04":"底盘检测",
                                  "05":"轮胎检测",
                                  "06":"汽车玻璃检测",
                                  "07":"汽车电子系统检测(全车电控电器系统检测)",
                                  "08":"车内环境检测",
                                  "09":"蓄电池检测",
                                  "10":"车辆综合安全检测"]
                JSONArray plcSecurityItems=new JSONArray()
                securityItem.each{key,value ->
                    JSONObject plcSecurityItem=new JSONObject()
                    plcSecurityItem.put("securityCode",key)
                    plcSecurityItem.put("securityName",value)
                    plcSecurityItems.add(plcSecurityItem)
                }
                plcClause.put("plcSecurityItems",plcSecurityItems)
            }
            plcClauses.add(plcClause)
        }
        plcSolution.put("plcClauses",plcClauses)

        plcSolutions.add(plcSolution)
        bizSoleActual.put("plcSolutions",plcSolutions)
        bizSoleActual.put("electronPolicyFlag",config?.electronPolicyFlag)
        soleActual.add(bizSoleActual)
    }

    JSONObject businessData=new JSONObject()
    businessData.put("actual",actual)
    businessData.put("soleActual",soleActual)

    JSONObject jsonParams=new JSONObject()
    jsonParams.put("commonData",commonData)
    jsonParams.put("businessData",businessData)
    tempValues.repeatParams=jsonParams
    return jsonParams

}

def handleBigdecimalTypeParam(amout){

    if(amout && amout.indexOf(".")>0){
        Pattern p1=Pattern.compile("(^[^0][0-9]*|0)\\.0*\$")
        Matcher matcher=p1.matcher(amout)
        if(matcher.matches()){
            amout = amout.substring(0,amout.indexOf("."))
        }else {
            amout= new BigDecimal(amout).setScale(2,BigDecimal.ROUND_HALF_UP).toPlainString()
        }
    }
    return amout
}

def repeatPostForService(){
    return tempValues.repeatParams
}

if (tempValues?.plcCarShipTax) {
    jsonParams?.businessData?.soleActual?.find {it -> it.productCode == "0507"}?.plcCarShipTax = tempValues?.plcCarShipTax
}

common.setRequestSignature(reqHeaders,jsonParams.toString())

jsonParams.toString()
