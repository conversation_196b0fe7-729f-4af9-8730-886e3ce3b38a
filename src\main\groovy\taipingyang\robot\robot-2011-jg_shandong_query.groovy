package taipingyang.robot

import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotDict_2011

/**
 * https://issue.cpic.com.cn/ecar/ecar/querySDVehicleInfo
 */
@BaseScript BaseScript_Http_Enq _

skip '非鲁牌，跳过交管', config.querySDVehicleInfo != 'true' || !order.carInfo.plateNum.startsWith('鲁')


head Robot2011Util.getDefaultHead(tempValues, config)

def TbUtil = new common_2011()

body Robot2011Util.serializer(tempValues), {
    RobotDict_2011.makeBaseReqBody {
        [
                'plateNo'  : order.carInfo?.plateNum,
                'vin'      : order.carInfo?.vin,
                'plateType': TbUtil.reMap(autoTask, "LicenseType", entity.order.carInfo.plateType?.toString())
        ]
    }
}