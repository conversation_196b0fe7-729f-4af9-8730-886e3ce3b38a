package com.cheche365.bc.hbase.impl;

import com.cheche365.bc.entity.hbase.AutoTaskLog;
import com.cheche365.bc.hbase.AutoTaskLogRepository;
import com.cheche365.bc.hbase.BaseHBaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.stereotype.Repository;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AutoTaskLog HBase服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@Repository
public class AutoTaskLogRepositoryImpl extends BaseHBaseRepository<AutoTaskLog> implements AutoTaskLogRepository {

    @Override
    protected String getTableName() {
        return AutoTaskLog.TABLE_NAME;
    }

    @Override
    protected Class<AutoTaskLog> getEntityClass() {
        return AutoTaskLog.class;
    }

    @Override
    protected Map<String, String> getColumnFamilyMapping() {
        Map<String, String> mapping = new HashMap<>();
        // 所有字段都存储在data列族中
        mapping.put("autoTraceId", AutoTaskLog.CF_DATA);
        mapping.put("traceKey", AutoTaskLog.CF_DATA);
        mapping.put("applyJson", AutoTaskLog.CF_DATA);
        mapping.put("feedbackJson", AutoTaskLog.CF_DATA);
        mapping.put("resultStr", AutoTaskLog.CF_DATA);
        return mapping;
    }

    @Override
    protected String generateRowKey(AutoTaskLog entity) {
        return entity.generateRowKey();
    }

    @Override
    public AutoTaskLog findByAutoTraceId(String autoTraceId) throws IOException {
        if (autoTraceId == null || autoTraceId.isEmpty()) {
            throw new IllegalArgumentException("autoTraceId不能为空");
        }

        // 根据autoTraceId生成rowKey
        AutoTaskLog tempEntity = AutoTaskLog.builder().autoTraceId(autoTraceId).build();
        String rowKey = tempEntity.generateRowKey();

        return findByRowKey(rowKey);
    }

    @Override
    public boolean exists(String autoTraceId) throws IOException {
        if (autoTraceId == null || autoTraceId.isEmpty()) {
            return false;
        }

        AutoTaskLog tempEntity = AutoTaskLog.builder().autoTraceId(autoTraceId).build();
        String rowKey = tempEntity.generateRowKey();

        return super.exists(rowKey);
    }

    @Override
    public void delete(String autoTraceId) throws IOException {
        if (autoTraceId == null || autoTraceId.isEmpty()) {
            throw new IllegalArgumentException("autoTraceId不能为空");
        }

        AutoTaskLog tempEntity = AutoTaskLog.builder().autoTraceId(autoTraceId).build();
        String rowKey = tempEntity.generateRowKey();

        super.delete(rowKey);
    }

    @Override
    public void batchSave(List<AutoTaskLog> autoTaskLogs) throws IOException {
        if (autoTaskLogs == null || autoTaskLogs.isEmpty()) {
            return;
        }

        try (Table table = hbaseConnection.getTable(TableName.valueOf(getTableName()))) {
            List<Put> puts = new ArrayList<>();
            Map<String, String> columnFamilyMapping = getColumnFamilyMapping();

            for (AutoTaskLog autoTaskLog : autoTaskLogs) {
                if (autoTaskLog == null) {
                    continue;
                }

                String rowKey = generateRowKey(autoTaskLog);
                if (rowKey == null || rowKey.isEmpty()) {
                    log.warn("跳过RowKey为空的实体");
                    continue;
                }

                Put put = new Put(Bytes.toBytes(rowKey));

                // 添加非空字段
                addColumnIfNotNull(put, columnFamilyMapping, "autoTraceId", autoTaskLog.getAutoTraceId());
                addColumnIfNotNull(put, columnFamilyMapping, "traceKey", autoTaskLog.getTraceKey());
                addColumnIfNotNull(put, columnFamilyMapping, "applyJson", autoTaskLog.getApplyJson());
                addColumnIfNotNull(put, columnFamilyMapping, "feedbackJson", autoTaskLog.getFeedbackJson());
                addColumnIfNotNull(put, columnFamilyMapping, "resultStr", autoTaskLog.getResultStr());

                if (!put.isEmpty()) {
                    puts.add(put);
                }
            }

            if (!puts.isEmpty()) {
                table.put(puts);
                log.debug("批量保存{}条AutoTaskLog记录", puts.size());
            }
        }
    }

    @Override
    public List<AutoTaskLog> findByTraceKey(String traceKey) throws IOException {
        if (traceKey == null || traceKey.isEmpty()) {
            throw new IllegalArgumentException("traceKey不能为空");
        }

        List<AutoTaskLog> results = new ArrayList<>();

        try (Table table = hbaseConnection.getTable(TableName.valueOf(getTableName()))) {
            Scan scan = new Scan();
            // 设置列族
            scan.addFamily(Bytes.toBytes(AutoTaskLog.CF_DATA));

            try (ResultScanner scanner = table.getScanner(scan)) {
                for (Result result : scanner) {
                    AutoTaskLog autoTaskLog = convertResultToEntity(result);
                    if (autoTaskLog != null && traceKey.equals(autoTaskLog.getTraceKey())) {
                        results.add(autoTaskLog);
                    }
                }
            }
        }

        log.debug("根据traceKey: {} 查询到{}条记录", traceKey, results.size());
        return results;
    }

    /**
     * 添加列到Put对象，如果值不为空
     */
    private void addColumnIfNotNull(Put put, Map<String, String> columnFamilyMapping,
                                    String fieldName, Object value) {
        if (value != null) {
            String columnFamily = columnFamilyMapping.get(fieldName);
            if (columnFamily != null) {
                byte[] valueBytes = convertToBytes(value);
                if (valueBytes != null && valueBytes.length > 0) {
                    put.addColumn(Bytes.toBytes(columnFamily), Bytes.toBytes(fieldName), valueBytes);
                }
            }
        }
    }
}
