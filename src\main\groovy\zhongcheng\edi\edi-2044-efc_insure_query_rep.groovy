package zhongcheng.edi

import com.cheche365.bc.exception.InsReturnException

import static zhongcheng.edi.edi_2044_common_new.formatTransferDate

def root = root
def edi_2044_common_new = new edi_2044_common_new()
def script = new edi_2044_common()
def responseCode = root?.HeadDto?.ResponseCode?.toString()
def errorCode = root?.HeadDto?.ErrorCode?.toString()
def transMessage = root?.HeadDto?.TransMessage?.toString() ?: "众城EDI交强险精确报价接口调用无返回"
if (!"0000".equals(errorCode)) {
    throw new InsReturnException(transMessage)
}
if (root?.BodyDto?.MainDto?.PolicyNo?.toString()) {
    enquiry?.SQ?.efcPolicyCode = root?.BodyDto?.MainDto?.PolicyNo?.toString()
}
Map enquiry = enquiry
def SQ = enquiry?.SQ
if (!SQ) {
    SQ = [:]
    enquiry.put("SQ", SQ)
}


//交强险起始日期
String efcStartDate = root?.BodyDto?.MainDto?.StartDate?.toString()
if (efcStartDate) {
    def EffectDate = edi_2044_common_new.TPYDateTimeToBC(efcStartDate + "00")
    enquiry?.baseSuiteInfo?.efcSuiteInfo?.start = EffectDate
    //交强险终止日期
    String trafficInsInvalidDate = root?.BodyDto?.MainDto?.EndDate?.toString()
    def InvalidDate = edi_2044_common_new.TPYDateToBC(trafficInsInvalidDate.substring(0, 8)) + " 23:59:59"
    enquiry?.baseSuiteInfo?.efcSuiteInfo?.end = InvalidDate
}


//险别信息   ItemKindDtoList
//交强险信息
def ItemKindDto = root?.BodyDto?.ItemKindDtoList?.ItemKindDto?.getAt(0)

enquiry?.baseSuiteInfo?.efcSuiteInfo?.amount = ItemKindDto?.SumLimit?.toString() ? ItemKindDto?.SumLimit?.toDouble() : 0
enquiry?.baseSuiteInfo?.efcSuiteInfo?.orgCharge = ItemKindDto?.BasicPremium?.toString() ? ItemKindDto?.BasicPremium?.toDouble() : 0
enquiry?.baseSuiteInfo?.efcSuiteInfo?.discountCharge = ItemKindDto?.Premium?.toString() ? ItemKindDto?.Premium?.toDouble() : 0
enquiry?.baseSuiteInfo?.efcSuiteInfo?.discountRate = ItemKindDto?.Discount?.toString() ? ItemKindDto?.Discount?.toDouble() : 0
SQ.put("efcCharge", ItemKindDto?.Premium?.toString() ? ItemKindDto?.Premium?.toDouble() : 0)

//车船税
def CarshipTaxDto = root?.BodyDto?.CarshipTaxDto

def SumPayTax = CarshipTaxDto?.SumPayTax?.toString() ? CarshipTaxDto?.SumPayTax?.toDouble() : 0
def LateFee = CarshipTaxDto?.LateFee?.toString() ? CarshipTaxDto?.LateFee?.toDouble() : 0

enquiry?.baseSuiteInfo?.taxSuiteInfo?.start = CarshipTaxDto?.TaxStartDate?.toString()
enquiry?.baseSuiteInfo?.taxSuiteInfo?.end = CarshipTaxDto?.TaxEndDate?.toString()
enquiry?.baseSuiteInfo?.taxSuiteInfo?.charge = SumPayTax
enquiry?.baseSuiteInfo?.taxSuiteInfo?.delayCharge = LateFee
SQ.put("taxCharge", SumPayTax)

def bizCharge = SQ?.bizCharge ?: 0   //商业险
SQ.put("totalCharge", (bizCharge?.toBigDecimal() + SQ?.efcCharge?.toBigDecimal() + SQ?.taxCharge?.toBigDecimal())?.toDouble())

//保险关系人信息列表
def InsuredDtoList = root?.BodyDto?.InsuredDtoList?.InsuredDto;
Integer n = InsuredDtoList?.size();

for (int i = 0; i < n; i++) {
    def InsuredDto = InsuredDtoList?.getAt(i)
    String InsuredFlag = InsuredDto?.InsuredFlag?.toString()       //2投保人，1被保人，3车主
    if (InsuredFlag == "1") {
        enquiry?.insuredPersonList[0]?.name = InsuredDto?.InsuredName?.toString() ?: ""
        enquiry?.insuredPersonList[0]?.idCardType = script.formatIdCardType(InsuredDto?.CertiType?.toString() ?: "")
        enquiry?.insuredPersonList[0]?.idCard = InsuredDto?.CertiCode?.toString() ?: ""
        enquiry?.insuredPersonList[0]?.sex = script.formatGender(InsuredDto?.Gender?.toString() ?: "");
        enquiry?.insuredPersonList[0]?.email = InsuredDto?.Email?.toString() ?: ""
        enquiry?.insuredPersonList[0]?.mobile = InsuredDto?.MobileNo?.toString() ?: ""
        enquiry?.insuredPersonList[0]?.address = InsuredDto?.Address?.toString() ?: ""
        enquiry?.insuredPersonList[0]?.phone = InsuredDto?.PhoneNumber?.toString() ?: ""

    } else if (InsuredFlag == "2") {
        //投保人
        enquiry?.insurePerson?.name = InsuredDto?.InsuredName?.toString() ?: ""
        enquiry?.insurePerson?.idCardType = script.formatIdCardType(InsuredDto?.CertiType?.toString() ?: "")
        enquiry?.insurePerson?.idCard = InsuredDto?.CertiCode?.toString() ?: ""
        enquiry?.insurePerson?.sex = script.formatGender(InsuredDto?.Gender?.toString() ?: "")
        enquiry?.insurePerson?.email = InsuredDto?.Email?.toString() ?: ""
        enquiry?.insurePerson?.mobile = InsuredDto?.MobileNo?.toString() ?: ""
        enquiry?.insurePerson?.address = InsuredDto?.Address?.toString() ?: ""
        enquiry?.insurePerson?.phone = InsuredDto?.PhoneNumber?.toString() ?: ""
    } else if (InsuredFlag == "3") {
        //车主
        enquiry?.carOwnerInfo?.name = InsuredDto?.InsuredName?.toString() ?: ""
        enquiry?.carOwnerInfo?.idCardType = script.formatIdCardType(InsuredDto?.CertiType?.toString() ?: "")
        enquiry?.carOwnerInfo?.idCard = InsuredDto?.CertiCode?.toString() ?: ""
        enquiry?.carOwnerInfo?.sex = script.formatGender(InsuredDto?.Gender?.toString() ?: "")
        enquiry?.carOwnerInfo?.email = InsuredDto?.Email?.toString() ?: ""
        enquiry?.carOwnerInfo?.mobile = InsuredDto?.MobileNo?.toString() ?: ""
        enquiry?.carOwnerInfo?.address = InsuredDto?.Address?.toString() ?: ""
        enquiry?.carOwnerInfo?.phone = InsuredDto?.PhoneNumber?.toString() ?: ""
    }
}


//车辆车型车价信息
def ItemCarDto = root?.BodyDto?.ItemCarDto;

if (ItemCarDto?.ActualValue?.toString()) {
    enquiry?.carInfo?.plateNum = ItemCarDto?.LicensePlateNo?.toString() ?: ""
    enquiry?.carInfo?.plateColor = script.formatPlateColor(ItemCarDto?.LicensePlateColorCode?.toString() ?: "")
    enquiry?.carInfo?.engineNum = ItemCarDto?.EngineNo?.toString() ?: ""
    enquiry?.carInfo?.vin = ItemCarDto?.Vin?.toString() ?: "";
    enquiry?.carInfo?.carModelName = ItemCarDto?.ModelName?.toString() ?: "";
    enquiry?.carInfo?.useProps = script.formatUseProps(ItemCarDto?.MotorUsageTypeCode?.toString() ?: "");
    enquiry?.carInfo?.modelLoad = ItemCarDto?.Tonnage?.toString() ? ItemCarDto?.Tonnage?.toBigDecimal() * 1000 : 0;
    enquiry?.carInfo?.displacement = ItemCarDto?.Haulage?.toString() ? ItemCarDto?.Haulage?.toDouble() : 0;
    enquiry?.carInfo?.price = ItemCarDto?.PurchasePrice?.toString() ? ItemCarDto?.PurchasePrice?.toBigDecimal() : 0;
    enquiry?.carInfo?.isTransfer = ItemCarDto?.ChgOwnerFlag?.toString() == "1" ? true : false;
    enquiry?.carInfo?.transferDate = formatTransferDate(ItemCarDto?.TransferDate?.toString())
    enquiry?.carInfo?.fullLoad = ItemCarDto?.WholeWeight?.toString() ? ItemCarDto?.WholeWeight?.toDouble()?.toInteger() : 0;
    enquiry?.carInfo?.rbCode = ItemCarDto?.ModelCode?.toString() ?: "";
    enquiry?.carInfo?.jyCode = ItemCarDto?.ModelCode?.toString() ?: "";
    enquiry?.carInfo?.carBrandName = ItemCarDto?.Brand?.toString() ?: "";
    enquiry?.carInfo?.plateType = script.formatPlateType(ItemCarDto?.LicensePlateType?.toString() ?: "");
}
