package taipingyang.robot


import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.task.AutoTask
import taipingyang.robot.module.Robot2011Util

AutoTask autoTask = autoTask
Enquiry enquiry = (Enquiry) autoTask?.taskEntity
def backResult = Robot2011Util.initResp(autoTask.tempValues)(autoTask.backRoot)

def resultJSONArray = backResult.getJSONArray('result')
if (resultJSONArray) {
    if (enquiry?.bizProposeNum) {
        def bizJSONObject = backResult?.result?.find { enquiry?.bizProposeNum?.equals(it?.insuredNo) }
        if (bizJSONObject) {
            enquiry.misc?.bizInsureDate = bizJSONObject?.paymentTime
        }
    }
    if (enquiry?.efcProposeNum) {
        def efcJSONObject = backResult?.result?.find { enquiry?.efcProposeNum?.equals(it?.insuredNo) }
        if (efcJSONObject) {
            enquiry.misc?.efcInsureDate = efcJSONObject?.paymentTime
        }
    }
}

