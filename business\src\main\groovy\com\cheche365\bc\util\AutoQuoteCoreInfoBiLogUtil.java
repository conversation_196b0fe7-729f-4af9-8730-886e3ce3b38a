package com.cheche365.bc.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.dto.AutoQuoteCoreInfoSchema;
import com.cheche365.bc.dto.AutoTaskBiLogSchema;
import com.cheche365.bc.model.PlatformKey;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.PlatformUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * <AUTHOR>
 */

@Slf4j
public class AutoQuoteCoreInfoBiLogUtil {

    private static final List<Integer> GROUP_CARD_TYPES = List.of(6, 8, 9, 10);

    private static final Map<String, Integer> CHINESE_NUMBERS = new HashMap<>();

    static {
        CHINESE_NUMBERS.put("一", 1);
        CHINESE_NUMBERS.put("两", 2);
        CHINESE_NUMBERS.put("三", 3);
        CHINESE_NUMBERS.put("四", 4);
        CHINESE_NUMBERS.put("五", 5);
        CHINESE_NUMBERS.put("六", 6);
        CHINESE_NUMBERS.put("七", 7);
        CHINESE_NUMBERS.put("八", 8);
        CHINESE_NUMBERS.put("九", 9);
        CHINESE_NUMBERS.put("十", 10);
    }

    public static AutoQuoteCoreInfoSchema getAutoTaskBiLogSchema(AutoTask task, AutoTaskBiLogSchema schema) {

        // 处理生成 BI 日志
        try {
            AutoQuoteCoreInfoSchema autoQuoteCoreInfoSchema = new AutoQuoteCoreInfoSchema(schema);
            JSONObject enquiry = (JSONObject) JSONObject.parse(task.getFeedbackJson());
            initAutoQuoteCoreInfo(autoQuoteCoreInfoSchema, enquiry, task);
            return autoQuoteCoreInfoSchema;
        } catch (Exception e) {
            log.error("BI 信息AutoQuoteCoreInfo初始化异常：{}", ExceptionUtils.getStackTrace(e));
        }
        return null;
    }

    private static void initAutoQuoteCoreInfo(AutoQuoteCoreInfoSchema schema, JSONObject enquiry, AutoTask task) {
        initPersonType(schema, enquiry);
        initCarInfo(schema, enquiry);
        initDefinitionInfo(schema, enquiry);
        initPlatformInfo(schema, task);
    }

    private static void initPlatformInfo(AutoQuoteCoreInfoSchema schema, AutoTask task) {
        String platformInfo = task.getPlatformInfo();
        if (StringUtils.isNotBlank(platformInfo)) {
            JSONObject platformInfoMap = JSONObject.parseObject(platformInfo);
            Optional.ofNullable(platformInfoMap.get(PlatformKey.bwCommercialClaimTimes))
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .map(AutoQuoteCoreInfoBiLogUtil::getClaimTimes)
                .ifPresent(schema::setBwCommercialClaimTimes);

            Optional.ofNullable(platformInfoMap.get(PlatformKey.bizBrokerageRate))
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .map(Float::parseFloat)
                .ifPresent(schema::setCommercialBrokerageRate);

            Optional.ofNullable(platformInfoMap.get(PlatformKey.efcBrokerageRate))
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .map(Float::parseFloat)
                .ifPresent(schema::setCompulsoryBrokerageRate);

            Optional.ofNullable(platformInfoMap.get(PlatformKey.bizContinuityInsureYears))
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .map(Double::valueOf)
                .ifPresent(bizContinuityInsureYears -> schema.setBizContinuityInsureYears(bizContinuityInsureYears.intValue()));
        }
    }
    private static void initDefinitionInfo(AutoQuoteCoreInfoSchema schema, JSONObject enquiry) {
        Optional.ofNullable(enquiry.getJSONObject("definition"))
            .ifPresent(definition -> {
                Optional.ofNullable(definition.get(PlatformKey.selfRate))
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .map(Float::parseFloat)
                    .ifPresent(schema::setSelfRate);

                Optional.ofNullable(definition.get("totalScore"))
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .map(Float::parseFloat)
                    .ifPresent(schema::setTotalScore);

                Optional.ofNullable(definition.get(PlatformKey.bizScore))
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .map(Float::parseFloat)
                    .ifPresent(schema::setBizScore);

                Optional.ofNullable(definition.get(PlatformKey.TRAFFIC_SCORE))
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .map(Float::parseFloat)
                    .ifPresent(schema::setTrafficScore);

                Optional.ofNullable(definition.get(PlatformKey.application_bizRate))
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(schema::setBizRate);

                Optional.ofNullable(definition.get(PlatformKey.application_trafficRate))
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(schema::setTrafficRate);

                Optional.ofNullable(definition.get(PlatformKey.noClaimDiscountCoefficient))
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .map(Float::parseFloat)
                    .ifPresent(schema::setNoClaimDiscountCoefficient);

                Optional.ofNullable(definition.get(PlatformKey.trafficOffenceDiscount))
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .map(Float::parseFloat)
                    .ifPresent(schema::setTrafficOffenceDiscount);
            });

    }

    private static void initCarInfo(AutoQuoteCoreInfoSchema schema, JSONObject enquiry) {
        Optional.ofNullable(enquiry.getJSONObject("carInfo"))
            .ifPresent(carInfo -> {
                Optional.ofNullable(carInfo.get("seatCnt"))
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .map(Double::valueOf)
                    .ifPresent(seatCnt -> schema.setSeatCnt(seatCnt.intValue()));

                Optional.ofNullable(carInfo.get("isLocalDriving"))
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(isLocalDriving -> schema.setIsLocalDriving("1".equals(isLocalDriving)));

                Optional.ofNullable(carInfo.get("isLocalRegistration"))
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(isLocalRegistration -> schema.setIsLocalRegistration("1".equals(isLocalRegistration)));

                Optional.ofNullable(carInfo.get("firstRegDate"))
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(firstRegDate -> schema.setFirstRegDate(strToLocalDate(firstRegDate)));

                Optional.ofNullable(carInfo.get("transferDate"))
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(transferDate -> schema.setTransferDate(strToLocalDate(transferDate)));

                Optional.ofNullable(carInfo.get("fuelType"))
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(schema::setFuelType);
            });

        schema.setCarOriginProofDate(strToLocalDate(getSupplyParam(enquiry, "carOriginProofDate")));
        // 车生态该字段在carInfo节点下，磐石该字段在补充信息
        String isLocalDriving = getSupplyParam(enquiry, "isLocalDriving");
        if ("0".equals(isLocalDriving)) {
            schema.setIsLocalDriving(false);
        }
        String isLocalRegistration = getSupplyParam(enquiry, "isLocalRegistration");
        if ("0".equals(isLocalRegistration)) {
            schema.setIsLocalRegistration(false);
        }
        schema.setIsSaleBy4S("1".equals(getSupplyParam(enquiry, "isSaleBy4S")));

    }

    private static String getSupplyParam(JSONObject enquiry, String key) {
        JSONArray supplyParam = enquiry.getJSONArray("supplyParam");
        if (CollectionUtil.isNotEmpty(supplyParam)) {
            Optional<String> optional = supplyParam.stream()
                .filter(item -> item instanceof JSONObject)
                .map(item -> (JSONObject) item)
                .filter(item -> key.equals(item.getString("itemcode")))
                .map(item ->item.getString("itemvalue"))
                .findFirst();
            return optional.orElse(null);
        }
        return null;
    }

    private static LocalDate strToLocalDate(String dateString) {
        if (StringUtils.isBlank(dateString)) {
            return null;
        } else {
            return dateString.length() == 10 ? LocalDate.parse(dateString) : LocalDate.from(DateUtil.parseLocalDateTime(dateString));
        }
    }

    private static void initPersonType(AutoQuoteCoreInfoSchema schema, JSONObject enquiry) {
        Optional.ofNullable(enquiry.getJSONObject("carOwnerInfo"))
            .map(carOwnerInfo -> carOwnerInfo.getInteger("idCardType"))
            .ifPresent(idCardType -> schema.setCarOwnerType(isGroup(idCardType) ? 1 : 0));

        Optional.ofNullable(enquiry.getJSONObject("applicantPersonInfo"))
            .map(applicantPersonInfo -> applicantPersonInfo.getInteger("idCardType"))
            .ifPresent(idCardType -> schema.setApplicantType(isGroup(idCardType) ? 1 : 0));

        Optional.ofNullable(enquiry.getJSONArray("insuredPersonInfoList"))
            .filter(array -> !array.isEmpty())
            .map(array -> array.getJSONObject(0))
            .map(jsonObject -> jsonObject.getInteger("idCardType"))
            .ifPresent(idCardType -> schema.setInsuredType(isGroup(idCardType) ? 1 : 0));

        Optional.ofNullable(enquiry.getJSONArray("insuredPersonInfoList"))
           .filter(array -> !array.isEmpty())
           .map(array -> array.getJSONObject(0))
           .map(jsonObject -> jsonObject.getInteger("idCardType"))
           .filter(idCardType -> idCardType == 0)
           .ifPresent(idCardType -> initInsuredPerson(schema, enquiry));
    }

    private static void initInsuredPerson(AutoQuoteCoreInfoSchema schema, JSONObject enquiry) {
        JSONObject insuredInfo = (JSONObject) enquiry.getJSONArray("insuredPersonInfoList").get(0);
        if (StringUtils.isNotBlank(insuredInfo.getString("idCard")) && insuredInfo.getString("idCard").length() == 18) {
            String idCard = insuredInfo.getString("idCard");
            schema.setInsuredAge(IdcardUtil.getAgeByIdCard(idCard));
            schema.setInsuredSex(IdcardUtil.getGenderByIdCard(idCard) ^ 1);
        }
    }

    private static boolean isGroup(int idCardType) {
        return GROUP_CARD_TYPES.contains(idCardType);
    }

    private static Integer getClaimTimes(String bwCommercialClaimTimes) {
        return PlatformUtil.CLAIM_TIMES_MAP.entrySet()
            .stream()
            .filter(entry -> entry.getValue().equals(bwCommercialClaimTimes))
            .map(Map.Entry::getKey)
            .findFirst()
            .orElse(null);
    }
}
