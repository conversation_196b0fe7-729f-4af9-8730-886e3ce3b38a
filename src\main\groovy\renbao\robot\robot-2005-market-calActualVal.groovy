package renbao.robot


import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript

import java.time.ZoneId

import static renbao.common.renbao_dict.getCarKindCode
import static renbao.common.renbao_dict.useNatureCode
import static renbao.common.renbao_dict.useProp
import static renbao.robot.common_market.header
import static renbao.robot.common_market.hostPrefix

@BaseScript BaseScript_Http_Enq _

head(header(autoTask))

def isEnergyCar = tempValues?.isEnergyCar
def clauseType = useProp(entity.order.carInfo.useProps)
def carKindCode = getCarKindCode(entity.order.carInfo.carModelName)
def enrollDate = entity.order.carInfo.firstRegDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().toString()
def startDateBI = entity?.order?.suiteInfo?.bizSuiteInfo?.start ? entity?.order?.suiteInfo?.bizSuiteInfo?.start?.toString()?.substring(0, 10) : entity?.order?.suiteInfo?.efcSuiteInfo?.start?.toString()?.substring(0, 10)
def useNatureCode = useNatureCode(entity.order.carInfo.useProps)
def calActualVal = hostPrefix(autoTask) + "/khyx/newFront/price/calActualVal.do?energyTypePlat=${isEnergyCar}&energyFlag=${isEnergyCar}&clauseType=${clauseType}&carKindCode=${carKindCode}&seatCount=${entity.order.carInfo.seatCnt}&tonCount=0&enrollDate=${enrollDate}&useNatureCode=${useNatureCode}&startDateBI=${startDateBI}&purchasePrice=${entity.order.carInfo.price}" as String
tempValues['calActualVal'] = calActualVal

