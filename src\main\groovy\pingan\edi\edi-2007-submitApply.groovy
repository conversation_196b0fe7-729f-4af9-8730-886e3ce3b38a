package pingan.edi

import com.alibaba.fastjson.JSONObject
import com.alibaba.fastjson.serializer.SerializerFeature
import com.cheche365.bc.exception.InsReturnException
import java.time.LocalDate
import pingan.edi.edi_2007_util as util
import pingan.edi.edi_2007_maputil as mapUtil

def params = [:]
//如果上传影像则调用影像上传接口
if (tempValues.needImage) {
    if (!enquiry.imgAddress) {
        throw new InsReturnException("未上传影像件")
    }
    params << ['docList': util.upload(enquiry, tempValues)]
}
util.getHeader(reqHeaders)
tempValues.submitApplyUrl = tempValues?.envInfo?.interface + util.getUrl('submitApplyUrl', tempValues['token'], tempValues['flowid'])
//补充数据/账号补充信息 项 处理车主 被保险人 被保人手机号 证件有效期 等
def supplyParam = tempValues.supplyParam
params << [
        'flowid'                              : tempValues.flowid,
        'userId'                              : enquiry.configInfo.configMap.login,
        //是否核保，如果只
        'isApply'                             : 'true',
        //(04)电子保单,  (06)监制保单  默认06  北京04
        'bizFormatType'                       : '06',
        //(04)电子保单,  (06)监制保单  默认06  北京04
        'forceFormatType'                     : '06',
        'insured.mobile'                      : enquiry.insuredPersonList.get(0).mobile,
        'insured.address'                     : enquiry.insuredPersonList.get(0).address,
        'applicant.personnelType'             : mapUtil.personnelType(enquiry.insurePerson.idCardType as String),
        'applicant.name'                      : enquiry.insurePerson.name,
        'applicant.idType'                    : mapUtil.idCardTypeChange(enquiry.insurePerson.idCardType as String, enquiry.carInfo?.carUserType as String),
        'applicant.idNo'                      : enquiry.insurePerson.idCard,
        'applicant.birthday'                  : enquiry.insurePerson?.birthday  ?: supplyParam?.applicantBirthday ,
        'applicant.gender'                    : mapUtil.gender(enquiry.insurePerson.sex as String ?: supplyParam?.applicantGender as String),
        'applicant.mobile'                    : enquiry.insurePerson.mobile,
        'applicant.organizationType'          : '',
        'applicant.phoneExchange'             : '',
        'applicant.contactName'               : '',
        'applicant.address'                   : enquiry.insurePerson.address,
        'applicant.linkManCertificateTypeCode': '',
        'applicant.linkManCertificateNo'      : '',
        'applicant.phoneHolderName'           : '',
        'applicant.phoneHolderCertificateType': '',
        'applicant.phoneHolderCertificateNo'  : '',
        'address.receiveMode'                 : '1',
        'address.address'                     : '',
        'address.name'                        : '',
        'address.mobile'                      : '',
        'address.townCityCode'                : '',
        'address.townCode'                    : '',
        'sortIndex'                           : '',
        'promiseCode'                         : '',
        'promiseDesc'                         : '',
        'promiseType'                         : '',
        'disputedSettleMode'                  : '',
        'arbitralDepartment'                  : '',
        'expectationUnderwriteLimit'          : '',
        'vehicleCheckType'                    : '',
        'checkVehicleDate'                    : '',
        'checkVehicleDateZone'                : '',
        'checkVehicleAddress'                 : '',
        'bizApplyCheckCode'                   : '',
        'forceApplyCheckCode'                 : '',
        'beijingIssueCode'                    : '',
        'invoiceType'                         : '',
        'taxpayerCertificateType'             : '',
        'taxpayerCertificateNo'               : '',
        'taxpayerAddress'                     : '',
        'taxpayerPhone'                       : '',
        'taxpayerBankName'                    : '',
        'taxpayerBankAccount'                 : '',
        'forbidOprIbcsFlag'                   : '',
        'isElectronicApplyPolicy'             : '',
        'doubleInputSaleName'                 : '',
        'doubleInputSaleCode'                 : '',
        'saleAgentProfCertNo'                 : enquiry.configInfo.configMap?.saleAgentProfCertNo,
        'agencySaleProfCertifNo'              : enquiry.configInfo.configMap?.agencySaleProfCertifNo
]
//陕西 必须填
if (enquiry.configInfo.configMap?.agencySaleName) {
    params << ['agencySaleName': enquiry.configInfo.configMap?.agencySaleName]
}
//团体自核
if (mapUtil.personnelType(enquiry.insurePerson.idCardType as String) == '0') {
    params << [
            'applicant.organizationType': '13',
            'applicant.phoneExchange'   : enquiry.configInfo.configMap?.applicantPhone ?: enquiry.configInfo.configMap?.insuredPhone,
            'applicant.contactName'     : enquiry.insurePerson.name,
    ]
}
//处理关系人信息
params << ['insured.mobile': supplyParam?.insuredMobile ?: supplyParam?.applicantMobile]
params << ['insured.address': supplyParam?.insuredAddress ?: supplyParam?.applicantAddress ?: supplyParam?.ownerAddress]
params << ['applicant.mobile': supplyParam?.applicantMobile ?: supplyParam?.insuredMobile]
params << ['applicant.address': supplyParam?.applicantAddress ?: supplyParam?.insuredAddress ?: supplyParam?.ownerAddress]
params << ['register.address': supplyParam?.ownerAddress ?: supplyParam?.applicantAddress ?: supplyParam?.insuredAddress]
params << ['register.mobile': supplyParam?.ownerMobile ?: supplyParam?.insuredMobile ?: supplyParam?.applicantMobile]
params << ['insured.certificateIssueDate': supplyParam?.insuredIDCardRegistrationDate ?: supplyParam?.applicantIDCardRegistrationDate]
params << ['insured.certificateValidDate': util.checkCertificateValidDate(supplyParam?.insuredIDCardValidDate ?: supplyParam?.applicantIDCardValidDate)]
params << ['applicant.certificateIssueDate': supplyParam?.applicantIDCardRegistrationDate ?: supplyParam?.insuredIDCardRegistrationDate]
params << ['applicant.certificateValidDate': util.checkCertificateValidDate(supplyParam?.applicantIDCardValidDate ?: supplyParam?.insuredIDCardValidDate)]
//增值服务费折服联动开关
if (tempValues.clauseCaseSwitch == 'true') {
    def chooseRiskParams = util.chooseRiskParams(enquiry as Map, "", "", tempValues.isNewEnergy)
    def keys = ['amount01SvXNY', 'amount02SvXNY', 'amount03SvXNY', 'amount04SvXNY',
                'amount01Sv', 'amount02Sv', 'amount03Sv', 'amount04Sv']
    keys.each {
        if(chooseRiskParams[(it)]) {
            params << [(it): chooseRiskParams[(it)]]
        }
    }
}
//事中验车
if (tempValues.cheVehicle) {
    params << [
            //01-业务员验车  05-外网账号
            "vehicleCheckType"    : "05",
            "checkVehicleDate"    : LocalDate.now() as String,
            //01-上午02-下午
            "checkVehicleDateZone": "02",
            "checkVehicleAddress" : enquiry.configInfo.configMap.checkVehicleAddress
    ]
}
//需求7562
if (enquiry.carInfo?.carUserType == 0) {
    if (enquiry.insurePerson.idCardType != 0) {
        if (!params['applicant.birthday']) {
            params << ['applicant.birthday': util.getDefaultBirthDay()]
        }
        if (!params['applicant.gender']) {
            params << ['applicant.gender': 'M']
        }
    }
}
//地区 特殊参数 ==========
//双录经办人  上海/深圳
if ((mapUtil.personnelType(enquiry.insurePerson.idCardType as String) == '0' || mapUtil.personnelType(enquiry.insuredPersonList.get(0).idCardType as String) == '0')
        && (enquiry.insArea.city == '440300' || enquiry.insArea.province =='310000')) {
    params << [
        'applicant.contactName' : supplyParam?.liaisonName,
        'applicant.linkManCertificateNo' : supplyParam.liaisonIDCardNo,
        'applicant.linkManCertificateTypeCode' : '01',
        'insured.contactName' : supplyParam?.liaisonName,
        'insured.linkManCertificateNo' : supplyParam.liaisonIDCardNo,
        'insured.linkManCertificateTypeCode' : '01'
    ]
}
//上海 需求 11706
if (enquiry.insArea.province == '310000') {
    params << [
            //手机持有人姓名 -- 个团标志为0-团体时且为上海机构且联系人证件类型为身份证且不是联系人本人 个团标志为1-个人且为上海机构且被保人证件 类型为身份证且不是被保人本人
            'applicant.phoneHolderName'           : supplyParam?.phoneHoldName,
            //同 手机持有人姓名
            'applicant.phoneHolderCertificateType': '01',
            //同 手机持有人姓名
            'applicant.phoneHolderCertificateNo'  : supplyParam?.phoneHoldCertificateNo
    ]
}
//深圳需要双录
if (enquiry.insArea.city == '440300') {
    List supplyParamList = enquiry.supplyParam
    def doubleInputSaleCode = supplyParamList.find { s -> s.itemcode == "doubleInputSaleIDCardNo" }?.itemvalue ?: enquiry.configInfo.configMap.doubleInputSaleCode
    def doubleInputSaleName = supplyParamList.find { s -> s.itemcode == "doubleInputSaleName" }?.itemvalue ?: enquiry.configInfo.configMap.doubleInputSaleName
    if (!doubleInputSaleName || !doubleInputSaleCode) {
        throw new InsReturnException("深圳地区 参数：doubleInputSaleName，doubleInputSaleCode 不能为空")
    }
    params << [
            "doubleInputSaleName": doubleInputSaleName,
            "doubleInputSaleCode": doubleInputSaleCode
    ]
}
//北京
if (enquiry.insArea.city == '110000') {
    params << [
            //(04)电子保单,  (06)监制保单  默认06  北京04
            'bizFormatType'                       : '04',
            //(04)电子保单,  (06)监制保单  默认06  北京04
            'forceFormatType'                     : '04',
            //选择电子保单时、选择增值税电子普通发票时必填
            'applicant.email'                     : supplyParam.applicantEmail,
            //是否电子投保单（0—纸质投保单，1—电子投保单）
            'isElectronicApplyPolicy'             : '1'
    ]
}
//广东新车 深圳不用
if (enquiry.insArea.province == '440000' && enquiry.carInfo.isNew && enquiry.insArea.city != '440300') {
    params << [
        'saleCompanyName' : enquiry['configInfo']['configMap']?.newCarSaleComName ?: supplyParam.newCarSaleComName,
        'saleCompanyAreaCode' : enquiry['configInfo']['configMap']?.newCarSaleComCityCode ?: supplyParam.newCarSaleComCityCode,
        'sale4SFlag' : (enquiry['configInfo']['configMap']?.isSaleBy4S ?: supplyParam.isSaleBy4S) as String
    ]
}
//江西 需要车主地址 需求10595 【百川】【需求分析】【车生态】平安EDI 小鹏项目 适配「江西」特殊流程
if (enquiry.insArea.province == '360000') {
    //register.address 地址上面已经传参数
    //addressCode 需要传 区 编码
    def addressCode = (supplyParam?.ownerAddressCode ?: supplyParam?.applicantAddressCode ?: supplyParam?.insuredAddressCode)
    if(addressCode && addressCode.size() >=3) {
        params['register.addressCode'] = addressCode[2]
    } else {
        params['register.addressCode'] = mapUtil.jiangxi_default_areaCode(enquiry.insArea['city'])
    }
}
//需求 10376 【车生态】平安新增特约传值  0-通用特约；1-商业险特约；2-交强险特约
def specialStr = enquiry?.specialStr ?: tempValues?.specialStr
if(specialStr) {
    def specialInfos = specialStr.split('@#@')
    def bizSpecialPromiseList = []
    def forceSpecialPromiseList = []
    specialInfos.each{it ->
        def specialInfo = it.split('\\|')
        def info = [
                //特约排序序号
                'sortIndex' : specialInfo[2],
                //特约编码
                'promiseCode' : specialInfo[1],
                //特约描述
                'promiseDesc' :  specialInfo[3],
                //Y为可改特约
                'promiseType' : 'Y',
        ]
        //本保单的投保人为******，车主为******
        if(info['promiseCode'] == 'Z0063') {
            info['promiseDesc'] = '本保单的投保人为' + enquiry.insurePerson.name + "，车主为" + enquiry.carOwnerInfo.name
        }
        //
        if (specialInfo[0] == '0') {
            bizSpecialPromiseList << info
            forceSpecialPromiseList << info
        } else if (specialInfo[0] == '1') {
            bizSpecialPromiseList << info
        } else if (specialInfo[0] == '2') {
            forceSpecialPromiseList << info
        }
    }
    if(bizSpecialPromiseList && enquiry?.baseSuiteInfo?.bizSuiteInfo) {
        params << ['bizSpecialPromiseList' : bizSpecialPromiseList]
    }
    if(forceSpecialPromiseList && enquiry?.baseSuiteInfo?.efcSuiteInfo) {
        params << ['forceSpecialPromiseList' : forceSpecialPromiseList]
    }
}
//11944 【车生态】平安企业车发票类型默认按专票，
//需求12979 //如果有垫资请求 使用垫资的发票信息
def invoiceInfo = enquiry?.invoiceInfos ? enquiry?.invoiceInfos?.find {it.invoiceType == 1 || it.invoiceType == '1'} : enquiry?.invoiceInfo
if (invoiceInfo) {
    def idCardType = mapUtil.personnelType(invoiceInfo?.idCardType ?: enquiry.insurePerson.idCardType as String)
    params << [
            'invoiceType'            : invoiceInfo?.ifSpecialTicket in [1, '1'] ? '08' : '06', // 专票
            'taxpayerCertificateType': idCardType == '0' ? '02' : '01',
            //磐石不传 空取投保人
            'taxpayerCertificateNo'  : invoiceInfo?.idCard ?: enquiry?.insurePerson?.idCard,
            'taxpayerAddress'        : invoiceInfo?.companyAddress,
            'taxpayerPhone'          : invoiceInfo?.mobile ?: '',
            'taxpayerBankName'       : invoiceInfo?.bankName ?: '',
            'taxpayerBankAccount'    : invoiceInfo?.bankAccountNumber ?: ''
    ]
} else if (mapUtil.personnelType(enquiry.insurePerson.idCardType as String) == '0') {
    params << ['invoiceType' : '03']
}
//【车生态】【平安edi】适配兼业代理从业人员信息传值
if(tempValues.salerSortNo) {
    params << ['salerSortNo' : tempValues.salerSortNo]
}
JSONObject.toJSONString(params, SerializerFeature.DisableCircularReferenceDetect)
