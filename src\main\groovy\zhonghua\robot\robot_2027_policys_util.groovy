package zhonghua.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.utils.sdas.SDASUtils
import com.cheche365.bc.utils.sender.HttpSender
import com.google.common.base.Joiner
import common.common_all
import org.apache.http.entity.ByteArrayEntity
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.slf4j.Logger
import org.slf4j.LoggerFactory

static elecPolicyDownloadAll(AutoTask autoTask, String appNo, String policyNo, Enquiry entity, String type, Map pathMap) {
    Logger LOG = LoggerFactory.getLogger("elecPolicyDownloadAll")
    def elecPolicyDownloadAllUrl = "http://jscd.cic.cn:8897/ipartner/print/elecPolicyDownloadAll.json"
    if (!autoTask.tempValues.rolesNames) {
        doLoginAgain(autoTask, "http://jscd.cic.cn:8897/ipartner/index")
    }
    def param = [
            rolesNames: autoTask.tempValues.rolesNames,
            appNo     : appNo,
            policyNo  : policyNo,
            policyType: "A",
            name      : entity.order.carOwnerInfo.name,
            certfCde  : entity.order.carOwnerInfo.idCard,
            plateNo   : entity.misc?.carLicense ?: entity.order.carInfo.plateNum,
            engNo     : entity.order.carInfo.engineNum,
            newFlag   : "1",
    ]
    def paramString = Joiner.on("&")
    // 用指定符号代替空值,key 或者value 为null都会被替换
            .useForNull("")
            .withKeyValueSeparator("=")
            .join(param);
    LOG.info("paramString:{}", paramString)
    autoTask.reqHeaders."Referer" = "http://jscd.cic.cn:8897/ipartner/print/printList"
    String elecPolicyDownloadAllResult = HttpSender.doPostWithRetry(3, autoTask.httpClient, true, elecPolicyDownloadAllUrl, null,
            param, autoTask.reqHeaders, "utf-8", null, "");
    def elecPolicyDownloadAllJson = JSON.parseObject(elecPolicyDownloadAllResult)
    def downLoadUrl = elecPolicyDownloadAllJson.insuredinfos.find {
        it.policyNum == "1"
    }.downLoad

    def byteArrayEntity = doGetPdfByte(autoTask.httpClient, downLoadUrl)
    // 交强险额外下载标志
    if (type == 'efc') {
        downLoadUrl = elecPolicyDownloadAllJson.insuredinfos.find {
            it.policyNum == "2"
        }.downLoad
        def bzArrayEntity = doGetPdfByte(autoTask.httpClient, downLoadUrl)
        def common_all = new common_all()
        // 合并后的字节
        def newByte = common_all.mergePDF(byteArrayEntity.content.bytes, bzArrayEntity.content.bytes)
        byteArrayEntity = new ByteArrayEntity(newByte)
    }

    def path = Joiner.on("/").join(pathMap.values())
    def policyNoName = policyNo + ".pdf"
    def data = path + policyNo + "_" + System.currentTimeMillis() + ".pdf"
    def occupancy = SDASUtils.assetOccupancy("application/pdf", policyNoName, data)
    if (!occupancy) {
        throw new InsReturnException(InsReturnException.Others, "上传资产库占位失败,失败暂存单号:" + policyNo)
    }
//返回bizId:60a74fa5e1f39342af11bb11,efcId:60a74fa5e1f39342af11bb11
    JSONObject uploadPutAsset = SDASUtils.uploadPutAsset(policyNoName, occupancy, byteArrayEntity)
    if (uploadPutAsset) {
        type = type + "Id"
        entity.misc."$type" = occupancy.payload.assets[0].id
    } else {
        throw new InsReturnException(InsReturnException.Others, "真正上传资产库失败,失败暂存单号:" + policyNo)
    }
}

static getUserName(Document doc, String login) {
    def originUserName = doc.select("img[title~=^工号]").first().attr("title")
    def originLength = originUserName.indexOf(login) + login.length()
    return login + ";" + originUserName[(originLength + 1)..(originLength + 3)]
}

static ByteArrayEntity doGetPdfByte(httpClient, String imgURL) {
    try {
        byte[] bytes = (byte[]) HttpSender.doGet(httpClient, imgURL, null, "UTF-8", null, true)
        if (!bytes) {
            throw new InsReturnException(InsReturnException.Others, "中华联合获取保险公司io失败！")
        }
        return new ByteArrayEntity(bytes)
    } catch (e) {
        throw new InsReturnException(InsReturnException.Others, "中华联合获取保险公司io失败！" + e)
    }
}

static boolean doLoginAgain(AutoTask autoTask, testUrl) {
    def testResult = HttpSender.doPostWithRetry(3, autoTask?.httpClient, true, testUrl, "", null, autoTask.reqHeaders, "UTF-8", null, "");
    Document docc = testResult instanceof Document ? (Document) testResult : Jsoup.parse(testResult)
    if (docc.toString().contains("极速出单系统")) {
        autoTask.tempValues.rolesNames = getUserName(docc, autoTask?.configs?.login as String)
        return true
    }
    return false
}
