package com.cheche365.bc.instest.taipingyang.robot;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.SingleCompanyTest;
import com.cheche365.bc.exception.InsReturnException;
import com.cheche365.bc.exception.TempSkipException;
import com.cheche365.bc.exception.UniqueException;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.sdas.SDASUtils;
import com.cheche365.bc.utils.sender.HttpSender;
import com.google.common.collect.ImmutableSortedMap;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.client.CookieStore;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.cookie.BasicClientCookie;
import org.junit.Test;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.List;
import java.util.Map;


/**
 * Copyright (C), 2019-2019
 * FileName: MyTest
 * Author:   s·D·bs
 * Date:     2019/5/14 10:25
 * Description: 测试所有城市报价核保承保
 * Motto: 0.45%
 */
public class MyTest extends SingleCompanyTest {

    //    private static String[] enquiryArrays = {
    //        "enquiry_beijing.json", "enquiry_jilin.json","enquiry_shanxi.json",
//            "enquiry_hangzhou.json","enquiry_jinan.json","enquiry_chengdu.json",
//            "enquiry_wuhan.json","enquiry_shenyang.json","enquiry_fuzhou.json",
//            "enquiry_hefei.json","enquiry_dongguan.json",""
//            ,"enquiry_guangzhou.json"};
    private static String[] enquiryArrays = {"enquiry.json"};
    private static String queryQuickOfferUrl = "https://issue.cpic.com.cn/ecar/quickoffer/queryQuickOfferByPlate";
    private static String queryStatusUrl = "https://issue.cpic.com.cn/ecar/quotationPolicy/queryQuotationPolicy";
    private static String loginUrl = "https://issue.cpic.com.cn/ecar/j_spring_security_check";
    private static String secLoginUrl = "https://issue.cpic.com.cn/ecar/j_spring_security_check";
    //承保 验证码
    private static String smsForPay = "https://issue.cpic.com.cn/ecar/payment/smsGetVerificatiionCode";

    private static String getCarInfoUrl = "https://issue.cpic.com.cn/ecar/ecar/queryCarModel";
    private static String cancelQuotationPolicyUrl = "https://issue.cpic.com.cn/ecar/quotationPolicy/nuclearErrorQuotationPolicy";
    private static String getJGInfoUrl = "https://issue.cpic.com.cn/ecar/ecar/vehicleQueryConfirm";
    private static String getGDJGInfoUrl = "https://issue.cpic.com.cn/ecar/ecar/queryGDVehicleInfo";
    private static String calculateUrl = "https://issue.cpic.com.cn/ecar/insure/calculate";
    private static String prepare2deleteUrl = "https://issue.cpic.com.cn/ecar/quotationPolicy/queryQuotationPolicy";
    private static String insureUrl = "https://issue.cpic.com.cn/ecar/insure/saveClauseInfo";
    private static String uploadIndex = "https://issue.cpic.com.cn/ecar/view/portal/page/file_upload/file_upload.html";
    private static String queryUrl = "https://issue.cpic.com.cn/ecar/quotationPreview/queryQuotationPreview";
    private static String submitUrl = "https://issue.cpic.com.cn/ecar/insure/submitInsureInfo";
    private static String payInfoUrl = "https://issue.cpic.com.cn/ecar/payment/queryPayment";
    private static String getSmsUrl = "https://issue.cpic.com.cn/ecar/payment/smsconfirm";
    private static String sendSmsUrl = "https://issue.cpic.com.cn/ecar/update/moreBjIdentifyCode";
    private static String checkPinCodeUrl = "https://issue.cpic.com.cn/ecar/payment/queryPayment";
    private static String checkPayUrl = "https://issue.cpic.com.cn/ecar/paymentrecord/nullify";
    private static String carTaskListUrl = "https://issue.cpic.com.cn/ecar/renewaltask/taskquery/carTasklist";
    private static String queryConfigsUrl = "https://issue.cpic.com.cn/ecar/page/queryConfigs";
    private static String queryEcarVoByJYUrl = "https://issue.cpic.com.cn/ecar/ecar/queryEcarVoByJY";
    private static String queryQuickOfferByPlateUrl = "https://issue.cpic.com.cn/ecar/quickoffer/queryQuickOfferByPlate";
    private static String saveUrl = "https://issue.cpic.com.cn/ecar/quickInsure/quickSave";
    private static String calcUrl = "https://issue.cpic.com.cn/ecar/quickoffer/calculate";

    /**
     * @return void
     * <AUTHOR>
     * @Description //模拟脚本载入
     * @Date 15:15 2019/5/18
     * @Param []
     */
//    public void loadlocalScript() throws Exception {
//        DataUtil.get("common_2011", this.readResource("common_2011.groovy"));
//    }

    /**
     * @return void
     * <AUTHOR>
     * @Description //添加初始信息
     * @Date 10:50 2019/5/14
     * @Param []
     */
    @Override
    protected void init() {
        this.comId = "2011";
//        this.cityCode = "440100";
//        this.cityCode = "220100";//吉林
//        this.cityCode = "140100";//太原
//        this.cityCode = "440100";//广州
//        this.cityCode = "330100";//杭州
//        this.cityCode = "370100";//济南
//        this.cityCode = "510100";//成都
//        this.cityCode = "420100";//湖北武汉
//        this.cityCode = "430100";//湖南长沙 无账号
//        this.cityCode = "210100";//辽宁沈阳
//        this.cityCode = "350100";//福建福州
        this.cityCode = "320100";//江苏南京
        this.org = "1261000000";
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description //批量报价
     * @Date 15:26 2019/5/14
     * @Param []
     */
    @Test
    public void allQuotes() throws Exception {
        List<AutoTask> autoTasks = getAutoTasksFromFile(enquiryArrays);
        autoTasks.forEach(t -> {
                    try {
//                        TimeUnit.SECONDS.sleep(60);
                        quote(t);//报价
                    } catch (Exception e) {
                        log.error("allTests error : {} ", e);
                    }
                }
        );
    }

    @Test
    public void allRenewal() throws Exception {
        List<AutoTask> autoTasks = getAutoTasksFromFile(enquiryArrays);
        autoTasks.forEach(t -> {
            try {
                renewal(t);
            } catch (Exception e) {
                log.error("allTests error : {} ", e);
            }
        });
    }

    public void renewal(AutoTask t) throws Exception {
        CookieStore cookieStore = new BasicCookieStore();
        BasicClientCookie route = new BasicClientCookie("route", "4828c0a964083f3159cf4c76308eeb66");
        route.setDomain("issue.cpic.com.cn");
        route.setPath("/");
        BasicClientCookie JSESSIONID = new BasicClientCookie("JSESSIONID", "E3E6C9E2B8CF1BB914BEEBC63E08BCAF");
        JSESSIONID.setDomain("issue.cpic.com.cn");
        JSESSIONID.setPath("/");
        cookieStore.addCookie(JSESSIONID);
        cookieStore.addCookie(route);
        CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultCookieStore(cookieStore)
                .build();
        t.setHttpClient(httpClient);
        t.getTempValues().put("loginTimes", 0);
        t.setTaskType("-2011-quote");
        try {
            this.singleInterface("robot-2011-init", "", t,"初始化模版", HttpSender.HTTP_GET_METHOD);
        } catch (TempSkipException e) {
            log.info("代码模版忽略");
        }
        this.singleInterface("robot-2011-queryConfigs", queryConfigsUrl, t,"配置查询", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-carTaskList", carTaskListUrl, t,"续期查询", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-queryQuickOfferByPlate", queryQuickOfferByPlateUrl, t,"续期查询", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-queryEcarVoByJY", queryEcarVoByJYUrl, t,"车型查询", HttpSender.HTTP_POST_METHOD);
        try {
            this.singleInterface("robot-2011-code_renewal", "", t,"写入续期信息", HttpSender.HTTP_GET_METHOD);
        } catch (TempSkipException e) {
            log.info("代码模版忽略");
        }
        this.singleInterface("robot-2011-calc", calcUrl, t,"报价", HttpSender.HTTP_POST_METHOD);

    }

    /**
     * @return void
     * <AUTHOR>
     * @Description //批量核保包括查车
     * @Date 15:26 2019/5/14
     * @Param []
     */
    @Test
    public void allInsure() throws Exception {
        List<AutoTask> autoTasks = getAutoTasksFromFile(enquiryArrays);
        autoTasks.forEach(t -> {
                    try {
//                        TimeUnit.SECONDS.sleep(60);
                        insure(t);//查车∈核保
                    } catch (Exception e) {
                        log.error("allTests error : {} ", e);
                    }
                }
        );
    }


    /**
     * @return void
     * <AUTHOR>
     * @Description 本地批量报价接口
     * @Date 13:58 2019/5/9
     * @Param [t]
     */
    public void quote(AutoTask t) throws Exception {
        t.setHttpClient(buildHttpClient());
        t.getConfigs().remove("errorCarSolution");
        t.getTempValues().put("loginTimes", 0);
        
        t.setTaskType("-2011-quote");
//        loadlocalScript();
//        this.singleInterface("robot-2011-quote-login", loginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
//        this.singleInterface("robot-2011-businessHandler", secLoginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
        if ("true".equals(t.getConfigs().get("needJgPtSerch"))) {
            do {
                try {
                    this.singleInterface("robot-2011-jgPtSerch", getJGInfoUrl, t,"交管平台查询", HttpSender.HTTP_POST_METHOD);
                } catch (InsReturnException e) {
                    e.printStackTrace();
                    t.getTempValues().put("finishGetJGinfo", "false");
                }
            } while (!"true".equals(t.getTempValues().get("finishGetJGinfo")));
        }
        this.singleInterface("robot_2011_partnerselect", "${ReqURL}", t,"太平洋精灵代理点、终端及经办人选择", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-init", "https://baidu.com", t,"初始化", HttpSender.HTTP_GET_METHOD);
        this.singleInterface("robot-2011-getCarInfo", getCarInfoUrl, t,"车型查询", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-calc", calcUrl, t,"报价", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-save", saveUrl, t,"核保", HttpSender.HTTP_POST_METHOD);


        System.out.println("getTaskEntity:" + JSON.toJSONString(t.getTaskEntity()));
    }


    /**
     * @return void
     * <AUTHOR>
     * @Description 核保
     * @Date 13:58 2019/5/9
     * @Param [t]
     */
    public void insure(AutoTask t) throws Exception {
        t.setHttpClient(HttpClients.custom().build());
        t.getConfigs().remove("errorCarSolution");
        t.getTempValues().put("loginTimes", 0);
        
        t.setTaskType("-2011-autoinsure");
        do {
            try {
                this.singleInterface("robot-2011-quote-login", loginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
            } catch (UniqueException e) {
                e.printStackTrace();
                t.getTempValues().put("loginSuccess", "false");
            }
        } while ((int) t.getTempValues().get("loginTimes") != 5 && !"true".equals(t.getTempValues().get("loginSuccess")));


        this.singleInterface("robot-2011-businessHandler", secLoginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-getCarInfo", getCarInfoUrl, t,"车型查询", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-calculate", calculateUrl, t,"报价", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-uploadImages", uploadIndex, t,"上传影像", HttpSender.HTTP_GET_METHOD);
        this.singleInterface("robot-2011-insure", insureUrl, t,"核保", HttpSender.HTTP_POST_METHOD);
        //this.singleInterface("robot-2011-submit", submitUrl, t,"提交", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-getProposeNum", queryStatusUrl, t,"提交", HttpSender.HTTP_POST_METHOD);
        //this.singleInterface("robot-2011-queryStatus", queryStatusUrl, t,"提交", HttpSender.HTTP_POST_METHOD);
        //this.singleInterface("robot-2011-queryByProposeNum", queryUrl, t,"提交", HttpSender.HTTP_POST_METHOD);
        System.out.println("getTaskEntity:" + JSON.toJSONString(t.getTaskEntity()));
    }

    @Test
    public void policyDownload() throws Exception {
        AutoTask t = this.getMockDataFromFile();
        t.setHttpClient(HttpClients.custom().build());
        t.getConfigs().remove("errorCarSolution");
        t.getTempValues().put("loginTimes", 0);
        
        t.setTaskType("-2011-policyDownload");
        do {
            try {
                this.singleInterface("robot-2011-quote-login", loginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
            } catch (UniqueException e) {
                e.printStackTrace();
                t.getTempValues().put("loginSuccess", "false");
            }
        } while ((int) t.getTempValues().get("loginTimes") != 5 && !"true".equals(t.getTempValues().get("loginSuccess")));
        this.singleInterface("robot-2011-businessHandler", secLoginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-queryStatus", queryStatusUrl, t,"提交", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-queryByProposeNum", queryUrl, t,"提交", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-policyDownload", "", t,"提交", HttpSender.HTTP_POST_METHOD);
        System.out.println("getTaskEntity:" + JSON.toJSONString(t.getTaskEntity()));

    }

    @Test
    public void getPdf() {
        JSONObject asset = SDASUtils.getAsset("611626a8a68804785ccc4d91");
        System.out.println("asset = " + asset.toJSONString());
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description //申请二维码
     * @Date 18:26 2019/5/7
     * @Param []
     * QGUZ2L0Y1419F174613A
     */
    @Test
    public void getPayInfo() throws Exception {
        this.dataType = "map";
        AutoTask t = this.getMockDataFromFile();
        t.setHttpClient(HttpClients.custom().build());
        t.getTempValues().put("loginTimes", 0);
//        t.getTempValues().put("paytype", "qrcodeB64");
//        t.setRuleUrl("***********");
//        t.setRuleId("172");
//        loadlocalScript();// 读取本地脚本载入
        t.setTaskType("robot-2011-qrcode_insurequery");
        this.singleInterface("robot-2011-login4Pay", loginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-businessHandler4Pay", loginUrl, t,"支付处理", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-check4Pay", checkPayUrl, t,"获取支付状态", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-getPayInfo", payInfoUrl, t,"拉起支付", HttpSender.HTTP_POST_METHOD);
    }


    //北京地区身份采集+发送验证码
    @Test
    public void beijing_IDCardCollect() throws Exception {
        this.dataType = "map";
        AutoTask t = this.getMockDataFromFile();
        t.setHttpClient(HttpClients.custom().build());
        t.setTaskType("robot-2011-getPinCodeBj");
        https:
//issue.cpic.com.cn/ecar/payment/smsconfirm
        this.singleInterface("robot-2011-login4Pay", loginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-businessHandler4Pay", loginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-getPinCodeBj", getSmsUrl, t,"上传身份采集和发送短信验证码", HttpSender.HTTP_POST_METHOD);
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description //申请二维码
     * @Date 18:26 2019/5/7
     * @Param []
     * QGUZ2L0Y1419F174613A
     */
    @Test
    public void beijing_getPayInfo() throws Exception {
        this.dataType = "map";
        AutoTask t = this.getMockDataFromFile();
        t.setHttpClient(HttpClients.custom().build());
        t.getTempValues().put("loginTimes", 0);
        t.getTempValues().put("bjIdentifyCode", "2NC43Q");
//        t.setRuleUrl("***********");
//        t.setRuleId("172");
        t.setTaskType("robot-2011-qrcode_insurequery");
        this.singleInterface("robot-2011-login4Pay", loginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-businessHandler4Pay", loginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-getPayInfo", payInfoUrl, t,"拉起支付", HttpSender.HTTP_POST_METHOD);
    }

    @Test
    public void policyquery() throws Exception {
        AutoTask t = this.getMockDataFromFile();
        t.setHttpClient(HttpClients.custom().build());
        t.getConfigs().remove("errorCarSolution");
        t.getTempValues().put("loginTimes", 0);
        Map<String, String> policyno = ImmutableSortedMap.of("efcPolicyCode", "AJINJ50CTP21B116401Z"
                , "efcProposeNum", "AJINJ50CTP21B116401Z");
        t.getTempValues().put("policyno", policyno);
        
        t.setTaskType("-2011-policyquery");
        do {
            try {
                this.singleInterface("robot-2011-quote-login", loginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
            } catch (UniqueException e) {
                e.printStackTrace();
                t.getTempValues().put("loginSuccess", "false");
            }
        } while ((int) t.getTempValues().get("loginTimes") != 5 && !"true".equals(t.getTempValues().get("loginSuccess")));
        this.singleInterface("robot-2011-businessHandler", secLoginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-queryStatusByPolicyno", queryStatusUrl, t,"提交", HttpSender.HTTP_POST_METHOD);
//        this.singleInterface("robot-2011-queryStatus", queryStatusUrl, t,"提交", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-queryByProposeNum", queryUrl, t,"提交", HttpSender.HTTP_POST_METHOD);
        System.out.println("getTaskEntity:" + JSON.toJSONString(t.getTaskEntity()));
    }

    @Test
    public void quotequery() throws Exception {
        AutoTask t = this.getMockDataFromFile();
        t.setHttpClient(HttpClients.custom().build());
        t.getConfigs().remove("errorCarSolution");
        t.getTempValues().put("loginTimes", 0);
        
        t.setTaskType("robot-2011-qrcode_approvedquery");
        do {
            try {
                this.singleInterface("robot-2011-quote-login", loginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
            } catch (UniqueException e) {
                e.printStackTrace();
                t.getTempValues().put("loginSuccess", "false");
            }
        } while ((int) t.getTempValues().get("loginTimes") != 5 && !"true".equals(t.getTempValues().get("loginSuccess")));
        this.singleInterface("robot-2011-businessHandler", secLoginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-queryStatus", queryStatusUrl, t,"提交", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-queryByProposeNum", queryUrl, t,"提交", HttpSender.HTTP_POST_METHOD);
        System.out.println("getTaskEntity:" + JSON.toJSONString(t.getTaskEntity()));
    }


    @Test
    public void getEncrypt() throws Exception {
        String bbb = "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";
        String key = "MSBTOM2400000000";
        String ccc = MyTest.Decrypt(bbb, "MSBTOM2400000000");
        System.out.println(ccc);
    }

    public static String Encrypt(String sSrc, String sKey) throws Exception {
        byte[] raw = sKey.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");//"算法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes("utf-8"));
        return new Base64().encodeToString(encrypted);//此处使用BASE64做转码功能，同时能起到2次加密的作用。
    }

    // 解密 AES ecb  128  sKey.length() == 16
    public static String Decrypt(String sSrc, String sKey) throws Exception {
        byte[] raw = sKey.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);
        byte[] encrypted1 = new Base64().decode(sSrc);//先用base64解密
        byte[] original = cipher.doFinal(encrypted1);
        String originalString = new String(original, "utf-8");
        return originalString;

    }

    @Test
    public void autoinsure() throws Exception {
        AutoTask t = this.getMockDataFromFile();
        t.setHttpClient(HttpClients.custom().build());
        t.getConfigs().remove("errorCarSolution");
        t.getTempValues().put("loginTimes", 0);
        
        t.setTaskType("-2011-autoinsure");
        do {
            try {
                this.singleInterface("robot-2011-quote-login", loginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
            } catch (UniqueException e) {
                e.printStackTrace();
                t.getTempValues().put("loginSuccess", "false");
            }
        } while ((int) t.getTempValues().get("loginTimes") != 5 && !"true".equals(t.getTempValues().get("loginSuccess")));


        this.singleInterface("robot-2011-businessHandler", secLoginUrl, t,"登陆动作", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-getCarInfo", getCarInfoUrl, t,"车型查询", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-calculate", calculateUrl, t,"报价", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-uploadImages", uploadIndex, t,"上传影像", HttpSender.HTTP_GET_METHOD);
        this.singleInterface("robot-2011-insure", insureUrl, t,"核保", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-submit", submitUrl, t,"提交", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-queryStatus", queryStatusUrl, t,"提交", HttpSender.HTTP_POST_METHOD);
        this.singleInterface("robot-2011-queryByProposeNum", queryUrl, t,"提交", HttpSender.HTTP_POST_METHOD);
        System.out.println("getTaskEntity:" + JSON.toJSONString(t.getTaskEntity()));
    }

    private CloseableHttpClient buildHttpClient() {
        CookieStore cookieStore = new BasicCookieStore();
        BasicClientCookie cookie = new BasicClientCookie("JSESSIONID", "DF450C65049C5D9C9B215060518756E1");
        cookie.setDomain("issue.cpic.com.cn");
        cookie.setPath("/");
        BasicClientCookie cookie2 = new BasicClientCookie("route", "fc92620ecbca3174b9c6b97d7d910d05");
        cookie2.setDomain("issue.cpic.com.cn");
        cookie2.setPath("/");
        cookieStore.addCookie(cookie);
        cookieStore.addCookie(cookie2);
        return HttpClients.custom().setDefaultCookieStore(cookieStore).build();

    }
}

