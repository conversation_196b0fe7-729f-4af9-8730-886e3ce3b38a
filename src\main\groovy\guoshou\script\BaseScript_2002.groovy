package guoshou.script

import cn.hutool.http.ContentType
import com.alibaba.fastjson.JSONObject
import guoshou.constants.GuoShouConstants
import org.apache.commons.codec.digest.DigestUtils

import java.nio.charset.StandardCharsets

import static guoshou.edi.edi_2002_common_new.setCommonDataForPost

abstract class BaseScript_2002 extends Script {

    Map<String, Object> tempValues

    Map<String, Object> config

    Map<String, Object> enquiry

    Map<String, Object> carInfo // 原始车辆信息

    Map<String, Object> carInfoBj // 交管查车车辆信息

    Map<String, Object> carInfoQuery // 查车车辆信息

    Map<String, Object> bizSuiteInfo

    Map<String, Object> efcSuiteInfo


    abstract def runScript()

    def preRun() {
        this.tempValues = this.binding.tempValues as Map<String, Object>
        this.enquiry = this.binding.enquiry as Map<String, Object>
        this.config = this.binding.config as Map<String, Object>
        this.carInfo = enquiry.carInfo as Map<String, Object>
        this.carInfoBj = tempValues?.carMessForBeiJing as Map<String, Object>
        this.carInfoQuery = tempValues?.carMessResult as Map<String, Object>
        this.bizSuiteInfo = enquiry.bizSuiteInfo as Map<String, Object>
        this.efcSuiteInfo = enquiry.efcSuiteInfo as Map<String, Object>
    }

    @Override
    Object run() {
        preRun()
        runScript()
    }

    /**
     * 通用拼接 requestBody & 加签
     * @param tempValues
     * @param reqHeaders
     * @param method
     * @param bodyMap
     * @return
     */
    String body(Map tempValues, Map reqHeaders, String method, Map bodyMap) {

        def commonData = setCommonDataForPost(this.config, tempValues.businessNumber, method)
        def request = [
                "commonData"  : commonData,
                "businessData": bodyMap
        ] as JSONObject

        def sign = DigestUtils.md5Hex((request.toString() + GuoShouConstants.SIGN_KEY).getBytes(StandardCharsets.UTF_8))
        reqHeaders["signature"] = sign
        reqHeaders["Content-Type"] = ContentType.JSON.value

        request.toString()
    }
}
