package jintai.edi

import com.cheche365.bc.message.TaskStatus
import com.cheche365.bc.task.AutoTask
import org.json.XML

/**
 * 核保回调
 */
def script = new edi_2050_common()
def bizContent = XML.toJSONObject(callbackBody?.toString())
// 基本信息
def basePart = bizContent?.Packet?.BasePart

def sq = enquiry.SQ ?: [:]
sq.orderNo = basePart.QtnId
sq.bizProposeNum = basePart.QtnId
sq.efcProposeNum = basePart.QtnId
enquiry.SQ = sq
def misc = enquiry.SQ.misc ?: [:]
enquiry.SQ.misc = misc

// 通知结果(11支付成功，21核保通过，22核保退回，31撤单成功，41出单成功)
def NoticeResult = basePart?.NoticeResult?.toString()
if (["11","21","41"].contains(NoticeResult)) { // 核保成功
    enquiry.SQ.misc.auditOpinion = "核保成功" ?: basePart?.UndrOpinion            // 审核意见
    enquiry.SQ.misc.callResult = "核保成功"
    enquiry.taskStatus = TaskStatus.INSURE_SUCCESS.State()
} else {
    enquiry.SQ.misc.callResult = "核保失败"
    enquiry.taskStatus = TaskStatus.INSURE_FAILED.State()
    enquiry['errorInfo'] = enquiry['errorInfo'] ?: [:]
    enquiry['errorInfo']['errorcode'] = 11
    def errorMsg = script.getInsureError(NoticeResult)
    enquiry['errorInfo']['errordesc'] = errorMsg
}


