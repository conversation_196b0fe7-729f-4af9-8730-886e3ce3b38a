package com.cheche365.bc.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "hbase.conf")
public class HBaseProperties {

    private String zkQuorum;
    private String zkZoneParent;

    public String getZkQuorum() {
        return zkQuorum;
    }

    public void setZkQuorum(String zkQuorum) {
        this.zkQuorum = zkQuorum;
    }

    public String getZkZoneParent() {
        return zkZoneParent;
    }

    public void setZkZoneParent(String zkZoneParent) {
        this.zkZoneParent = zkZoneParent;
    }
}
