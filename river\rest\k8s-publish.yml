server:
  port: ${profiles_port}
  undertow:
    io-threads: ${undertow_io_threads}
    worker-threads: ${undertow_worker_threads}
spring:
  data:
    mongodb:
      uri: ${mongo_uri}
    redis:
      host: ${redis_host}
      port: ${redis_port}
      password: ${redis_password}
      database: ${redis_database}

web:
  groovy-files: ${web_groovy_files}
feishu:
  url: ${feishu_url}
  secret: ${feishu_secret}
mysql_username: ${mysql_username}
mysql_password: ${mysql_password}
mysql_host: ${mysql_host}
mysql_db: ${mysql_db}
