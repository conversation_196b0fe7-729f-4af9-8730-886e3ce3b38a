package taipingyang.robot.module

import cn.hutool.core.codec.Base64
import cn.hutool.core.collection.CollUtil
import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.IdcardUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.tools.PhoneUtil
import org.apache.http.impl.client.CloseableHttpClient
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import taipingyang.robot.robot_2011_dict

/**
 * 太保非车投保新流程<p>
 * status说明:<p>
 * 1. baseInfo:基本信息，autoTask获取的投保基本信息
 * 2. carData： 跳转非车链接需要暂存的车险信息，是太保精灵页面的一个临时存储
 * <AUTHOR>
 */
class Robot2011NonMotor {

    private static final Logger log = LoggerFactory.getLogger(Robot2011NonMotor.class)

    /**
     * https://issue.cpic.com.cn
     * 原投保密钥
     */
    private static final String ISSUE_KEY = "MSBTOM2400000000"

    /**
     * https://dwx.cpic.com.cn
     * 嵌入的非车投保页面密钥
     */
    private static final String DWX_KEY = "TdOvQRBA7Byx5uJE"

    private static final PLAN_TO_PRODUCT_DICT = [
            "PL2418980024A10000172": "PDL20240418A00006904",//任我行七代（基础版)-A款
            "PL2418980024A10000198": "PDL20240418A00006904",//任我行七代（基础版)-B款
            "PL2418980024A10000192": "PDL20240418A00006952",//任我行七代(升级版)-A款
            "PL2418980024A10000193": "PDL20240418A00006952",//任我行七代(升级版)-B款
            "PL2418980024A10000194": "PDL20240418A00006952",//任我行七代(升级版)-C款
            "PL2418980024A10000751": "PDL20240717A00010664",//粤享行六代（代步版）-A款
            "PL242M980025A10000079": "PDL20250326A00021047",//畅行保 3.0 两座版A
            "PL242M980025A10000080": "PDL20250326A00021047",//畅行保 3.0 两座版B
            "PL242M980025A10000081": "PDL20250326A00021047",//畅行保 3.0 两座版C
            "PL242M980025A10000088": "PDL20250326A00021048",//畅行保 3.0 三座及以上  A
            "PL242M980025A10000089": "PDL20250326A00021048",//畅行保 3.0 三座及以上  B
            "PL242M980025A10000090": "PDL20250326A00021048",//畅行保 3.0 三座及以上  C
    ]

    private static final URL_DICT = [
            //获取投保非车产品的跳转链接
            QUERY_PRODUCT_DETAIL   : "https://issue.cpic.com.cn/ecar/msb/queryProductDetail",
            //暂存车险信息，应该是部分hashCode太长，超过了浏览器GET请求参数最长限制
            GET_CAR_TEMP_PARAM     : "https://dwx.cpic.com.cn/nonvehicle/napi/otherTip/getCarTempParam",
            //获取非车产品列表
            GET_PRDS_BY_CHANNEL_NEW: "https://dwx.cpic.com.cn/nonvehicle/napi/cldActiveChannel/getPrdsByChannelNew",
            //初始化产品信息
            INIT_PRD               : "https://dwx.cpic.com.cn/nonvehiclev2/napi/insurance/initPrd",
            //初始化参数
            GET_INIT_PARAMS        : "https://dwx.cpic.com.cn/nonvehicle/napi/insurance/getInitParams",
            //获取产品基本信息
            GET_PRODUCT_BASE       : "https://dwx.cpic.com.cn/nonvehiclev2/napi/pageNewData/getProductBase",
            //获取计划表单信息
            GET_PLAN_FORM          : "https://dwx.cpic.com.cn/nonvehiclev2/napi/pageNewData/getPlanForm",
            //获取计划险种信息
            GET_PLAN_COVERAGE      : "https://dwx.cpic.com.cn/nonvehiclev2/napi/pageNewData/getPlanCoverage",
            //保费计算
            CALCULATE              : "https://dwx.cpic.com.cn/nonvehiclev2/napi/premium/calculate",
            //代理人校验
            CHECK_AGENT            : "https://dwx.cpic.com.cn/nonvehiclev2/napi/insurance/checkAgent",
            // 提交投保
            SUBMIT_POLICY          : "https://dwx.cpic.com.cn/nonvehiclev2/napi/insurance/submitPolicy",
            // 非车投保信息暂存
            FORM_INFO_TEMP         : "https://dwx.cpic.com.cn/nonvehicle/napi/formInfoTemp"
    ]

    private static final def PLATE_COLOR_MAP = [
            '1': 'C0000',
            '2': 'C0001',
            '3': 'C0002',
            '4': 'C0003',
    ]
    private static final def CARD_TYPE_MAP = [
            "6" : "1",//组织机构代码
            "0" : "111",//身份证
            "3" : "1114",//军官证
            "10": "3",//营业执照
            "4" : "414",//护照
            "8" : "9",//统一社会信用代码
            '5' : '513'//港澳居民来往内地通行证

    ]

    /**
     * 是否是新流程产品
     * @param planCode 计划编码
     * @return
     */
    static boolean isNewProcess(String planCode) {
        return PLAN_TO_PRODUCT_DICT.keySet().contains(planCode)
    }

    /**
     * 非车投保接口<p>
     *
     * 太保精灵非车为单独系统，选择非车产品并投保成功后，会调用暂存接口，将报价单号和非车投保后的订单号关联
     * @param autoTask
     * @return
     */
    static def nonMotorInsure(AutoTask autoTask) {

        //状态记录
        def status = [
                "baseInfo": initBaseInfo(autoTask)
        ] as HashMap<String, Object>

        def httpClient = autoTask.httpClient as CloseableHttpClient
        //不加密http调用
        def standardFetch = fetch(httpClient, null, null)
        //加密版本http调用
        def encryptFetch = fetch(httpClient, buildRequestBody(), handleResponse())
        def issueEncryptFetch = fetch(httpClient,
                buildRequestBodyIssue(status.baseInfo.sm4Key as String),
                handleResponseIssue(status.baseInfo.sm4Key as String)
        )

        //跳转非车投保页面
        jumpToNonMotorPage(status, issueEncryptFetch, encryptFetch)

        //选择非车产品
        selectNonMotorProduct(status, standardFetch, encryptFetch)

        //计算保费
        calculate(status, encryptFetch)

        //代理人校验
        checkAgent(status, encryptFetch)

        //投保
        submitPolicy(status, encryptFetch)

        //暂存
        tempForm(status, encryptFetch)
    }

    private static void checkAgent(Map status, Closure<JSONObject> encryptFetch) {
        def response = encryptFetch(
                URL_DICT.CHECK_AGENT,
                'post',
                [
                        'Content-Type': 'application/json;charset=UTF-8',
                        'Host'        : 'dwx.cpic.com.cn',
                        'Origin'      : 'https://dwx.cpic.com.cn',
                        'Referer'     : makeReferer(status)
                ],
                makeRequestParamsCheckAgent(status),
                makeRequestBodyCheckAgent(status)
        )
        responseHandlerCheckAgent(status, response)
    }

    private static def makeRequestBodyCheckAgent(Map status) {
        def carData = status.carData as Map
        def handlerList = carData.handlerList
        def unitCode = ''
        if (handlerList) {
            unitCode = handlerList[0].hdlrUnitCode
        }
        def plan = [
                "plnBase"    : [
                        "ifDesensitization" : carData.ifDesensitization,
                        "isByCarSellFlag"   : carData.isByCarSellFlag,
                        "plnMainClassesCode": status.productDetail.classesCode,
                        "plnPlanCode"       : status.plan.planCode,
                        "plnPlanVersion"    : status.plan.planVersion,
                        "renewInsuredFlag"  : carData.renewInsuredFlag
                ],
                "handlerList": carData.handlerList,
                "agentList"  : [
                        [
                                "agentCode": carData.agentCode,
                                "unitCode" : unitCode
                        ]
                ]
        ]
        return [
                "activitiesList": [
                        [
                                "activitiesCode"   : status.productDetail.activitiesCode,
                                "activitiesVersion": status.productDetail.activitiesVersion,
                                "branchCode"       : carData.branchCode,
                                "partnerCode"      : status.productDetail?.activitiesInfo?.partnerCode,
                                "sellChannelCode"  : carData.channelCode,
                                "unilateralCode"   : status.productDetail.unilateralCode,
                                "productList"      : [
                                        [
                                                "msbProductCode"   : status.product.productCode,
                                                "msbProductVersion": status.product.productVersion,
                                                "planGroupList"    : [
                                                        [
                                                                "plangroupcode": status.productDetail.plangroupcode,
                                                                "planList"     : [
                                                                        plan
                                                                ]
                                                        ]
                                                ]

                                        ]
                                ]
                        ]
                ]
        ]
    }

    private static def makeRequestParamsCheckAgent(Map status) {
        return [
                "sourceType": status.carData.sourceType,
                "saleType"  : status.carData.saleType,
                "branchCode": status.carData.branchCode,
        ]
    }

    private static void responseHandlerCheckAgent(Map status, JSONObject response) {
        if (response.getInteger("code") != 0) {
            fail(response)
        }
    }

    private static void calculate(HashMap<String, Object> status, Closure<JSONObject> encryptFetch) {
        def requestBody = makeRequestBodyCalculate(status)
        status["calcRequestBody"] = requestBody
        def response = encryptFetch(
                URL_DICT.CALCULATE,
                'post',
                [
                        'Content-Type': 'application/json;charset=UTF-8',
                        'Host'        : 'dwx.cpic.com.cn',
                        'Origin'      : 'https://dwx.cpic.com.cn',
                        'Referer'     : makeReferer(status)
                ],
                null,
                requestBody
        )
        responseHandlerCalculate(status, response)
    }

    private static void responseHandlerCalculate(Map status, JSONObject response) {
        if (response.getInteger("code") != 0) {
            fail(response)
        }
        def data = response.getJSONArray("data")?.getJSONObject(0)
        status["policyBase"] = data?.getJSONObject("policyBase")
        status["planList"] = data?.getJSONArray("planList")?.getJSONObject(0)
    }

    private static def makeRequestBodyCalculate(Map status) {
        def carData = status.carData as Map
        def productDetail = status.productDetail as JSONObject
        def product = status.product as JSONObject
        def plan = status.plan as JSONObject
        def planGroup = status.planGroup as JSONObject
        def plnBase = [
                "plnPlanCode"   : plan.planCode,
                "plnPlanVersion": plan.planVersion,
                "plnCopies"     : 1,
                "plnStartDate"  : carData.startDate + " 00:00:00",
                "plnEndDate"    : carData.endDate + " 00:00:00"
        ]
        def productFeatureInfo = [
                "productFeatureType"    : "common",
                "productFeatureBaseInfo": generateProductFeatureBaseInfo(status)
        ]
        status["productFeatureInfoList"] = CollUtil.newArrayList(productFeatureInfo)

        def subjectWrapper = generateSubjectWrapper(status)
        status["subjectWrapperList"] = CollUtil.newArrayList(subjectWrapper)

        def productInfo = [
                "msbProductCode"   : product.productCode,
                "msbProductVersion": product.version,
                "planGroupList"    : [
                        [
                                "plangroupcode": planGroup.planGroupCode,
                                "planList"     : [
                                        [
                                                "coverageList"          : [],
                                                "plnBase"               : plnBase,
                                                "productFeatureInfoList": status.productFeatureInfoList,
                                                "subjectWrapperList"    : status.subjectWrapperList
                                        ]
                                ]
                        ]
                ]
        ]
        def activities = [
                "activitiesCode"   : productDetail.activitiesCode,
                "activitiesVersion": productDetail.activitiesVersion,
                "productList"      : CollUtil.newArrayList(productInfo)
        ]
        return [
                "sourceType"    : carData.sourceType,
                "branchCode"    : carData.branchCode,
                "saleType"      : carData.saleType,
                "activitiesList": CollUtil.newArrayList(activities)
        ]
    }


    /**
     * 暂存
     * @param status
     * @param encryptFetch
     */
    private static void tempForm(HashMap<String, Object> status, Closure<JSONObject> encryptFetch) {
        def response = encryptFetch(
                URL_DICT.FORM_INFO_TEMP,
                'post',
                [
                        'Content-Type': 'application/json;charset=UTF-8',
                        'Host'        : 'dwx.cpic.com.cn',
                        'Origin'      : 'https://dwx.cpic.com.cn',
                        'Referer'     : makeReferer(status)
                ],
                null,
                makeRequestBodyTempForm(status)
        )
        responseHandlerTempForm(status, response)
    }

    private static def makeRequestBodyTempForm(Map status) {
        def carData = status.carData
        return [
                "branchCode" : status.carData.branchCode,
                "orderId"    : status.policy.orderId,
                "planCode"   : status.plan.planCode,
                "productCode": status.product.productCode,
                "extend"     : [
                        "agentMo" : [
                                "agentCode"        : carData.agentCode,
                                "branchCode"       : carData.branchCode,
                                "channelCode"      : carData.channelCode,
                                "handlerCode"      : carData.hdlrCode,
                                "centralBranchCode": carData.centralBranchCode,
                                "groupOrderId"     : carData.groupOrderId,
                                "otherSource"      : carData.otherSource,
                                "recordWay"        : carData.recordWay,
                        ],
                        "query"   : [
                                "isByVehicleFlag": carData.isByVehicleFlag,
                                "isByCarSellFlag": carData.isByCarSellFlag,
                                "saleType"       : carData.saleType,
                                "salesmanCode"   : carData.salesmanCode,
                                "oldTempId"      : status.productDetail?.temp?.id,
                        ],
                        "formData": [:]
                ]
        ]
    }

    private static void responseHandlerTempForm(Map status, JSONObject response) {
        if (response.getInteger("code") != 0) {
            throw new InsReturnException(InsReturnException.Others, "太平洋精灵非车险暂存失败")
        }
    }


    /**
     * 投保
     * @param status 状态信息
     * @param nonMotorFetch
     */
    private static void submitPolicy(HashMap<String, Object> status, Closure<JSONObject> encryptFetch) {
        def response = encryptFetch(
                URL_DICT.SUBMIT_POLICY,
                'post',
                [
                        'Content-Type': 'application/json;charset=UTF-8',
                        'Host'        : 'dwx.cpic.com.cn',
                        'Origin'      : 'https://dwx.cpic.com.cn',
                        'Referer'     : makeReferer(status)
                ],
                null,
                makeRequestBodySubmitPolicy(status)
        )
        responseHandlerSubmitPolicy(status, response)
    }

    private static void responseHandlerSubmitPolicy(Map status, JSONObject response) {
        if (response.getInteger("code") != 0) {
            def msg = response.getString("msg") ?: ""
            throw new InsReturnException(InsReturnException.Others, "太平洋精灵非车险投保失败:" + msg)
        }
        status["policy"] = response.getJSONObject("data")
    }

    private static def makeRequestBodySubmitPolicy(Map status) {
        def carData = status.carData as Map
        def carInfo = status.carInfo as Map
        def insurePerson = status.baseInfo.insurePerson
        def person = [
                'branchCode'       : carData.branchCode,
                'certificationNo'  : carData.insurCardNo,
                'certificationType': CARD_TYPE_MAP.get(insurePerson.idCardType as String),
                "customerType"     : "01",
                'emailAddress'     : status.baseInfo.email,
                'mobilePhone'      : status.baseInfo.mobile,
                'personName'       : carData.insurName,
                'personType'       : 1,
        ]
        if (insurePerson?.sex) {
            person['gender'] = insurePerson.sex == 0 ? '1' : '2'
        }
        if (insurePerson?.birthday && carData.insurCardNo) {
            def birthDate = null
            try {
                birthDate = IdcardUtil.getBirthDate(carData.insurCardNo as String)
            } catch (Exception e) {
                log.info("证件类型错误获取生日异常：证件号：${carData.insurCardNo}", e)
            }
            if (birthDate) {
                person['birthDate'] = birthDate?.toString("yyyy-MM-dd")
            } else if (insurePerson?.birthday) {
                person['birthDate'] = DateUtil.format(insurePerson?.birthday as Date, DatePattern.NORM_DATE_PATTERN)
            }
        }
        def appli = [
                "appntType": "01"
        ]
        if (insurePerson?.idCardType) {
            def type = insurePerson.idCardType == 7 || insurePerson.idCardType < 6 ? "01" : "02"
            appli['appntType'] = type
            person["customerType"] = type
        }
        def plan = [
                "applicantList"         : [
                        appli
                ],
                "drawerList"            : [
                        [
                                "draNo"          : 1,
                                "draCustomerName": person.personName,
                                "draCertType"    : person.certificationType,
                                "draCustomerType": person.customerType,
                                "draCertNo"      : person.certificationNo,
                                "draGatPassport" : ""
                        ]
                ],
                "plnBase"               : [
                        "plnPlanCode"       : status.plan.planCode,
                        "plnPlanVersion"    : status.plan.planVersion,
                        "renewInsuredFlag"  : carData.renewInsuredFlag,
                        "ifDesensitization" : carData.ifDesensitization,
                        "plnMainClassesCode": status.productDetail.classesCode,
                        "isByCarSellFlag"   : carData.isByCarSellFlag,
                        "groupInsuranceFlag": status.plan.groupInsuranceFlag ?: '2',//可能是团体险标识
                        "ifContract"        : "0",
                        "elcEmlFlag"        : "1",
                        "elcMsgFlag"        : "1",
                        "elcMobile"         : status.baseInfo.mobile,
                        "elcEmail"          : status.baseInfo.email,
                        "plnElcFlag"        : "01",
                        "loadPolicyFlag"    : "1"
                ],
                "productFeatureInfoList": status.productFeatureInfoList,
                "subjectWrapperList"    : status.subjectWrapperList
        ]
        return [
                "branchCode"       : carData.branchCode,
                "groupOrderId"     : carData.groupOrderId,
                "sourceType"       : carData.sourceType,
                "saleType"         : carData.saleType,
                "handlerList"      : carData.handlerList,
                "productCode"      : status.product.productCode,
                "tempId"           : status.productDetail?.temp?.id,
                "plcCalcPolicyFlag": "1",//估计是是否计算保费
                "iscombineProduct" : 0,
                "iseeId"           : "",//功能上可能是可回溯接口
                "carFactorInfo"    : [
                        "carColor"  : PLATE_COLOR_MAP.get(carData.plateColor) ?: 'C0000',
                        "carEngine" : carData.engineNo,
                        "carNature" : carInfo.vehicleUsage,
                        "carSeating": carData.seatCount,
                        "carType"   : carInfo.vehicleKind,
                        "carVin"    : carData.carVin,
                        "carNumber" : carData.plateNo,
                        "extend1"   : carData.purchasePriceJY,
                        "extend2"   : carData.vehicleClassCode,
                        "extend3"   : carData.vehicleClassName

                ],
                "policyList"       : [
                        [
                                //估计是 是否是赠险标识
                                "isGiveFlag"  : "",
                                "persons"     : [person],
                                "policy"      : [
                                        "activityCode"    : status.productDetail.activitiesCode,
                                        "activityVersion" : status.productDetail.activitiesVersion,
                                        "agentCode"       : carData.agentCode,
                                        "branchCode"      : carData.branchCode,
                                        "channelCode"     : carData.channelCode,
                                        "elcIsCombine"    : "0",
                                        "endDate"         : carData.endDate,
                                        "groupOrderId"    : carData.groupOrderId,
                                        "handlerCode"     : carData.handlerCode,
                                        "ifNewProduct"    : 1,
                                        "iscombineProduct": "0",
                                        "offsitBranchCode": carData.branchCode,
                                        "payBillOrNot"    : "0",
                                        "planCode"        : status.plan.planCode,
                                        "planGroupCode"   : status.planGroup.planGroupCode,
                                        "planVersion"     : status.plan.planVersion,
                                        "plcCopies"       : status.planList.salesNumber,
                                        "plcElcflag"      : 0,
                                        "plcAmount"       : status.planList.planTotalAmount,
                                        "plcPremium"      : status.planList.planTotalPremium,
                                        "productCode"     : status.product.productCode,
                                        "productVersion"  : status.product.version,
                                        "recordWay"       : carData.recordWay,
                                        "saleType"        : carData.saleType,
                                        "sourceType"      : carData.sourceType,
                                        "startDate"       : carData.startDate,
                                        "tempId"          : status.productDetail.temp.id
                                ],
                                "policyExtend": [
                                        "handlerName"       : "",
                                        "dcLicenseNbr"      : "",
                                        "dcOrgName"         : "",
                                        "validateIspqc"     : "1",
                                        "isCarAndNoCarPrize": "1"
                                ],
                                "policyExtra" : [
                                        "extend2"          : "",
                                        "extend3"          : "",
                                        "extend4"          : "",
                                        "extend5"          : "",
                                        "plnInvoiceType"   : "0",
                                        "jkxSplit"         : "0",
                                        "ifDesensitization": "0"
                                ],
                                "underWrite"  : [
                                        "activitiesList": [
                                                [
                                                        "activitiesCode"   : status.productDetail.activitiesCode,
                                                        "activitiesVersion": status.productDetail.activitiesVersion,
                                                        "branchCode"       : carData.branchCode,
                                                        "partnerCode"      : status.productDetail?.activitiesInfo?.partnerCode,
                                                        "sellChannelCode"  : carData.channelCode,
                                                        "plcInuredType"    : carData.insuranceType,
                                                        "unilateralCode"   : status.productDetail.unilateralCode,
                                                        "productList"      : [
                                                                [
                                                                        "msbProductCode"   : status.product.productCode,
                                                                        "msbProductVersion": status.product.productVersion,
                                                                        "planGroupList"    : [
                                                                                [
                                                                                        "plangroupcode": status.productDetail.plangroupcode,
                                                                                        "planList"     : [
                                                                                                plan
                                                                                        ]
                                                                                ]
                                                                        ]

                                                                ]
                                                        ]
                                                ]
                                        ]
                                ]
                        ]
                ]
        ]
    }

    /**
     * 选择非车产品
     * @param status 状态信息
     * @param nonMotorFetch
     */
    private static void selectNonMotorProduct(HashMap<String, Object> status, Closure<JSONObject> standardFetch, Closure<JSONObject> encryptFetch) {
        //根据产品列表获取产品信息
        def getPrdsByChannelNewJsonObject = standardFetch(
                URL_DICT.GET_PRDS_BY_CHANNEL_NEW,
                'post',
                [
                        'Content-Type': 'application/json;charset=UTF-8',
                        'Host'        : 'dwx.cpic.com.cn',
                        'Origin'      : 'https://dwx.cpic.com.cn'
                ],
                null,
                makeRequestBodyGetPrdsByChannelNew(status)
        )
        responseHandlerGetPrdsByChannelNew(status, getPrdsByChannelNewJsonObject)

        def getHeader = [
                'Host'   : 'dwx.cpic.com.cn',
                'Origin' : 'https://dwx.cpic.com.cn',
                'Referer': makeReferer(status)
        ]
        def postHeader = [
                'Content-Type': 'application/json;charset=UTF-8',
                'Host'        : 'dwx.cpic.com.cn',
                'Origin'      : 'https://dwx.cpic.com.cn',
                'Referer'     : makeReferer(status)
        ]
        def responseHandlerForPutData = responseHandlerPutData(status)
        //获取产品详情
        def initPrdJsonObject = encryptFetch(
                URL_DICT.INIT_PRD,
                'get',
                getHeader,
                makeRequestBodyInitPrd(status),
                null,
        )
        responseHandlerForPutData(initPrdJsonObject, "productDetail")

        def getInitParamsJsonObject = encryptFetch(
                URL_DICT.GET_INIT_PARAMS,
                'post',
                postHeader,
                null,
                makeRequestBodyGetInitParams(status)
        )
        responseHandlerForPutData(getInitParamsJsonObject, "initParams")
        responseHandlerGetInitParams(status)

        //获取产品基本信息
        def getProductBaseJsonObject = encryptFetch(
                URL_DICT.GET_PRODUCT_BASE,
                'post',
                getHeader,
                makeRequestBodyGetProductBase(status),
                null
        )
        responseHandlerForPutData(getProductBaseJsonObject, "productBase")
        log.info("productBase:{}", JSON.toJSONString(status.productBase))
        responseHandlerGetProductBase(status)

        def getPlanFormJsonObject = encryptFetch(
                URL_DICT.GET_PLAN_FORM,
                'get',
                getHeader,
                makeRequestBodyGetPlanForm(status),
                null
        )
        responseHandlerPlanForm(status, getPlanFormJsonObject)

        def getPlanCoverageJsonObject = encryptFetch(
                URL_DICT.GET_PLAN_COVERAGE,
                'get',
                getHeader,
                makeRequestBodyGetPlanCoverage(status),
                null
        )
        responseHandlerGetPlanCoverage(status, getPlanCoverageJsonObject)

    }

    private static void responseHandlerGetInitParams(Map status) {
        def initParams = status.initParams as JSONObject
        def vehicleKindJQMapping = initParams.getString('vehicleKindJQMapping')
        def vehicleKindSYMapping = initParams.getString('vehicleKindSYMapping')
        def vehicleUsageJQMapping = initParams.getString('vehicleUsageJQMapping')
        def vehicleUsageSYMapping = initParams.getString('vehicleUsageSYMapping')
        def vehicleKindMapping = JSON.parseObject(vehicleKindJQMapping ?: vehicleKindSYMapping)
        def vehicleUsageMapping = JSON.parseObject(vehicleUsageJQMapping ?: vehicleUsageSYMapping)
        status.putAll([
                "vehicleKindJQMapping" : JSON.parseObject(vehicleKindJQMapping ?: "{}"),
                "vehicleKindSYMapping" : JSON.parseObject(vehicleKindSYMapping ?: "{}"),
                "vehicleUsageJQMapping": JSON.parseObject(vehicleUsageJQMapping ?: "{}"),
                "vehicleUsageSYMapping": JSON.parseObject(vehicleUsageSYMapping ?: "{}"),
                "vehicleKindMapping"   : vehicleKindMapping,
                "vehicleUsageMapping"  : vehicleUsageMapping
        ])

        log.info("initParams:{}", initParams.toJSONString())
    }

    private static void responseHandlerGetPlanCoverage(Map status, JSONObject response) {
        if (response.getInteger("code") != 0) {
            fail(response)
        }
        def planCoverage = response.getJSONArray("data").get(0)
        status["planCoverage"] = planCoverage
    }

    private static def makeRequestBodyGetPlanCoverage(status) {
        return [
                "planCode"      : status.plan.planCode,
                "planVersion"   : status.plan.planVersion,
                "planGroupCode" : status.planGroup.planGroupCode,
                "productCode"   : status.product.productCode,
                "productVersion": status.product.version,
                "sellType"      : status.planGroup.sellType
        ]
    }

    private static void responseHandlerPlanForm(Map status, JSONObject response) {
        if (response.getInteger("code") != 0) {
            fail(response)
        }
        def planForm = response.getJSONObject("data")
                .getJSONArray("planList")
                .get(0)
        status["planForm"] = planForm
    }

    private static void responseHandlerGetProductBase(Map status) {
        def productBase = status.productBase as JSONObject
        def planCode = status.baseInfo.planCode
        def planGroupList = productBase.getJSONArray("planGroupList")
        for (int i = 0; i < planGroupList.size(); i++) {
            def planGroup = planGroupList.getJSONObject(i)
            def planList = planGroup.getJSONArray("planList")
            for (int j = 0; j < planList.size(); j++) {
                def plan = planList.getJSONObject(j)
                if (plan.getString("planCode") == planCode) {
                    status.planGroup = planGroup
                    status.plan = plan
                    break
                }
            }
            if (status.plan) {
                break
            }
        }
    }

    private static def makeRequestBodyGetPlanForm(status) {
        return [
                "planCode"      : status.plan.planCode,
                "planVersion"   : status.plan.planVersion,
                "planGroupCode" : "",
                "productCode"   : status.product.productCode,
                "productVersion": status.product.version,
                "sellType"      : status.planGroup.sellType
        ]
    }


    private static def makeRequestBodyGetProductBase(status) {
        return [
                "productCode": status.product.productCode,
                "sourceType" : status.carData.sourceType,
                "version"    : status.product.version
        ]
    }

    private static Closure responseHandlerPutData(Map status) {
        return {
            JSONObject response, String key ->
                if (response.getInteger("code") != 0) {
                    fail(response)
                }
                status.put(key, response.getJSONObject("data"))
        }
    }

    private static def makeRequestBodyGetInitParams(Map status) {
        def product = status.product
        def productDetail = status.productDetail
        def carData = status.carData
        return [
                "productCode": product.productCode,
                "sourceType" : carData.sourceType,
                "saleType"   : carData.saleType,
                "branchCode" : carData.branchCode,
                "classesCode": productDetail.classesCode
        ]
    }

    private static void responseHandlerGetPrdsByChannelNew(HashMap<String, Object> status, JSONObject response) {
        if (response.getInteger('code') != 0) {
            fail(response, '太保精灵非车险产品查询错误')
        }
        def productList = response.getJSONArray("data")
        if (CollUtil.isEmpty(productList)) {
            throw new InsReturnException(InsReturnException.Others, "太保精灵非车险产品查询错误")
        }
        def productCode = status.baseInfo.productCode
        def product = productList.find { item -> item["productCode"] == productCode }
        if (!product) {
            throw new InsReturnException(InsReturnException.Others, "太平洋精灵非车险查询错误，不支持该产品代码：${productCode}")
        }
        status["product"] = product
    }

    /**
     * 跳转非车投保页面
     * @param status
     * @param standardFetch
     * @param encryptFetch
     */
    private static void jumpToNonMotorPage(HashMap<String, Object> status, Closure<JSONObject> issueEncryptFetch, Closure<JSONObject> encryptFetch) {
        //查询跳转链接和初始化基本信息
        def queryProductDetailJsonObject = issueEncryptFetch(
                URL_DICT.QUERY_PRODUCT_DETAIL,
                'post',
                [
                        'Content-Type'    : status.baseInfo.needSM4Flag ? 'text/plain' : 'application/json;charset=UTF-8',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Origin'          : 'https://issue.cpic.com.cn',
                        'Referer'         : 'https://issue.cpic.com.cn/ecar/view/portal/page/car_insurance/insurancePlan.html'
                ],
                null,
                makeRequestBodyQueryProductDetail(status)
        )
        responseHandlerQueryProductDetail(status, queryProductDetailJsonObject)

        if (status.needGetCarTempParam) {
            //获取hashCode
            def getCarTempParamJsonObject = encryptFetch(
                    URL_DICT.GET_CAR_TEMP_PARAM,
                    'post',
                    [
                            'Host'        : 'dwx.cpic.com.cn',
                            'Content-Type': 'application/json;charset=UTF-8',
                            'Origin'      : 'https://dwx.cpic.com.cn'
                    ],
                    null,
                    ["busiNo": status.carData.hashCode]
            )
            responseHandlerGetCarTempParam(status, getCarTempParamJsonObject)
        }

        def carData = status["carData"] as Map
        def data = JSON.parseObject(Robot2011Util.aesDecrypt(ISSUE_KEY, carData.decodeHashCode as String))
        log.info("carData:{}", data.toJSONString())
        carData.putAll([
                "branchCode"      : data.getString("brchCode"),
                "agentCode"       : data.getString("agentCode"),
                "saleType"        : data.getString("saleType"),
                "groupOrderId"    : data.getString("groupOrderId"),
                "channelCode"     : data.getString("channelCode"),
                "handlerList"     : data.getJSONArray("handlerList"),
                "startDate"       : data.getString("insuranceStartDate"),
                "endDate"         : data.getString("insuranceEndDate"),
                "carVin"          : data.getString("carVin"),
                "engineNo"        : data.getString("engineNo"),
                "plateNo"         : data.getString("plateNo"),
                "vehicleClassCode": data.getString("vehicleClassCode"),
                "vehicleKind"     : data.getString("vehicleKind"),
                "vehicleUsage"    : data.getString("vehicleUsage"),
                "personType"      : data.getString("personType"),
                "seatCount"       : data.getInteger("seatCount"),
                "hdlrCode"        : data.getString("hdlrCode"),
                "insurCardType"   : data.getString("insurCardType"),
                "insurCardNo"     : data.getString("insurCardNo"),
                "insurName"       : data.getString("insurName"),
                "vehicleClassName": data.getString("vehicleClassName"),
                "purchasePriceJY" : data.getString("purchasePriceJY"),
        ])
        status.put("temp_carData", data)
    }

    private static void responseHandlerGetCarTempParam(Map status, JSONObject response) {
        if (response.getInteger("code") != 0) {
            fail(response, '太平洋精灵非车险查询错误，无法获取hashCode')
        }
        def data = response.getString("data")
        def carData = status.carData as Map
        carData.putAll([
                "hashCode"      : data,
                "decodeHashCode": data
        ])
    }

    private static void responseHandlerQueryProductDetail(Map status, JSONObject response) {
        def result = response.getJSONObject('result')
        if (!result) {
            def message = response.getJSONObject("message")?.getString("message")
            throw new InsReturnException(message)
        }
        String returnURL = Base64.decodeStr(result.getString('returnURL'), RobotConstant_2011.DEFAULT_CHARSET)
        def index = returnURL.indexOf('sourceType=')
        def sourceType = returnURL.substring(index + 'sourceType='.length(), index + 'sourceType='.length() + 4)
        def binary = result.getString("binary")
        status.putAll([
                "carData"            : [
                        "hashCode"      : binary,
                        "decodeHashCode": URLDecoder.decode(binary, RobotConstant_2011.DEFAULT_CHARSET_NAME),
                        "sourceType"    : sourceType,
                ],
                "needGetCarTempParam": !returnURL.contains('hashCode')
        ])
    }

    private static def makeRequestBodyQueryProductDetail(Map status) {
        def sendStrParam = [
                'meta'  : [:],
                'redata': [
                        'quotationNo': status.baseInfo.quotationNo,
                        'userIp'     : 'issue.cpic.com.cn'
                ]
        ]
        //展业方式，如提示需展业方式则需要
        if (status.baseInfo.detail4) {
            sendStrParam.redata.putAll([
                    "detail4"        : status.baseInfo.detail4,
                    "noCarAgencyCode": status.baseInfo.noCarAgencyCode,
                    "agentCode"      : status.baseInfo.agentCode
            ] as Map<? extends String, ? extends String>)
        }
        return sendStrParam
    }

    private static def makeRequestBodyInitPrd(Map status) {
        return [
                plcPlanCode  : status.product.productCode,
                version      : status.product.version,
                sourceType   : status.carData.sourceType,
                agentCode    : status.carData.agentCode,
                channelCode  : status.carData.channelCode,
                hashCode     : status.carData.decodeHashCode,
                diyEmployeeno: '',
                isProductList: 1
        ]
    }

    static def makeRequestBodyGetPrdsByChannelNew(Map status) {
        return [
                "sourceType": status.carData.sourceType,
                "hashCode"  : status.carData.decodeHashCode
        ]
    }


    private static String makeReferer(Map status) {
        return "https://dwx.cpic.com.cn/cxNew/dwx/mobilemsbv2/?plcPlanCode=${status.product.productCode}&sourceType=${status.carData.sourceType}&version=${status.product.version}&hashCode=&agentCode=${status.carData.agentCode}&channelCode=${status.carData.channelCode}&isProductList=1"
    }

    /**
     * 通用响应处理
     *
     * @param response 响应密文信息
     * @return 返回解密后的JsonObject
     */
    private static Closure<JSONObject> handleResponse(String key = DWX_KEY) {
        return {
            String response ->
                if (response.startsWith("{")) {
                    def respJson = JSONObject.parseObject(response)
                    if (respJson?.code == 999) {
                        throw new InsReturnException(InsReturnException.Others, "太保系统异常")
                    }
                    def msg = respJson?.getString("message")
                    log.warn(msg)
                    throw new InsReturnException("太保系统异常: ${msg}")
                }
                def respStr = Robot2011Util.removeQuotationMarks(response)
                respStr = Robot2011Util.aesDecrypt(key, respStr)
                return JSONObject.parseObject(respStr)
        }
    }

    private static Closure<JSONObject> handleResponseIssue(String key) {
        return {
            String response ->
                if (key) {
                    response = Robot2011Util.decodeBody(key, response)
                }
                return JSONObject.parseObject(response)
        }
    }

    /**
     * 构建请求报文
     * @param params 请求参数
     * @param key 加密密钥
     * @param needMark 是否需要加引号
     * @return 请求字符串报文
     */
    private static Closure<String> buildRequestBody(String key = DWX_KEY, boolean needMark = true) {
        { Object params ->
            def sendStr = JSON.toJSONString(params)
            log.info("请求报文：${sendStr}")
            if (key) {
                sendStr = Robot2011Util.aesEncrypt(key, sendStr)
            }
            return needMark ? '"' + sendStr + '"' : sendStr
        }
    }

    private static Closure<String> buildRequestBodyIssue(String key) {
        { Object params ->
            def sendStr = JSON.toJSONString(params)
            log.info("请求报文：${sendStr}")
            if (key) {
                sendStr = Robot2011Util.genBody(key, sendStr)
            }
            return sendStr
        }
    }
    /**
     * 发起请求
     * @param httpClient
     * @param buildRequestBody
     * @param handlerResponse
     * @return
     */
    private static Closure fetch(CloseableHttpClient httpClient, Closure<String> buildRequestBody, Closure<JSONObject> handlerResponse) {
        def requestBodyHandler = buildRequestBody ?: { Object reqBody -> JSON.toJSONString(reqBody) }
        def responseHandler = handlerResponse ?: { String responseBody -> JSON.parseObject(responseBody) }
        return {
            String url,
            String httpMethod,
            Map header,
            Map params,
            Map requestBody
                ->
                String reqBody = null
                if (requestBody) {
                    reqBody = requestBodyHandler(requestBody)
                }
                def responseBody = Robot2011Util.fetch(httpClient, url, header, httpMethod, params, reqBody)
                JSONObject result = null
                try {
                    result = responseHandler(responseBody)
                } catch (InsReturnException e) {
                    if (e.code == InsReturnException.Others) {
                        responseBody = Robot2011Util.fetch(httpClient, url, header, httpMethod, params, reqBody)
                        result = responseHandler(responseBody)
                    }
                }
                return result
        }
    }


    private static List generateProductFeatureBaseInfo(Map status) {
        def planForm = status.planForm as JSONObject
        def result = []
        def form = planForm.getJSONObject("form")
        def factorMap = form.getJSONObject("factorMap")
        if (factorMap) {
            factorMap.each { k, v ->
                if (k == 'EA') {
                    result.add([
                            "code" : "jsonContent",
                            "value": JSON.toJSONString(["EA": status.carData.plateNo])
                    ])
                }
                if (k == 'NI') {
                    def jo = factorMap.getJSONObject(k)
                    result.add([
                            "code" : "jsonContent",
                            "value": JSON.toJSONString(["NI": jo.getString("value")])
                    ])
                }
            }
        }
        return result
    }

    private static Map generateSubjectWrapper(Map status) {
        def onlyCI = status.baseInfo.onlyCI
        def carInfo = status["carInfo"] = [:]
        def planForm = status.planForm as JSONObject
        def form = planForm.getJSONObject("form")
        def featureModeProps = form.getJSONObject("featureModeProps")
        def policyFeatureMap = form.getJSONObject("policyFeatureMap")
        def carData = status.carData as Map
        def result = [:]
        featureModeProps.forEach { k, v ->
            if (k == "vehicleInfoList") {
                def vehicleInfoList = policyFeatureMap.getJSONArray("vehicleInfoList")
                if (CollUtil.isNotEmpty(vehicleInfoList)) {
                    def vehicleInfoForm = vehicleInfoList.getJSONObject(0)
                    def vehicleInfo = []
                    String vehicleKindKey = 'k' + carData.vehicleKind
                    String vehicleUsageKey = "usage" + carData.vehicleUsage
                    vehicleInfoForm.forEach { key, value ->
                        if (key == "engineNo") {
                            vehicleInfo.add([
                                    "code" : key,
                                    "value": carData.engineNo
                            ])
                        }
                        if (key == "frameNumber") {
                            vehicleInfo.add([
                                    "code" : key,
                                    "value": carData.carVin
                            ])
                        }
                        if (key == "licensePlateNo") {
                            vehicleInfo.add([
                                    "code" : key,
                                    "value": carData.plateNo
                            ])
                        }
                        if (key == "seatNum") {
                            vehicleInfo.add([
                                    "code" : key,
                                    "value": carData.seatCount
                            ])
                        }
                        if (key == "licensePlateColor") {
                            def plateColor = PLATE_COLOR_MAP.get(carData.plateColor) ?: 'C0000'
                            carInfo.plateColor = plateColor
                            vehicleInfo.add([
                                    "code" : key,
                                    "value": plateColor
                            ])
                        }
                        if (key == "vehicleCategory") {
                            def vehicleKindJQMapping = status.vehicleKindJQMapping as JSONObject
                            def vehicleKindSYMapping = status.vehicleKindSYMapping as JSONObject
                            def vehicleKindMapping = onlyCI
                                    ? (vehicleKindJQMapping?.isEmpty() ? vehicleKindSYMapping : vehicleKindJQMapping)
                                    : (vehicleKindSYMapping?.isEmpty() ? vehicleKindJQMapping : vehicleKindSYMapping)
                            if (vehicleKindMapping) {
                                def vehicleKind = vehicleKindMapping.getString(vehicleKindKey)
                                if (vehicleKind) {
                                    carInfo.vehicleKind = vehicleKind
                                    vehicleInfo.add([
                                            "code" : key,
                                            "value": vehicleKind
                                    ])
                                }
                            }
                        }
                        if (key == "vehicleUsage") {
                            def vehicleUsageJQMapping = status.vehicleUsageJQMapping as JSONObject
                            def vehicleUsageSYMapping = status.vehicleUsageSYMapping as JSONObject
                            boolean isExistKeyInJQ = vehicleUsageJQMapping.containsKey(vehicleUsageKey)
                            boolean isExistKeyInSY = vehicleUsageSYMapping.containsKey(vehicleUsageKey)
                            def vehicleUsageMapping
                            def uv
                            if (isExistKeyInJQ && isExistKeyInSY) {
                                vehicleUsageMapping = onlyCI ? vehicleUsageJQMapping : vehicleUsageSYMapping
                            } else if (isExistKeyInJQ) {
                                vehicleUsageMapping = vehicleUsageJQMapping
                            } else if (isExistKeyInSY) {
                                vehicleUsageMapping = vehicleUsageSYMapping
                            } else {
                                vehicleUsageMapping = vehicleUsageJQMapping
                            }
                            def vehicleUsage = vehicleUsageMapping.getJSONObject(vehicleUsageKey)
                            def vu = vehicleUsage.getString("F7000")
                            if (!vu && vehicleUsage.containsKey("kind")) {
                                def kind = vehicleUsage.getJSONObject("kind")
                                if (kind.containsKey(vehicleKindKey)) {
                                    def values = kind.getJSONArray(vehicleKindKey)
                                    vu = values.find { ((String) it).startsWith("F") }
                                }
                            }
                            if (!vu) {
                                vu = 'F7000'
                            }
                            carInfo.vehicleUsage = vu
                            vehicleInfo.add([
                                    "code" : key,
                                    "value": vu
                            ])
                        }
                    }
                    result["vehicleInfoList"] = Collections.singletonList(vehicleInfo)
                }
            }

            if (k == "subjectGroupList") {
                def subjectGroupList = featureModeProps.getJSONObject(k)
                result["subjectType"] = subjectGroupList.getString("subjectType")
                result["constraintLevel"] = subjectGroupList.getString("constraintLevel") + "#0"
                def subjectGroupListData = []
                def layerList = form.getJSONArray("layerList")
                for (int i = 0; i < layerList.size(); i++) {
                    def layer = layerList.getJSONObject(i)
                    def layerCode = layer.getString("layerCode")
                    def values = []
                    def featureMap = layer.getJSONObject("featureMap")
                    for (int j = 0; j < featureMap.size(); j++) {
                        def feature = featureMap.getJSONArray("subjectGroupList")
                        def subjectGroup = feature.getJSONObject(0)
                        subjectGroup.keySet().forEach { field ->
                            def value = [
                                    "code"          : field,
                                    "subjectGroupNo": layerCode
                            ] as HashMap<String, Object>
                            if (field == "occupationType") {
                                value["value"] = subjectGroup.getJSONObject(field).getString("value")
                            }
                            if (field == "insuredQuantity") {
                                if (layerCode == "L001") {
                                    value["value"] = "1"
                                } else if (layerCode == "L002") {
                                    value["value"] = (carData.seatCount as Integer) - 1
                                }
                            }
                            values.add(value)
                        }
                    }
                    subjectGroupListData.add(values)
                }
                result["subjectGroupList"] = subjectGroupListData
            }
        }
        return result
    }

    private static void fail(JSONObject response, String prevMsg = '太平洋精灵非车险投保失败') {
        def msg = response?.getString("msg") ?: ''
        log.info("非车险投保响应异常[{}]", response?.toJSONString() ?: '')
        throw new InsReturnException(InsReturnException.Others, "${prevMsg}[${msg}]")
    }

    private static def initBaseInfo(AutoTask autoTask) {
        def enquiry = autoTask.taskEntity as Enquiry
        def misc = enquiry.misc
        def supplyParam = misc['supplyParam']
        def nonMotor = misc['nonMotor']
        def findFromSupplyParam = { key ->
            if (!supplyParam) {
                return ''
            }

            def find = supplyParam.find({ item -> item['key'] == key })
            return find ? find['value'] as String : ''
        }

        def taskType = autoTask.taskType.split(/-/)[-1]
        def mobile
        def email
        if (taskType in ['insure', 'autoinsure']) {
            mobile = findFromSupplyParam('applicantMobile')
            email = findFromSupplyParam('applicantEmail') ?: autoTask.configs['defaultEmail']
        } else {
            mobile = PhoneUtil.getMobile()
            email = autoTask.configs['defaultEmail']
        }
        def dict = new robot_2011_dict()
        def planCode = dict.nonMotorProductCodeMap(nonMotor['productCode'] as String)
        def queryMsgAgent = autoTask?.tempValues?.queryMsgAgent as Map
        def onlyCI = autoTask.tempValues.ExistCI && !autoTask.tempValues.ExistBI
        return [
                //报价单号
                quotationNo    : autoTask.tempValues.quotationNo,
                //展业方式
                detail4        : autoTask.configs.detail4 ?: queryMsgAgent?.detail4,
                noCarAgencyCode: autoTask.configs.agentCode ?: queryMsgAgent?.noCarAgencyCode,
                agentCode      : autoTask.configs.agent_code ?: queryMsgAgent?.msbAgentVoList?.get(0)?.agentCode,
                //投保人信息
                insurePerson   : enquiry.order.insurePerson,
                mobile         : mobile,
                email          : email,
                planCode       : planCode,
                productCode    : PLAN_TO_PRODUCT_DICT.get(planCode),
                //单交强
                onlyCI         : onlyCI,
                needSM4Flag    : autoTask.tempValues.needSM4Flag,
                sm4Key         : autoTask.tempValues.sm4Key,
        ]
    }

}
