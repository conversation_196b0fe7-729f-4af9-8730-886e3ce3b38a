package taipingyang.robot

import cn.hutool.core.util.ObjectUtil
import com.alibaba.fastjson.JSONObject
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import groovy.transform.Field
import taipingyang.robot.module.Robot2011Util

/**
 * 续保流程的精友车型查询响应模版
 * <AUTHOR>
 */
@BaseScript BaseScript_Http_Enq _

@Field JSONObject resp = null

check Robot2011Util.serializer(tempValues), {
    resp = it
    if (ObjectUtil.isEmpty(resp)) {
        fail('精友车型信息查询失败')
    }
    assertTrue('精友车型信息查询失败', 'success' == resp?.message?.code)
}

output resp.getJSONObject('result')