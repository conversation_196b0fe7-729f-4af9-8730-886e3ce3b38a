package renbao.edi


import cn.hutool.core.util.IdcardUtil
import com.cheche365.bc.exception.InsReturnException
import org.springframework.util.Assert
import renbao.common.renbao_tools

import java.time.LocalDate
import java.time.format.DateTimeFormatter

import static common.common_all.checkEnergyType
import static common.common_all.getAddress
import static common.common_all.getAge
import static common.common_all.getBirth
import static common.common_all.getMobile
import static common.common_all.getSex
import static common.common_all.getSupplyParam
import static common.common_all.shareAmount
import static renbao.common.renbao_dict.drivLicenseCode
import static renbao.common.renbao_dict.formatPlateColor
import static renbao.common.renbao_dict.getCarKindCode
import static renbao.common.renbao_dict.getCountryCode
import static renbao.common.renbao_dict.getPlateType
import static renbao.common.renbao_dict.identifyType
import static renbao.common.renbao_dict.kindCodeNew
import static renbao.common.renbao_dict.kindNameNew
import static renbao.common.renbao_dict.taxPayerNature
import static renbao.common.renbao_dict.useNatureCode
import static renbao.common.renbao_dict.useProp
import static renbao.common.renbao_util.getIDCardValidDate



def getBiEnd(enquiry) {
    def dateTime = enquiry?.baseSuiteInfo?.bizSuiteInfo?.start
    def dateTimeFormatter = DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss')
    def localDate = LocalDate.parse(dateTime, dateTimeFormatter)
    def source
    if (dateTime.contains("00:00:00")) {
        // 结束时间需开始时间+1年-1天24时
        // 如果是闰年2月29日 加1年不减1天
        if (localDate.isLeapYear() && localDate.monthValue == 2 && localDate.dayOfMonth == 29) {
            source = localDate.plusYears(1).format(DateTimeFormatter.ofPattern('yyyy-MM-dd'))
        } else {
            source = localDate.plusYears(1).plusDays(-1).format(DateTimeFormatter.ofPattern('yyyy-MM-dd'))
        }

    } else {
        // 结束时间需开始时间+1年24时
        if (localDate.isLeapYear() && localDate.monthValue == 2 && localDate.dayOfMonth == 29) {
            source = localDate.plusYears(1).plusDays(1).format(DateTimeFormatter.ofPattern('yyyy-MM-dd'))
        } else {
            source = localDate.plusYears(1).format(DateTimeFormatter.ofPattern('yyyy-MM-dd'))
        }

    }
    enquiry?.baseSuiteInfo?.bizSuiteInfo?.end = source + " 23:59:59"
    return source
}

def configs = enquiry?.configInfo?.configMap
def script = new edi_common_2005()
def tools = new renbao_tools()
def queryId = UUID.randomUUID().toString()
def userCode = configs?.UserCode ?: ''
def channelCode = configs?.ChannelCode ?: ''
def handler1Code = configs?.Handler1Code ?: ''
def handler1Code_uni = configs?.Handler1Code_uni ?: ''
def handlerCode_uni = configs?.HandlerCode_uni ?: ''
def configTel = getMobile()
def configAddress = configs?.address
def comCode = configs?.ComCode ?: ''
def engineNo = enquiry?.carInfo?.engineNum
def frameNo = enquiry?.carInfo?.vin

//未上牌 车牌号=发动号后六位 否则会判断为异地车
def licenseNo = enquiry?.carInfo?.plateNum
if ('新车未上牌' == enquiry?.carInfo?.plateNum) {
    def tempEngine = engineNo.toString().replaceAll('-', '')
    if (tempEngine.length() < 6) {
       throw new InsReturnException('发动机号长度不足6位')
    }
    licenseNo = tempEngine.substring(tempEngine.length() - 6, tempEngine.length())
    if ('510000' == enquiry.insArea['province']) {
        def sichuanLicensePrefix = tools.sichuanLicensePrefix(enquiry.insArea['city'])
        licenseNo = sichuanLicensePrefix + tempEngine.substring(tempEngine.length() - 5, tempEngine.length())
    }
    if (['410000', '210000'].contains(enquiry.insArea['province'])) {
        licenseNo = engineNo.substring(engineNo.length() - 6, engineNo.length())
    }

    if (['310000', '500000'].contains(enquiry.insArea['province'])) {
        licenseNo = "暂" + licenseNo
    }

    if (['440100', '330200'].contains(enquiry.insArea['city'])) {
        licenseNo = "暂未上牌"
    }

    if ('350200' == enquiry.insArea['city']) {
        licenseNo = '闽D新车' + engineNo.substring(engineNo.length() - 6, engineNo.length())
    }

    if ('120000' == enquiry.insArea['province']) {
        licenseNo = '津' + frameNo.substring(frameNo.length() - 7, frameNo.length())
    }
}
def licenseType = getPlateType(enquiry?.carInfo?.plateType)

if (enquiry?.tempValues?.LicenseType) {
    licenseType = enquiry?.tempValues?.LicenseType
}
def licenseColorCode = formatPlateColor(enquiry?.carInfo?.plateColor)
//    def licenseColorCode = "03"//测试环境临时限定车牌颜色为白色可以自核通过

def carKindCode = enquiry?.carInfo?.syvehicletypename ? (enquiry?.carInfo?.syvehicletypename?.indexOf("货车") > -1  ? "B01" : getCarKindCode(enquiry?.carInfo?.carModelName)) : getCarKindCode(enquiry?.carInfo?.carModelName)

if (enquiry?.tempValues?.VehicleCategory) {
    carKindCode = enquiry?.tempValues?.VehicleCategory
}

def countryNature = (frameNo.toString().startsWith("L") || frameNo.toString().startsWith("H")) ? "B" : "A"
def startDateBI = enquiry?.baseSuiteInfo?.bizSuiteInfo?.start ? script.getStartDay(enquiry.baseSuiteInfo.bizSuiteInfo.start) : ""
def startHourBI = enquiry?.baseSuiteInfo?.bizSuiteInfo?.start ? (script.getHour(enquiry?.baseSuiteInfo?.bizSuiteInfo?.start)) : ""
def startMinuteBI = enquiry?.baseSuiteInfo?.bizSuiteInfo?.start ? (script.getMinute(enquiry?.baseSuiteInfo?.bizSuiteInfo?.start)) : ""
def endDateBI = enquiry?.baseSuiteInfo?.bizSuiteInfo?.end ? getBiEnd(enquiry) : ""
def endHourBI = enquiry?.baseSuiteInfo?.bizSuiteInfo?.end ? 24 : ""
def startDateCI = enquiry?.baseSuiteInfo?.efcSuiteInfo?.start ? script.getStartDay(enquiry.baseSuiteInfo.efcSuiteInfo.start) : ""
def startHourCI = enquiry?.baseSuiteInfo?.efcSuiteInfo?.start ? (script.getHour(enquiry?.baseSuiteInfo?.efcSuiteInfo?.start)) : ""
def startMinuteCI = enquiry?.baseSuiteInfo?.efcSuiteInfo?.start ? (script.getMinute(enquiry?.baseSuiteInfo?.efcSuiteInfo?.start)) : ""
if (enquiry?.baseSuiteInfo?.efcSuiteInfo?.start && !(enquiry?.baseSuiteInfo?.efcSuiteInfo?.start?.contains('00:00:00'))) {
    def start = enquiry?.baseSuiteInfo?.efcSuiteInfo?.start
    def end = enquiry?.baseSuiteInfo?.efcSuiteInfo?.end
    def dateTimeFormatter = DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss')
    def localDate = LocalDate.parse(start as String, dateTimeFormatter)
    if (localDate.isLeapYear() && localDate.monthValue == 2 && localDate.dayOfMonth == 29) {
        def endDateStr = localDate.plusYears(1).plusDays(1).format(DateTimeFormatter.ofPattern('yyyy-MM-dd'))
        enquiry?.baseSuiteInfo?.efcSuiteInfo?.end = endDateStr + enquiry?.baseSuiteInfo?.efcSuiteInfo?.end?.substring(10, end.length())
    }
}
def endDateCI = enquiry?.baseSuiteInfo?.efcSuiteInfo?.end ? script.getStartDay(enquiry.baseSuiteInfo.efcSuiteInfo.end) : ""
def endHourCI = enquiry?.baseSuiteInfo?.efcSuiteInfo?.end ? (enquiry?.baseSuiteInfo?.efcSuiteInfo?.end?.toString()?.contains("23:59:59") ? 24 : startHourCI) : ""
def endMinuteCI = enquiry?.baseSuiteInfo?.efcSuiteInfo?.end ? (enquiry?.baseSuiteInfo?.efcSuiteInfo?.end?.toString()?.contains("23:59:59") ? 0 : startMinuteCI) : ""
def enrollDate = script.getStartDay(enquiry?.carInfo?.firstRegDate)
def clauseType = useProp(enquiry?.carInfo?.useProps)
def useNatureCode = useNatureCode(enquiry?.carInfo?.useProps)
def modelCode = enquiry?.carInfo?.jyCode
def brandName = enquiry?.carInfo?.carModelName
def purchasePrice = '2' == enquiry?.carInfo?.carPriceType?.toString() ? enquiry?.carInfo?.definedCarPrice?.toBigDecimal()?.setScale(2, BigDecimal.ROUND_HALF_UP)?.doubleValue() : (enquiry?.carInfo?.price ? enquiry?.carInfo?.price?.toBigDecimal()?.setScale(2, BigDecimal.ROUND_HALF_UP)?.doubleValue() : 0.00)
def seatCount = enquiry?.carInfo?.seatCnt
def carLotEquQuality = enquiry?.carInfo?.fullLoad ? enquiry?.carInfo?.fullLoad?.toBigDecimal()?.intValue() : 0
def exhaustScale = enquiry?.carInfo?.displacement ?  enquiry?.carInfo?.displacement?.toBigDecimal()?.setScale(4, BigDecimal.ROUND_HALF_UP)?.doubleValue() : 0.0000

// 需求14718
if (enquiry.insArea['province'] == '440000') {
    exhaustScale = (exhaustScale * 1000 as BigDecimal).intValue()
}

def makeCom = configs?.MakeCom
def chgOwnerFlag = enquiry?.carInfo?.isTransfer ? 1 : 0
def chgOwnerDate = chgOwnerFlag == 0 ? "" : script.getStartDay(enquiry?.carInfo?.transferDate)
def handlerCode = configs?.HandlerCode ?: ''
def carInsuredRelation = script.carInsuredRelation(enquiry)
def vehicleBrand = enquiry?.carInfo?.carModelName//车辆品牌
def licenseFlag = enquiry?.carInfo?.plateNum == '新车未上牌' ? 0 : 1
def calculateMode = enquiry?.carInfo?.plateNum == '新车未上牌' ? 'C2' : 'C1'
def taxPayerNature = taxPayerNature(enquiry?.carOwnerInfo?.idCardType)
def taxPayerCode = enquiry?.carOwnerInfo?.idCard
def taxPayerName = enquiry?.carOwnerInfo?.name
def ownerName = enquiry?.carOwnerInfo?.name
def ownerAge = [6, 8, 9, 10].contains(enquiry?.carOwnerInfo?.idCardType) ? "" : script.calculateAge(enquiry?.carOwnerInfo?.birthday)
def ownerInsuredNature = [6, 8, 9, 10].contains(enquiry?.carOwnerInfo?.idCardType) ? 2 : 1 //1个人 2 团体
def ownerAddress = taskType?.toString()?.contains("quote") ? configAddress : (script.ownerAdress(enquiry) ?: (script.applicantAdress(enquiry) ?: (script.insuredAdress(enquiry) ?: configAddress)))
def ownerIdentifyType = identifyType(enquiry?.carOwnerInfo?.idCardType)
def ownerIdentifyNumber = [8, 9, 10].contains(enquiry?.carOwnerInfo?.idCardType) ? "" : enquiry?.carOwnerInfo?.idCard
def ownerUnitType = [6, 8, 9, 10].contains(enquiry?.carOwnerInfo?.idCardType) ? "300" : "" // 单位性质，产品经理规定传100 机关团体
def ownerUnifiedSocialCreditCode = ownerInsuredNature == 1 ? "" : (enquiry?.carOwnerInfo?.idCardType == 6 ? "" : enquiry?.carOwnerInfo?.idCard)
def ownerOrganizeCode = enquiry?.carOwnerInfo?.idCardType == 6 ? enquiry?.carOwnerInfo?.idCard : ""
def ownerSex = ownerInsuredNature == 1 ? (enquiry?.carOwnerInfo?.sex ? (enquiry?.carOwnerInfo?.sex + 1) + '' : '1') : ""
def ownerCountryCode = [5, 12, 14].contains(enquiry?.carOwnerInfo?.idCardType) ? getCountryCode(enquiry?.carOwnerInfo?.idCardType, enquiry?.carOwnerInfo?.idCard) : '156'
def ownerBirth = enquiry?.carOwnerInfo?.birthday ?: ""
def ownerMobile = taskType?.toString()?.contains("quote") ? configTel : ((getSupplyParam(enquiry, 'ownerMobile') ?: getSupplyParam(enquiry, 'applicantMobile')) ?: getSupplyParam(enquiry, 'insuredMobile'))

def ownerIdCardValidDate = getIDCardValidDate(autoTask, 'owner')
def ownerDateValid = ownerIdCardValidDate['end']
def ownerDateValidStart = ownerIdCardValidDate['start']

def appBirth = enquiry?.insurePerson?.birthday ?: ""
def appName = enquiry?.insurePerson?.name
def appAge = [6, 8, 9, 10].contains(enquiry?.insurePerson?.idCardType) ? "" : script.calculateAge(enquiry?.insurePerson?.birthday)
def appAddress = taskType?.toString()?.contains("quote") ? configAddress : (script.applicantAdress(enquiry) ?:(script.insuredAdress(enquiry) ?: (script.ownerAdress(enquiry) ?: configAddress)))
def appInsuredNature = [6, 8, 9, 10].contains(enquiry?.insurePerson?.idCardType) ? 2 : 1 //1个人 2 团体
def appMobile = taskType?.toString()?.contains("quote") ? configTel : ((getSupplyParam(enquiry, 'applicantMobile') ?: getSupplyParam(enquiry, 'insuredMobile')) ?: getSupplyParam(enquiry, 'ownerMobile'))

def applicantIdCardValidDate = getIDCardValidDate(autoTask, 'applicant')
def appDateValid = applicantIdCardValidDate['end']
def appDateValidStart = applicantIdCardValidDate['start']

def appIdentifyType = identifyType(enquiry?.insurePerson?.idCardType)
def appIdentifyNumber = [8, 9, 10].contains(enquiry?.insurePerson?.idCardType) ? "" : enquiry?.insurePerson?.idCard
def appUnitType = [6, 8, 9, 10].contains(enquiry?.insurePerson?.idCardType) ? "300" : "" // 单位性质，产品经理规定传100 机关团体
def appUnifiedSocialCreditCode = appInsuredNature == 1 ? "" : (enquiry?.insurePerson?.idCardType == 6 ? "" : enquiry?.insurePerson?.idCard )
def appOrganizeCode = enquiry?.carOwnerInfo?.idCardType == 6 ? enquiry?.insurePerson?.idCard : ""
def appSex = appInsuredNature == 1 ? (enquiry?.insurePerson?.sex ? (enquiry?.insurePerson?.sex + 1) + '' : '1') : ""
def appCountryCode = [5, 12, 14].contains(enquiry?.insurePerson?.idCardType) ? getCountryCode(enquiry?.insurePerson?.idCardType, enquiry?.insurePerson?.idCard) : '156'
def insuredPerson = enquiry?.insuredPersonList?.get(0)//被保人信息
def insuredName =  insuredPerson?.name
def insuredAge = [6, 8, 9, 10].contains(insuredPerson?.idCardType) ? "" : script.calculateAge(insuredPerson?.birthday)
def insuredBirth = insuredPerson?.birthday ?: ""
def insuredInsuredNature = [6, 8, 9, 10].contains(insuredPerson?.idCardType) ? 2 : 1 // 1个人 2 团体
def insuredUnitType = [6, 8, 9, 10].contains(insuredPerson?.idCardType) ? "300" : "" // 单位性质，产品经理规定传100 机关团体
def insuredAddress = taskType?.toString()?.contains("quote") ? configAddress : (script.insuredAdress(enquiry) ?: (script.applicantAdress(enquiry) ?: (script.ownerAdress(enquiry) ?: configAddress)))
def insuredMobile = taskType?.toString()?.contains("quote") ? configTel : ((getSupplyParam(enquiry, 'insuredMobile') ?: getSupplyParam(enquiry, 'applicantMobile')) ?: getSupplyParam(enquiry, 'ownerMobile'))

def insuredIdCardValidDate = getIDCardValidDate(autoTask, 'insured')
def insuredDateValid = insuredIdCardValidDate['end']
def insuredDateValidStart = insuredIdCardValidDate['start']

def insuredIdentifyType = identifyType(insuredPerson?.idCardType)
def insuredIdentifyNumber = [8, 9, 10].contains(insuredPerson?.idCardType) ? "" : insuredPerson?.idCard
def insuredUnifiedSocialCreditCode = insuredInsuredNature == 1 ? "" :(insuredPerson?.idCardType == 6 ? "" : insuredPerson?.idCard)
def insuredOrganizeCode = insuredPerson?.idCardType == 6 ? insuredPerson?.idCard : ""
def insuredSex = insuredInsuredNature == 1  ? (insuredPerson?.sex ? (insuredPerson?.sex + 1) + '' : '1') : ""
def insuredCountryCode = [5, 12, 14].contains(insuredPerson?.idCardType) ? getCountryCode(insuredPerson?.idCardType, insuredPerson?.idCard) : '156'
def suiteList = enquiry?.baseSuiteInfo?.bizSuiteInfo?.suites
def efcBaseSuiteInfo = enquiry?.baseSuiteInfo?.efcSuiteInfo
def cstTime = script.getCurCSTtime()
def tonCount = enquiry?.carInfo?.modelLoad ?: 0
def carChecker = configs?.CarChecker
def energyTypePlat = tempValues?.energyTypePlat ?: checkEnergyType(enquiry?.carInfo?.carModelName, enquiry?.carInfo?.plateNum)
def energyFlag = energyTypePlat == "0" ? "" : "1"
def vehicleStyleUniqueId = ''
def vehicleStyleUniqueIdList = tempValues.vehicleStyleUniqueIdList
if (vehicleStyleUniqueIdList && vehicleStyleUniqueIdList?.size() > 0) {
    // 也许是正确的车型id，其他逻辑需要再次报价不能每次都取新的
    def uniqueIdIndex = tempValues?.uniqueIdIndex ?: 0
    tempValues?.uniqueIdIndex = uniqueIdIndex
    vehicleStyleUniqueId = (vehicleStyleUniqueIdList as List).get(uniqueIdIndex)
}

licenseType = energyFlag == "1" ? "52" : licenseType

if (enquiry?.tempValues?.LicenseType) {
    licenseType = enquiry?.tempValues?.LicenseType
}

def piccAreaCode = script.getPiccAreaCode(enquiry, config)
def monopolyCode = configs?.monopolyCode ?: ""
def monopolyFlag = monopolyCode ? "1" : ""
def monopolyName = monopolyCode ? (configs?.monopolyName ?: "") : ""
def clauseFlag = 1
def fuelType = enquiry?.carInfo?.carModelName?.contains("纯电动") ? "C" :(enquiry?.carInfo?.carModelName?.contains("混合动力") ? "O" : "A")
def queryArea = config?.QueryArea ?: enquiry.insArea['province']
def bizOrigin = configs?.BizOrigin ?: "2"
def businessPropertyCode = configs?.BusinessPropertyCode ?: (enquiry?.insArea?.city == '440100' ? "JG" : "")
def projectCode = config?.ProjectCode ?: ""
// 接人保通知，理想项目代码只有在统一价才传
def client = tempValues['requestSource'] ? tempValues['requestSource']['sourceChannel'] == 'lx' : false

if (enquiry['SQ'] && enquiry['SQ']['discountRates'] && !tempValues['hadAdjustDiscount']) {
    def bedRockDiscount
    // 手工录入整单折扣
    def inputPolicyDiscount = enquiry['SQ']['discountRates']['geniusItem.inputPolicyDiscount']
    if (inputPolicyDiscount)
        bedRockDiscount = inputPolicyDiscount
    // 整单折扣
    def policyDiscount = enquiry['SQ']['discountRates']['geniusItem.policyDiscount']
    if (!bedRockDiscount && policyDiscount)
        bedRockDiscount = policyDiscount
    if (bedRockDiscount) {
        tempValues['preDiscount'] = bedRockDiscount.toString().toBigDecimal() * 100
        // 报价前直接送折扣系数标志，报价完成后如果存在此标志则不再调用规则并调整折扣，此字段不影响车生态统一价实现
        tempValues['hadAdjustDiscount'] = true
    }
}
def preDiscount = tempValues['preDiscount'] ?: ''
if (client) {
    // 车生态，只有统一价才传projectCode
    projectCode = preDiscount ? projectCode : ''
}
def supplyAddress = getAddress(autoTask, 'applicantAddress')
def eastProvince = (supplyAddress && supplyAddress['province']) ? supplyAddress['province'] : enquiry?.insArea['province']
def eastCity = (supplyAddress && supplyAddress['city']) ? supplyAddress['city'] : enquiry?.insArea['city']
def eastCountry = (supplyAddress && supplyAddress['district']) ? supplyAddress['district'] : (Integer.valueOf(enquiry?.insArea['city'] as String) + 2) + ''

eastCountry = tools.fixDistrict(eastCountry)
def ownerContactName = ownerInsuredNature == 2 ? (getSupplyParam(enquiry, 'liaisonName') ?: (configs?.contactName ?: "张先生")) : ""
def appContactName = appInsuredNature == 2 ? (getSupplyParam(enquiry, 'liaisonName') ?: (configs?.contactName ?: "张先生")) : ""
def insuredContactName = insuredInsuredNature == 2 ? (getSupplyParam(enquiry, 'liaisonName') ?: (configs?.contactName ?: "张先生")) : ""

def nechargerDuty = false
def nechargerDamage = false

// 附加自用充电桩损失保险保额 变量名与保司保持一致
def pileLiabilityAmount

// 附加自用充电桩责任保险保额
def pileLossAmount

// 上海特殊字段
def vehicleTypeDescription = enquiry.insArea['province'] == '310000' ? "小型普通客车" : ""

def certificateDate = ''
if (enquiry.insArea['province'] == '310000' && enquiry.carInfo?.isNew) {
    certificateDate = getSupplyParam(enquiry, 'carOriginProofDate') ?: enrollDate
}
def carProofDate = getSupplyParam(enquiry, 'carOriginProofDate') ?: enrollDate
def taxPayerType = appIdentifyType ?: ""
def newCarFlag = enquiry.carInfo?.isNew ? "1" : "0"
def certificateNo = getSupplyParam(enquiry, 'carOriginProofNo') ?: (getSupplyParam(enquiry, 'carproofno') ?: "")
def drivLicenseCode = enquiry.insArea['province'] == '310000' ? drivLicenseCode(enquiry) : ""
def searchSequenceNo = tempValues['searchSequenceNo'] ?: ""
def taxType = 1
def taxAbateReason = ''
//  2022年华为项目验证上海传07，2025-03-31 运营验证上海核保报错 ：缴费方式值不在有效范围内，取消特殊逻辑
def payMethod = configs?.payMethod ?: ""

// 需求11612
def localUse = (getSupplyParam(enquiry, 'isLocalDriving') ?:  enquiry?.carInfo?.isLocalDriving) ?: '1'
def localLicense = (getSupplyParam(enquiry, 'isLocalRegistration') ?:  enquiry?.carInfo?.isLocalRegistration) ?: ('新车未上牌' == enquiry?.carInfo?.plateNum ? '1' : '')
def presaleCarFlag = tempValues['PresaleCarFlag'] ?: ''

// 需求13356
def nonLocalFlag = getSupplyParam(enquiry, 'isNonLocalFlag') ?: ''

// 需求14813
def insuranceAfterPayment = configs?.insuranceAfterPayment
def inceptionDateTime = enquiry?.baseSuiteInfo?.bizSuiteInfo?.start ?: enquiry?.baseSuiteInfo?.efcSuiteInfo?.start
def inceptionDate = inceptionDateTime.substring(0, 10)
def inceptionDateIsToday = inceptionDate == LocalDate.now().toString()
def payAndEffect = (enquiry?.carInfo?.isNew && insuranceAfterPayment == '1' && inceptionDateIsToday) ? '1' : '0'

def reqXml =
        """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Header xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
        <nshead:requesthead	xmlns:nshead="http://pub.webservice.cmp.com">
            <nshead:request_type>Q012</nshead:request_type>
            <nshead:uuid>${queryId}</nshead:uuid>
            <nshead:sender>${config?.sender ?: '0595'}</nshead:sender>
            <nshead:server_version>00000000</nshead:server_version>
            <nshead:user>${config?.user ?: '0595'}</nshead:user>
            <nshead:password>${config?.password ?: 'EC00FFFD8E9F7F2289752081793E9905'}</nshead:password>
            <nshead:ChnlNo>${config?.ChnlNo ?: 'cheche'}</nshead:ChnlNo>
            <nshead:areacode>${piccAreaCode}</nshead:areacode>
            <nshead:flowintime>${cstTime}</nshead:flowintime>
        </nshead:requesthead>
    </soap:Header>
    <soapenv:Body>
        <pan:TEMPSTORAGEREQ	xmlns:pan="http://pan.prpall.webservice.cmp.com">
            <pan:BIZ_ENTITY>
                <CarQuoteGenReq>
                    <PresaleCarFlag>${presaleCarFlag}</PresaleCarFlag>
                    <UserCode>${userCode}</UserCode>
                    <BizOrigin>${bizOrigin}</BizOrigin>
                    <ChannelCode>${channelCode}</ChannelCode>
                    <Handler1Code>${handler1Code}</Handler1Code>
                    <ComCode>${comCode}</ComCode>
                    <LastBizPolicyNo></LastBizPolicyNo>
                    <LastCIPolicyNo></LastCIPolicyNo>
                    <LicenseNo>${licenseNo}</LicenseNo>
                    <LicenseType>${licenseType}</LicenseType>
                    <LicenseColorCode>${licenseColorCode}</LicenseColorCode>
                    <CarKindCode>${carKindCode}</CarKindCode>
                    <EngineNo>${engineNo}</EngineNo>
                    <FrameNo>${frameNo}</FrameNo>
                    <VinNo>${frameNo}</VinNo>
                    <StartDateBI>${startDateBI}</StartDateBI>
                    <StartHourBI>${startHourBI}</StartHourBI>
                    <StartMinuteBI>${startMinuteBI}</StartMinuteBI>
                    <EndDateBI>${endDateBI}</EndDateBI>
                    <EndHourBI>${endHourBI}</EndHourBI>
                    <EndMinuteBI>0</EndMinuteBI>
                    <StartDateCI>${startDateCI}</StartDateCI>
                    <StartHourCI>${startHourCI}</StartHourCI>
                    <StartMinuteCI>${startMinuteCI}</StartMinuteCI>
                    <EndDateCI>${endDateCI}</EndDateCI>
                    <EndHourCI>${endHourCI}</EndHourCI>
                    <EndMinuteCI>${endMinuteCI}</EndMinuteCI>
                    <EnrollDate>${enrollDate}</EnrollDate>
                    <ClauseType>${clauseType}</ClauseType>
                    <UseNatureCode>${useNatureCode}</UseNatureCode>
                    <RunMiles>10000</RunMiles>
                    <RunAreaCode>11</RunAreaCode>
                    <ModelCode>${modelCode}</ModelCode>
                    <BrandName>${brandName}</BrandName>
                    <ModelCodeAlias></ModelCodeAlias>
                    <SeatCount>${seatCount}</SeatCount>
                    <TonCount>${tonCount}</TonCount>
                    <CarLotEquQuality>${carLotEquQuality}</CarLotEquQuality>
                    <Displacement>${exhaustScale}</Displacement>
                    <CountryNature>${countryNature}</CountryNature>
                    <Currency>CNY</Currency>
                    <ChgOwnerFlag>${chgOwnerFlag}</ChgOwnerFlag>
                    <ChgOwnerDate>${chgOwnerDate}</ChgOwnerDate>
                    <LoanVehicleFlag>0</LoanVehicleFlag>
                    <MakeCom>${makeCom}</MakeCom>
                    <HandlerCode>${handlerCode}</HandlerCode>
                    <AgentCode></AgentCode>
                    <NoDamageYears>0</NoDamageYears>
                    <LastDamagedBI>0</LastDamagedBI>
                    <ThisDamagedBI>0</ThisDamagedBI>
                    <NoDamYearsBI>0</NoDamYearsBI>
                    <LastDamaged>0</LastDamaged>
                    <RaterLoatFlag>0</RaterLoatFlag>
                    <NoDamYearsCI>0</NoDamYearsCI>
                    <LastDamagedCI>0</LastDamagedCI>
                    <ThisDamagedCI>0</ThisDamagedCI>
                    <Remark1></Remark1>
                    <Remark2></Remark2>
                    <NoDamageYearsBI>0</NoDamageYearsBI>
                    <RenewalFlag>0</RenewalFlag>
                    <GroupCode></GroupCode>
                    <UnitType></UnitType>
                    <CarInsuredRelation>${carInsuredRelation}</CarInsuredRelation>
                    <TaxPayerType>${taxPayerType}</TaxPayerType>
                    <DrivLicenseCode>${drivLicenseCode}</DrivLicenseCode>
                    <VehicleBrand>${vehicleBrand}</VehicleBrand>
                    <RenewalType>0</RenewalType>
                    <MonopolyCode>${monopolyCode}</MonopolyCode>
                    <AgentType>3O9</AgentType>
                    <Ext2></Ext2>
                    <Ext3></Ext3>
                    <ProjectCode>${projectCode}</ProjectCode>
                    <MonopolyFlag>${monopolyFlag}</MonopolyFlag>
                    <MonopolyName>${monopolyName}</MonopolyName>
                    <ProfitOperateMode>2</ProfitOperateMode>
                    <Resource>${config?.Resource ?: '0595'}</Resource>
                    <BusinessPropertyCode>${businessPropertyCode}</BusinessPropertyCode>
                    <Handler1Code_uni>${handler1Code_uni}</Handler1Code_uni>
                    <HandlerCode_uni>${handlerCode_uni}</HandlerCode_uni>
                    <LicenseFlag>${licenseFlag}</LicenseFlag>
                    <SearchSequenceNo>${searchSequenceNo}</SearchSequenceNo>
                    <NonLocalFlag>${nonLocalFlag}</NonLocalFlag>
                    <QueryArea>${queryArea}</QueryArea>
                    <PreDiscount>${preDiscount}</PreDiscount>
                    <CarShipTaxFlag>1</CarShipTaxFlag>
                    <CommissionFlag>0</CommissionFlag>
                    <CarCheckStatus>1</CarCheckStatus>
                    <CarChecker>${carChecker}</CarChecker>
                    <ClauseFlag>${clauseFlag}</ClauseFlag>
                    <IsCache>0</IsCache>
                    <FuelType>${fuelType}</FuelType>
                    <EnergyTypePlat>${energyTypePlat}</EnergyTypePlat>
                    <EnergyFlag>${energyFlag}</EnergyFlag>
                    <VehicleStyleUniqueId>${vehicleStyleUniqueId}</VehicleStyleUniqueId>
                    <CertificateType>01</CertificateType>
                    <CertificateNo>${certificateNo}</CertificateNo>
                    <CarProofDate>${carProofDate}</CarProofDate>
                    <CertificateDate>${certificateDate}</CertificateDate>
                    <VehicleTypeDescription>${vehicleTypeDescription}</VehicleTypeDescription>
                    <NewCarFlag>${newCarFlag}</NewCarFlag>
                    <HKFlag/>
                    <HKLicenseNo/>
                    <PayMethod>${payMethod}</PayMethod>
                    <LocalUse>${localUse}</LocalUse>
                    <LocalLicense>${localLicense}</LocalLicense>"""
   if ('1' == payAndEffect) {
       reqXml  += """<PayAndEffect>${payAndEffect}</PayAndEffect>"""
   }
        reqXml += """<CarQuoteCarShipTax>
                        <TaxType>${taxType}</TaxType>
                        <TaxAbateType></TaxAbateType>
                        <TaxAbateProportion></TaxAbateProportion>
                        <TaxAbateAmount></TaxAbateAmount>
                        <TaxAbateReason>${taxAbateReason}</TaxAbateReason>
                        <CalculateMode>${calculateMode}</CalculateMode>
                        <TaxPayerIdentNo>${taxPayerCode}</TaxPayerIdentNo>
                        <TaxPayerNumber>${taxPayerCode}</TaxPayerNumber>
                        <TaxPayerCode>${taxPayerCode}</TaxPayerCode>
                        <TaxPayerName>${taxPayerName}</TaxPayerName>
                        <TaxPayerNature>${taxPayerNature}</TaxPayerNature>
                        <TaxComCode></TaxComCode>
                        <TaxComName></TaxComName>
                        <DutyPaidProofNo></DutyPaidProofNo>
                        <TaxPrintProofNo></TaxPrintProofNo>
                        <TaxUnitAmount></TaxUnitAmount>
                        <PrePayTaxYear></PrePayTaxYear>
                        <Currency></Currency>
                        <TaxUnit></TaxUnit>
                        <ThisPayTax></ThisPayTax>
                        <PrePayTax></PrePayTax>
                        <DelayPayTax></DelayPayTax>
                        <SumPayTax></SumPayTax>
                        <TaxPayerNo></TaxPayerNo>
                    </CarQuoteCarShipTax>
                    <CarQuoteInsuredIndivList>
                        <CarQuoteInsuredIndiv>
                            <SerialNo>1</SerialNo>
                            <VersionNo>1</VersionNo>
                            <InsuredFlag>0010000</InsuredFlag>
                            <InsuredCode>1</InsuredCode>
                            <InsuredName>${ownerName}</InsuredName>
                            <AliasName></AliasName>
                            <InsuredAddress>${ownerAddress}</InsuredAddress>
                            <PostCode></PostCode>
                            <InsuredNature>${ownerInsuredNature}</InsuredNature>
                            <AppendPrintName></AppendPrintName>
                            <UnitType>${ownerUnitType}</UnitType>
                            <IdentifyType>${ownerIdentifyType}</IdentifyType>
                            <IdentifyNumber>${ownerIdentifyNumber}</IdentifyNumber>
                            <PhoneNumber></PhoneNumber>
                            <Mobile>${ownerMobile}</Mobile>
                            <AuditStatus></AuditStatus>
                            <Sex>${ownerSex}</Sex>
                            <Age>${ownerAge}</Age>
                            <DrivingLicenseNo></DrivingLicenseNo>
                            <DrivingCarType></DrivingCarType>
                            <UnifiedSocialCreditCode>${ownerUnifiedSocialCreditCode}</UnifiedSocialCreditCode>
                            <OrganizeCode>${ownerOrganizeCode}</OrganizeCode>
                            <CountryCode>${ownerCountryCode}</CountryCode>
                            <DateValid>${ownerDateValid}</DateValid>
                            <OrgDateValid>${ownerDateValid}</OrgDateValid>
                            <CreditDateValid>${ownerDateValid}</CreditDateValid>
                            <IsPersonConsistentWithID>1</IsPersonConsistentWithID>
                            <BirthDate>${ownerBirth}</BirthDate>
                            <Province>${eastProvince}</Province>
                            <City>${eastCity}</City>
                            <Area>${eastCountry}</Area>
                            <ContactPersonName>${ownerContactName}</ContactPersonName>
                            <StartDateValid>${ownerDateValidStart}</StartDateValid>
                            <OrgDateValidStart>${ownerDateValidStart}</OrgDateValidStart>
                            <CreditDateValidStart>${ownerDateValidStart}</CreditDateValidStart>
                            <Resident>0</Resident>"""
if (enquiry?.insArea['province'] == '320000' && appInsuredNature == 2) {
     reqXml += """<PayAccountName>${appName}</PayAccountName>
                  <RelationShipCode>01</RelationShipCode>"""
}
reqXml += """</CarQuoteInsuredIndiv>
                        <CarQuoteInsuredIndiv>
                            <SerialNo>2</SerialNo>
                            <VersionNo>1</VersionNo>
                            <InsuredFlag>1000000</InsuredFlag>
                            <InsuredCode>1</InsuredCode>
                            <InsuredName>${appName}</InsuredName>
                            <AliasName></AliasName>
                            <InsuredAddress>${appAddress}</InsuredAddress>
                            <PostCode></PostCode>
                            <InsuredNature>${appInsuredNature}</InsuredNature>
                            <AppendPrintName></AppendPrintName>
                            <UnitType>${appUnitType}</UnitType>
                            <IdentifyType>${appIdentifyType}</IdentifyType>
                            <IdentifyNumber>${appIdentifyNumber}</IdentifyNumber>
                            <PhoneNumber></PhoneNumber>
                            <Mobile>${appMobile}</Mobile>
                            <AuditStatus></AuditStatus>
                            <Sex>${appSex}</Sex>
                            <Age>${appAge}</Age>
                            <DrivingLicenseNo></DrivingLicenseNo>
                            <DrivingCarType></DrivingCarType>
                            <UnifiedSocialCreditCode>${appUnifiedSocialCreditCode}</UnifiedSocialCreditCode>
                            <OrganizeCode>${appOrganizeCode}</OrganizeCode>
                            <CountryCode>${appCountryCode}</CountryCode>
                            <DateValid>${appDateValid}</DateValid>
                            <OrgDateValid>${appDateValid}</OrgDateValid>
                            <CreditDateValid>${appDateValid}</CreditDateValid>
                            <IsPersonConsistentWithID>1</IsPersonConsistentWithID>
                            <BirthDate>${appBirth}</BirthDate>
                            <Province>${eastProvince}</Province>
                            <City>${eastCity}</City>
                            <Area>${eastCountry}</Area>
                            <ContactPersonName>${appContactName}</ContactPersonName>
                            <StartDateValid>${appDateValidStart}</StartDateValid>
                            <OrgDateValidStart>${appDateValidStart}</OrgDateValidStart>
                            <CreditDateValidStart>${appDateValidStart}</CreditDateValidStart>
                            <Resident>0</Resident>"""
if (enquiry?.insArea['province'] == '320000' && appInsuredNature == 2) {
    reqXml += """<PayAccountName>${appName}</PayAccountName>
                 <RelationShipCode>01</RelationShipCode>"""
}

reqXml += """</CarQuoteInsuredIndiv>
                        <CarQuoteInsuredIndiv>
                            <SerialNo>3</SerialNo>
                            <VersionNo>1</VersionNo>
                            <InsuredFlag>0100000</InsuredFlag>
                            <InsuredCode>1</InsuredCode>
                            <InsuredName>${insuredName}</InsuredName>
                            <AliasName></AliasName>
                            <InsuredAddress>${insuredAddress}</InsuredAddress>
                            <PostCode></PostCode>
                            <InsuredNature>${insuredInsuredNature}</InsuredNature>
                            <AppendPrintName></AppendPrintName>
                            <UnitType>${insuredUnitType}</UnitType>
                            <IdentifyType>${insuredIdentifyType}</IdentifyType>
                            <IdentifyNumber>${insuredIdentifyNumber}</IdentifyNumber>
                            <PhoneNumber></PhoneNumber>
                            <Mobile>${insuredMobile}</Mobile>
                            <AuditStatus></AuditStatus>
                            <Age>${insuredAge}</Age>
                            <Sex>${insuredSex}</Sex>
                            <DrivingLicenseNo></DrivingLicenseNo>
                            <DrivingCarType></DrivingCarType>
                            <UnifiedSocialCreditCode>${insuredUnifiedSocialCreditCode}</UnifiedSocialCreditCode>
                            <OrganizeCode>${insuredOrganizeCode}</OrganizeCode>
                            <CountryCode>${insuredCountryCode}</CountryCode>
                            <DateValid>${insuredDateValid}</DateValid>
                            <OrgDateValid>${insuredDateValid}</OrgDateValid>
                            <CreditDateValid>${insuredDateValid}</CreditDateValid>
                            <IsPersonConsistentWithID>1</IsPersonConsistentWithID>
                            <BirthDate>${insuredBirth}</BirthDate>
                            <Province>${eastProvince}</Province>
                            <City>${eastCity}</City>
                            <Area>${eastCountry}</Area>
                            <ContactPersonName>${insuredContactName}</ContactPersonName>
                            <StartDateValid>${insuredDateValidStart}</StartDateValid>
                            <OrgDateValidStart>${insuredDateValidStart}</OrgDateValidStart>
                            <CreditDateValidStart>${insuredDateValidStart}</CreditDateValidStart>
                            <Resident>0</Resident>"""
if (enquiry?.insArea['province'] == '320000' && insuredInsuredNature == 2) {
    reqXml += """<PayAccountName>${appName}</PayAccountName>
                <RelationShipCode>01</RelationShipCode>"""
}
reqXml += """</CarQuoteInsuredIndiv>"""
if (appInsuredNature == 2) {
    def liaisonName = getSupplyParam(enquiry, 'liaisonName')
    def liaisonMobile = getSupplyParam(enquiry, 'liaisonMobile')
    def liaisonIDCardNo = getSupplyParam(enquiry, 'liaisonIDCardNo')
    if (liaisonName && liaisonMobile && liaisonIDCardNo) {
        Assert.isTrue(IdcardUtil.isValidCard(liaisonIDCardNo as String), '联系人证件号应该是有效身份证')
        def liaisonAge = getAge(liaisonIDCardNo)
        def liaisonSex = getSex(liaisonIDCardNo)
        def liaisonBirth = getBirth(liaisonIDCardNo)
        def liaisonCardValidDate = getIDCardValidDate(autoTask, 'liaison')
        def liaisonDateValid = liaisonCardValidDate['end']
        def liaisonDateValidStart = liaisonCardValidDate['start']
        reqXml += """
         <CarQuoteInsuredIndiv>
            <SerialNo>4</SerialNo>
            <VersionNo>1</VersionNo>
            <InsuredFlag>${enquiry.insArea['city'] == '440300' ? '0000101' : '0000001'}</InsuredFlag>
            <InsuredCode>1</InsuredCode>
            <InsuredName>${liaisonName}</InsuredName>
            <AliasName></AliasName>
            <InsuredAddress>${insuredAddress}</InsuredAddress>
            <PostCode></PostCode>
            <InsuredNature>1</InsuredNature>
            <AppendPrintName></AppendPrintName>
            <UnitType></UnitType>
            <IdentifyType>01</IdentifyType>
            <IdentifyNumber>${liaisonIDCardNo}</IdentifyNumber>
            <PhoneNumber></PhoneNumber>
            <Mobile>${liaisonMobile}</Mobile>
            <AuditStatus></AuditStatus>
            <Age>${liaisonAge}</Age>
            <Sex>${liaisonSex}</Sex>
            <DrivingLicenseNo></DrivingLicenseNo>
            <DrivingCarType></DrivingCarType>
            <UnifiedSocialCreditCode></UnifiedSocialCreditCode>
            <OrganizeCode></OrganizeCode>
            <CountryCode>${insuredCountryCode}</CountryCode>
            <DateValid>${liaisonDateValid}</DateValid>
            <OrgDateValid></OrgDateValid>
            <CreditDateValid></CreditDateValid>
            <IsPersonConsistentWithID>1</IsPersonConsistentWithID>
            <BirthDate>${liaisonBirth}</BirthDate>
            <Province>${eastProvince}</Province>
            <City>${eastCity}</City>
            <Area>${eastCountry}</Area>
            <ContactPersonName>${liaisonName}</ContactPersonName>
            <StartDateValid>${liaisonDateValidStart}</StartDateValid>
            <OrgDateValidStart></OrgDateValidStart>
            <CreditDateValidStart></CreditDateValidStart>
            <Resident>1</Resident>
         </CarQuoteInsuredIndiv>
         """
    }
}
reqXml += """</CarQuoteInsuredIndivList>"""
if (enquiry?.tempValues?.answerci) {
    reqXml += """<CarQuoteBasePartCIReq>
                     <QuerySequenceNo>${enquiry?.tempValues?.querySequenceNoCI}</QuerySequenceNo>
                     <Answer>${enquiry?.tempValues?.answerci}</Answer>
                     <IsRenewalFlag>${enquiry?.tempValues?.isRenewalFlagCI}</IsRenewalFlag>
                </CarQuoteBasePartCIReq>"""
}

if (enquiry?.tempValues?.answerbi) {
    reqXml += """<CarQuoteBasePartBIReq>
                    <QuerySequenceNo>${enquiry?.tempValues?.querySequenceNoBI}</QuerySequenceNo>
                    <Answer>${enquiry?.tempValues?.answerbi}</Answer>
                    <IsRenewalFlag>${enquiry?.tempValues?.isRenewalFlagBI}</IsRenewalFlag>
                </CarQuoteBasePartBIReq>"""
}
reqXml += """<CarQuoteItemKindBIList>"""
if (suiteList) {
    def index = 0
    def shareAmount = shareAmount(enquiry)
    suiteList.each { suite ->
        def kindCode = kindCodeNew(suite.code)
        def kindName = kindNameNew(suite.code)
        def amount = ""
        def quantity = 1
        def modeCode
        def passengerAmount
        switch (kindCode) {
            case ['051050', '051078', '051085', '051089'] :
                if (enquiry.carInfo?.isNew) {
                    amount = purchasePrice
                }
                if (tempValues['vehicleDamageAdjust'] && tempValues['adjustVehicleDamageAmount']) {
                    amount = tempValues['adjustVehicleDamageAmount']
                }
                // 需求14271
                if (enquiry?.carInfo?.definedCarPrice) {
                    amount = enquiry?.carInfo?.definedCarPrice?.toBigDecimal()?.setScale(2, BigDecimal.ROUND_HALF_UP)?.doubleValue()
                }
                break;
            case '050232' :
                modeCode = suite.amount == 1 ? "20" : "10"
                amount = suite.amount.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue().toString()
                break
            case '051058' :
                amount = 100
                quantity = 3
                break
            case ['051080', '051064', '051081', '051089'] :
                quantity = Double.valueOf(suite.amount).intValue()
                break
            case ['051053', '051073', '051068'] :
                quantity = Double.valueOf(seatCount).intValue() - 1
                if ('051073' == kindCode && suite?.share) {
                    def mainKey = suite?.code - 'NIHC'
                    amount = shareAmount[(mainKey)].toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue().toString()
                    passengerAmount = Double.valueOf(amount).intValue() * quantity
                } else {
                    amount = suite.amount.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue().toString()
                    passengerAmount = Double.valueOf(suite.amount).intValue() * quantity
                }
                break
            case '051087' :
                nechargerDuty = true
                amount = suite.amount.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue().toString()
                pileLiabilityAmount = suite.amount.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue().toString()
                break
            case '051086' :
                nechargerDamage = true
                amount = suite.amount.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue().toString()
                pileLossAmount = suite.amount.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue().toString()
                break
            case ['051063', '051072'] :
                if (suite?.share) {
                    def mainKey = suite?.code - 'NIHC'
                    amount = shareAmount[(mainKey)].toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue().toString()
                } else {
                    amount = suite.amount.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue().toString()
                }
                break
            case '051079' :
                quantity = Double.valueOf(suite.amount).intValue()
                break
            default:
                amount = suite.amount.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue().toString()
                break

        }

        reqXml += """<CarQuoteItemKindBI>
                        <KindCode>${kindCode}</KindCode>
                        <KindName>${kindName}</KindName>
                        <ItemKindNo>${++index}</ItemKindNo>
                        <Quantity>${quantity}</Quantity>
                        <Flag>1001000</Flag>
                        <Currency1>CNY</Currency1> 
                        <ShortRate>100.0</ShortRate>
                        <CalculateFlag>Y</CalculateFlag>"""
        if ('051079' != kindCode) {
            reqXml += """<Amount>${passengerAmount ?: amount}</Amount>
                         <UnitAmount>${amount}</UnitAmount>"""
        }
        if (suite?.share) {
            reqXml += """<SharedAmountFlag>1</SharedAmountFlag>"""
        }
        if (modeCode) {
            reqXml += """<ModeCode>${modeCode}</ModeCode>"""
        }
        if (['051065', '051066', '051067', '051068'].contains(kindCode)) {
            reqXml += """<DeductibleRate>${amount}</DeductibleRate>"""
        }
        if ('051079' == kindCode) {
            def clause = (tools.getClauseCodeByAreaCode(enquiry?.insArea['city']) ?: tools.getClauseCodeByAreaCode(enquiry?.insArea['province'])) ?: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10']
            reqXml += """<SafeCheckItemList>"""
            clause.eachWithIndex { tag, clauseIndex ->
                reqXml += """<SafeCheckItem>"""
                reqXml += """<ItemKindNo>${++clauseIndex}</ItemKindNo>"""
                reqXml += """<ItemCode>${tag}</ItemCode>"""
                reqXml += """<ItemName>${tools.getClauseNameByCode(tag)}</ItemName>"""
                reqXml += """<KindCode>051079</KindCode>"""
                reqXml += """<Quantity>1</Quantity>"""
                reqXml += """</SafeCheckItem>"""
            }
            reqXml += """</SafeCheckItemList>"""
        }
        reqXml += """</CarQuoteItemKindBI>"""
    }
}
reqXml += """</CarQuoteItemKindBIList>"""
if (efcBaseSuiteInfo) {
    reqXml += """<CarQuoteItemKindCIList>
                    <CarQuoteItemKindCI>
                        <KindCode>051074</KindCode>
                        <KindName>机动车交通事故责任强制保险</KindName>
                        <ItemKindNo>1</ItemKindNo>
                        <Amount></Amount>
                        <UnitAmount></UnitAmount>
                        <Quantity>0</Quantity>
                        <Currency1>CNY</Currency1>
                        <ShortRate>100.0</ShortRate>
                        <CalculateFlag>Y</CalculateFlag>
                    </CarQuoteItemKindCI>
                </CarQuoteItemKindCIList>"""

}
if (nechargerDuty || nechargerDamage) {
    def chargingPileModel = getSupplyParam(enquiry, "neChargerModel") ?: configs?.NEChargerModel
    def chargingPileCode = getSupplyParam(enquiry, "neChargerNo") ?: configs?.NEChargerNo
    def chargingAddress = getSupplyParam(enquiry, "neChargerAddress") ?: configs?.NEChargerAddress
    def chargingPileType = getSupplyParam(enquiry, "neChargerType") ?: configs?.NEChargerType
    def addressType = getSupplyParam(enquiry, "neChargerLocationType") ?: configs?.NEChargerLocationType
    def chargingPileUseYears = getSupplyParam(enquiry, "neChargerWarranty") ?: configs?.NEChargerWarranty
    def serialNo = 0
    reqXml += """<ChargingPileInfoList>
                    <ChargingPileInfo>
                        <ChargingPileModel>${chargingPileModel}</ChargingPileModel>
                        <ChargingPileCode>${chargingPileCode}</ChargingPileCode>
                        <Address>${chargingAddress}</Address>
                        <SerialNo>${++serialNo}</SerialNo>
                        <ChargingPileType>${chargingPileType}</ChargingPileType>
                        <AddressType>${addressType}</AddressType>
                        <ChargingPileUseYears>${chargingPileUseYears}</ChargingPileUseYears>"""
    if (nechargerDuty) {
        reqXml += """<PileLiabilityAmount>${pileLiabilityAmount}</PileLiabilityAmount>"""
    }
    if (nechargerDamage) {
        reqXml += """<PileLossAmount>${pileLossAmount}</PileLossAmount>"""
    }
    reqXml += """                     
                    </ChargingPileInfo>
                </ChargingPileInfoList>"""
}
reqXml += """</CarQuoteGenReq>
            </pan:BIZ_ENTITY>
            <pan:APP_INFO>
                <pan:MAKECOME>string</pan:MAKECOME>
                <pan:REQMOD>string</pan:REQMOD>
            </pan:APP_INFO>
        </pan:TEMPSTORAGEREQ>
    </soapenv:Body>
</soapenv:Envelope>"""
