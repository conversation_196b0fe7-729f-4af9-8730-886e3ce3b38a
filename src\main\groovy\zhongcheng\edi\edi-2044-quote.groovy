package zhongcheng.edi

import com.cheche365.bc.exception.TempSkipException
import common.common_all

/**
 * 报价模板
 */
def efc = enquiry?.baseSuiteInfo?.efcSuiteInfo
def biz = enquiry?.baseSuiteInfo?.bizSuiteInfo?.suites as List

def edi_2044_common_new = new edi_2044_common_new()
def script = new edi_2044_common()
def personInfo = edi_2044_common_new.getPersonInfo(enquiry?.supplyParam)
def common_all = new common_all()
def reqXml = """<?xml version="1.0" encoding="GBK"?>"""
reqXml += edi_2044_common_new.makeHeadDto("0294_n", enquiry?.insArea?.city, config)
reqXml += edi_2044_common_new.makeVehicleDto(enquiry, tempValues, script, config)
reqXml += edi_2044_common_new.makeCoverageDtoList(enquiry)
reqXml += edi_2044_common_new.makeApplicationDto(enquiry, tempValues, config)
reqXml += edi_2044_common_new.makeVehicleOwnerDtoQuote(enquiry, script, common_all, personInfo).replaceAll('<MobileNo>.*</MobileNo>', '') // 删除 MobileNo 字段
reqXml += edi_2044_common_new.makeInsuredDtoListQuote(enquiry, script, common_all, personInfo).replaceAll('MobileNo', 'Mobile') // 替换标签名
reqXml += edi_2044_common_new.makeCarShipTaxDto(enquiry, script)
//转保验证码
if (tempValues?.questionAnswerEfc) {
    reqXml += """
        <ValidateDto>
            <Answer>${tempValues?.questionAnswerEfc}</Answer>
        </ValidateDto>
"""
}
reqXml += edi_2044_common_new.makeTail()
println("测试5")
reqXml
