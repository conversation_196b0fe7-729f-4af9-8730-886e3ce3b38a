package renbao.edi

import com.cheche365.bc.exception.InsReturnException
import static common.common_all.*

import java.time.LocalDate



def getNeedDay(dayPlus, yearPlus){

    def date = LocalDate.now()
    def newYear = date.plusYears(yearPlus)
    def newDate = newYear.plusDays(dayPlus)
    return  newDate.toString()
}
def _LICENSE_ADDRESS_MAPPING = [
        '赣A' : '360100',
        '赣M' : '360100',
        '赣B' : '360700',
        '赣C' : '360900',
        '赣D' : '360800',
        '赣E' : '361100',
        '赣F' : '361000',
        '赣G' : '360400',
        '赣H' : '360200',
        '赣J' : '360300',
        '赣K' : '360500',
        '赣L' : '360600'
]

def tBOrderId = enquiry?.SQ['topOrderNo']
if (!tBOrderId)
    throw new InsReturnException("核保主单号topOrderNo不能为空")
def tBbizOrderId = enquiry?.SQ['bizOrderNo'] ?: ""
def tBefcOrderId = enquiry?.SQ['efcOrderNo'] ?: ""
def script =  new edi_common_2005()
def queryId = tBOrderId
def cur = script.getTimePattern("yyyy-MM-dd HH:mm:ss")

def bizFlag = enquiry?.baseSuiteInfo?.bizSuiteInfo?.start ? 1 : 0
def forceFlag = enquiry?.baseSuiteInfo?.efcSuiteInfo?.start ? 1 : 0
def nsCommon = new edi_nscommon_2005()
def isEnergyCar = checkEnergyType(enquiry.carInfo.carModelName, enquiry.carInfo.plateNum) == '0' ? false : true
def startDateBI = enquiry?.baseSuiteInfo?.bizSuiteInfo?.start ? script.getStartDay(enquiry.baseSuiteInfo.bizSuiteInfo.start) : ""
def startHourBI = enquiry?.baseSuiteInfo?.bizSuiteInfo?.start ? (script.getHour(enquiry?.baseSuiteInfo?.bizSuiteInfo?.start)) : ""

def startDateCI = enquiry?.baseSuiteInfo?.efcSuiteInfo?.start ? script.getStartDay(enquiry.baseSuiteInfo.efcSuiteInfo.start) : ""
def startHourCI = enquiry?.baseSuiteInfo?.efcSuiteInfo?.start ?(script.getHour(enquiry?.baseSuiteInfo?.efcSuiteInfo?.start)) : ""

def totalPremium = enquiry?.SQ['totalCharge']
def bizTotalPremium = enquiry?.SQ['bizCharge']
def forceTotalPremium = enquiry?.SQ['forceTotalPremium']
def forcePremium = enquiry?.SQ['efcCharge']
def vehicleTaxPremium = enquiry?.SQ['taxCharge']
def configs = enquiry?.configInfo?.configMap
def ownerName = enquiry?.carOwnerInfo?.name
def ownerIdType = [6, 8, 9, 10].contains(enquiry?.carOwnerInfo?.idCardType) ? "37": "01" //1个人 2 团体
def ownerIdNo =  enquiry?.carOwnerInfo?.idCard
def ownerMobile = script.ownerMobile(enquiry)
def ownerStartDate = script.ownerStart(enquiry) ?: getNeedDay(-1, 0)
def ownerEndDate = script.ownerEnd(enquiry) ?: getNeedDay(0, 10)

def applicantName = enquiry?.insurePerson?.name
def applicantIdType = [6, 8, 9, 10].contains(enquiry?.insurePerson?.idCardType) ? "37" : "01" //1个人 2 团体
def applicantMobile = script.applicantMobile(enquiry)
def applicantIdNo = enquiry?.insurePerson?.idCard
def applicantCertiStartDat = script.appStart(enquiry) ?: getNeedDay(-1, 0)
def applicantCertiEndDate = script.appEnd(enquiry) ?: getNeedDay(0, 10)
def applicantEmail = configs?.policyEmail ?: script.applicantEmail(enquiry)

def insuredPerson = enquiry?.insuredPersonList?.get(0)//被保人信息
def insuredName =  insuredPerson?.name
def insuredIdType = [6, 8, 9, 10].contains(insuredPerson?.idCardType) ? "37" : "01" //1个人 2 团体
def insuredMobile = script.insuredMobile(enquiry)
def insuredIdNo = [8, 9, 10].contains(insuredPerson?.idCardType) ? "" : insuredPerson?.idCard
def insuredCertiStartDat = script.insuredStart(enquiry) ?: getNeedDay(-1, 0)
def insuredCertiEndDate = script.insuredEnd(enquiry) ?: getNeedDay(0, 10)

def areaInfo = getAddress(autoTask, 'applicantAddress')
def addresseeDetails = areaInfo ? areaInfo['detail'] : configs.address

def province =  areaInfo ? areaInfo['province'] : enquiry?.insArea['province']
def city = areaInfo ? areaInfo['city'] : enquiry?.insArea['city']
def area = areaInfo ? areaInfo['district'] : enquiry?.insArea['city']
def addresseeName = applicantName
def verifyCode = tempValues?.verificationCode ?: ""
def insProvince = enquiry?.insArea?.province
def policyFlag = insProvince.toString().startsWith("44") ? 2 : 1
def imgFlag = enquiry?.SQ['imgFlag']
def img = enquiry?.imgAddress?.find {
    it.key == '3'
}
def imgType = ''
def imgUrl = ''
if (img && '1' == imgFlag) {
    imgType = '1'
    imgUrl = (img['value'] as String).replaceAll('&', '&amp;')
}
def licenseAddress = enquiry['insArea']['province'] == '360000' ? _LICENSE_ADDRESS_MAPPING.get(enquiry?.carInfo?.plateNum?.substring(0, 2)) : ''
def readTime = enquiry['insArea']['province'] == '460000' ? script.getTimePattern("yyyyMMddHHmmss") : ''

def reqXml = """
<PackageList>
    <Package>
    <Header>
        <Version>2</Version>
        <RequestType>101115</RequestType>
        <InsureType>100</InsureType>
        <SessionId>${queryId}</SessionId>
        <SellerId>123456</SellerId>
        <From>CHECHE</From>
        <SendTime>${cur}</SendTime>
        <Status>100</Status>
        <ErrorMessage></ErrorMessage>
    </Header>"""
def  request = """<Request><VerifyCode>${verifyCode}</VerifyCode><InputsList><Inputs type="optional"><Input name="forceFlag">${forceFlag}</Input><Input name="bizFlag">${bizFlag}</Input><Input name="totalPremium">${totalPremium}</Input><Input name="bizTotalPremium">${bizTotalPremium}</Input>"""
if (forceFlag) {
    if (isEnergyCar) {
        request += """<Input name="E010000"></Input>"""
    } else {
        request += """<Input name="F010000"></Input>"""
    }

}
if (bizFlag) {
    def suiteList = enquiry?.baseSuiteInfo?.bizSuiteInfo?.suites
    suiteList.each {
        def code = nsCommon.kindeCode(it.code)
        if (isEnergyCar)
            code = code.toString().replaceAll("F", "E")
        if (["F110000", "F110400", "F120300", "F400100", "F400300", "F400400","E110400", "E120300", "E110000",  "E400100", "E400300", "E400400"].contains(code)) {
            request += """<Input name="${code}">1</Input>"""
        } else {
            if (it.amount < 1) {
                request += """<Input name="${code}">${Double.valueOf(it.amount * 100).intValue()}</Input>"""
            } else {
                request += """<Input name="${code}">${Double.valueOf(it.amount).intValue()}</Input>"""
            }
        }
    }

}
request += """</Inputs>"""
request += """<Inputs type="force"><Input name="forceTotalPremium">${forceTotalPremium}</Input><Input name="carShipFlag"></Input><Input name="forcePremium">${forcePremium}</Input><Input name="chooseCarShipFlag"></Input><Input name="vehicleTaxPremium">${vehicleTaxPremium}</Input></Inputs><Inputs type="vehicleInfo"><Input name="forceBeginDate">${startDateCI}</Input><Input name="forceBeginDateHour">${startHourCI}</Input><Input name="bizBeginDate">${startDateBI}</Input><Input name="bizBeginDateHour">${startHourBI}</Input></Inputs><Inputs type="applicantInfo"><Input name="applicantIdNo">${applicantIdNo}</Input><Input name="applicantMobile">${applicantMobile}</Input><Input name="applicantCertiEndDate">${applicantCertiEndDate}</Input><Input name="applicantCertiStartDat">${applicantCertiStartDat}</Input><Input name="applicantName">${applicantName}</Input><Input name="applicantIdType">${applicantIdType}</Input><Input name="applicantEmail">${applicantEmail}</Input></Inputs><Inputs type="ownerInfo"><Input name="ownerIdType">${ownerIdType}</Input><Input name="ownerName">${ownerName}</Input><Input name="ownerIdNo">${ownerIdNo}</Input><Input name="ownerMobile">${ownerMobile}</Input><Input name="ownerStartDate">${ownerStartDate}</Input><Input name="ownerEndDate">${ownerEndDate}</Input></Inputs><Inputs type="deliverInfo"><Input name="licenseAddress">${licenseAddress}</Input><Input name="addresseeDetails">${addresseeDetails}</Input><Input name="readingCompletedTime">${readTime}</Input><Input name="agreeAuthorizeTime">${readTime}</Input><Input name="checkedTime">${readTime}</Input><Input name="addresseeProvince">${province}</Input><Input name="addresseeCity">${city}</Input><Input name="addresseeTown">${area}</Input><Input name="imgType">${imgType}</Input><Input name="imgUrl">${imgUrl}</Input><Input name="policyFlagCI"></Input><Input name="addresseeName">${addresseeName}</Input><Input name="policyFlagBI"></Input></Inputs><Inputs type="insuredInfo"><Input name="insuredIdType">${insuredIdType}</Input><Input name="insuredName">${insuredName}</Input><Input name="insuredCertiEndDate">${insuredCertiEndDate}</Input><Input name="insuredIdNo">${insuredIdNo}</Input><Input name="insuredMobile">${insuredMobile}</Input><Input name="insuredCertiStartDat">${insuredCertiStartDat}</Input></Inputs></InputsList><Order><TBOrderId>${tBOrderId}</TBOrderId><Premium>${totalPremium}</Premium><SubOrderList>"""
if (forceFlag) {
    request += """<SubOrder type="force"><TBOrderId>${tBefcOrderId}</TBOrderId><Premium>${forceTotalPremium}</Premium></SubOrder>"""
}
if (bizFlag) {
    request += """<SubOrder type="biz"><TBOrderId>${tBbizOrderId}</TBOrderId><Premium>${bizTotalPremium}</Premium></SubOrder>"""
}

request += """</SubOrderList></Order></Request>"""
def sign = nsCommon.encrypt(request)
reqXml += request
reqXml += """<Sign>${sign}</Sign></Package></PackageList>"""

