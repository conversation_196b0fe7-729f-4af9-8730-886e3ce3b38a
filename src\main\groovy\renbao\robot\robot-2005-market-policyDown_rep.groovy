package renbao.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.message.TaskStatus
import com.cheche365.bc.utils.sender.HttpSender
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import groovy.transform.Field
import org.apache.http.impl.client.CloseableHttpClient

import static common.common_all.uploadOBS
import static renbao.common.Constants.SUCCESS_CODE
import static renbao.robot.common_market.hostPrefix


@BaseScript BaseScript_Http_Enq _

@Field JSONObject result = null
check {
    result = it
    assertTrue('人保营销系统电子保单下载失败', SUCCESS_CODE == result['statusText'])
}

def bizPolicyNo = entity.bizPolicyCode
def efcPolicyNo = entity.efcPolicyCode
def nonMotorPolicyNo = entity?.misc?.nonMotor?.accidentPolicyCode
def insuredIdCard = entity.order.insuredPersons[0].idCard
if (efcPolicyNo) {
    down(autoTask, efcPolicyNo, insuredIdCard)
}
if (bizPolicyNo) {
    down(autoTask, bizPolicyNo, insuredIdCard)
}
if (nonMotorPolicyNo) {
    down(autoTask, nonMotorPolicyNo, insuredIdCard)
}
autoTask.taskStatus = TaskStatus.EXECUTE_SUCCESS.State()

def down(autoTask, policyNo, insuredIdCard) {
    def areaAbbreviation = autoTask?.configs['areaAbbreviation']
    def commonMarket = new common_market()
    def header = commonMarket.header(autoTask)
    def downUrl = hostPrefix(autoTask) + "/khyx/newFront/tool/epdownload/download.do?filePath=&epFlag=&checkEpdownloadFlag=0&idCard=${insuredIdCard}&epQueryVo.flag=2&epQueryVo.policyNo=${policyNo}&epQueryVo.licenseNo=&epQueryVo.signDateSt=&epQueryVo.signDateEnd=&epQueryVo.yearStart=2022&epQueryVo.policyType=1"
    def downResult = HttpSender.doGet(autoTask.httpClient as CloseableHttpClient, downUrl, header as Map<String, String>,null, "UTF-8",null, false)
    if (downResult.toString().contains('Success')) {
        def urlSuffix = JSON.parseObject(downResult as String).getJSONObject('data').getString('msg').split("webapp")[1]
        def policyUrl = hostPrefix(autoTask) + "/khyx" + urlSuffix as String
        def byteArray = HttpSender.doGet(autoTask.httpClient as CloseableHttpClient, policyUrl, header as Map<String, String>,null, "UTF-8",null, true)
        def type = policyNo.startsWith("PEBS") ? 'nonMotor' : (policyNo.startsWith("PDAA") ? 'biz' : 'efc')
        uploadOBS(entity, type, byteArray, '2005', policyNo)
    } else {
        autoTask.taskStatus = TaskStatus.EXECUTE_FAILURE.State()
        throw new InsReturnException('保单号：'+ policyNo + '下载电子保单失败')
    }
}
