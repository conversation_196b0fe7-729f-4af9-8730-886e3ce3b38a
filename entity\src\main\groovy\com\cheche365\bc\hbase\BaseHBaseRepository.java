package com.cheche365.bc.hbase;

import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * HBase服务基类
 * 提供通用的HBase操作方法
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
public abstract class BaseHBaseRepository<T> {

    @Autowired
    protected Connection hbaseConnection;

    /**
     * 日期时间格式化器
     */
    protected static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取表名
     */
    protected abstract String getTableName();

    /**
     * 获取实体类型
     */
    protected abstract Class<T> getEntityClass();

    /**
     * 获取列族映射
     * key: 字段名, value: 列族名
     */
    protected abstract Map<String, String> getColumnFamilyMapping();

    /**
     * 生成RowKey
     */
    protected abstract String generateRowKey(T entity);

    /**
     * 保存实体到HBase
     *
     * @param entity 实体对象
     * @throws IOException HBase操作异常
     */
    public void save(T entity) throws IOException {
        if (entity == null) {
            throw new IllegalArgumentException("实体对象不能为空");
        }

        String rowKey = generateRowKey(entity);
        if (rowKey == null || rowKey.isEmpty()) {
            throw new IllegalArgumentException("RowKey不能为空");
        }

        try (Table table = hbaseConnection.getTable(TableName.valueOf(getTableName()))) {
            Put put = new Put(Bytes.toBytes(rowKey));

            // 将实体字段转换为HBase列
            Map<String, String> columnFamilyMapping = getColumnFamilyMapping();
            Field[] fields = getEntityClass().getDeclaredFields();

            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    Object value = field.get(entity);
                    if (value != null && !isStaticField(field)) {
                        String fieldName = field.getName();
                        String columnFamily = columnFamilyMapping.get(fieldName);

                        if (columnFamily != null) {
                            byte[] valueBytes = convertToBytes(value);
                            if (valueBytes != null && valueBytes.length > 0) {
                                put.addColumn(Bytes.toBytes(columnFamily), Bytes.toBytes(fieldName), valueBytes);
                            }
                        }
                    }
                } catch (IllegalAccessException e) {
                    log.warn("无法访问字段: {}", field.getName(), e);
                }
            }

            if (!put.isEmpty()) {
                table.put(put);
                log.debug("成功保存实体到HBase, RowKey: {}", rowKey);
            } else {
                log.warn("实体所有字段都为空，跳过保存, RowKey: {}", rowKey);
            }
        }
    }

    /**
     * 根据RowKey查询实体
     *
     * @param rowKey RowKey
     * @return 实体对象，如果不存在返回null
     * @throws IOException HBase操作异常
     */
    public T findByRowKey(String rowKey) throws IOException {
        if (rowKey == null || rowKey.isEmpty()) {
            throw new IllegalArgumentException("RowKey不能为空");
        }

        try (Table table = hbaseConnection.getTable(TableName.valueOf(getTableName()))) {
            Get get = new Get(Bytes.toBytes(rowKey));
            Result result = table.get(get);

            if (result.isEmpty()) {
                return null;
            }

            return convertResultToEntity(result);
        }
    }

    /**
     * 检查实体是否存在
     *
     * @param rowKey RowKey
     * @return 是否存在
     * @throws IOException HBase操作异常
     */
    public boolean exists(String rowKey) throws IOException {
        if (rowKey == null || rowKey.isEmpty()) {
            return false;
        }

        try (Table table = hbaseConnection.getTable(TableName.valueOf(getTableName()))) {
            Get get = new Get(Bytes.toBytes(rowKey));
            return table.exists(get);
        }
    }

    /**
     * 删除实体
     *
     * @param rowKey RowKey
     * @throws IOException HBase操作异常
     */
    public void delete(String rowKey) throws IOException {
        if (rowKey == null || rowKey.isEmpty()) {
            throw new IllegalArgumentException("RowKey不能为空");
        }

        try (Table table = hbaseConnection.getTable(TableName.valueOf(getTableName()))) {
            Delete delete = new Delete(Bytes.toBytes(rowKey));
            table.delete(delete);
            log.debug("成功删除实体, RowKey: {}", rowKey);
        }
    }

    /**
     * 将HBase Result转换为实体对象
     */
    protected T convertResultToEntity(Result result) {
        try {
            T entity = getEntityClass().getDeclaredConstructor().newInstance();
            Map<String, String> columnFamilyMapping = getColumnFamilyMapping();
            Field[] fields = getEntityClass().getDeclaredFields();

            for (Field field : fields) {
                if (isStaticField(field)) {
                    continue;
                }

                field.setAccessible(true);
                String fieldName = field.getName();
                String columnFamily = columnFamilyMapping.get(fieldName);

                if (columnFamily != null) {
                    byte[] value = result.getValue(Bytes.toBytes(columnFamily), Bytes.toBytes(fieldName));
                    if (value != null) {
                        Object convertedValue = convertFromBytes(value, field.getType());
                        field.set(entity, convertedValue);
                    }
                }
            }

            return entity;
        } catch (Exception e) {
            log.error("转换HBase结果为实体对象失败", e);
            throw new RuntimeException("转换HBase结果为实体对象失败", e);
        }
    }

    /**
     * 将Java对象转换为字节数组
     */
    protected byte[] convertToBytes(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            return Bytes.toBytes((String) value);
        } else if (value instanceof Integer) {
            return Bytes.toBytes((Integer) value);
        } else if (value instanceof Long) {
            return Bytes.toBytes((Long) value);
        } else if (value instanceof LocalDateTime) {
            return Bytes.toBytes(((LocalDateTime) value).format(DATE_TIME_FORMATTER));
        } else {
            return Bytes.toBytes(value.toString());
        }
    }

    /**
     * 将字节数组转换为Java对象
     */
    protected Object convertFromBytes(byte[] bytes, Class<?> targetType) {
        if (bytes == null || bytes.length == 0) {
            return null;
        }

        if (targetType == String.class) {
            return Bytes.toString(bytes);
        } else if (targetType == Integer.class || targetType == int.class) {
            return Integer.valueOf(Bytes.toString(bytes));
        } else if (targetType == Long.class || targetType == long.class) {
            return Long.valueOf(Bytes.toString(bytes));
        } else if (targetType == LocalDateTime.class) {
            return LocalDateTime.parse(Bytes.toString(bytes), DATE_TIME_FORMATTER);
        } else {
            return Bytes.toString(bytes);
        }
    }

    /**
     * 检查是否为静态字段
     */
    protected boolean isStaticField(Field field) {
        return java.lang.reflect.Modifier.isStatic(field.getModifiers());
    }
}
