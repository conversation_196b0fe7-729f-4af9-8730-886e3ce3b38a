package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.sender.HttpSender
import org.apache.http.impl.client.CloseableHttpClient
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotConstant_2011

Map entity = enquiry
Map tempValues = null != entity.get("tempValues") ? entity.get("tempValues") : tempValues
Map postParameters = postParameters
Map reqHeaders = reqHeaders
Map config = config;
def TbUtil = new common_2011()
//URL
String queryPaymentUrl = "https://issue.cpic.com.cn/ecar/payment/queryPayment"
String IDCardCollectUrl = "https://issue.cpic.com.cn/ecar/collect/IDCardCollect"
//是否个人

def appPerson = entity.insurePerson //投保人
def insuredPerson = entity.insuredPersonList[0]//被保人信息
//true:个人
boolean appliIsPerson = appPerson.idCardType == 7 || appPerson.idCardType < 6;
boolean insuredIsPerson = insuredPerson.idCardType == 7 || insuredPerson.idCardType < 6;
if (!insuredIsPerson && !appliIsPerson) {//都为团体车就报错
    TbUtil.getUserDefinedException('不支持投被保人都是团车类型')
}

//所有参数都要校验没有就报错
def header = [:]
header.'Content-Type' = 'application/json;charset=utf-8'
def param = JSON.parseObject('{"meta":{"pageSize":8},"redata":{"quotationNo":"","insuredNo":"INSUREDNO","policyHolder":"","partyName":"","insuredStartDate":"","insuredEndDate":""}}');
String insuredNo = entity?.SQ?.bizProposeNum ?: entity?.SQ?.efcProposeNum
if (!insuredNo)
    TbUtil.getUserDefinedException('身份证有效期为空,查询用投保单号为空，请确认查询数据正确性')
param.redata.insuredNo = insuredNo
reqHeaders.Host = "issue.cpic.com.cn"
reqHeaders.Accept = "application/json, text/javascript"
reqHeaders."X-Requested-With" = "XMLHttpRequest"
reqHeaders."Content-Type" = tempValues?.needSM4Flag ? 'text/plain' : "application/json;charset=UTF-8"
reqHeaders.Referer = "https://issue.cpic.com.cn/ecar/view/portal/page/premium_payment/premium_payment.html"
reqHeaders.Origin = "https://issue.cpic.com.cn"

def queryPaymentResult = HttpSender.doPostWithRetry(5,
        httpClient as CloseableHttpClient, true,
        RobotConstant_2011.URL.QUERY_PAYMENT,
        param.toJSONString(), null,
        reqHeaders,
        "UTF-8", null, "");
queryPaymentResult = Robot2011Util.decodeBody(tempValues, queryPaymentResult)
JSONObject queryPaymentResultObj = JSON.parseObject(queryPaymentResult)

def result = queryPaymentResultObj?.result

if (!result || result?.size() == 0)
    throw new InsReturnException("按投保单号" + insuredNo + "查无记录")
String quotationNo = result[0].'quotationNo'
if (!quotationNo)
    throw new InsReturnException("报价单号为空")

tempValues?.quotationNo = quotationNo
//整合其他不需要的省份
// 目前写死 团体车？
String idCardStart = ((String) appPerson.certificateIssueDate) ?: '2019-05-19'
String idCardEnd = ((String) insuredPerson.certificateValidDate) ?: '2039-05-19'
if (!StringUtil.areNotEmpty(idCardStart, idCardEnd)) {
    TbUtil.getUserDefinedException('身份证有效期为空,请检查申请数据')
}
param = JSON.parseObject('{"meta":{},"redata":{"type":1,"salesChannel":"","amendQueryNo":"","cancelQueryNo":"","machineCode":"05-03-20150316-0003066208","quotationNo":"QBEJ920Y1417F004898I","holderVo":{"type":1,"certiCode":"******************","name":"颜昊","customerType":"2","telephone":"13560184794","gender":"1","issuer":"广州市公安局越秀分局","nation":"汉族","certiStartDate":"2013-02-07","certiEndDate":"2023-02-07","birthDate":"1987-04-04","address":"上海市浦东新区外高桥保税区奥尼路88号"},"insureVo":{"type":2,"certiCode":"******************","name":"颜昊","customerType":"2","telephone":"13560184794","gender":"1","issuer":"广州市公安局越秀分局","nation":"汉族","certiStartDate":"2013-02-07","certiEndDate":"2023-02-07","birthDate":"1987-04-04","address":"上海市浦东新区外高桥保税区奥尼路88号"},"saveOrUpload":1}}')
param?.redata << [
        type        : 1,
        saveOrUpload: 1,
        quotationNo : quotationNo,
        machineCode : config?.machineCode,//采集器编码
]
def applicantMobile = TbUtil.getSupplyParamValue(entity?.supplyParam as List, tempValues, "applicantMobile")
def insuredMobile = TbUtil.getSupplyParamValue(entity?.supplyParam as List, tempValues, "insuredMobile")

if (appliIsPerson) {
    param?.redata?.holderVo = TbUtil.makeDataMap(appPerson, config, entity)
    param?.redata?.holderVo?.telephone = applicantMobile ?: insuredMobile
} else {
    param?.redata?.holderVo = TbUtil.baseDataMap()
}
if (insuredIsPerson) {
    param?.redata?.insureVo = TbUtil.makeDataMap(insuredPerson, config, entity, 2)
    param?.redata?.insureVo?.telephone = insuredMobile ?: applicantMobile
} else {
    param?.redata?.insureVo = TbUtil.baseDataMap("2")
}
//上传身份信息
def IDCardCollectResult = HttpSender.doPostWithRetry(5, httpClient, true, IDCardCollectUrl, StringUtil.chinesetoUnicode(param.toJSONString()), null, header, "UTF-8", null, "");

if (IDCardCollectResult) {
    def IDCardCollectResultObj = JSON.parseObject(IDCardCollectResult)
    if (IDCardCollectResultObj?.'message'?.'code' != 'success')
        TbUtil.getUserDefinedException("上传身份信息失败，平台提示" + IDCardCollectResultObj?.message?.message)
    param.clear()
    param = JSON.parseObject('{"meta":{},"redata":{"quotationNo":"QBEJ920Y1417F004898I"}}')
    param?.redata?.quotationNo = quotationNo
} else {
    TbUtil.getUserDefinedException('身份采集的信息返回异常')
}

String nullifyJson = param.toJSONString()
reqHeaders.'Content-Type' = tempValues?.needSM4Flag ? 'text/plain' : "application/json;charset=UTF-8"
postParameters.clear()
entity.tempValues = tempValues

Robot2011Util.genBody(tempValues, nullifyJson)
