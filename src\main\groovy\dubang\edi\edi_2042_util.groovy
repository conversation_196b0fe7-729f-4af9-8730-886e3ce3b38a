package dubang.edi

import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.cache.RedisCache
import com.cheche365.bc.constants.Constants
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.model.RuleInfoKey
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.PlatformUtil
import com.cheche365.bc.utils.RuleUtil
import com.cheche365.bc.utils.sdas.SDASUtils
import com.cheche365.bc.utils.sender.HttpSender
import common.common_all
import org.apache.commons.lang3.StringUtils
import org.apache.http.entity.ByteArrayEntity

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.regex.Matcher
import java.util.regex.Pattern

def getHeader(reqHeaders) {
    reqHeaders << [
            'content-type' : 'application/json;charset=UTF-8',
            'Authorization': 'eyJhbGciOiJIUzUxMiJ9.eyJpcCI6Im51bGwiLCJ1dWlkIjoiMzEyMjVjMTQ1YWVlNDQ3ZjgwZDA2MTBhMzlkZTQ2YjgiLCJhdWQiOiJodHRwOi8vd3d3LmRmaHRrZy5jb20uY24iLCJpc3MiOiJodHRwOi8vd3d3LmRiaWMuY29tLmNuIiwic3ViIjoiSzAwMDAwMDAwNSIsImlhdCI6MTcwNjg1NzU0MiwiZXhwIjoxNzA2OTAwNDAwfQ.-HC5MKw9PbtE6nkS6TrW5XWurJcfHHxArhJOL5brkV6WPVPwYfIc1t8h3oisSsd_uZSWDz6uVcd3RVQkypC5jw'
    ]
}

static def getEnv(env) {
    env == 'test' ? 'https://saastest.dbic.com.cn' : 'https://s.dbic.com.cn'
}

static def getUrl(key, env) {
    def urls = [
            //获取公钥
            'pubKeyUrl'              : '/dbic/ucs/rsa/pubKey',
            //获取token
            'tokenUrl'               : '/dbic/ucs/user/login',
            //查车接口
            'carModelQueryUrl'       : '/dbic/qos/carModel/query',
            //险别初始化
            'productClauseInitUrl'   : '/dbic/bds/productClause/init',
            //车价计算
            'calculaCarUrl'          : '/dbic/callout/calculaCar/calcula',
            //报价
            'quoteUrl'               : '/dbic/qos/channel/quote/enquiry',
            //转保单投保查询校验接口
            'quoteVerifyUrl'         : '/dbic/qos/quote/verify',
            //新车备案
            'newCarRecordUrl'        : '/dbic/qos/quote/newCarRecord',
            //获取特约
            'getBDEngageUrl'         : '/dbic/bds/bdengage/getBDEngage',
            //报价单保存
            'preSaveUrl'             : '/dbic/qos/channel/quote/preSave',
            //询价单提交核保
            'quoteToProposalUrl'     : '/dbic/qos/proposal/quoteToProposal',
            //影像上传
            'uploadImageUrl'         : '/dbic/ecm/images/uploadAndQuoteToQosImage',
            //查询保单核保以及生效状态
            'queryStatusUrl'         : '/dbic/qos/channel/quote/queryStatus',
            //电子保单下载
            'downLoadUrl'            : '/dbic/pls/elePolicy/downLoad',
            //人工核保信息查询接口
            'queryUwNotionUrl'       : '/dbic/qos/proposal/queryUwNotion',
            //平台信息查询接口
            'queryPlatInfoUrl'       : '/dbic/qos/quote/queryPlatInfo',
            //非车险查询
            'getCarRelatedManPlanUrl': '/dbic/ncs/process/getCarRelatedManPlan'

    ]
    getEnv(env) + urls[(key)]
}

static def getParam(body) {
    def nonceStr = System.currentTimeMillis()
    def param = [
            'body'    : body,
            'nonceStr': nonceStr
    ]
    def bodyStirng = JSONObject.toJSONString(body)
    if (bodyStirng) {
        Pattern p = Pattern.compile("\\s*|\t|\r|\n")
        Matcher m = p.matcher(bodyStirng)
        bodyStirng = m.replaceAll("")
    }
    def signStr = "${bodyStirng}&${nonceStr}&EFQQLJNTR7HF8U3DJFPDO6FNX6XWTJY9".toString()
    param << [sign: StringUtil.MD5(signStr)]
}

static def getResult(result, userCode) {
    if (result?.responseCode != '1') {
        def errorMsg = result?.errorMsg as String
        if (errorMsg == 'token expired') {
            def redisTemplate = RedisCache.getStringRedis()
            redisTemplate.delete(Constants.EDI_TOKEN + '2042:token:' + userCode)
            redisTemplate.delete(Constants.EDI_TOKEN + '2042:pubKey')
        }
        if (errorMsg && (errorMsg.contains('备案') || errorMsg.contains('重复投保'))) {
            return errorMsg
        }
        if (!errorMsg && result?.responseMsg) {
            errorMsg = result?.result?.data?.records[0] ? result?.result?.data?.records[0].info : result?.responseMsg
        }
        throw new InsReturnException(errorMsg)
    }
}

//判断关系人类型
static def checkRelation(idType) {
    // InsuredNature 3-自然人 4-法人;
    // InsuredType 1-个人 2-机关 3-企业
    def insuredNature = '3'
    def insuredType = '1'
    if (idType in [6, 8, 10]) {
        insuredNature = '4'
        insuredType = '3'
    }
    return ['insuredNature': insuredNature, 'insuredType': insuredType]
}

// InsureType 1-被保险人 2-投保人 3-车主;
// InsuredNature 3-自然人 4-法人;
// InsuredType 1-个人 2-机关 3-企业
static def getInsuredList(person, insureType, autoTask) {
    def map = new edi_2042_mapUtil()
    def mobileNumber = common_all.getPersonSupply(autoTask, 'applicantMobile') ?: person?.mobile ?: '13317781778'
    def email = common_all.getPersonSupply(autoTask, 'applicantEmail') ?: person?.email ?: '<EMAIL>'
    return [
            'insuredName'   : person?.name,//关系人名称
            'identifyType'  : map.relationIdType(person?.idCardType),//证件类型， 字典：IdentifyType(上海地区:IdentifyTypeSH)
            'idenfityNumber': person?.idCard,//个人身份证号码/法人组织机构号码
            'insuredFlag'   : insureType,//关系人标志， 字典：InsureType
            'insuredNature' : checkRelation(person?.idCardType)?.insuredNature,//关系人性质， 字典：InsuredNature
            'insuredType'   : checkRelation(person?.idCardType)?.insuredType,//关系人类型,  字典:InsuredType
            'mobileNumber'  : mobileNumber,//手机号，投保意健险时必传
            'email'         : email //邮箱，投保意健险时必传
    ]
}

static def getEndTime(date) {
    def dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    def LocalDate = LocalDate.parse(date.substring(0, 10) as String, dateTimeFormatter)
    if (LocalDate.getDayOfMonth() == 29 && LocalDate.getMonthValue() == 2) {
        return LocalDate.parse(date.substring(0, 10) as String, dateTimeFormatter).plusYears(1).plusDays(1).format(dateTimeFormatter) + ' 00:00:00'
    }
    def end = LocalDate.plusYears(1).format(dateTimeFormatter) + ' 00:00:00'
    if (date.substring(11, 16) != '00:00') {
        end = LocalDate.plusYears(1).format(dateTimeFormatter) + ' ' + date.substring(11, 19)
    }
    end
}

static def getEngageBody(riskCode, kindList, enquiry) {
    def mapUtil = new edi_2042_mapUtil()

    return [
            'insuranceId'  : 'DBIC',
            'riskCode'     : riskCode,
            'comCode'      : enquiry.configInfo.configMap.comCode,
            'carKindCode'  : mapUtil.carKind(enquiry?.carInfo?.syvehicletypecode),
            'seatCount'    : enquiry?.carInfo?.seatCnt,
            'useNatureCode': mapUtil.useNature(enquiry.carInfo.useProps),
            'appliName'    : enquiry?.insurePerson?.name,
            'insuredName'  : enquiry?.insuredPersonList[0]?.name,
            'carOwner'     : enquiry?.carOwnerInfo?.name,
            'kindList'     : kindList
    ]
}


//询价单保存关系人
static def getInsures(insureType, mobileNumber, emailAddress, address, idValidDate, effectiveDate) {
    idValidDate = idValidDate == '长期' ? '2099-12-31' : idValidDate
    return [
            //序号
            'serialNo'     : insureType,
            //关系人标志
            'insureFlag'   : insureType,
            //手机号
            'mobileNumber' : mobileNumber,
            //邮箱
            'emailAddress' : emailAddress,
            //联系地址
            'address'      : address?.detail,
            //证件有效止期
            'idValidDate'  : idValidDate,
            //证件有效起期
            'effectiveDate': effectiveDate
    ]
}

// 1:商业 2：交强
static def getEngages(engages, tempValuesEngages, riskType) {
    def serialNo = engages.size() + 1
    tempValuesEngages.each {
        engages << [
                'serialNo'  : serialNo,
                'riskType'  : riskType,
                'clauseCode': it?.clauseCode,
                'title'     : it?.clauseName,
                'content'   : it?.clauseContext
        ]
        serialNo++
    }
    engages
}

/**
 *  保单上传
 */
static def uploadPolicy(enquiry, type, policyNo, records) {
    def url = records?.elecDownAddress as String
    if (!url) {
        throw new InsReturnException('未返回保单下载地址')
    }
    try (def httpClient = HttpSender.buildHttpClient()) {
        byte[] b = HttpSender.doGet(httpClient, url, null, 'UTF-8', null, true) as byte[]
        if (b) {
            String fileHeader = new common_all().bytesToHexString(b)
            if (StringUtils.isNotBlank(fileHeader) && fileHeader.length() > 14) {
                def upperCase = fileHeader.substring(0, 14).toUpperCase() as String
                if ('255044462D312E' == upperCase) {
                    if (type == 'efc') {
                        def flagUrl = records?.markDownAddress as String
                        if (!flagUrl) {
                            throw new InsReturnException('未返回交强险标志下载地址')
                        }
                        def flag_b = HttpSender.doGet(httpClient, flagUrl, null, 'UTF-8', null, true) as byte[]
                        //合并PDF
                        if (flag_b.length > 100000) {
                            try {
                                b = new common_all().mergePDF(b, flag_b)
                            } catch (Exception e) {
                                throw new InsReturnException('2042edi保单下载异常')
                            }
                        }
                    }
                    def fileName = "river_attachment/2042/${policyNo}/${LocalDate.now().toString()}/${policyNo}${type}_${System.currentTimeMillis()}.pdf"
                    //占位
                    def assetResult = SDASUtils.assetOccupancy('application/pdf', fileName, fileName)
                    if (!assetResult || assetResult.code != 0) {
                        throw new InsReturnException('2042--PDF占位异常')
                    }
                    //上传
                    ByteArrayEntity byteArrayEntity = new ByteArrayEntity(b)
                    def uploadResult = SDASUtils.uploadPutAsset(fileName, assetResult, byteArrayEntity)
                    if (!uploadResult) {
                        throw new InsReturnException(InsReturnException.Others, "2042上传资产库失败")
                    }
                    //回写ID
                    enquiry?.SQ << [(type + 'Id'): uploadResult.id]
                } else {
                    throw new InsReturnException(InsReturnException.Others, "保司文件异常请稍后重")
                }
            }
        } else {
            throw new InsReturnException(InsReturnException.Others, "保司返回文件为空")
        }
    }
}

/**
 * 解析补充数据项
 * @param supplyParams
 * @return
 */
def getSupplyParamMap(supplyParams) {
    def supplyParamMap = [:]
    supplyParams.each {
        if (it['itemcode'].contains('Gender')) {
            it['itemvalue'] = (Integer.valueOf(it['itemvalue'] as String) - 1).toString()
        }
        supplyParamMap << [(it['itemcode']): it['itemvalue']]
    }
    supplyParamMap
}

/**
 * 去除地址无用信息
 */
def splitAddress(def addressAll) {
    def addressInfo = '' as String
    if (addressAll) {
        if (!addressAll.contains('|') && !addressAll.contains('#')) {
            return addressAll
        }
        def addressAllList = addressAll.split('\\|').size() > 1 ? addressAll.split('\\|') : [] as List
        addressAllList.each {
            if (it.contains('#')) {
                addressInfo = addressInfo + it?.split('#')[1]
            } else {
                addressInfo = addressInfo + it
            }
        }
    }
    addressInfo
}

static def writeBack(records, enquiry, tempValues) {
    enquiry?.SQ = enquiry?.SQ ?: [:]
    //询价单保存需要 询价流水号
    tempValues.calculateNo = records['calculateNo']
    //总保费
    def totalCharge = new BigDecimal(0)
    //商业保费
    if (enquiry.baseSuiteInfo.bizSuiteInfo) {
        def bizSuiteInfo = enquiry.baseSuiteInfo.bizSuiteInfo
        bizSuiteInfo << [
                //折扣保费
                'discountCharge': new BigDecimal(records?.premiumBi?.sumPremium as String ?: '0'),
                //原始保费
                'orgCharge'     : new BigDecimal(records?.premiumBi?.sumBenchmarkPremium as String ?: '0'),
                //折扣系数
                'discountRate'  : new BigDecimal(records?.premiumBi?.discount as String ?: '0')
        ]
        totalCharge = totalCharge.add(new BigDecimal(records?.premiumBi?.sumPremium as String ?: '0'))
        //险种
        def premiumKinds = records?.premiumKinds as List
        def mapUtil = new edi_2042_mapUtil()
        def enquirySuiteInfo = enquiry.baseSuiteInfo?.bizSuiteInfo?.suites as List
        def suitesArray = []
        tempValues.kindForBDEngage = []
        premiumKinds.each {
            def kindInfo = mapUtil.getReturnRiskCode(it?.kindCode)
            if (!kindInfo) {
                throw new InsReturnException('险种匹配错误！')
            }
            def amount = enquirySuiteInfo.find { it['code'] == kindInfo[0] }?.amount as String
            def suite = [
                    'discountRate'  : new BigDecimal(records?.premiumBi?.discount as String ?: '1'),
                    //折扣保费
                    'discountCharge': new BigDecimal(it['premium'] as String ?: '0'),
                    //原始保费
                    'orgCharge'     : new BigDecimal(it['benchMarkPremium'] as String ?: '0'),
                    //保额
                    'amount'        : amount ? new BigDecimal(amount) : '0',
                    //
                    'code'          : kindInfo[0],
                    //
                    'name'          : kindInfo[1]
            ]
            //车损险保额
            if (kindInfo[0] == 'VehicleDamage') {
                suite['amount'] = new BigDecimal(it['amount'] as String)
            }
            tempValues.kindForBDEngage << ['kindcode': it?.kindCode]
            suitesArray << suite
        }
        enquiry.baseSuiteInfo.bizSuiteInfo.suites = suitesArray
        enquiry?.SQ << [
                'bussDiscountRate': enquiry.baseSuiteInfo.bizSuiteInfo.discountRate,
                'bizCharge'       : enquiry.baseSuiteInfo.bizSuiteInfo.discountCharge
        ]
    }
    //交强保费
    if (enquiry.baseSuiteInfo.efcSuiteInfo) {
        def efcSuiteInfo = enquiry.baseSuiteInfo.efcSuiteInfo
        efcSuiteInfo << [
                'discountRate'  : new BigDecimal(records?.premiumCi?.discount as String ?: '1'),
                'amount'        : new BigDecimal(records?.premiumCi?.sumAmount as String ?: '0'),
                'orgCharge'     : new BigDecimal(records?.premiumCi?.basedPremium as String ?: '0'),
                'discountCharge': new BigDecimal(records?.premiumCi?.standardPremium as String ?: '0')
        ]
        totalCharge = totalCharge.add(new BigDecimal(records?.premiumCi?.standardPremium as String ?: '0'))
        enquiry?.SQ << [
                'trafficDiscountRate': enquiry.baseSuiteInfo.efcSuiteInfo.discountRate,
                'efcCharge'          : enquiry.baseSuiteInfo.efcSuiteInfo.discountCharge
        ]
    }
    //车船税
    if (records?.carShipTax) {
        enquiry?.baseSuiteInfo?.taxSuiteInfo = enquiry?.baseSuiteInfo?.taxSuiteInfo ?: [:]
        enquiry?.baseSuiteInfo?.taxSuiteInfo << ['charge': new BigDecimal(records?.carShipTax?.sumTax as String)]
        totalCharge = totalCharge.add(new BigDecimal(records?.carShipTax?.sumTax as String))
        enquiry?.SQ << ['taxCharge': enquiry.baseSuiteInfo.taxSuiteInfo.charge]
    }
    //非车险
    if (enquiry?.SQ?.nonMotor) {
        def premiumHi = records?.premiumHi ?: tempValues.premiumHi
        def trialPremium = new BigDecimal(premiumHi?.trialpremium as String)
        enquiry?.SQ?.nonMotor << [
                'discountCharge'     : trialPremium,
                'accidentProposeCode': premiumHi?.subpolicyno
        ]
        totalCharge = totalCharge.add(trialPremium)
    }
    enquiry?.SQ << ['totalCharge': totalCharge]
    //回写理赔信息
    def ciPlatQueryOutClaims = records?.ciPlatQuery?.ciPlatQueryOutBody?.ciPlatQueryOutClaims
    if (ciPlatQueryOutClaims) {
        dealClaimInfo(enquiry, ciPlatQueryOutClaims)
    }
    //回写平台车型代码
    def modelCode = records?.carModels?.get(0)?.modelCode ?: records?.modelPrices?.get(0)?.modelCode
    if (modelCode) {
        tempValues?.carRecord?.platModelCode = modelCode
    }
}

//验证码替换
static def replaceCheckCode(code) {
    return code.replace('0', 'O')
}

//回写平台规则
static def doRuleAndPlat(records, autoTask, enquiry, tempValues) {
    def platformBack = [:]
    if (enquiry?.claimInformation) {
        platformBack << [
                (PlatformKey.bwCompulsoryClaimTimes)          : enquiry?.claimInformation?.size() as String,
                (RuleInfoKey.application_compulsoryClaimTimes): enquiry?.claimInformation?.size() as String
        ]
    }
    if (records?.premiumBi) {
        platformBack << [
                //无赔款优待系数
                (PlatformKey.noClaimDiscountCoefficient)      : records?.premiumBi?.claimAdjustValue,
                //折扣系数,
                (RuleInfoKey.geniusItem_policyDiscount)       : records?.premiumBi?.discount,
                //连续承保年数
                (PlatformKey.bizContinuityInsureYears)        : records?.premiumBi?.insureYears as Integer,
                //连续承保期间出险次数
                (PlatformKey.bwCommercialClaimTimes)          : records?.premiumBi?.claimTimes as String,
                (RuleInfoKey.application_commercialClaimTimes): records?.premiumBi?.claimTimes as String,
        ]
    }
    if (platformBack) {
        enquiry.definition = enquiry?.definition ?: [:]
        enquiry.definition << platformBack
        platformBack.definition = enquiry.definition
        tempValues.platformBack = platformBack
        RuleUtil.doRuleInfo(autoTask, enquiry, tempValues, '2042', '', '')
        PlatformUtil.doBackPlatformInfo(autoTask, enquiry, tempValues)
        def ruleInfo = tempValues['ruleInfo']
        tempValues.geniusItemTotalDiscount = ruleInfo ? ruleInfo['geniusItem.policyDiscount'] : ''
    }
}

static def dealClaimInfo(enquiry, claims) {
    def claimInformation = []
    claims?.each {
        if (it) {
            def accidentTime = it?.accidentTime
            def endcaseTime = it?.endcaseTime
            claimInformation << [
                    "company"     : it?.companyId,
                    "accidentTime": accidentTime ? "${accidentTime.substring(0, 4)}-${accidentTime.substring(4, 6)}-${accidentTime.substring(6, 8)}".toString() : '',
                    "closingTime" : endcaseTime ? "${endcaseTime.substring(0, 4)}-${endcaseTime.substring(4, 6)}-${endcaseTime.substring(6, 8)}".toString() : '',
                    "amount"      : it?.claimAmount ? new BigDecimal(it?.claimAmount as String) : ''
            ]
        }
    }
    enquiry << ['claimInformation': claimInformation]
}

static def policyDownParam(businessNo) {
    getParam([
            'businessNo'  : businessNo,
            'businessType': 'P',
            'riskCode'    : businessNo.substring(1, 5)
    ])
}