package taipingyang.robot.module

import java.nio.charset.Charset
import java.nio.charset.StandardCharsets

/**
 * 太保精灵常量定义
 * <AUTHOR>
 */
class RobotConstant_2011 {

    static final def JY_FUEL_TYPE_LIST = ['D6', 'D8', 'D12']

    /**
     * 太保官网永远只会报今年的
     */
    static final def TAX_NOW_TIME_AREAS = ['山东', '河北', '东莞', '四川', '广州市', '河南', '重庆', '佛山', '湖北', '宁波', '陕西', '佛山市']

    /**
     * 广东平台增加新车销售信息承保管控规则
     */
    static final def NEED_NEW_CAR_SALE_INFO_AREA = ['东莞', '广州', '广州市']

    public static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8

    public static final String DEFAULT_CHARSET_NAME = DEFAULT_CHARSET.name()

    public static final def REGEX_ADDRESS = /^(?<provinceName>.*?)\|(?<provinceCode>\d{6})#(?<cityName>.*?)\|(?<cityCode>\d{6})/ + /#(?<districtName>.*?)\|(?<districtCode>\d{6})#(?<address>.*)/
    /**
     * 默认请求头
     */
    public static final Map<String, String> DEFAULT_HTTP_HEAD = [
            'Content-Type': 'application/json;charset=UTF-8'
    ] as HashMap

    public static final Map<String, String> DEFAULT_HTTP_TEXT_HEAD = [
            'Content-Type': 'text/plain'
    ] as HashMap

    public static final String DEFAULT_RSA_PRIVATE_KEY = 'MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAhuJQ9YCs82meHB3+iuGZWlYAAwSYWJX0oYriieGd0nzNpOeYSBqoMuh31blJMYVPV5h45cYIAMXsmKzCTyKBQwIDAQABAkBdzkNTmbuuRUPKdimyWJcYNjsn3ig9Y3yQZFCMZ7YkzdiO5kVcp2xhXBYicce/ATqqnxXX4+Gs+PeRvYl2SFuxAiEAvMseIbCwN5+oEsnmScaiVXIew9WrKfVIbVpxAnMvwFcCIQC25mXDugWRhMTaBH09fUgXNJVCqrOoECvFNmie2E9C9QIhAKHx9jneD3rXWFwtln4ohm2XQ+6m5XlLb0Jmd1QrrIVnAiBNDy15FXsbIHE/2fxaV9pzzHfGqt0exhFq+c2Cf2iFsQIgFy+WAG7hW9aKimo2iqTkJRZjb1l5DF21u9fU+DiY+3E='

    public static final String DEFAULT_RSA_PUBLIC_KEY = 'MIGMMA0GCSqGSIb3DQEBAQUAA3sAMHgCcQCgsLGfojzISaR2cfzohePXmM0zTL+hHM1Csg33yWrCtuIc8pLl9jnf9fhmA/SImHwTg/8yvSrZM/v1EGII7ZxAm8qKQXa0NlsnbXuyPYbIfdWGaLojlSbPVzKKVzEWGH4MtAjj+dQtlhXa5JfmhMftAgMBAAE='

    interface URL {

        String queryPureriskAndVehicleInfoURL = 'https://issue.cpic.com.cn/ecar/quickoffer/queryPureriskAndVehicleInfo'
        String getQuotationNoURL = 'https://issue.cpic.com.cn/ecar/quickInsure/quickSave'
        //查询税率规则，保留
        String getTaxTypeURL = 'https://issue.cpic.com.cn/ecar/tax/queryTaxRule'
        //
        String queryInsureInfoURL = 'https://issue.cpic.com.cn/ecar/insure/queryInsureInfo'
        String queryPlatformBodyByVinAndVehicle = 'https://issue.cpic.com.cn/ecar/ecar/queryPlatformBodyByVinAndVehicle'

        String SAVE_INSURE_INFO = 'https://issue.cpic.com.cn/ecar/insure/saveInsureInfo'
        /**
         * 暂存接口
         */
        String QUICK_SAVE = 'https://issue.cpic.com.cn/ecar/quickInsure/quickSave'

        /**
         * 报价接口
         */
        String CALCULATE = 'https://issue.cpic.com.cn/ecar/quickoffer/calculate'

        /**
         * 查询非车经办人信息
         */
        String QUERY_MSB_AGENT = 'https://issue.cpic.com.cn/ecar/insure/queryMsbAgent'

        /**
         * 查询特约条款
         */
        String QUERY_SPECIAL_AGREEMENT = 'https://issue.cpic.com.cn/ecar/quickInsure/querySpecialAgreement'

        /**
         * 交管查询获取验证码
         */
        String VEHICLE_QUERY_VALIDATION = 'https://issue.cpic.com.cn/ecar/ecar/vehicleQueryValidation'

        /**
         * 文件上传
         */
        String FILE_UPLOAD = 'https://issue.cpic.com.cn/ecar/fileUpload/uploadMore'

        String FIND_THREE_PEOPLE = 'https://issue.cpic.com.cn/ecar/jyBusiness/findThreePeople'

        /**
         * 查询配置信息
         */
        String QUERY_CONFIGS = 'https://issue.cpic.com.cn/ecar/page/queryConfigs'

        String QUERY_FAST_LOGIN_INFO = 'https://issue.cpic.com.cn/ecar/auth/queryFastLoginInfo'

        String PARTNERSELECT = 'https://issue.cpic.com.cn/ecar/view/portal/page/common/partnerselect.html'

        String QUERY_AGENCY_BY_CODE = 'https://issue.cpic.com.cn/ecar/queryAgencyByCode/query'

        String QUERY_QUOTATION_POLICY = 'https://issue.cpic.com.cn/ecar/quotationPolicy/queryQuotationPolicy'

        String QUERY_PAYMENT = 'https://issue.cpic.com.cn/ecar/payment/queryPayment'

        String ID_CARD_COLLECT = 'https://issue.cpic.com.cn/ecar/collect/IDCardCollect'

        String QUERY_ECARCERT_TYPE = 'https://issue.cpic.com.cn/ecar/payment/queryEcarcertType'

        String QUERY_PAYMENT_RECORD = 'https://issue.cpic.com.cn/ecar/paymentrecord/query'

        String WHOLE_COUNTRY_CERTIFICATION = 'https://issue.cpic.com.cn/ecar/payment/wholeCountryCertification'

        String PAYMENT_RECORD_MAN = 'https://issue.cpic.com.cn/ecar/payment/paymentRecordMan'

        String MORE_BJ_IDENTIFY_CODE = 'https://issue.cpic.com.cn/ecar/update/moreBjIdentifyCode'

        String PAYMENT_QUERY_PAY = 'https://issue.cpic.com.cn/ecar/paymentQuery/paymentQuery'
    }

}
