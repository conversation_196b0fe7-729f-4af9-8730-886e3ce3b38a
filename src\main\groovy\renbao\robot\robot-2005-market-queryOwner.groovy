package renbao.robot

import com.cheche365.bc.exception.TempSkipException
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript

import static renbao.robot.common_market.header
import static renbao.robot.common_market.personInfoQueryUrl



@BaseScript BaseScript_Http_Enq _


assertNotNull('未送传车主信息', entity?.order?.carOwnerInfo)

if (entity.order.carOwnerInfo.idCard == entity.order.insuredPersons[0].idCard || entity.order.carOwnerInfo.idCard == entity.order.insurePerson.idCard) {
    throw new TempSkipException(1, '车主与被保人或投保人相同，跳过当前模板')
}

head(header(autoTask))

tempValues['queryOwner'] = personInfoQueryUrl(autoTask, 'owner')
