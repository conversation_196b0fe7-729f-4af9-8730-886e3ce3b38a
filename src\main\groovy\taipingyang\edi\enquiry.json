{"changeSuites": {"add": [{"amount": 7, "code": "RoadsideService", "name": "附加机动车增值服务特约条款（道路救援服务）"}], "update": [], "remove": []}, "insComId": "2011", "agentInfo": {"idCardType": 0, "idCard": "340111199509108510", "mobile": "***********", "name": "汤震", "misc": {}}, "carOwnerInfo": {"birthday": "1995-09-10", "censusRegister": "340111", "idCard": "340111199509108510", "sex": 0, "mobile": "***********", "idCardType": 0, "name": "汤震", "email": "<EMAIL>"}, "insuredPersonInfoList": [{"birthday": "1995-09-10", "censusRegister": "340111", "idCard": "340111199509108510", "sex": 0, "mobile": "***********", "idCardType": 0, "name": "汤震", "email": "<EMAIL>"}], "businessId": "2024112600030475@2011", "insArea": {"province": "340000", "city": "340100"}, "supplyParam": [{"itemvalue": "***********", "itemcode": "applicantMobile"}, {"itemvalue": "***********", "itemcode": "insuredMobile"}, {"itemvalue": "2043-11-26", "itemcode": "insuredIDCardValidDate"}, {"itemvalue": "***********", "itemcode": "ownerMobile"}, {"itemvalue": "2023-11-26", "itemcode": "applicantIDCardRegistrationDate"}, {"itemvalue": "2024-11-26", "itemcode": "carOriginProofDate"}, {"itemvalue": "2023-11-26", "itemcode": "insuredIDCardRegistrationDate"}, {"itemvalue": "81006027", "itemcode": "carOriginProofNo"}, {"itemvalue": "安徽省合肥市瑶海区张洼路花香美地8-2501", "itemcode": "owner<PERSON><PERSON><PERSON>"}, {"itemvalue": "<EMAIL>", "itemcode": "applicantEmail"}, {"itemvalue": "2043-11-26", "itemcode": "applicantIDCardValidDate"}], "baseSuiteInfo": {"taxSuiteInfo": {}, "bizSuiteInfo": {"suites": [{"amount": 1, "code": "VehicleDamage", "name": "新能源汽车损失险", "share": false}, {"amount": 3000000, "code": "ThirdParty", "name": "新能源汽车第三者责任险", "share": false}, {"amount": 20000, "code": "Driver", "name": "新能源汽车车上人员责任险(司机)", "share": false}, {"amount": 20000, "code": "Passenger", "name": "新能源汽车车上人员责任险(乘客)", "share": false}, {"amount": 7, "code": "RoadsideService", "name": "附加机动车增值服务特约条款（道路救援服务）"}], "start": "2024-11-27 00:00:00", "end": "2025-11-26 23:59:59"}, "efcSuiteInfo": {"amount": 200000, "start": "2024-11-27 00:00:00", "end": "2025-11-26 23:59:59"}}, "appId": "F1E5CA11", "applicantPersonInfo": {"birthday": "1995-09-10", "censusRegister": "340111", "idCard": "340111199509108510", "sex": 0, "mobile": "***********", "idCardType": 0, "name": "汤震", "email": "<EMAIL>"}, "carInfo": {"yearPattern": "2024", "taxAnalogyPrice": "0", "carModelName": "特斯拉TSL6480BEVAR0纯电动多用途乘用车", "fullLoad": 1911, "modelLoad": 0, "noLicenseFlag": false, "plateColor": 2, "jyPrice": 249900, "listedYear": "202402", "price": 239900, "familyName": "特斯拉MODEL Y", "vin": "LRWYGCFJ7RC853794", "displacement": 0, "vehicleId": "4028d06d8e79e5b2018e983b4b265d55", "isLocalDriving": "1", "aliasName": "特斯拉MODEL Y 后轮驱动版", "analogyPrice": 0, "carPriceType": "0", "useProps": 1, "isTransfer": false, "plateType": 1, "firstRegDate": "2024-11-26 00:00:00", "engineNum": "TG324321001XK4", "isLocalRegistration": "1", "isNew": true, "plateNum": "新车未上牌", "carBrandName": "特斯拉(中国)", "carUserType": 0, "syvehicletypename": "六座以下客车", "rbCode": "TSHADD0020", "jgVehicleType": 13, "syvehicletypecode": "KA", "fuelType": 1, "seatCnt": 5, "jyCode": "TSHADD0020", "taxPrice": 249900}, "configInfo": {"configMap": {"partnerClientType": 1, "terminalNo": "TSL_CC_HEFEI", "sellingChannel": "21", "domain": "https://nbc.chetimes.com", "litigationArbitration": "1", "payClientCode": "10", "apiType": 2, "isTPreminum": "0", "preInsure": true}}, "processType": "edi", "sq": {"nonMotor": {"productCode": "P241898002200001355", "count": 1}, "checkMsg": "", "topOrderNo": "top3421a0bb3615d4cb", "isRuleTips": true, "efcOrderNo": "efc3421a0bb3615d4cb", "isRuleSuccess": "1", "bizOrderNo": "biz3421a0bb3615d4cb", "misc": {}}}