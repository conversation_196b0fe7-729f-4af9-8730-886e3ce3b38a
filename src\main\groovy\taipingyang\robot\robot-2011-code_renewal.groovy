package taipingyang.robot

import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateTime
import cn.hutool.core.date.DateUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.model.car.*
import com.cheche365.bc.tools.BigDecimalUtil
import com.cheche365.bc.tools.StringUtil
import com.google.common.collect.Maps
import common.scripts.BaseScript_Code_Enq
import groovy.transform.BaseScript
import org.apache.http.impl.client.CloseableHttpClient
import taipingyang.robot.module.*

@BaseScript BaseScript_Code_Enq _

//精友车型信息
def queryEcarVoByJYResp = tempValues?.queryEcarVoByJY as JSONObject
//续保查询信息
def queryQuickOfferByPlateResp = tempValues?.queryQuickOfferByPlate as JSONObject
//当期登录人员信息
def currentUser = tempValues?.queryConfigs?.currentUser as JSONObject
def TbUtil = new common_2011()
//设置车辆信息
tempValues.special_car = queryEcarVoByJYResp
//写入新能源标识
if (RobotConstant_2011.JY_FUEL_TYPE_LIST.contains(queryEcarVoByJYResp?.jyFuelType)) {
    //新能源标识
    tempValues.NEFlag = '1'
}
if (queryEcarVoByJYResp.containsKey('tpyRiskflagName')) {
    tempValues.tpyRiskflagName = queryEcarVoByJYResp.getString('tpyRiskflagName')
}
//写入投保人信息
if (queryQuickOfferByPlateResp.containsKey('holderVo')) {
    writeInsurePerson(queryQuickOfferByPlateResp.getJSONObject('holderVo'))
}
//写入被保人信息
if (queryQuickOfferByPlateResp.containsKey('insuredVo')) {
    writeInsuredPersons(queryQuickOfferByPlateResp.getJSONObject('insuredVo'))
}
//写入权益人信息
if (queryEcarVoByJYResp.containsKey('claimerVo')) {
    writeBeneficiaryPersons(queryEcarVoByJYResp.getJSONObject('claimerVo'))
}
//写入车辆信息和车主信息
if (queryQuickOfferByPlateResp.containsKey('ecarvo')) {
    def ecarvo = queryQuickOfferByPlateResp.getJSONObject('ecarvo')
    //写入车主信息
    writeCarOwner(ecarvo)
    //写入车辆信息
    writeCarInfo(ecarvo)
    //合作伙伴信息
    writeCooperatorInfoToTempValues(ecarvo, currentUser)
    //写入平台信息
    writePTMsg(ecarvo, queryEcarVoByJYResp)
    //车辆使用性质细类
    tempValues.usageSubdivs = ecarvo.getString('usage') == '101' ? '' : ecarvo.getString('vehicleUsage2')
    tempValues.seatCnt = order.carInfo.seatCnt
}

//flag、车辆种类、车辆用途、车船税交管车辆类型（宁波）
def (flag, vehicleType, vehiclePurpose, taxVehicleCode) = RobotDict_2011.getVehicle(autoTask, entity)
tempValues.tb_car_info = [
        'vehiclePurpose': vehiclePurpose,
        'vehicleType'   : vehicleType,
        'taxVehicleCode': taxVehicleCode
]


//初始化投保险种配置信息
def baseSuiteInfo = order.suiteInfo ?: new BaseSuiteInfo()
order.suiteInfo = baseSuiteInfo
//交强险信息
if (tempValues.ExistCI
        && queryQuickOfferByPlateResp.containsKey('compulsory')
        && queryQuickOfferByPlateResp.getBooleanValue('compulsory')) {
    //计算交强险原价
    String efcOrgCharge = TbUtil.getEfcOrgCharge(order.carInfo.useProps.toString(), flag)
    if (StringUtil.isNoEmpty(efcOrgCharge)) {
        tempValues.efcOrgCharge = efcOrgCharge
    }
    def compulsoryInsuransVo = queryQuickOfferByPlateResp.getJSONObject('compulsoryInsuransVo')
    writeEfcSuiteInfo(compulsoryInsuransVo)
    order.suiteInfo.taxSuiteInfo = order.suiteInfo.taxSuiteInfo ?: new TaxSuiteInfo()
}
//商业险信息
boolean needWriteBiz = tempValues.bizSameLastYear
        && queryQuickOfferByPlateResp.containsKey('commercial')
        && queryQuickOfferByPlateResp.getBooleanValue('commercial')

if (tempValues.ExistBI && needWriteBiz) {
    def commercialInsuransVo = queryQuickOfferByPlateResp.getJSONObject('commercialInsuransVo')
    def bizSuiteInfo = order.suiteInfo.bizSuiteInfo ?: new BizSuiteInfo()
    order.suiteInfo.bizSuiteInfo = bizSuiteInfo
    bizSuiteInfo.tap {
        start = DateTime.of(commercialInsuransVo.getLong('startDate')).toString(DatePattern.NORM_DATETIME_PATTERN)
        end = DateTime.of(commercialInsuransVo.getLong('endDate')).toString(DatePattern.NORM_DATETIME_PATTERN)

    }
    if (queryQuickOfferByPlateResp.containsKey('chargingPostList')) {
        def chargingPostList = queryQuickOfferByPlateResp.getJSONArray('chargingPostList')
        if (chargingPostList) {
            tempValues.chargingPostList = chargingPostList
        }
    }
}
//车辆实际价值
tempValues.actualValue = RobotDict_2011.getActualValue(autoTask, entity)
//写入险种信息
if (tempValues.ExistBI && needWriteBiz && queryQuickOfferByPlateResp.containsKey('quoteInsuranceVos')) {
    def quoteInsuranceVos = queryQuickOfferByPlateResp.getJSONArray('quoteInsuranceVos')
    order.suiteInfo.bizSuiteInfo.suites = writeBizSuites(quoteInsuranceVos)
}

//暂存流程不会取车主、投被保人信息，需要修改原始数据
writeBackMsg()

//获取非车代理人参数
reqQueryMsbAgent()
//暂存
reqQuickSave()


//需求9494 北京驾意险前置
//非车前置或者地区设置 都可进该流程
if (!autoTask.tempValues['nonMotorCalculate']) {
    if ("1".equals(autoTask?.configs?.processControl) || ["110000", "110100", "441900"].contains(entity?.order?.insureArea?.city?.toString())) {
        //假如配置了 0-预报价不能走这种逻辑
        if (!"0".equals(autoTask?.configs?.processControl?.toString())) {
            def accidentNewInsuranceUtil = new robot_2011_new_accident_util()
            def accidentF = accidentNewInsuranceUtil.checkNewInitAccident(autoTask)
            if (accidentF) {
                //走了前置就不要走后置非车
                autoTask.tempValues.again = "true"
                //走了前置就不要走预报价
                autoTask.tempValues.doPreCalculateFlag = true
//                def robot_2011_special_util = new robot_2011_special_util()
//                robot_2011_special_util.doSaveInsureInfo(autoTask, entity)
                accidentNewInsuranceUtil.accident(autoTask, autoTask?.tempValues?.quotationNo?.toString(), null)
                autoTask.tempValues['nonMotorCalculate'] = true

            }
        }
    }
}
/**
 * 写入投保人信息
 * @param holderVo
 */
void writeInsurePerson(JSONObject holderVo) {
    def insurePerson = order.insurePerson ?: new InsurePerson()
    order.insurePerson = insurePerson
    setPersonInfo(insurePerson, holderVo)
}

/**
 * 写入被保人信息
 * @param insuredVo
 */
void writeInsuredPersons(JSONObject insuredVo) {
    //初始化被保人信息
    def insuredPersons = order.insuredPersons ?: [new InsurePerson()]
    order.insuredPersons = insuredPersons
    //被保人信息
    setPersonInfo(insuredPersons[0], insuredVo)
}

private static void setPersonInfo(InsurePerson person, JSONObject personVo) {
    person.tap {
        name = personVo.getString('name')
        idCardType = RobotDict_2011.getValue('Certificate4Return', personVo.getString('certificateType')) as Integer
        idCard = personVo.getString('certificateCode')
        email = email ?: personVo.getString('email')
        mobile = mobile ?: personVo.getString('mobile')
        address = address ?: personVo.getString('address')
    }
}

/**
 * 写入车主信息
 * @param ecarvo
 */
void writeCarOwner(JSONObject ecarvo) {
    def carOwner = order.carOwnerInfo ?: new CarOwnerInfo()
    order.carOwnerInfo = carOwner
    carOwner.tap {
        name = ecarvo.getString('ownerName')
        idCardType = RobotDict_2011.getValue('Certificate4Return', ecarvo.getString('certType')) as Integer
        idCard = ecarvo.getString('certNo')
    }
}

/**
 * 写入车辆信息
 * @param ecarvo
 */
void writeCarInfo(JSONObject ecarvo) {
    def carInfo = order.carInfo ?: new CarInfo()
    carInfo.with {
        //车牌号
        plateNum = ecarvo.getString('plateNo')
        //车架号
        vin = ecarvo.getString('carVIN')
        //发动机号
        engineNum = ecarvo.getString('engineNo')
        //座位数
        seatCnt = ecarvo.getInteger('seatCount')
        //车身自重
        fullLoad = ecarvo.getBigDecimal('emptyWeight')
        //排气量
        displacement = ecarvo.getBigDecimal('engineCapacity') ?: BigDecimal.ZERO
        //车辆用户类型
        carUserType = RobotDict_2011.getValue('BelongsNatureCode4Return', ecarvo.getString('ownerProp')) as Integer
        //初登日期
        String stRegisterDate = ecarvo.getString('stRegisterDate')
        if (StringUtil.isNoEmpty(stRegisterDate)) {
            firstRegDate = DateUtil.parse(stRegisterDate, DatePattern.NORM_DATE_PATTERN)
        }
        //车辆种类代码,包含特种车信息
        syvehicletypecode = RobotDict_2011.getValue('syvehicletypecode4Return', ecarvo.getString('vehicleType'))
        //车型品牌名称，没有返回
        carBrandName = ''
        //车型名称
        carModelName = ecarvo.getString('modelType')
        //车价
        price = ecarvo.getBigDecimal('purchasePrice')
        //载重量
        modelLoad = ecarvo.getBigDecimal('tonnage') ?: BigDecimal.ZERO
        //号牌颜色
        plateColor = RobotDict_2011.getValue('PlateColor4Return', ecarvo.getString('plateColor')) as Integer
        //号牌种类
        plateType = RobotDict_2011.getValue('PlateType4Return', ecarvo.getString('plateType')) as Integer
        //是否是过户车
        isTransfer = false
        //使用性质
        useProps = RobotDict_2011.getValue('usageAttributeCode4Return', ecarvo.getString('usage')) as Integer
    }
}

/**
 * 写入合伙人信息
 * @param ecarvo
 * @param currentUser
 */
void writeCooperatorInfoToTempValues(JSONObject ecarvo, JSONObject currentUser) {
    if (currentUser?.partnerType?.toString() == '3' || !ecarvo.containsKey('cooperatorCode')) {
        tempValues.cooperatorVo = [
                'cooperatorCode': '',
                'cooperatorName': ''
        ]
    } else {
        tempValues.cooperatorVo = [
                'cooperatorCode': ecarvo.getString('cooperatorCode'),
                'cooperatorName': ecarvo.getString('cooperativeName')
        ]
    }
}

/**
 * 写入权益人信息
 * @param claimerVo
 */
void writeBeneficiaryPersons(JSONObject claimerVo) {
    //初始化权益人信息
    def beneficiaryPersons = order.beneficiaryPersons ?: [new BeneficiaryPerson()]
    order.beneficiaryPersons = beneficiaryPersons
    beneficiaryPersons[0].tap {
        name = claimerVo.getString('claimerName')
        idCardType = RobotDict_2011.getValue('Certificate4Return', claimerVo.getString('certificateType')) as Integer
        idCard = claimerVo.getString('certificateCode')
    }
}

void writeBackMsg() {
    if (tempValues.taskType != 'insure') {
        return
    }
    JSONObject backMsg = JSONObject.parseObject(autoTask.applyJson)
    backMsg << [
            'carOwnerInfo'         : JSONObject.toJSON(order.carOwnerInfo),
            'applicantPersonInfo'  : JSONObject.toJSON(order.insurePerson),
            'insuredPersonInfoList': JSONArray.toJSON(order.insuredPersons)
    ]
    autoTask.applyJson = JSON.toJSONString(backMsg)
}

/**
 * 回写平台信息
 * @param ecarvo
 */
void writePTMsg(JSONObject ecarvo, JSONObject queryEcarVoByJYResp) {
    def ptMsg = tempValues.get('PTMsg') ?: new JSONObject()
    tempValues.put('PTMsg', ptMsg)
    def carInfo = order.carInfo
    ptMsg << [
            (PlatformKey.carBrandName): carInfo.carModelName, //车型名称
            (PlatformKey.price)       : ecarvo.getString('purchasePrice'),//新车购置价
            (PlatformKey.carModelDate): ecarvo.getString('yearPattern'),//上市年份
            (PlatformKey.seatCnt)     : ecarvo.getString('seatCount'),//核定载客
            (PlatformKey.modelLoad)   : carInfo.modelLoad.toString(),//核定载质量
            (PlatformKey.fullLoad)    : 50 > ecarvo.getDoubleValue('fullWeight') ? BigDecimalUtil.multi(ecarvo.getString('fullWeight'), '1000', 2) : ecarvo.getString('fullWeight'),//整备质量
            (PlatformKey.displacement): ecarvo.getString('engineCapacity') ?: '0',//排气量
            (PlatformKey.modelCode)   : ecarvo.getString('moldCharacterCode'),//车型编码
            (PlatformKey.licenseNo)   : ecarvo.getString('plateNo'),//车牌
            (PlatformKey.engineNo)    : ecarvo.getString('engineNo'),//发动机
            (PlatformKey.vinNo)       : ecarvo.getString('carVIN'),//vin
            (PlatformKey.enrollDate)  : ecarvo.getString('stRegisterDate'),//初登日期
    ]
    def vehicleInfo_vehicleType = RobotDict_2011.getValue('CarTypeMap', carInfo.syvehicletypecode)
    if (vehicleInfo_vehicleType) {
        ptMsg[PlatformKey.vehicleInfo_vehicleType] = vehicleInfo_vehicleType
    }
    if (queryEcarVoByJYResp.containsKey('tpyRiskflagName')) {
        ptMsg['tpyRiskflagName'] = queryEcarVoByJYResp.getString('tpyRiskflagName')
    }
}

void writeEfcSuiteInfo(JSONObject compulsoryInsuransVo) {
    def efcSuiteInfo = order.suiteInfo.efcSuiteInfo ?: new EfcSuiteInfo()
    order.suiteInfo.efcSuiteInfo = efcSuiteInfo
    efcSuiteInfo.tap {
        if (compulsoryInsuransVo.containsKey('stCipremium')) {
            discountCharge = compulsoryInsuransVo.getBigDecimal('stCipremium')
        }
        amount = BigDecimal.ONE
        start = DateTime.of(compulsoryInsuransVo.getLong('startDate')).toString(DatePattern.NORM_DATETIME_PATTERN)
        end = DateTime.of(compulsoryInsuransVo.getLong('endDate')).toString(DatePattern.NORM_DATETIME_PATTERN)
    }
}

Map<String, SuiteDef> writeBizSuites(JSONArray quoteInsuranceVos) {
    Map<String, SuiteDef> result = Maps.newHashMapWithExpectedSize(quoteInsuranceVos.size())
    def applyJson = JSONObject.parseObject(autoTask.applyJson)
    def baseSuiteInfo = applyJson.getJSONObject('baseSuiteInfo')
    def bizSuiteInfoJson = baseSuiteInfo?.getJSONObject('bizSuiteInfo')
    if (!bizSuiteInfoJson) {
        bizSuiteInfoJson = new JSONObject()
        baseSuiteInfo.put('bizSuiteInfo', bizSuiteInfoJson)
    }
    def suitesJsonArray = bizSuiteInfoJson.getJSONArray('suites')
    if (!suitesJsonArray) {
        suitesJsonArray = new JSONArray()
        bizSuiteInfoJson.put('suites', suitesJsonArray)
    }
    for (int i = 0; i < quoteInsuranceVos.size(); i++) {
        def risk = quoteInsuranceVos.getJSONObject(i)
        def suiteDef = new SuiteDef()
        suiteDef.tap {
            //编码
            def insuranceCode = risk.getString('insuranceCode')
            code = RobotDict_2011.getCode4Return(autoTask, insuranceCode)
            if (!code) {
                fail('险种信息回写失败：出现无法解析的险种' + insuranceCode)
            }
            //保额
            amount = risk.getBigDecimal('amount')
        }
        RobotDict_2011.writeSuiteDef(autoTask, suiteDef, risk, suitesJsonArray)
        result.put(suiteDef.code, suiteDef)
    }
    autoTask.applyJson = JSON.toJSONString(applyJson)
    return result
}

private void reqQueryMsbAgent() {
    def reqBody = RobotCar_2011.makeQueryMsbAgentReqBody(autoTask)
    String requestBody = JSON.toJSONString(reqBody)
    log.info("请求报文：{}", requestBody)
    requestBody = StringUtil.chinesetoUnicode(requestBody)
    requestBody = Robot2011Util.genBody(tempValues, requestBody)
    String response = Robot2011Util.postWithRetry3Times(autoTask.httpClient as CloseableHttpClient, RobotConstant_2011.URL.QUERY_MSB_AGENT, requestBody, tempValues, config)
    response = Robot2011Util.decodeBody(tempValues, response)
    RobotOutput_2011.handleQueryMsgAgent(autoTask, response)
}

private void reqQuickSave() {
    def reqBody = RobotCar_2011.makeQuickSaveReqBody(autoTask, entity)
    autoTask.tempValues.saveParam = reqBody
    String requestBody = JSON.toJSONString(reqBody)
    log.info("请求报文：{}", requestBody)
    requestBody = StringUtil.chinesetoUnicode(requestBody)
    requestBody = Robot2011Util.genBody(tempValues, requestBody)
    String response = Robot2011Util.postWithRetry3Times(autoTask.httpClient as CloseableHttpClient, RobotConstant_2011.URL.QUICK_SAVE, requestBody,tempValues, config)
    response = Robot2011Util.decodeBody(tempValues, response)
    RobotOutput_2011.handleQuickSave(autoTask, response)
}