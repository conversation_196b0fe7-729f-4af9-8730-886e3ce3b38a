package com.cheche365.bc.sharding;

import com.google.common.collect.Range;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingValue;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * 按月分表算法 - 复合分片算法
 * 支持autoTraceId、startTime和opType三个分片键
 * 分片逻辑：
 * 1. 数据迁移过渡期间：根据USE_OLD_TABLE标识决定是否使用旧表
 * - 查询操作(SELECT)：如果USE_OLD_TABLE为true，使用旧表；否则使用分表
 * - 写入操作(INSERT/UPDATE)：如果USE_OLD_TABLE为true，双写到旧表和分表；否则只写分表
 * 2. 正常分片逻辑：
 * - 如果提供了startTime：使用startTime进行分片，返回对应月份的表
 * - 如果startTime不存在但autoTraceId存在：返回当前月的表
 * - 如果startTime和autoTraceId都不存在：返回当前月和前两个月的表（例如：202506、202505、202504）
 * - 写入场景：建议在应用层提供startTime字段以确保数据写入正确的表
 */
public class MonthlyShardingAlgorithm implements ComplexKeysShardingAlgorithm<Comparable<?>> {

    private static final String OLD_TABLE_NAME = "auto_task";


    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");


    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, ComplexKeysShardingValue<Comparable<?>> shardingValue) {
        // 数据迁移过渡逻辑：根据USE_OLD_TABLE标识和opType决定使用哪个表
        if (OldTableSwitch.isUseOldTable()) {
            return handleMigrationPeriod(availableTargetNames, shardingValue);
        }

        // 正常分片逻辑
        // 检查是否为范围查询
        if (isRangeQuery(shardingValue)) {
            return handleRangeQuery(availableTargetNames, shardingValue);
        }

        // 处理单值查询
        return handleSingleValueQuery(availableTargetNames, shardingValue);
    }

    /**
     * 处理数据迁移过渡期间的分片逻辑
     */
    private Collection<String> handleMigrationPeriod(Collection<String> availableTargetNames, ComplexKeysShardingValue<Comparable<?>> shardingValue) {
        // 从ThreadLocal获取操作类型
        String opType = ShardingContextHolder.getOpType();

        if ("INSERT".equals(opType)) {
            // INSERT操作：只写入分表，避免双写导致的路由冲突
            Boolean flag = ShardingContextHolder.getUseOldTable();
            if (flag != null && flag) {
                return Collections.singleton(OLD_TABLE_NAME);
            }
            return getShardedTables(availableTargetNames, shardingValue);
        } else if ("UPDATE".equals(opType)) {
            // UPDATE操作：双写到旧表和分表
            Set<String> result = new LinkedHashSet<>();
            result.add(OLD_TABLE_NAME);
            Collection<String> shardedTables = getShardedTables(availableTargetNames, shardingValue);
            result.addAll(shardedTables);
            return result;
        } else {
            // 查询操作：使用旧表
            return Collections.singleton(OLD_TABLE_NAME);
        }
    }

    /**
     * 获取分表名称（用于双写场景）
     */
    private Collection<String> getShardedTables(Collection<String> availableTargetNames, ComplexKeysShardingValue<Comparable<?>> shardingValue) {
        // 检查是否为范围查询
        if (isRangeQuery(shardingValue)) {
            return handleRangeQuery(availableTargetNames, shardingValue);
        }

        // 处理单值查询
        return handleSingleValueQuery(availableTargetNames, shardingValue);
    }

    /**
     * 检查是否为范围查询
     */
    private boolean isRangeQuery(ComplexKeysShardingValue<Comparable<?>> shardingValue) {
        Map<String, Range<Comparable<?>>> columnNameAndRangeValuesMap = shardingValue.getColumnNameAndRangeValuesMap();
        return columnNameAndRangeValuesMap.get("startTime") != null;
    }

    /**
     * 处理范围查询
     */
    private Collection<String> handleRangeQuery(Collection<String> availableTargetNames, ComplexKeysShardingValue<Comparable<?>> shardingValue) {
        Map<String, Range<Comparable<?>>> columnNameAndRangeValuesMap = shardingValue.getColumnNameAndRangeValuesMap();
        Range<Comparable<?>> startTimeRange = columnNameAndRangeValuesMap.get("startTime");

        Object lowerBound = startTimeRange.hasLowerBound() ? startTimeRange.lowerEndpoint() : null;
        Object upperBound = startTimeRange.hasUpperBound() ? startTimeRange.upperEndpoint() : null;

        return doRangeSharding(availableTargetNames, lowerBound, upperBound, shardingValue.getLogicTableName());
    }

    /**
     * 处理单值查询
     */
    private Collection<String> handleSingleValueQuery(Collection<String> availableTargetNames, ComplexKeysShardingValue<Comparable<?>> shardingValue) {
        // 检查是否提供了startTime分片键
        Map<String, Collection<Comparable<?>>> columnNameAndShardingValuesMap = shardingValue.getColumnNameAndShardingValuesMap();
        Collection<Comparable<?>> startTimeValues = columnNameAndShardingValuesMap.get("startTime");
        Collection<Comparable<?>> autoTraceIdValues = columnNameAndShardingValuesMap.get("autoTraceId");

        if (startTimeValues != null && !startTimeValues.isEmpty()) {
            // 如果提供了startTime，使用原有逻辑
            LocalDateTime dateTime = determineShardingDateTime(shardingValue);
            YearMonth queryMonth = YearMonth.from(dateTime);
            validateQueryMonth(queryMonth);
            String targetTableName = buildTargetTableName(shardingValue.getLogicTableName(), dateTime);
            return selectTargetTable(availableTargetNames, targetTableName);
        } else if (autoTraceIdValues != null && !autoTraceIdValues.isEmpty()) {
            // 如果startTime不存在但autoTraceId存在，返回当前月的表名
            LocalDateTime currentTime = LocalDateTime.now();
            String targetTableName = buildTargetTableName(shardingValue.getLogicTableName(), currentTime);
            return selectTargetTable(availableTargetNames, targetTableName);
        } else {
            // 如果startTime和autoTraceId都不存在，返回当前月和前两个月的表名
            return selectMultipleMonthTables(availableTargetNames, shardingValue.getLogicTableName());
        }
    }

    /**
     * 确定分片使用的时间
     */
    private LocalDateTime determineShardingDateTime(ComplexKeysShardingValue<Comparable<?>> shardingValue) {
        Map<String, Collection<Comparable<?>>> columnNameAndShardingValuesMap = shardingValue.getColumnNameAndShardingValuesMap();

        Collection<Comparable<?>> startTimeValues = columnNameAndShardingValuesMap.get("startTime");
        Collection<Comparable<?>> autoTraceIdValues = columnNameAndShardingValuesMap.get("autoTraceId");

        if (startTimeValues != null && !startTimeValues.isEmpty()) {
            // 优先使用startTime进行分片
            Object startTimeValue = startTimeValues.iterator().next();
            return convertToLocalDateTime(startTimeValue);
        } else if (autoTraceIdValues != null && !autoTraceIdValues.isEmpty()) {
            // 如果startTime不存在但autoTraceId存在，使用当前时间进行分片
            return LocalDateTime.now();
        } else {
            // 如果两个分片键都不存在，使用当前时间
            return LocalDateTime.now();
        }
    }

    /**
     * 验证查询月份是否在允许范围内
     */
    private void validateQueryMonth(YearMonth queryMonth) {
        YearMonth currentMonth = YearMonth.now();
        YearMonth minAllowedMonth = currentMonth.minusMonths(3);
        YearMonth maxAllowedMonth = currentMonth;

        if (queryMonth.isBefore(minAllowedMonth) || queryMonth.isAfter(maxAllowedMonth)) {
            throw new UnsupportedOperationException(
                    String.format("Query month %s is out of allowed range [%s, %s]",
                            queryMonth.format(FORMATTER),
                            minAllowedMonth.format(FORMATTER),
                            maxAllowedMonth.format(FORMATTER)));
        }
    }

    /**
     * 构建目标表名
     */
    private String buildTargetTableName(String logicTableName, LocalDateTime dateTime) {
        String suffix = dateTime.format(FORMATTER);
        return logicTableName + "_" + suffix;
    }

    /**
     * 选择目标表
     */
    private Collection<String> selectTargetTable(Collection<String> availableTargetNames, String targetTableName) {
        if (availableTargetNames.contains(targetTableName)) {
            return Collections.singletonList(targetTableName);
        }

        // 如果目标表不存在，返回第一个可用表
        return Collections.singletonList(availableTargetNames.iterator().next());
    }

    /**
     * 选择多个月份的表（当前月和前两月）
     * 适用于startTime字段不存在的查询场景
     */
    private Collection<String> selectMultipleMonthTables(Collection<String> availableTargetNames, String logicTableName) {
        Set<String> result = new LinkedHashSet<>();
        YearMonth currentMonth = YearMonth.now();

        // 生成当前月和前两月的表名
        for (int i = 0; i <= 2; i++) {
            YearMonth targetMonth = currentMonth.minusMonths(i);
            String suffix = targetMonth.format(FORMATTER);
            String targetTableName = logicTableName + "_" + suffix;

            // 只添加实际存在的表
            if (availableTargetNames.contains(targetTableName)) {
                result.add(targetTableName);
            }
        }

        // 如果没有找到任何匹配的表，返回第一个可用表
        if (result.isEmpty() && !availableTargetNames.isEmpty()) {
            result.add(availableTargetNames.iterator().next());
        }

        return result;
    }

    /**
     * 范围查询逻辑 - 支持startTime的范围查询
     */
    public Collection<String> doRangeSharding(final Collection<String> availableTargetNames, final Object lowerBound, final Object upperBound, final String logicTableName) {
        Set<String> result = new LinkedHashSet<>();

        // 获取并限制查询范围
        RangeQueryBounds queryBounds = calculateRangeQueryBounds(lowerBound, upperBound);

        // 筛选符合条件的表
        for (String tableName : availableTargetNames) {
            if (isTableInRange(tableName, logicTableName, queryBounds)) {
                result.add(tableName);
            }
        }
        return result;
    }

    /**
     * 计算范围查询的边界
     */
    private RangeQueryBounds calculateRangeQueryBounds(Object lowerBound, Object upperBound) {
        YearMonth currentMonth = YearMonth.now();
        YearMonth minAllowedMonth = currentMonth.minusMonths(3);
        YearMonth maxAllowedMonth = currentMonth;

        // 获取查询范围的上下边界对应的年月
        Optional<YearMonth> lowerBoundMonth = lowerBound != null
                ? Optional.of(convertToYearMonth(lowerBound))
                : Optional.empty();
        Optional<YearMonth> upperBoundMonth = upperBound != null
                ? Optional.of(convertToYearMonth(upperBound))
                : Optional.empty();

        // 应用限制：将查询范围限制在允许的范围内
        YearMonth effectiveLowerBound = lowerBoundMonth
                .filter(month -> !month.isBefore(minAllowedMonth))
                .orElse(minAllowedMonth);

        YearMonth effectiveUpperBound = upperBoundMonth
                .filter(month -> !month.isAfter(maxAllowedMonth))
                .orElse(maxAllowedMonth);

        return new RangeQueryBounds(effectiveLowerBound, effectiveUpperBound);
    }

    /**
     * 检查表是否在查询范围内
     */
    private boolean isTableInRange(String tableName, String logicTableName, RangeQueryBounds bounds) {
        Optional<YearMonth> tableMonth = parseYearMonthFromTableName(tableName, logicTableName);

        if (!tableMonth.isPresent()) {
            return false;
        }

        YearMonth month = tableMonth.get();
        return !month.isBefore(bounds.getLowerBound()) && !month.isAfter(bounds.getUpperBound());
    }

    /**
     * 范围查询边界封装类
     */
    private static class RangeQueryBounds {
        private final YearMonth lowerBound;
        private final YearMonth upperBound;

        public RangeQueryBounds(YearMonth lowerBound, YearMonth upperBound) {
            this.lowerBound = lowerBound;
            this.upperBound = upperBound;
        }

        public YearMonth getLowerBound() {
            return lowerBound;
        }

        public YearMonth getUpperBound() {
            return upperBound;
        }
    }

    /**
     * 从表名中安全地解析出年月。
     *
     * @param tableName      物理表名 (e.g., "t_order_202506")
     * @param logicTableName 逻辑表名 (e.g., "t_order")
     * @return 解析出的 YearMonth 对象，如果格式不匹配则返回 empty
     */
    private Optional<YearMonth> parseYearMonthFromTableName(String tableName, String logicTableName) {
        String prefix = logicTableName + "_";
        if (tableName.startsWith(prefix)) {
            try {
                String datePart = tableName.substring(prefix.length());
                return Optional.of(YearMonth.parse(datePart, FORMATTER));
            } catch (DateTimeParseException | StringIndexOutOfBoundsException e) {
                // 如果后缀不符合 "yyyyMM" 格式，则忽略
            }
        }
        return Optional.empty();
    }


    /**
     * 将不同类型的时间对象转换为YearMonth
     */
    private YearMonth convertToYearMonth(Object value) {
        if (value == null) {
            return YearMonth.now();
        }

        if (value instanceof YearMonth) {
            return (YearMonth) value;
        } else if (value instanceof LocalDateTime) {
            return YearMonth.from((LocalDateTime) value);
        } else if (value instanceof java.sql.Timestamp) {
            return YearMonth.from(((java.sql.Timestamp) value).toLocalDateTime());
        } else if (value instanceof Date) {
            return YearMonth.from(((Date) value).toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime());
        } else if (value instanceof String) {
            String strValue = (String) value;
            try {
                // 首先尝试解析yyyy-MM格式
                if (strValue.matches("\\d{4}-\\d{2}")) {
                    return YearMonth.parse(strValue, FORMATTER);
                }
                // 尝试解析yyyy-MM-dd格式，提取年月部分
                if (strValue.matches("\\d{4}-\\d{2}-\\d{2}")) {
                    LocalDate date = LocalDate.parse(strValue);
                    return YearMonth.from(date);
                }
                // 尝试解析完整的日期时间格式
                return YearMonth.from(LocalDateTime.parse(strValue));
            } catch (Exception e) {
                // 如果解析失败，使用当前年月
                return YearMonth.now();
            }
        } else {
            // 其他类型，使用当前年月
            return YearMonth.now();
        }
    }

    /**
     * 将不同类型的时间对象转换为LocalDateTime
     */
    private LocalDateTime convertToLocalDateTime(Object value) {
        if (value == null) {
            return LocalDateTime.now();
        }

        if (value instanceof LocalDateTime) {
            return (LocalDateTime) value;
        } else if (value instanceof java.sql.Timestamp) {
            return ((java.sql.Timestamp) value).toLocalDateTime();
        } else if (value instanceof Date) {
            return ((Date) value).toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
        } else if (value instanceof String) {
            // 尝试解析字符串格式的时间
            try {
                return LocalDateTime.parse((String) value);
            } catch (Exception e) {
                // 如果解析失败，使用当前时间
                return LocalDateTime.now();
            }
        } else {
            // 其他类型，使用当前时间
            return LocalDateTime.now();
        }
    }

    @Override
    public String getType() {
        return "CLASS_BASED";
    }
}
