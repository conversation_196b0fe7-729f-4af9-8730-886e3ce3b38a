package zhongcheng.edi

import com.cheche365.bc.utils.DataUtil
import zhongcheng.edi.edi_2044_common
import com.cheche365.bc.exception.InsReturnException

    java.text.SimpleDateFormat df3 = new java.text.SimpleDateFormat("yyyy-MM-dd");
java.text.SimpleDateFormat df4 = new java.text.SimpleDateFormat("yyyyMMdd");
def script  = new edi_2044_common()

def ResponseCode = root?.HeadDto?.ResponseCode?.toString()
def ErrorCode = root?.HeadDto?.ErrorCode?.toString()
def TransMessage = root?.HeadDto?.TransMessage?.toString()

if (!ResponseCode || ResponseCode == "0"|| !ErrorCode|| ErrorCode != "0000") {
    throw new InsReturnException(TransMessage);
}

Map enquiry = enquiry
def SQ = enquiry?.SQ
if (!SQ) {
    SQ = new HashMap<String, Object>();
    enquiry.put("SQ", SQ)
}
def UndwrtFlag = root?.BodyDto?.MainDto?.UndwrtFlag?.toString() ?:""

if ("0000" == ErrorCode && UndwrtFlag == "1") {
    //交强险起始日期
    String trafficInsEffectDate = root?.BodyDto?.MainDto?.StartDate?.toString() ?: "1970010100"
    def EffectDate = df3.format(df4.parse(trafficInsEffectDate.substring(0, 8))) + " 00:00:00"

    enquiry?.baseSuiteInfo?.efcSuiteInfo?.start = EffectDate

    //交强险终止日期
    String trafficInsInvalidDate = root?.BodyDto?.MainDto?.EndDate?.toString() ?: "1970010100"
    def InvalidDate = df3.format(df4.parse(trafficInsInvalidDate.substring(0, 8))) + " 23:59:59"

    enquiry?.baseSuiteInfo?.efcSuiteInfo?.end = InvalidDate

    //险别信息   ItemKindDtoList
    //交强险信息
    def ItemKindDto = root?.BodyDto?.ItemKindDtoList?.ItemKindDto?.getAt(0);

    enquiry?.baseSuiteInfo?.efcSuiteInfo?.amount = ItemKindDto?.SumLimit?.toString() ? ItemKindDto?.SumLimit?.toDouble() : 0
    enquiry?.baseSuiteInfo?.efcSuiteInfo?.orgCharge = ItemKindDto?.BasicPremium?.toString() ? ItemKindDto?.BasicPremium?.toDouble() : 0
    enquiry?.baseSuiteInfo?.efcSuiteInfo?.discountCharge = ItemKindDto?.Premium?.toString() ? ItemKindDto?.Premium?.toDouble() : 0
    enquiry?.baseSuiteInfo?.efcSuiteInfo?.discountRate = ItemKindDto?.Discount?.toString() ? ItemKindDto?.Discount?.toDouble() : 0
    SQ.put("efcCharge", ItemKindDto?.Premium?.toString() ? ItemKindDto?.Premium?.toDouble() : 0)
    SQ.put("trafficDiscountRate", ItemKindDto?.Discount?.toString() ? ItemKindDto?.Discount?.toDouble() : 0)

    //车船税
    def CarshipTaxDto = root?.BodyDto?.CarshipTaxDto;

    def SumPayTax = CarshipTaxDto?.SumPayTax?.toString() ? CarshipTaxDto?.SumPayTax?.toDouble() : 0
    def LateFee = CarshipTaxDto?.LateFee?.toString() ? CarshipTaxDto?.LateFee?.toDouble() : 0

    enquiry?.baseSuiteInfo?.taxSuiteInfo?.start = CarshipTaxDto?.TaxStartDate?.toString()
    enquiry?.baseSuiteInfo?.taxSuiteInfo?.end = CarshipTaxDto?.TaxEndDate?.toString()
    enquiry?.baseSuiteInfo?.taxSuiteInfo?.charge = SumPayTax - LateFee
    enquiry?.baseSuiteInfo?.taxSuiteInfo?.delayCharge = LateFee
    SQ.put("taxCharge", SumPayTax)

    def bizCharge = SQ?.bizCharge ?: 0   //商业险
    SQ.put("totalCharge", (bizCharge?.toBigDecimal() + SQ?.efcCharge?.toBigDecimal() + SQ?.taxCharge?.toBigDecimal())?.toDouble())

    //保险关系人信息列表
    def InsuredDtoList = root?.BodyDto?.InsuredDtoList?.InsuredDto;
    Integer n = InsuredDtoList?.size();

    for (int i = 0; i < n; i++) {
        def InsuredDto = InsuredDtoList?.getAt(i);
        String InsuredFlag = InsuredDto?.InsuredFlag?.toString()       //2投保人，1被保人，3车主

        if (InsuredFlag == "1") {
            //被保险人
            def insuredPersonList = enquiry?.insuredPersonList;
            int j = 0;

            for (; j < insuredPersonList.size(); j++) {
                def insuredPerson = insuredPersonList[j];
                if (insuredPerson?.name == InsuredDto?.InsuredName?.toString() && insuredPerson?.idCard == InsuredDto?.CertiCode?.toString()) {
                    insuredPerson?.name = InsuredDto?.InsuredName?.toString() ?: "";
                    insuredPerson?.idCardType = script.formatIdCardType(InsuredDto?.CertiType?.toString() ?: "");
                    insuredPerson?.idCard = InsuredDto?.CertiCode?.toString() ?: "";
                    insuredPerson?.sex = script.formatGender(InsuredDto?.Gender?.toString() ?: "");
                    insuredPerson?.email = InsuredDto?.Email?.toString() ?: "";
                    insuredPerson?.mobile = InsuredDto?.MobileNo?.toString() ?: "";
                    insuredPerson?.address = InsuredDto?.Address?.toString() ?: "";
                    insuredPerson?.postCode = InsuredDto?.PostCode?.toString() ?: "";
                    insuredPerson?.phone = InsuredDto?.PhoneNumber?.toString() ?: "";

                    break;
                }
            }

            if (j == insuredPersonList.size()) {
                Map<String, String> jsObject = new HashMap<String, String>();

                jsObject.put("name", InsuredDto?.InsuredName?.toString() ?: "");
                jsObject.put("idCardType", script.formatIdCardType(InsuredDto?.CertiType?.toString() ?: ""));
                jsObject.put("idCard", InsuredDto?.CertiCode?.toString() ?: "");
                jsObject.put("sex", script.formatGender(InsuredDto?.Gender?.toString() ?: ""));
                jsObject.put("email", InsuredDto?.Email?.toString() ?: "");
                jsObject.put("mobile", InsuredDto?.MobileNo?.toString() ?: "");
                jsObject.put("address", InsuredDto?.Address?.toString() ?: "");
                jsObject.put("postCode", InsuredDto?.PostCode?.toString() ?: "");
                jsObject.put("phone", InsuredDto?.PhoneNumber?.toString() ?: "");

                insuredPersonList.add(jsObject);
            }
        } else if (InsuredFlag == "2") {
            //投保人
            enquiry?.insurePerson?.name = InsuredDto?.InsuredName?.toString() ?: "";
            enquiry?.insurePerson?.idCardType = script.formatIdCardType(InsuredDto?.CertiType?.toString() ?: "");
            enquiry?.insurePerson?.idCard = InsuredDto?.CertiCode?.toString() ?: "";
            enquiry?.insurePerson?.sex = script.formatGender(InsuredDto?.Gender?.toString() ?: "");
            enquiry?.insurePerson?.email = InsuredDto?.Email?.toString() ?: "";
            enquiry?.insurePerson?.mobile = InsuredDto?.MobileNo?.toString() ?: "";
            enquiry?.insurePerson?.address = InsuredDto?.Address?.toString() ?: "";
            enquiry?.insurePerson?.postCode = InsuredDto?.PostCode?.toString() ?: "";
            enquiry?.insurePerson?.phone = InsuredDto?.PhoneNumber?.toString() ?: "";
        } else if (InsuredFlag == "3") {
            //车主
            enquiry?.carOwnerInfo?.name = InsuredDto?.InsuredName?.toString() ?: "";
            enquiry?.carOwnerInfo?.idCardType = script.formatIdCardType(InsuredDto?.CertiType?.toString() ?: "");
            enquiry?.carOwnerInfo?.idCard = InsuredDto?.CertiCode?.toString() ?: "";
            enquiry?.carOwnerInfo?.sex = script.formatGender(InsuredDto?.Gender?.toString() ?: "");
            enquiry?.carOwnerInfo?.email = InsuredDto?.Email?.toString() ?: "";
            enquiry?.carOwnerInfo?.mobile = InsuredDto?.MobileNo?.toString() ?: "";
            enquiry?.carOwnerInfo?.address = InsuredDto?.Address?.toString() ?: "";
            enquiry?.carOwnerInfo?.postCode = InsuredDto?.PostCode?.toString() ?: "";
            enquiry?.carOwnerInfo?.phone = InsuredDto?.PhoneNumber?.toString() ?: "";
        }
    }

    //说明：约定驾驶员信息列表  CarDriverDtoList
    def CarDriverDtoList = root?.BodyDto?.CarDriverDtoList?.CarDriverDto;
    Integer d = CarDriverDtoList?.size()

    for (int i = 0; i < d; i++) {
        def CarDriverDto = CarDriverDtoList?.getAt(i);
        def driverInfoList = enquiry?.driverInfoList;
        int j = 0;

        for (; j < driverInfoList.size(); j++) {
            def driverInfo = driverInfoList[j];
            if (driverInfo?.name == CarDriverDto?.DriverName?.toString() && driverInfo?.idCard == CarDriverDto?.CredentialNo?.toString()) {
                driverInfo?.name = CarDriverDto?.DriverName?.toString() ?: "";
                driverInfo?.idCardType = script.formatIdCardType(CarDriverDto?.CredentialCode?.toString() ?: "");
                driverInfo?.idCard = CarDriverDto?.CredentialNo?.toString() ?: "";
                driverInfo?.sex = script.formatGender(CarDriverDto?.Gender?.toString() ?: "");

                driverInfo?.licenseType = CarDriverDto?.DriverTypeCode?.toString() ?: "";
                driverInfo?.licenseDesc = CarDriverDto?.LicenseDesc?.toString() ?: "";
                driverInfo?.licenseNo = CarDriverDto?.LicenseNo?.toString() ?: "";
                driverInfo?.licenseState = CarDriverDto?.LicenseStatusCode?.toString() ?: "";
                driverInfo?.firstRegDate = CarDriverDto?.LicensedDate?.toString() ?: "";
                driverInfo?.isPrimary = CarDriverDto?.IsMajorDriver?.toString() == "1" ? true : false;

                break;
            }
        }

        if (j == driverInfoList.size()) {
            Map<String, Object> jsObject = new HashMap<String, Object>();

            jsObject.put("name", CarDriverDto?.DriverName?.toString() ?: "");
            jsObject.put("idCardType", script.formatIdCardType(CarDriverDto?.CredentialCode?.toString() ?: ""));
            jsObject.put("idCard", CarDriverDto?.CredentialNo?.toString() ?: "");
            jsObject.put("sex", script.formatGender(CarDriverDto?.Gender?.toString() ?: ""));

            jsObject.put("licenseType", CarDriverDto?.DriverTypeCode?.toString() ?: "");
            jsObject.put("licenseDesc", CarDriverDto?.LicenseDesc?.toString() ?: "");
            jsObject.put("licenseNo", CarDriverDto?.LicenseNo?.toString() ?: "");
            jsObject.put("licenseState", CarDriverDto?.LicenseStatusCode?.toString() ?: "");
            jsObject.put("firstRegDate", CarDriverDto?.LicensedDate?.toString() ?: "");
            jsObject.put("isPrimary", CarDriverDto?.IsMajorDriver?.toString() == "1" ? true : false);

            driverInfoList.add(jsObject);
        }
    }

    //车辆车型车价信息
    def ItemCarDto = root?.BodyDto?.ItemCarDto;

    if (ItemCarDto?.ActualValue?.toString()) {
        enquiry?.carInfo?.plateNum = ItemCarDto?.LicensePlateNo?.toString() ?: "";
        enquiry?.carInfo?.plateColor = script.formatPlateColor(ItemCarDto?.LicensePlateColorCode?.toString() ?: "");
        enquiry?.carInfo?.jgVehicleType = script.formatMotorTypeCode(ItemCarDto?.MotorTypeCode?.toString() ?: "");
        enquiry?.carInfo?.engineNum = ItemCarDto?.EngineNo?.toString() ?: "";
        enquiry?.carInfo?.vin = ItemCarDto?.Vin?.toString() ?: "";
        enquiry?.carInfo?.driverArea = script.formatDriverArea(ItemCarDto?.RunArea?.toString() ?: "");
        enquiry?.carInfo?.avgMileage = ItemCarDto?.RunMiles?.toString() ? ItemCarDto?.RunMiles?.toLong() : 0;
        enquiry?.carInfo?.firstRegDate = ItemCarDto?.FirstRegisterDate?.toString() ? df3.format(df4.parse(ItemCarDto?.FirstRegisterDate?.toString())) + " 00:00:00" : "";
        enquiry?.carInfo?.carModelName = ItemCarDto?.ModelName?.toString() ?: "";
        enquiry?.carInfo?.carVehicleOrigin = script.formatCarVehicleOrigin(ItemCarDto?.ImportFlag?.toString() ?: "");
        enquiry?.carInfo?.useProps = script.formatUseProps(ItemCarDto?.MotorUsageTypeCode?.toString() ?: "");
        enquiry?.carInfo?.seatCnt = ItemCarDto?.RatedPassengerCapacity?.toString() ? ItemCarDto?.RatedPassengerCapacity?.toInteger() : 5;
        enquiry?.carInfo?.ratedPassengerCapacity =  enquiry?.carInfo?.seatCnt?.toString() ? enquiry?.carInfo?.seatCnt?.toInteger() -1 : 4;
        enquiry?.carInfo?.modelLoad = ItemCarDto?.Tonnage?.toString() ? ItemCarDto?.Tonnage?.toBigDecimal() * 1000 : 0;
        enquiry?.carInfo?.displacement = ItemCarDto?.Haulage?.toString() ? ItemCarDto?.Haulage?.toDouble() : 0;
        enquiry?.carInfo?.carBodyColor = script.formatCarBodyColor(ItemCarDto?.Color?.toString() ?: "");
        enquiry?.carInfo?.price = ItemCarDto?.PurchasePrice?.toString() ? ItemCarDto?.PurchasePrice?.toBigDecimal() : 0;
        enquiry?.carInfo?.isTransfer = ItemCarDto?.ChgOwnerFlag?.toString() == "1" ? true : false;
        enquiry?.carInfo?.noLicenseFlag = ItemCarDto?.NoLicenseFlag?.toString() == "1" ? true : false;
        enquiry?.carInfo?.isFieldCar = ItemCarDto?.EcdemicVehicleFlag?.toString() == "1" ? true : false;
        enquiry?.carInfo?.transferDate = ItemCarDto?.TransferDate?.toString() ?: null;
        enquiry?.carInfo?.fullLoad = ItemCarDto?.WholeWeight?.toString() ? ItemCarDto?.WholeWeight?.toDouble()?.toInteger() : 0;
        enquiry?.carInfo?.rbCarModelName = ItemCarDto?.Model?.toString() ?: "";
        enquiry?.carInfo?.jyCarModelName = ItemCarDto?.Model?.toString() ?: "";
        enquiry?.carInfo?.rbCode = ItemCarDto?.ModelCode?.toString() ?: "";
        enquiry?.carInfo?.jyCode = ItemCarDto?.ModelCode?.toString() ?: "";
        enquiry?.carInfo?.carBrandName = ItemCarDto?.Brand?.toString() ?: "";
        enquiry?.carInfo?.plateType = script.formatPlateType(ItemCarDto?.LicensePlateType?.toString() ?: "");
        }
    } else {

    }

com.alibaba.fastjson.JSON.toJSONString(enquiry)