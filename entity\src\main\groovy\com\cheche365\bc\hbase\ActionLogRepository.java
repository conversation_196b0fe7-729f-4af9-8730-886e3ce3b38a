package com.cheche365.bc.hbase;

import com.cheche365.bc.entity.hbase.ActionLog;

import java.io.IOException;
import java.util.List;

/**
 * ActionLog HBase服务接口
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
public interface ActionLogRepository {

    /**
     * 保存动作日志
     *
     * @param actionLog 动作日志对象
     * @throws IOException HBase操作异常
     */
    void save(ActionLog actionLog) throws IOException;

    /**
     * 根据RowKey查询动作日志
     *
     * @param rowKey RowKey
     * @return 动作日志对象，如果不存在返回null
     * @throws IOException HBase操作异常
     */
    ActionLog findByRowKey(String rowKey) throws IOException;

    /**
     * 根据autoTraceId查询所有相关的动作日志
     *
     * @param autoTraceId 任务ID
     * @return 动作日志列表，按时间排序
     * @throws IOException HBase操作异常
     */
    List<ActionLog> findByAutoTraceId(String autoTraceId) throws IOException;

    /**
     * 根据autoTraceId和actionId查询特定的动作日志
     * 注意：由于RowKey包含时间戳，这个方法需要扫描
     *
     * @param autoTraceId 任务ID
     * @param actionId 动作ID
     * @return 动作日志对象，如果不存在返回null
     * @throws IOException HBase操作异常
     */
    ActionLog findByAutoTraceIdAndActionId(String autoTraceId, String actionId) throws IOException;

    /**
     * 检查动作日志是否存在
     *
     * @param rowKey RowKey
     * @return 是否存在
     * @throws IOException HBase操作异常
     */
    boolean exists(String rowKey) throws IOException;

    /**
     * 删除动作日志
     *
     * @param rowKey RowKey
     * @throws IOException HBase操作异常
     */
    void delete(String rowKey) throws IOException;

    /**
     * 删除指定任务的所有动作日志
     *
     * @param autoTraceId 任务ID
     * @throws IOException HBase操作异常
     */
    void deleteByAutoTraceId(String autoTraceId) throws IOException;

    /**
     * 批量保存动作日志
     *
     * @param actionLogs 动作日志列表
     * @throws IOException HBase操作异常
     */
    void batchSave(List<ActionLog> actionLogs) throws IOException;

    /**
     * 根据autoTraceId查询最新的动作日志
     *
     * @param autoTraceId 任务ID
     * @return 最新的动作日志，如果不存在返回null
     * @throws IOException HBase操作异常
     */
    ActionLog findLatestByAutoTraceId(String autoTraceId) throws IOException;

    /**
     * 根据autoTraceId分页查询动作日志
     *
     * @param autoTraceId 任务ID
     * @param limit 限制条数
     * @return 动作日志列表，按时间倒序排列
     * @throws IOException HBase操作异常
     */
    List<ActionLog> findByAutoTraceIdWithLimit(String autoTraceId, int limit) throws IOException;
}
