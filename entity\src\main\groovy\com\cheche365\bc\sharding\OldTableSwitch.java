package com.cheche365.bc.sharding;

/**
 * <AUTHOR>
 */
public class OldTableSwitch {

    /**
     * 数据迁移过渡标识：是否使用旧表
     * true: 过渡期间，查询使用旧表，写入双写到旧表和分表
     * false: 正常模式，查询和写入都使用分表
     */
    private static volatile boolean USE_OLD_TABLE = true;

    /**
     * 设置是否使用旧表的标识
     *
     * @param useOldTable true表示使用旧表，false表示使用分表
     */
    public static void setUseOldTable(boolean useOldTable) {
        USE_OLD_TABLE = useOldTable;
    }

    /**
     * 获取当前是否使用旧表的标识
     *
     * @return true表示使用旧表，false表示使用分表
     */
    public static boolean isUseOldTable() {
        return USE_OLD_TABLE;
    }

}
