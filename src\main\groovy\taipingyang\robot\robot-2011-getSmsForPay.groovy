package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.sender.HttpSender
import org.apache.http.impl.client.CloseableHttpClient
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotConstant_2011

Map entity = enquiry
Map tempValues = null != entity.get("tempValues") ? entity.get("tempValues") : tempValues
Map postParameters = postParameters
Map reqHeaders = reqHeaders
def TbUtil = new common_2011()

Map header = new HashMap();
header.put("Content-Type", tempValues?.needSM4Flag ? 'text/plain' : "application/json;charset=UTF-8")

JSONObject param = new JSONObject()

String insuredNo = entity?.SQ?.bizProposeNum
if (StringUtil.isEmpty(insuredNo))
    insuredNo = entity?.SQ?.efcProposeNum;
if (StringUtil.isEmpty(insuredNo))
    throw new InsReturnException("查询用投保单号为空，请确认查询数据正确性")
param = JSON.parseObject("{\"meta\":{\"pageSize\":8},\"redata\":{\"quotationNo\":\"\",\"insuredNo\":\"\",\"policyHolder\":\"\",\"partyName\":\"\",\"insuredStartDate\":\"\",\"insuredEndDate\":\"\"}}")
param.getJSONObject("redata").put("insuredNo", insuredNo)
def reqBody = param.toJSONString()
def queryPaymentResult = HttpSender.doPostWithRetry(5,
        httpClient as CloseableHttpClient, true,
        RobotConstant_2011.URL.QUERY_PAYMENT,
        Robot2011Util.genBody(tempValues, reqBody), null,
        reqHeaders,
        "UTF-8", null, "");

queryPaymentResult = Robot2011Util.decodeBody(tempValues, queryPaymentResult)

JSONObject queryPaymentResultObj = null
if (queryPaymentResult instanceof JSONObject)
    queryPaymentResultObj = (JSONObject) queryPaymentResult
else
    queryPaymentResultObj = JSON.parseObject(queryPaymentResult)

if (null == queryPaymentResultObj.get("result") || queryPaymentResultObj.getJSONArray("result").size() == 0)
    throw new InsReturnException("按投保单号" + insuredNo + "查无支付信息")

JSONArray resultList = queryPaymentResultObj.getJSONArray("result").clone()
//类似检测步骤
param = JSON.parseObject(TbUtil.getBaseParam());
param.getJSONObject("redata").put("payments", resultList.clone());
for (int i = 0; i < param.getJSONObject("redata").getJSONArray("payments").size(); i++) {
    param.getJSONObject("redata").getJSONArray("payments").getJSONObject(i).put("paymentType", "");
    param.getJSONObject("redata").getJSONArray("payments").getJSONObject(i).put("cooperant", "");
}

reqBody = param.toJSONString()
reqBody = Robot2011Util.genBody(tempValues, reqBody)
def queryEcarcertTypeResult = HttpSender.doPostWithRetry(5,
        httpClient as CloseableHttpClient, true,
        RobotConstant_2011.URL.QUERY_ECARCERT_TYPE,
        reqBody,
        null,
        header,
        "UTF-8", null, "");

String quotationNo = resultList.getJSONObject(0).getString("quotationNo");
param = JSON.parseObject(TbUtil.getBaseParam());
param.getJSONObject("redata").put("quotationNo", quotationNo);
//url:http://issue.cpic.com.cn/ecar/payment/smsGetVerificatiionCode

String smsGetVerificatiionCodeJson = StringUtil.chinesetoUnicode(param.toJSONString())
reqHeaders.put("Content-Type", tempValues?.needSM4Flag ? 'text/plain' : "application/json;charset=UTF-8")
postParameters.clear()
entity.put("tempValues", tempValues)

Robot2011Util.genBody(tempValues, smsGetVerificatiionCodeJson)
