package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.sender.HttpSender
import org.apache.http.impl.client.CloseableHttpClient
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotConstant_2011

Map entity = enquiry
Map tempValues = entity.get("tempValues") ?: tempValues
def TbUtil = new common_2011()
//URL
Map header = new HashMap();
header.'Content-Type' = tempValues?.needSM4Flag ? 'text/plain' : "application/json;charset=utf-8"

def moreBjIdentifyCode = (String) root;
if (!moreBjIdentifyCode) {
    TbUtil.getUserDefinedException("承保验证码更新失败！")
}
moreBjIdentifyCode = Robot2011Util.decodeBody(tempValues, moreBjIdentifyCode)
JSONObject j = JSON.parseObject(moreBjIdentifyCode)
def taskId = entity?.taskId
entity.'result' = true
entity.'statusCode' = '2'
entity.'taskId' = taskId
if ("failed" == j.'message'.'code' || j.toJSONString().contains("验证码校验未通过") || j.toJSONString().contains("验证码错误")
        || j.toJSONString().contains("错误信息")) {
    entity.'identityStatusCode' = '1'
    entity.'taskStatus' = '10001'
    TbUtil.getUserDefinedException("发送验证码失败，平台提示:" + j.'message'.'message')
} else {
    entity.'statusCode' = '1'
    def flag = entity?.configInfo?.configMap?.areaComCode == '北京'
    if (flag) {
        //北京地区进行连接的验证
        JSONObject param = JSON.parseObject('{"meta":{"pageSize":8},"redata":{"quotationNo":"","insuredNo":"","policyHolder":"","partyName":"","insuredStartDate":"","insuredEndDate":""}}')
        def insuredNo = entity?.SQ?.bizProposeNum ?: entity?.SQ?.efcProposeNum
        if (!insuredNo) {
            TbUtil.getUserDefinedException("无法进行北京地区链接是否点击验证！缺少投保单号!")
        }
        param.'redata'.'insuredNo' = insuredNo
        def reqBody = param.toJSONString()
        reqBody = Robot2011Util.genBody(tempValues, reqBody)
        def result = HttpSender.doPostWithRetry(5, httpClient as CloseableHttpClient, true,
                RobotConstant_2011.URL.QUERY_PAYMENT,
                reqBody,
                null,
                header,
                "UTF-8", null, "")
        result = Robot2011Util.decodeBody(tempValues, result)
        JSONObject queryPaymentResultObj = JSON.parseObject(result)
        def queryPaymentResultObjResult = queryPaymentResultObj?.'result'
        if (!queryPaymentResultObjResult || queryPaymentResultObjResult.size() == 0) {
            TbUtil.getUserDefinedException("按投保单号" + insuredNo + "查无支付信息")
        } else {
        }
        for (int i = 0; i < queryPaymentResultObjResult.size(); i++) {
            def payment = queryPaymentResultObjResult[i];
            payment?.'paymentType' = entity?.configInfo?.configMap?.paymentType ?: 'weixin'
            //支付类型取配置如未配默认微信 1支票 2划卡 chinapay银联电子支付 weixin微信支付
            //合作银行 01农业银行 02中国银行 03交通银行 04建设银行 05交行上海市分行
            payment?.'cooperant' = entity?.configInfo?.configMap?.cooperant ?: '2'
        }
        param = JSON.parseObject('{"meta":{},"redata":{}}')
        param.'redata'.'payments' = queryPaymentResultObjResult
        String getPayInfoJsonString = StringUtil.chinesetoUnicode(param.toJSONString())
        getPayInfoJsonString = Robot2011Util.genBody(tempValues, getPayInfoJsonString)
        def getPayInfoResult = HttpSender.doPostWithRetry(5, httpClient as CloseableHttpClient, true,
                RobotConstant_2011.URL.PAYMENT_QUERY_PAY,
                getPayInfoJsonString, null,
                header,
                "UTF-8", null, "");
        getPayInfoResult = Robot2011Util.decodeBody(tempValues, getPayInfoResult)
        JSONObject getPayInfoResultObj = JSON.parseObject(getPayInfoResult);
        JSONObject responseJson = new JSONObject();
        String paytype = (String) tempValues.get("paytype");
        if ('success' == getPayInfoResultObj?.'message'?.'code') {
            //校验码
            String checkCode = TbUtil.getFromJson(getPayInfoResultObj, "result.paymentShowVos.[0].checkCode")
            //支付号
            String payNo = TbUtil.getFromJson(getPayInfoResultObj, "result.paymentShowVos.[0].payNo")
            //总保费
            String totalMoney = TbUtil.getFromJson(getPayInfoResultObj, "result.paymentShowVos.[0].totalMoney")

            if ("qrcodeB64".equals(paytype) || "weixin".equals(paytype) || "alipay".equals(paytype)) {
                responseJson.put("payCodeUrl", TbUtil.getFromJson(getPayInfoResultObj, "result.twoDimensionCodeLink"));
                responseJson.put("payCodeIsSuccess", "true");
                responseJson.put("payCodeErrorMsg", "");
                responseJson.put("payCodeSum", totalMoney);
                responseJson.put("paytype", paytype);
                if (!"qrcodeB64".equals(paytype)) {
                    responseJson.put("payOrderNo", payNo);
                    responseJson.put("checkCode", checkCode);
                }
            }
            entity.clear()
            entity.putAll(responseJson)
        } else {
            //错误提示
            String msg = TbUtil.getFromJson(getPayInfoResultObj, "message.message")
            responseJson.put("payCodeIsSuccess", "false");
            responseJson.put("payCodeErrorMsg", "");
            responseJson.put("payCodeUrl", "");
            responseJson.put("payCodeSum", 0);
            responseJson.put("orderPaymentId", tempValues.orderPaymentId);
            responseJson.put("sid", tempValues.sid);
            responseJson.put("orgCode", entity.orgCode);
            responseJson.put("payCodeErrorMsg", "获取支付信息失败");
            entity.clear()
            entity.putAll(responseJson)
            throw new InsReturnException(11, "获取支付信息失败,平台提示:" + msg)
        }
    } else {
        entity.'taskStatus' = '10000'
    }
}


