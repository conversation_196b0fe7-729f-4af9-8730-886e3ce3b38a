package taipingyang.edi

import cn.hutool.core.convert.Convert
import cn.hutool.core.date.DatePattern
import cn.hutool.core.util.ObjUtil
import com.cheche365.bc.utils.CommonUtils
import common.common_all
import groovy.transform.BaseScript
import taipingyang.edi.module.constants.EdiConstants_2011
import taipingyang.edi.module.mapping.EdiDict_2011
import taipingyang.edi.module.script.BaseScript_Map_2011

import java.time.format.DateTimeFormatter

/**
 * 新车备案
 *
 * <AUTHOR>
 */
@BaseScript BaseScript_Map_2011 _

log.info("dict is empty :{}", ObjUtil.isEmpty(dict))
//非新车跳过
skip '非新车未上牌跳过', common_all.skipNewCarRegistration(enquiry) as Boole<PERSON>

def carInfo = enquiry.carInfo
def carOwnerInfo = enquiry.carOwnerInfo
def reqBody = [
        //终端号
        'terminalNo'     : config.terminalNo,
        //车主证件号码
        'certiCode'      : carOwnerInfo?.idCard,
        //开具车辆来历所载日期
        'certificateDate': getCertificateDate(),
        //车辆来历凭证编号
        'certificateNo'  : common_all.getSupplyParam(enquiry, "carOriginProofNo") ?: common_all.getSupplyParam(enquiry, "carproofno"),
        //车辆来历凭证种类
        'certificateType': '01',
        //车主证件类型
        'certiType'      : EdiDict_2011.getCardType(carOwnerInfo?.idCardType),
        //号牌颜色
        'color'          : dict.plateColor,
        //发动机号
        'engineNo'       : carInfo?.engineNum,
        //燃料类型，非北京地区使用通用值
        'fuelType'       : dict.fuelTypeOther,
        //车架号
        'rackNo'         : carInfo?.vin,
        //车主姓名
        'owner'          : carOwnerInfo?.name,
        //核定载客量
        'lmtLoadPerson'  : carInfo?.seatCnt as Integer,
        //核定载质量（吨）
        'lmtLoad'        : carInfo?.modelLoad?.toString() ?: '0',
        //整备质量（吨） = 车身自重
        'poWeight'       : dict.fullLoad4Tonne,
        //排气量(L)
        'exhaustCapacity': Convert.toBigDecimal(carInfo?.displacement?.toString(), BigDecimal.ZERO),
        //车辆种类,除北京必传
        'vehCategory'    : dict.vehicleKind,
        //使用性质，除北京必传
        'useType'        : dict.vehicleUsage,
        //车辆型号，取公告型号，除北京必传
        'vehModel'       : tempValues?.noticeType

]
//北京必传或特殊
if (cityIn(EdiConstants_2011.BEIJING_CITY_CODES)) {
    //车辆类型（交管）
    reqBody.put('fuelType', dict.fuelType)
    reqBody.put('vehStyle', dict.jgVehicleType)
}

body 'third.insurance.registerNewVehicle', ['registerNewVehicleReq': reqBody]

private String getCertificateDate() {
    def aPattern = DateTimeFormatter.ofPattern(DatePattern.PURE_DATETIME_PATTERN)
    String certificateDate = enquiry?.carInfo?.firstRegDate?.toString()
    if (common_all.getSupplyParam(enquiry, "carOriginProofDate")) {
        certificateDate = common_all.getSupplyParam(enquiry, "carOriginProofDate") + " 00:00:00"
    }
    return CommonUtils.toLocalDateTime(certificateDate).format(aPattern)
}