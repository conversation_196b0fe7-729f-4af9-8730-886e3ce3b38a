package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.utils.RedisUtil
import org.apache.http.impl.client.BasicCookieStore
import org.apache.http.impl.client.CloseableHttpClient

/**
 * 太平洋精灵 - 代理点、终端及经办人选择响应模板
 */
def tempValues = tempValues as Map

def resultStr = root as String
if (resultStr.startsWith('{')) {
    def result = JSON.parseObject(resultStr)
    def authentication = result.getString('authentication')
    if (authentication != 'true') {
        throw new InsReturnException(InsReturnException.Others, result.getString('errMsg'))
    }
} else if (!resultStr.contains('退出')) {
    throw new InsReturnException(InsReturnException.Others, '登录失败')
}
def httpClient = httpClient as CloseableHttpClient
def cookieStore = httpClient.class.getDeclaredField("cookieStore")
cookieStore.setAccessible(true)
def cookies = (cookieStore.get(httpClient) as BasicCookieStore).getCookies()
def cookieStr = ''
cookies.each({ cookie ->
    if (cookie.getName() in ['route', 'JSESSIONID']) {
        cookieStr += (cookie.getName() + '=' + cookie.getValue() + '; ')
    }
})
def config = config as Map
def j_username = config['login'] ?: config['username']
RedisUtil.set('cookieStrKey:2011:' + j_username, cookieStr as String, 12 * 60 * 60)
