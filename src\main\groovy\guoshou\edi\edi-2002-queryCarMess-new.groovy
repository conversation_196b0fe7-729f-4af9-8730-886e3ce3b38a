package guoshou.edi

import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateUtil
import com.alibaba.fastjson.JSONObject
import groovy.transform.BaseScript
import guoshou.constants.GuoShouConstants
import guoshou.script.BaseScript_2002

import static common.common_all.checkEnergyType
import static guoshou.edi.edi_2002_common_new.*

@BaseScript BaseScript_2002 _

if (!tempValues.repeatCarModel) {
    tempValues.businessNumber = UUID.randomUUID().toString().replace("-", "")
}

def energyType = checkEnergyType(carInfo.carModelName, carInfo.plateNum)
tempValues.energyType = energyType

def enrollDate = DateUtil.parse(carInfo.firstRegDate.toString(), DatePattern.NORM_DATETIME_PATTERN).toString(DatePattern.NORM_DATE_PATTERN)
tempValues.enrollDate = enrollDate

def request = [
        "commonData"  : setCommonDataForPost(config, tempValues.businessNumber, GuoShouConstants.CAR_QUERY),
        "businessData": [
                "frameNo"     : carInfo.vin,
                "engineNo"    : carInfo.engineNum,
                "standardName": carInfo.carModelName,
                "rbCode"      : carInfo.rbCode,
                "localFlag"   : getLocalFlag(enquiry, tempValues),
                "enrollDate"  : enrollDate
        ]
] as JSONObject

if (carInfo.plateNum != "新车未上牌") {
    request.businessData.licensePlateNo = carInfo.plateNum
    //0燃油 1纯电 3混合
    request.businessData.licenseType = "0" == energyType ? getPlateType(carInfo.plateType)[0] : getNewEnergyPlateType(carInfo.plateType)[0]
}

setRequestSignature(reqHeaders, request.toString())

request.toString()
