{"carPriceType": "不含税价", "carOwnerInfo": {"birthday": "1968-06-14", "idCardType": 0, "idCard": "330922196806144515", "sex": 0, "name": "陆善和"}, "agentInfo": {"certNumber": "20211133020080002022002675", "teamName": "宁波车车滨江杨雪琴团队", "idCardType": 0, "idCard": "330227195701191226", "sex": "2", "mobile": "13123829197", "name": "潘静菊", "teamCode": "010002016002001001045", "platformName": "宁波车车滨江本部营业部", "platformCode": "010002016002001001"}, "insuredPersonInfoList": [{"birthday": "1968-06-14", "idCardType": 0, "idCard": "330922196806144515", "sex": 0, "name": "陆善和"}], "requestSource": {"sourceProduct": "cby", "sourceScenario": "main_app", "sourceChannel": "<PERSON><PERSON><PERSON><PERSON>", "desc": "车保易"}, "insArea": {"province": "330000", "city": "330200"}, "baseSuiteInfo": {"taxSuiteInfo": {}, "bizSuiteInfo": {"suites": [{"amount": 135800, "code": "VehicleDamage", "name": "车损险", "share": false}, {"amount": 2000000, "code": "ThirdParty", "name": "三者险", "share": false}, {"amount": 10000, "code": "Driver", "name": "司机责任险", "share": false}, {"amount": 10000, "code": "Passenger", "name": "乘客责任险", "share": false}, {"amount": 200000, "code": "NIHCThirdParty", "name": "附加医保外用药责任险（三者险）", "share": false}], "start": "2025-06-18 00:00:00", "end": "2026-06-17 23:59:59"}, "efcSuiteInfo": {"amount": 1, "start": "2025-06-18 00:00:00", "end": "2026-06-17 23:59:59"}}, "supplyParam": [{"itemvalue": "1", "itemcode": "isLocalRegistration"}, {"itemvalue": "1", "itemcode": "isLocalDriving"}], "orgCode": "1233300000", "renewalquoteitem": [], "deliverInfo": {"receiveTime": "3", "area": "330203", "receiveDay": "2", "address": "浙江省宁波市海曙区宁波车车滨江本部营业部", "isInvoice": 0, "province": "330000", "phone": "", "city": "330200", "deliveType": 0, "name": "陆善和", "isFreightCollect": false, "postCode": "100000"}, "applicantPersonInfo": {"birthday": "1968-06-14", "idCardType": 0, "idCard": "330922196806144515", "sex": 0, "name": "陆善和"}, "carInfo": {"taxAnalogyPrice": 0, "carModelName": "大通SH6551A2DB多用途乘用车", "fullLoad": 2430, "modelLoad": 0, "plateColor": 1, "price": 135800, "familyName": "大通V80", "vin": "LSKG5GC11NA112735", "displacement": 1.996, "analogyPrice": 135800, "carPriceType": "0", "isTransfer": false, "useProps": 1, "plateType": 1, "firstRegDate": "2022-07-18 00:00:00", "engineNum": "M9226035287", "isGreenCar": 0, "isNew": false, "plateNum": "浙BH081U", "carUserType": 0, "carBrandName": "上汽大通MAXUS", "syvehicletypename": "六座及十座以下客车", "rbCode": "DTBBGD0003", "jgVehicleType": 19, "syvehicletypecode": "KB", "fuelType": 0, "seatCnt": 7, "jyCode": "DTBBGD0003", "isMotorcycle": 0, "taxPrice": 147407}, "configInfo": {"configMap": {"stCertificateValidity": "2099-12-29", "agentCode": "342", "verifi_catCode": "347657", "vehicleInspection": "", "processControl": "1", "needExtraInfo": "true", "repairFactorRate2": "0.3", "needHolderTelphone": "17681663311", "default_taxBureauName": "宁波市税务局", "login": "w_wbHAISHUDAILIBU", "userCode": "w_wbHAISHUDAILIBU", "check4pay": "true", "repairFactorRate": "0.15", "needISCREATEEPOLICY": "true", "defaultEmail": "<EMAIL>", "needISCREATEEINVOICE": "true", "needISSENDEPOLICYSMS": "true", "partner_code": "4S397246", "detail4": "21", "agent_code": "0330", "derateNo": "111111", "reSelectTimes": "5", "refreshMin": "1", "expectSuccessFlag": "代理点、终端及经办人选择", "maxDif": "30", "registryNumber": "913302117369573902", "keepSessionUrl": "https://issue.cpic.com.cn/ecar/view/portal/page/common/partnerselect.html", "areaComCode": "宁波", "pwd": "Zyc@2013", "cooperant": "03", "loginTemplateName": "robot-2011-quote-login", "defaultAddress": "浙江省宁波市", "quoteLimit": "30"}}, "imgAddress": {"0": "https://chetimes-obs.cdn.chetimes.com/prod/PB20250617007978/mmexport1749967315924_1750128630580.jpg?x-image-process=image/resize,m_lfit,h_1600&cheche_token=1750128774-72bc13c2e0534c36b0308432120c8692-0-59F6566692AFD54F6A809D976E0FC86972D8067A3D0B956727E15D5A63B6DC6E", "3": "https://chetimes-obs.cdn.chetimes.com/prod/PB20250617007978/mmexport1749967311577.jpg?x-image-process=image/resize,m_lfit,h_1600&cheche_token=1750128774-a70fb6e0e5ba4fb6a9832f33c34d6f81-0-D7901677F9F37FE6CAFB679D5E635548C5C2C76C5E4DE1C59BC0900FAAA50ED7", "4": "https://chetimes-obs.cdn.chetimes.com/prod/PB20250617007978/mmexport1749967313864_1750128731803.jpg?x-image-process=image/resize,m_lfit,h_1600&cheche_token=1750128774-5758bea1acd64cd0aa4ab93254b89a58-0-6168B81EE1BDE0E3540A1F45B6EE6C67441E6075D377F53AAA3BB8CACEA14D55", "25": "https://chetimes-obs.cdn.chetimes.com/prod/PB20250617007978/mmexport1749967315924_1750128630580.jpg?x-image-process=image/resize,m_lfit,h_1600&cheche_token=1750128774-543b9cca120d456dae05434af315aa89-0-33B0A04B6A2C5EC957A5E4837C3F5759A515ACD22B6316FD20CBE43F0DA39E9E", "26": "https://chetimes-obs.cdn.chetimes.com/prod/PB20250617007978/mmexport1749967318209_1750128745633.jpg?x-image-process=image/resize,m_lfit,h_1600&cheche_token=1750128774-e6572e1dd96c47acac7f0bae6343290c-0-44F7D6F0F08033AD8005D0AA627FFCC84E3A9F57AD3BFFA8FA4B65BB210EC1CE", "28": "https://chetimes-obs.cdn.chetimes.com/prod/PB20250617007978/mmexport1749967315924_1750128630580.jpg?x-image-process=image/resize,m_lfit,h_1600&cheche_token=1750128774-91c2174a5e12405199b82ac0fa8b3fce-0-8A32D48F16CD843CCBD3EF6EFED03AAB5F7EA73BFCE3FB1ACD9CA38D6B4ECA1F"}, "singleSite": "1233192001", "sq": {"totalCharge": 0, "taxCharge": 0, "nonMotor": {"productCode": "PL2418980024A10000172", "count": "1"}, "reform2020": true, "deptCode": "010002016002001001", "misc": {}}}