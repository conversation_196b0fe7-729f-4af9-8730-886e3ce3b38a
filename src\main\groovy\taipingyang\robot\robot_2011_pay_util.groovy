package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.utils.sender.HttpSender
import org.apache.commons.io.FileUtils
import taipingyang.robot.module.Robot2011Util

def static stringURL() {
    return [
            Referer              : 'https://issue.cpic.com.cn/ecar/view/portal/page/premium_payment/premium_payment.html',
            getThcQrCodeUrl      : 'https://issue.cpic.com.cn/ecar/paymentQuery/getThcQrCode',
            getDrainageUrl       : 'https://issue.cpic.com.cn/ecar/paymentQuery/getDrainage',
            moreBjIdentifyCodeUrl: 'https://issue.cpic.com.cn/ecar/update/moreBjIdentifyCode',
    ]

}

def static getBaseThcQrCodeJson() {
    //"payNo":"","payChannel":"PAYCHANNEL"
    return JSON.parseObject('{"meta":{},"redata":{}}')
}


def static doGetNewThcQrCode(httpClient, Map reqHeaders, String payNo, String payChannel, Map tempValues) {
    reqHeaders.Referer = stringURL().Referer
    def getThcQrCodeUrl = stringURL().getThcQrCodeUrl
    def getThcQrCode = getBaseThcQrCodeJson()
    if (!payNo) {
        //exception
        MyException(InsReturnException.Others, "获取payNo失败！")
    } else {
        getThcQrCode?.redata?.payNo = payNo
    }
    if (payChannel) {
        if (payChannel == "1") {//东莞 分公司微信公众号
            getThcQrCodeUrl = stringURL().getDrainageUrl
        }
        getThcQrCode?.redata?.payChannel = payChannel
    }
    def reqBody = getThcQrCode.toJSONString()
    reqBody = Robot2011Util.genBody(tempValues, reqBody)
    def getThcQrCodeResult = HttpSender.doPostWithRetry(5, httpClient, true,
            getThcQrCodeUrl,
            reqBody, null,
            reqHeaders,
            "UTF-8", null, "");
    return Robot2011Util.decodeBody(tempValues, getThcQrCodeResult)
}


def getNewThcQrCode(httpClient, Map reqHeaders, String payNo, Map configs, Map tempValues) {
    String getThcQrCodeResult = ""
    if (configs) {
        getThcQrCodeResult = doGetNewThcQrCode(httpClient, reqHeaders, payNo, configs?.payChannel as String, tempValues)
    } else {
        getThcQrCodeResult = doGetNewThcQrCode(httpClient, reqHeaders, payNo, null, tempValues)
    }
    def getThcQrCodeJson = JSON.parseObject(getThcQrCodeResult)
    def carLifeImag = getThcQrCodeJson?.result?.carLifeImag as String
    if (configs?.payChannel as String == "3" || configs?.payChannel as String == "2") {
        carLifeImag = getThcQrCodeJson?.result?.imag as String
    } else if (configs?.payChannel as String == "1") {
        carLifeImag = getThcQrCodeJson?.result?.drainageImag as String
    } else if (carLifeImag?.contains("data:image/png;base64,")) {
        carLifeImag = carLifeImag?.replace("data:image/png;base64,", "")
    }
    carLifeImag
}


def static MyException(int type, String str) throws InsReturnException {
    throw new InsReturnException(type, str)
}
//支持获取二维码生成本地图片
def doImage(String imageString, String channel) {
    imageString = imageString.replaceAll("\\\\n", '')
    String outputFilePath = "2011test_image_copy" + channel + ".jpg";
    convertBase64String2ImageFile imageString, outputFilePath
}

def static convertBase64String2ImageFile(encodedString, outputFileName) {
    def decodedBytes = Base64.decoder.decode encodedString
    FileUtils.writeByteArrayToFile new File(outputFileName), decodedBytes
}

//身份采集的短信验证码校验 两个险种只要验证一次即可
def static moreBjIdentifyCode(Map tempValues, List resultList, httpClient, reqHeaders, Map entity) {
    if (tempValues.bjIdentifyCode) {
        String paramStr = '{"meta":{},"redata":{"bjIdentifyCodeVos":[{"quotationNo":"QUOTATIONNO","bjIdentifyCode":"BJIDENTIFYCODE"}]}}'
        paramStr = paramStr.replace("BJIDENTIFYCODE", tempValues.bjIdentifyCode).replace("QUOTATIONNO", resultList[0]?.quotationNo?.toString())
        paramStr = Robot2011Util.genBody(tempValues, paramStr)
        def result = HttpSender.doPostWithRetry(3, httpClient, true, stringURL().moreBjIdentifyCodeUrl, paramStr, null, reqHeaders, "UTF-8", tempValues.requestConfig, "");
        if (!result) {
            MyException(InsReturnException.Others, "提交身份采集短信验证码返回失败！")
        } else {
            JSONObject resultObj = JSON.parseObject(result)
            if ("success" != resultObj?.message?.code || resultObj?.message?.message?.toString()?.contains("验证码校验未通过")) {
                def responseJson = [
                        payCodeIsSuccess: "false",
                        isVerifSucc     : "false",
                        payCodeUrl      : "",
                        payCodeSum      : "0",
                        orderPaymentId  : tempValues?.orderPaymentId,
                        orgCode         : entity.orgCode,
                        paytype         : tempValues?.paytype,
                        payCodeErrorMsg : "发送短信验证码失败,系统返回:" + resultObj?.message?.message?.toString()
                ]
                entity.clear()
                entity << responseJson
            }
        }
    } else {
    }
}
