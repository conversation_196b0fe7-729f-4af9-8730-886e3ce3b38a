package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.tools.StringUtil
import com.cheche365.bc.utils.sender.HttpSender
import org.apache.http.client.config.RequestConfig
import org.apache.http.impl.client.CloseableHttpClient
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotConstant_2011

Map entity = enquiry
Map configs = config
Map tempValues = null != entity.get("tempValues") ? entity.get("tempValues") : tempValues
Map reqHeaders = null != tempValues.get("reqHeaders") ? tempValues.get("reqHeaders") : new HashMap<>();

def TbUtil = new common_2011()
reqHeaders.put("Content-Type", tempValues?.needSM4Flag ? 'text/plain' : "application/json;charset=utf-8")
JSONObject param = null;

String result = Robot2011Util.decodeBody(tempValues, (String) root)

JSONObject queryPaymentResultObj = JSON.parseObject(result)
if (null == queryPaymentResultObj.get("result") || queryPaymentResultObj.getJSONArray("result").size() == 0) {
    throw new InsReturnException("按投保单号" + tempValues.get("insuredNo") + "查无支付信息")
}

JSONArray resultList = queryPaymentResultObj.getJSONArray("result").clone() as JSONArray

//某些地区需要实名认证
if ("true".equals(entity?.configInfo?.configMap?.wholeCountryCertification)) {
    String url = "https://issue.cpic.com.cn/ecar/payment/wholeCountryCertification";
    param = JSON.parseObject(TbUtil.getBaseParam());
    param.getJSONObject("redata").put("payments", new JSONArray());
    for (int i = 0; i < resultList.size(); i++) {
        JSONObject j = new JSONObject();
        j.put("quotationNo", resultList.getJSONObject(i).get("quotationNo"));
        param.getJSONObject("redata").getJSONArray("payments").add(j);
    }
    def reqBody = StringUtil.chinesetoUnicode(param.toJSONString())
    reqBody = Robot2011Util.genBody(tempValues, reqBody)
    result = HttpSender.doPostWithRetry(5,
            httpClient as CloseableHttpClient, true,
            RobotConstant_2011.URL.WHOLE_COUNTRY_CERTIFICATION,
            reqBody,
            null,
            reqHeaders,
            "UTF-8", null, "");
    result = Robot2011Util.decodeBody(tempValues, result)
    JSONObject resultObj = JSON.parseObject(result);
    if (!"success".equals((String) TbUtil.getFromJson(resultObj, "message.code")))
        throw new InsReturnException("平台返回提示:" + (String) TbUtil.getFromJson(resultObj, "message.message"));

    url = "https://issue.cpic.com.cn/ecar/payment/paymentRecordMan";
    param = JSON.parseObject(TbUtil.getBaseParam());
    param.getJSONObject("redata").put("payments", resultList);
    reqBody = StringUtil.chinesetoUnicode(param.toJSONString())
    reqBody = Robot2011Util.genBody(tempValues, reqBody)
    result = HttpSender.doPostWithRetry(5, httpClient as CloseableHttpClient, true,
            RobotConstant_2011.URL.PAYMENT_RECORD_MAN,
            reqBody,
            null,
            reqHeaders,
            "UTF-8", null, "");
    result = Robot2011Util.decodeBody(tempValues, result)
    resultObj = JSON.parseObject(result);
    if (!"success".equals((String) TbUtil.getFromJson(resultObj, "message.code")))
        throw new InsReturnException("平台返回提示:" + (String) TbUtil.getFromJson(resultObj, "message.message"));
}
JSONObject getPayInfoResultObj = null;
//重试标识
boolean retry = true;
//是否已强制提交验证码
boolean submitedVerificationCode = false;
def newPayType = configs?.paymentType
def payChannel = configs?.payChannel
def newFlag = ["四川", "东莞", "北京", "湖北", "广州市", "广州", "山东"].contains(configs?.areaComCode?.toString())
if (retry) {//while 之前的隐患
    //是否需要提交验证码
    boolean needSubmitCode = StringUtil.isNoEmpty((String) tempValues.get("verificationCode")) && !"1".equals(resultList.getJSONObject(0).get("bjIdentifyCodeFlag"));
    boolean errVerificationCode = false;
    if (needSubmitCode || errVerificationCode) {
        if (errVerificationCode) {
        }
        submitedVerificationCode = true;
        String pinCode = tempValues.get("verificationCode");
        String quotationNo = resultList.getJSONObject(0).getString("quotationNo");
        param = JSON.parseObject("{\"meta\":{},\"redata\":{\"bjIdentifyCodeVos\":[{\"quotationNo\":\"\",\"bjIdentifyCode\":\"\"}]}}");
        param.getJSONObject("redata").getJSONArray("bjIdentifyCodeVos").getJSONObject(0).put("bjIdentifyCode", pinCode);
        param.getJSONObject("redata").getJSONArray("bjIdentifyCodeVos").getJSONObject(0).put("quotationNo", quotationNo);
        def reqBody = param.toJSONString()
        reqBody = Robot2011Util.genBody(tempValues, reqBody)
        result = HttpSender.doPostWithRetry(3,
                httpClient as CloseableHttpClient, true,
                RobotConstant_2011.URL.MORE_BJ_IDENTIFY_CODE,
                reqBody,
                null,
                reqHeaders,
                "UTF-8", tempValues.requestConfig as RequestConfig, "");
        result = Robot2011Util.decodeBody(tempValues, result)
        JSONObject resultObj = JSON.parseObject(result);
        if (!"success".equals(TbUtil.getFromJson(resultObj, "message.code")) || ((String) TbUtil.getFromJson(resultObj, "message.message")).contains("验证码校验未通过")) {
            //回写对象
            JSONObject responseJson = new JSONObject();
            responseJson.put("payCodeIsSuccess", "false");
            responseJson.put("isVerifSucc", "false");
            responseJson.put("payCodeErrorMsg", "");
            responseJson.put("payCodeUrl", "");
            responseJson.put("payCodeSum", 0);
            responseJson.put("orderPaymentId", tempValues.orderPaymentId)
            responseJson.put("sid", tempValues.sid)
            responseJson.put("orgCode", entity.orgCode)
            responseJson.put("paytype", tempValues.get("paytype"));
            responseJson.put("payCodeErrorMsg", "发送短信验证码失败,系统返回:" + TbUtil.getFromJson(resultObj, "message.message"));
            entity.clear()
            entity.putAll(responseJson)
            throw new InsReturnException(11, "发送短信验证码失败,系统返回:" + TbUtil.getFromJson(resultObj, "message.message"))
        } else {
            param = JSON.parseObject("{\"meta\":{\"pageSize\":8},\"redata\":{\"quotationNo\":\"\",\"insuredNo\":\"\",\"policyHolder\":\"\",\"partyName\":\"\",\"insuredStartDate\":\"\",\"insuredEndDate\":\"\"}}")
            param.getJSONObject("redata").put("quotationNo", quotationNo);

            reqBody = param.toJSONString()
            reqBody = Robot2011Util.genBody(tempValues, reqBody)
            result = HttpSender.doPostWithRetry(3, httpClient as CloseableHttpClient, true,
                    RobotConstant_2011.URL.QUERY_PAYMENT,
                    reqBody,
                    null, reqHeaders, "UTF-8", tempValues.requestConfig as RequestConfig, "");
            result = Robot2011Util.decodeBody(tempValues, result)
            resultObj = JSON.parseObject(result);
            if (null != resultObj.get("result") && resultObj.getJSONArray("result").size() > 0) {
                resultList = resultObj.getJSONArray("result").clone();
            }
        }
    }
    def robot_2011_pay_util = new robot_2011_pay_util()
    //身份采集成功承保验证码校验
    robot_2011_pay_util.moreBjIdentifyCode(tempValues, resultList, httpClient, reqHeaders, entity)
    String paymentType = null != entity?.configInfo?.configMap?.paymentType ? entity?.configInfo?.configMap?.paymentType : "weixin";
    if (("weixin".equals(tempValues.get("paytype")) || "alipay".equals(tempValues.get("paytype"))) && entity?.configInfo?.configMap?.areaComCode != '吉林') {
        paymentType = tempValues.get("paytype");
    }

    // 支付方式 payType
    //支付渠道 payChannel 2976
    for (int i = 0; i < resultList.size(); i++) {
        if (newPayType && newFlag) {
            JSONObject payment = resultList.getJSONObject(i);
            payment.put("cooperant", configs?.cooperant)
            payment.put("paymentType", newPayType)
        } else {
            boolean areaFlag = ['广州', '吉林'].contains(entity?.configInfo?.configMap?.areaComCode as String) || (String.valueOf(tempValues.paytype) == "mpay")
            String cooperant = entity?.configInfo?.configMap?.cooperant
            JSONObject payment = resultList.getJSONObject(i);
            //todo 吉林支付
            //支付类型取配置如未配默认微信 1支票 2划卡 chinapay银联电子支付 weixin微信支付 alipay 支付宝
            if (areaFlag) {//新支付，微信实名制支付方式
                payment.put("paymentType", "mpay")
                cooperant = ''
            } else {
                payment.put("paymentType", paymentType)
            }
            payment.put("cooperant", cooperant)
            if (["北京"].contains(config?.areaComCode)) {
                payment.paymentType = configs?.paymentType ?: "2"
                payment.cooperant = configs?.cooperant ?: "01"
                payment.bjIdentifyCodeFlag = "1"
                payment.bjIdentifyCode = tempValues?.bjIdentifyCode
                payment.isPartyPay = "0"
            }
        }
    }

    param = JSON.parseObject(TbUtil.getBaseParam());
    param.getJSONObject("redata").put("payments", resultList)

    String getPayInfoJson = StringUtil.chinesetoUnicode(param.toJSONString())
    def reqBody = Robot2011Util.genBody(tempValues, getPayInfoJson)
    def paymentRecordManResult = HttpSender.doPostWithRetry(5, httpClient as CloseableHttpClient, true,
            RobotConstant_2011.URL.PAYMENT_RECORD_MAN,
            reqBody,
            null,
            reqHeaders,
            "UTF-8", null, "");

    String getPayInfoUrl = "https://issue.cpic.com.cn/ecar/paymentQuery/pay"
    if (newPayType && newFlag && configs.payChannel) {
        param?.redata?.payChannel = configs?.payChannel
        getPayInfoJson = StringUtil.chinesetoUnicode(param.toJSONString())
    }
    reqBody = Robot2011Util.genBody(tempValues, getPayInfoJson)
    def getPayInfoResult = HttpSender.doPostWithRetry(
            5, httpClient as CloseableHttpClient, true,
            getPayInfoUrl,
            reqBody, null,
            reqHeaders,
            "UTF-8", null, "");
    getPayInfoResult = Robot2011Util.decodeBody(tempValues, getPayInfoResult)
    getPayInfoResultObj = JSON.parseObject(getPayInfoResult);
    if ("success".equals(TbUtil.getFromJson(getPayInfoResultObj, "message.code"))) {
        retry = false
    } else {
        String err = TbUtil.getFromJson(getPayInfoResultObj, "message.message");
        if (err.contains("短信验证码不正确")) {
            if (!submitedVerificationCode) {
                //进行强制提交
                errVerificationCode = true;
//                continue;
            }
            JSONObject responseJson = new JSONObject();
            responseJson.put("payCodeIsSuccess", "false");
            responseJson.put("isVerifSucc", "false");
            responseJson.put("payCodeErrorMsg", "");
            responseJson.put("payCodeUrl", "");
            responseJson.put("payCodeSum", 0);
            responseJson.put("orderPaymentId", tempValues.orderPaymentId);
            responseJson.put("sid", tempValues.sid);
            responseJson.put("orgCode", entity.orgCode);
            responseJson.put("paytype", tempValues.get("paytype"));
            responseJson.put("payCodeErrorMsg", "发送短信验证码失败,系统返回:" + err);
            entity.clear()
            entity.putAll(responseJson)
            throw new InsReturnException(11, "发送短信验证码失败,系统返回:" + err)
        }
        throw new InsReturnException(11, "支付失败,系统返回:" + err)
    }
}

//校验码
String checkCode = TbUtil.getFromJson(getPayInfoResultObj, "result.paymentShowVos.[0].checkCode")
//支付号
String payNo = TbUtil.getFromJson(getPayInfoResultObj, "result.paymentShowVos.[0].payNo")
//总保费
String totalMoney = TbUtil.getFromJson(getPayInfoResultObj, "result.paymentShowVos.[0].totalMoney")
//错误提示
String msg = TbUtil.getFromJson(getPayInfoResultObj, "message.message")
//回写对象
JSONObject responseJson = new JSONObject();
responseJson.put("payCodeIsSuccess", "false");
responseJson.put("payCodeErrorMsg", "");
responseJson.put("payCodeUrl", "");
if (["北京"].contains(configs?.areaComCode?.toString()) && getPayInfoResultObj?.result?.twoDimensionCodeLink) {
    responseJson.put("payCodeUrl", getPayInfoResultObj?.result?.twoDimensionCodeLink)
}

responseJson.put("payCodeSum", 0);
responseJson.put("orderPaymentId", tempValues.orderPaymentId)
responseJson.put("sid", tempValues.sid)
responseJson.put("orgCode", entity.orgCode)

String paytype = (String) tempValues.get("paytype");
boolean success = StringUtil.areNotEmpty(checkCode, payNo, totalMoney);
def paytypeList = ["qrcodeB64", "weixin", "alipay", "thirdPayType", "mpay",]
String twoDimensionCodeLink = TbUtil.getFromJson(getPayInfoResultObj, "result.twoDimensionCodeLink")
if (paytypeList.contains(paytype)) {
    if (StringUtil.isNoEmpty(twoDimensionCodeLink))
        responseJson.put("payCodeUrl", twoDimensionCodeLink);
    else {
        success = false;
        msg = "支付方式为" + paytype + "但未获取到二维码信息";
    }
}

if (success) {

    //todo 6929 百川】【精灵】东莞太保二维码支付方式变更，新增太保官微二维码支付 开发
    def robot_2011_pay_util = new robot_2011_pay_util()
    // 支付方式 payType
    //支付渠道 payChannel
    if (newPayType && newFlag && payChannel) {
        /**
         * 四川地区
         *
         * 支付方式 聚合支付  支付渠道 车生活服务号
         * configs?.paymentType : mpay
         * configs?.payChannel : 2
         *
         * 支付方式 聚合支付  支付渠道 车生活小程序
         * configs?.paymentType : mpay
         * configs?.payChannel : 3
         *
         * 支付方式 聚合支付  支付渠道 聚合支付
         * configs?.paymentType : mpay
         * configs?.payChannel : 4
         *
         * 东莞地区
         *
         * 支付方式 聚合支付  支付渠道 分公司微信公众号
         * configs?.paymentType : mpay
         * configs?.payChannel : 1
         *
         * 支付方式 聚合支付  支付渠道 车生活服务号
         * configs?.paymentType : mpay
         * configs?.payChannel : 2
         *
         * 支付方式 聚合支付  支付渠道 车生活小程序
         * configs?.paymentType : mpay
         * configs?.payChannel : 3
         *
         * 支付方式 聚合支付  支付渠道 聚合支付
         * configs?.paymentType : mpay
         * configs?.payChannel : 4
         *
         * 北京地区
         *
         * 划卡交通银行
         * configs?.paymentType : "2"
         * configs?.cooperant : "01"
         * configs?.payChannel : ""
         *
         * 聚合支付 车生活服务号
         * configs?.paymentType : mpay
         * configs?.cooperant : ""
         * configs?.payChannel : "2"
         *
         * 聚合支付 小程序
         * configs?.paymentType : mpay
         * configs?.cooperant : ""
         * configs?.payChannel : "3"
         */
        //支付方式 聚合支付  支付渠道 车生活服务号
        if (payChannel != "4") {
            responseJson.payCodeUrl = robot_2011_pay_util.getNewThcQrCode(httpClient, reqHeaders, payNo, configs, tempValues)
        } else {
            responseJson.payCodeUrl = getPayInfoResultObj?.result?.twoDimensionCodeLink
        }
//        robot_2011_pay_util.doImage(responseJson.payCodeUrl as String, payChannel as String) //本地生成图片
    } else {
        if (tempValues.paytype == "thirdPayType") {
            responseJson.payCodeUrl = robot_2011_pay_util.getNewThcQrCode(httpClient, reqHeaders, payNo, null, tempValues)
        }
    }
    responseJson.put("payCodeIsSuccess", "true");
    responseJson.put("payCodeErrorMsg", "");
    responseJson.put("payCodeSum", totalMoney);
    if (!"qrcodeB64".equals(paytype)) {
        responseJson.put("payOrderNo", payNo);
        responseJson.put("checkCode", checkCode);
    }
    responseJson.put("paytype", paytype);
    entity.clear()
    entity.putAll(responseJson)
} else {
    responseJson.put("payCodeErrorMsg", "获取支付信息失败");
    entity.clear()
    entity.putAll(responseJson)
    throw new InsReturnException(11, "获取支付信息失败,平台提示:" + msg)
}

