package com.cheche365.bc.config;

import com.cheche365.bc.sharding.HBaseDataSwitch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PostConstruct;

/**
 * HBase数据配置类
 * 用于初始化HBase数据开关配置
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Configuration
@Slf4j
public class HBaseDataConfig {

    /**
     * 是否从HBase查询大文本字段数据
     * 配置项：hbase.data.use-hbase-query
     * 默认值：false（从数据库表查询）
     */
    @Value("${hbase.data.use-hbase-query:false}")
    private boolean useHBaseQuery;

    /**
     * 是否启用双写
     * 配置项：hbase.data.enable-double-write
     * 默认值：true（启用双写）
     * true: 同时写入数据库表和HBase的大文本字段
     * false: 不写入数据库表的大文本字段，只写入HBase
     */
    @Value("${hbase.data.enable-double-write:true}")
    private boolean enableDoubleWrite;

    /**
     * 初始化HBase数据开关配置
     */
    @PostConstruct
    public void initHBaseDataSwitch() {
        HBaseDataSwitch.setUseHBaseData(useHBaseQuery);
        HBaseDataSwitch.setEnableDoubleWrite(enableDoubleWrite);

        log.info("HBase数据配置初始化完成:");
        log.info("  - 使用HBase查询: {}", useHBaseQuery);
        log.info("  - 启用双写: {}", enableDoubleWrite);
    }
}
