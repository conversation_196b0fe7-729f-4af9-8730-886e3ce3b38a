package taipingyang.robot

import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotDict_2011

/**
 * 本模版用于查询续保任务
 * 通过车牌号判断是否是续保车辆
 */
@BaseScript BaseScript_Http_Enq _

head Robot2011Util.getDefaultHead(tempValues, config)

body Robot2011Util.serializer(tempValues), {
    def currentUser = tempValues?.queryConfigs?.currentUser
    return RobotDict_2011.makeBaseReqBody(['pageNo': '1', 'pageSize': 50]) {
        [
                //车牌号
                'licensePlate'        : order.carInfo.plateNum,
                //VIN码
                'carVIN'              : '',
                //分公司代码
                'branchCode'          : currentUser?.branchCode,
                //操作人代码-经办人代码
                'inputorCode'         : currentUser?.inputorCode,
                //续保责任人-终端号
                'staffCode'           : currentUser?.partnerCode,
                //保司代码中写死的空字符串
                'carTaskStatus'       : '',
                // 1:查列表，2：查车牌
                'urlFlag'             : '1',
                //保司代码中写死的1
                'customerType'        : '1',
                // 责任人类型默认为"责任人"
                'renewalDutyType'     : '1',
                //来源系统，保司中固定值26
                'operationOrignSystem': '26',
        ]
    }
}
