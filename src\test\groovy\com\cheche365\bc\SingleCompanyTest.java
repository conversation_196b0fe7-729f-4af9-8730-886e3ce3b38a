package com.cheche365.bc;

import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.exception.InsReturnException;
import com.cheche365.bc.exception.TempSkipException;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.model.car.*;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.tools.CompactUtil;
import com.cheche365.bc.tools.FileUtil;
import com.cheche365.bc.utils.DataUtil;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.dama.Constant;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import groovy.lang.Binding;
import groovy.util.GroovyScriptEngine;
import groovy.util.ResourceException;
import groovy.util.ScriptException;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.Args;
import org.junit.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 单个公司测试方法基类
 *
 * <AUTHOR>
 * @Created by austinChen on 2015/12/16 19:10.
 */

public abstract class SingleCompanyTest {

    protected static Logger log = LoggerFactory.getLogger(SingleCompanyTest.class);

    protected String comId;
    protected String cityCode;
    protected String configFile = "conf.json";
    protected String dataType = "enquiry";
    /**
     * 机构代码
     */
    protected String org;
    protected String scriptFile = "common.groovy";
    protected String intType = "edi";

    private GroovyScriptEngine scriptEngine;

    {
        try {
            scriptEngine = new GroovyScriptEngine("src/main/groovy");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 初始化方法，初始化保险公司code和城市代码，以及配置文件
     */
    protected abstract void init();

    @Before
    public void initContext() {
        this.init();

        Constant constant = new Constant();
        constant.setDataTransFormPath("/msgParseRule.json");
        constant.init();
        ServiceLoader<BaseHandler> baseHandlers = ServiceLoader.load(BaseHandler.class);
        for (BaseHandler baseHandler : baseHandlers) {
            TaskUtil.requestMethodMap.put(baseHandler.getMethodType(), baseHandler);
        }
    }

    /**
     * @return java.util.List<AutoTask>
     * <AUTHOR>
     * @Description 直接批量获取指定的enquiry*.json的所有文件返回
     * @Date 10:42 2019/5/14
     * @Param []
     */
    protected List<AutoTask> getAutoTasksFromFile(String[] enquiryArrays) {
        return Sets
                .newHashSet(Arrays.asList(enquiryArrays))
                .stream()
                .map(enquiryId -> {
                    try {
                        return getMockData(enquiryId);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                })
                .toList();
    }

    protected AutoTask getMockDataFromFile() throws Exception {
        if (comId == null || comId.length() != 4) {
            throw new IllegalArgumentException("comId=" + comId + "不是正确的4位的保险公司名称，请在init方法中进行初始化");
        }
        if (cityCode == null || cityCode.length() != 6) {
            throw new IllegalArgumentException("cityCode=" + cityCode + "不是正确的6位的城市代码，请在init方法中进行初始化");
        }

        AutoTask task = new AutoTask();
        this.loadEnquiryAndConfFromFile(task, "enquiry.json");
        task.setCompanyId(comId);
        task.setTaskType("quote");
        task.setInterfaces(Collections.singletonList("edi"));

        if ((task.getConfigs() == null || task.getConfigs().isEmpty()) && configFile != null && !configFile.isEmpty()) {
            Map configMap = JSON.parseObject(this.readResource(configFile), Map.class);
            try {
                Map defaultMap = (Map) configMap.get("default");
                Map siteMap = (Map) configMap.get(this.org);
                if (defaultMap != null && siteMap != null)
                    defaultMap.putAll(siteMap);
                Map headers = (Map) configMap.get("headers");

                if (null == headers) {
                    headers = new HashMap<>();
                }

                task.setReqHeaders(headers);
                task.setConfigs(defaultMap);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return task;
    }

    /**
     * @return 资源文件夹名称
     */
    protected String getResourceDirName() {
        String fullName = this.getClass().getName();
        String[] names = fullName.split("\\.");
        this.intType = names[5];
        return names[4];
    }


    /**
     * @return 获取资源文件夹名称
     */
    protected String getResourceDir() {
        return "/templates/" + getResourceDirName() + "/";
    }

    /**
     * @param fileName 文件名
     * @return 返回文件内容
     */
    protected String readResource(String fileName) {
        try {
            String path = "src/main/groovy/" + this.getResourceDirName() + "/" + this.intType + "/" + fileName;
            log.debug("读取资源，相对路径{}", path);
            return FileUtil.read(path);
        } catch (Exception e) {
            log.error("路径{}I/O流读取异常：", fileName, e);
            throw e;
        }
    }

    /**
     * 仅仅支持HttpPost调用
     *
     * @param reqFile       请求模板名字
     * @param url           请求url
     * @param task          请求内容的任务对象
     * @param interfaceName 接口名称
     * @return 返回过模板后的Map对象
     * @throws Exception 异常对象
     */
    public AutoTask singleInterface(String reqFile, String url, AutoTask task, String interfaceName, String requestMethod) throws Exception {
        log.info("开始执行模板:{}", interfaceName);
        task.getTempValues().put("curTemplateName", reqFile);
        String repFile = reqFile + "_rep";
        if (requestMethod.equals("post") || requestMethod.equals("get")) {
            requestMethod = "http_" + requestMethod;
        }
        Map<String, Object> map = task.getConfigs();
        task.setConfigs(map);
        task.getTempValues().put("processType", this.intType);
        ObjectMapper mapper = new ObjectMapper();
        Date start = new Date();
        log.debug("=================步骤1、{}==调用,过模板{}，过请求模板前内容为={}", interfaceName, reqFile, mapper.writeValueAsString(task));
        Date firstTmpDate = new Date();
        String request;
        Map<String, Object> dataSource = Maps.newHashMapWithExpectedSize(16);
        dataSource.put("autoTask", task);
        if (task.getTaskEntity() instanceof Map) {
            Object enquiry = ((Map) task.getTaskEntity()).get("enquiry");
            if (null != enquiry) {
                dataSource.put("enquiry", enquiry);
            } else {
                dataSource.put("enquiry", task.getTaskEntity());
            }

            dataSource.put("script", "");
            dataSource.put("taskType", task.getTaskType());
            dataSource.put("config", task.getConfigs());
            dataSource.put("postParameters", task.getParams());
            dataSource.put("getParameters", Maps.newHashMap());
            dataSource.put("reqHeaders", task.getReqHeaders());
            dataSource.put("repHeaders", task.getRepHeaders());
            dataSource.put("tempValues", task.getTempValues());
            dataSource.put("httpClient", task.getHttpClient());
        }

        try {
            request = this.engine(reqFile, dataSource);
        } catch (TempSkipException skipE) {
            log.warn("任务不符合模板执行条件,该模板将被跳过:" + skipE.getMessage());
            return task;
        }
        long fistTmpMiniSecond = (new Date()).getTime() - firstTmpDate.getTime();
        log.info("=================步骤2、{}==调用,过模板{}，过请求模板后内容为={}", interfaceName, reqFile, request);
        if (request.trim().startsWith("<")) {
            request = CompactUtil.compactXml(request);
        }
        String response = null;
        Date startPost;
        log.debug("=================步骤3、{}==调用开始HttpPost,地址{}，请求报文内容为={}", interfaceName, url, request);
        startPost = new Date();
        String tempUrl = url;
        if (tempUrl.contains("${")) {
            Matcher urlMatcher = Pattern.compile("\\$(\\{(\\w+.)*\\})").matcher(tempUrl);
            while (urlMatcher.find()) {
                String args1 = urlMatcher.group(0);
                String key = urlMatcher.group(1);
                key = key.replaceAll("\\{", "");
                key = key.replaceAll("\\}", "");
                String value = null;
                if (task.getTempValues().containsKey(key)) {
                    value = (String) DataUtil.get(key, task.getTempValues());
                }
                if (Strings.isNullOrEmpty(value) && task.getConfigs().containsKey(key)) {
                    value = (String) DataUtil.get(key, task.getConfigs());
                }
                if (Strings.isNullOrEmpty(value)) {
                    value = (String) DataUtil.get(key, dataSource);
                }
                tempUrl = tempUrl.replace(args1, Strings.nullToEmpty(value));
            }
        }

        CloseableHttpClient closeableHttpClient = (CloseableHttpClient) task.getHttpClient();
        BaseHandler handler = TaskUtil.requestMethodMap.get(requestMethod);
        if (Objects.isNull(handler)) {
            handler = TaskUtil.requestMethodMap.get("defaultMethod");
        }
        response = handler.execute(task, request, CharsetUtil.UTF_8, tempUrl, closeableHttpClient);
        long postMiniSecond = (new Date()).getTime() - startPost.getTime();//通用查询通道
        if (StringUtils.isEmpty(response) && task.getRepHeaders() != null && !task.getRepHeaders().isEmpty()) {
            response = URLDecoder.decode(JSON.toJSONString(task.getRepHeaders()), StandardCharsets.UTF_8);
        }
        log.info("=================步骤5、{}==对返回结果进行解析后内容为response={}", interfaceName, response);
        Object backRoot = DataUtil.parse(response);
        log.info("=================步骤5、{}==对返回结果进行解析后内容为={}", interfaceName, backRoot);
        log.info("=================步骤6、{}==调用,过模板{}，过返回内容模板前内容为={}", interfaceName, repFile, mapper.writeValueAsString(task));
        Date secTmpDate = new Date();
        task.setBackRoot(backRoot);
        try {
            task.getTempValues().put("requestBody", request);
            dataSource.put("autoTask", task);
            dataSource.put("root", backRoot);
            dataSource.put("tempValues", task.getTempValues());
            this.engine(repFile, dataSource);
        } catch (InsReturnException insReE) {
            if (insReE.getCode() == InsReturnException.AllowRepeat) {
                return singleInterface(reqFile, url, task, "重试-".concat(interfaceName), requestMethod);
            }
            log.error("解析模板发生异常", insReE);
            throw insReE;
        }
        long secTmpMiniSecond = (new Date()).getTime() - secTmpDate.getTime();
        log.info("=================步骤7、{}==调用,过模板{}，过返回内容模板后内容为={}", interfaceName, repFile, mapper.writeValueAsString(task));
        long totalMiniSecond = (new Date()).getTime() - start.getTime();
        log.info("******************完成单个接口=={}==调用******************请求模板耗时:{}毫秒，返回模板耗时:{}毫秒， Http耗时:{}毫秒，总耗时:{}毫秒", interfaceName, fistTmpMiniSecond, secTmpMiniSecond, postMiniSecond, totalMiniSecond);
        //清楚params缓存
        if (task.getParams() != null) {
            task.getParams().clear();
        }
        if (task.getReqHeaders() != null) {
            task.getReqHeaders().clear();
        }
        return task;
    }

    protected String engine(String key, Map dataSource) throws ResourceException, ScriptException, InsReturnException, TempSkipException, IOException, ClassNotFoundException {
        String path = this.getResourceDirName() + "/" + this.intType + "/" + key + ".groovy";
        Object obj = scriptEngine.run(path, new Binding(dataSource));
        return String.valueOf(obj);
    }

    private void loadEnquiryAndConfFromFile(AutoTask task, String path) throws Exception {
        Args.notEmpty(path, "数据源文件名称");
        String enquiryJson = this.readResource(path);
        JSONObject enquiry = JSONObject.parseObject(enquiryJson);
        task.setApplyJson(enquiryJson);
        if (this.dataType.equals("policy")) {
            if (enquiry.containsKey("configInfo")) {
                task.setConfigs((Map<String, Object>) enquiry.remove("configInfo"));
                task.getTempValues().putAll(enquiry);
                task.setTaskEntity(buildEnq());
            }
        }else if (this.dataType.equals("enquiry")) {
            DataUtil.supplyParamConversion(enquiry);
            task.setTaskEntity(TaskUtil.transform(enquiry, "quote"));
            JSONObject j = JSON.parseObject(enquiryJson);
            if (j.containsKey("configInfo")) {
                task.setConfigs((Map<String, Object>) j.getJSONObject("configInfo").get("configMap"));
            }
        } else {
            task.setTaskEntity(DataUtil.parseInEdiInterface(enquiryJson));
            if (DataUtil.containsKey((Map) task.getTaskEntity(), "configInfo.configMap")) {
                task.setConfigs((Map<String, Object>) DataUtil.get("configInfo.configMap", task.getTaskEntity()));
            }
        }
    }

    /**
     * 构建enquiry结构
     */
    protected Enquiry buildEnq() {
        Enquiry enquiry = new Enquiry();
        Order order = new Order();
        CarInfo carInfo = new CarInfo();
        BaseSuiteInfo baseSuiteInfo = new BaseSuiteInfo();
        InsurePerson insurePerson = new InsurePerson();
        CarOwnerInfo carOwnerInfo = new CarOwnerInfo();
        List<BeneficiaryPerson> beneficiaryPersons = new ArrayList<>();
        List<InsurePerson> insuredPersons = new ArrayList<>();
        order.setCarInfo(carInfo);
        order.setInsuredPersons(insuredPersons);
        order.setInsurePerson(insurePerson);
        order.setBeneficiaryPersons(beneficiaryPersons);
        order.setCarOwnerInfo(carOwnerInfo);
        order.setSuiteInfo(baseSuiteInfo);
        enquiry.setOrder(order);
        return enquiry;
    }

    protected AutoTask getMockData(String enquiry) throws Exception {
        if (comId == null || comId.length() != 4) {
            throw new IllegalArgumentException("comId=" + comId + "不是正确的4位的保险公司名称，请在init方法中进行初始化");
        }
        if (cityCode == null || cityCode.length() != 6) {
            throw new IllegalArgumentException("cityCode=" + cityCode + "不是正确的6位的城市代码，请在init方法中进行初始化");
        }

        AutoTask task = new AutoTask();
        this.loadEnquiryAndConfFromFile(task, enquiry);
        task.setCompanyId(comId);
        task.setTaskType("quote");
        task.setInterfaces(Collections.singletonList("edi"));
        if ((task.getConfigs() == null || task.getConfigs().isEmpty()) && configFile != null && !configFile.isEmpty()) {
            Map configMap = JSON.parseObject(this.readResource(configFile), Map.class);
            try {
                Map defaultMap = (Map) configMap.get("default");
                Map siteMap = (Map) configMap.get(this.org);
                if (defaultMap != null && siteMap != null)
                    defaultMap.putAll(siteMap);
                Map headers = (Map) configMap.get("headers");
                if (null == headers) {
                    headers = new HashMap<>();
                }
                task.setReqHeaders(headers);
                task.setConfigs(defaultMap);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (scriptFile != null && !scriptFile.isEmpty()) {
            String commonScript;
            if (this.intType.equals("robot"))
                commonScript = this.readResource(scriptFile);
            else
                commonScript = this.readResource("edi-" + comId + "-" + scriptFile);
            task.setCommonScript(commonScript);
        }
        return task;
    }
}
