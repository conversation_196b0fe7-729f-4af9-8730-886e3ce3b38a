package renbao.robot

import com.alibaba.fastjson.JSON
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript

import java.time.LocalDate
import java.time.ZoneId

import static common.common_all.getPersonSupply
import static renbao.common.renbao_dict.identifyType
import static renbao.common.renbao_dict.useProp
import static renbao.robot.common_market.header
import static renbao.robot.common_market.hostPrefix


@BaseScript BaseScript_Http_Enq _



head(header(autoTask))
def commonMarket = new common_market()
def boundary = '----WebKitFormBoundary' + commonMarket.generateRandomString(16)
autoTask?.reqHeaders << ['Content-Type' : 'multipart/form-data; boundary=' + boundary]
tempValues['boundary'] = boundary

def insureUrl = hostPrefix(autoTask) + "/khyx/newFront/qth/price/trans.do"
tempValues['insureUrl'] = insureUrl

def param = [:]
def specialClauseMap = entity.specialClauseMap
if (specialClauseMap.size() > 0) {
    specialClauseMap.eachWithIndex {key, value, index ->
        def itemKindNo = 'riskEngageList[' + index + '].itemKindNo'
        def clauseCode = 'riskEngageList[' + index + '].clauseCode'
        def clauseName = 'riskEngageList[' + index + '].clauseName'
        def clauses = 'riskEngageList[' + index + '].clauses'
        def clauseInfo = [
                (itemKindNo) : (index + 1) + '',
                (clauseCode) : key,
                (clauseName) : value.des,
                (clauses) : value.clauses
        ]
        param += clauseInfo
    }
}

def carQuoteInsuredRealList = [
        'carQuoteInsuredRealList[0].serialNo' : '0',
        'carQuoteInsuredRealList[0].holdName' :  '',
        'carQuoteInsuredRealList[0].holdIdentifyNumber' :  '',
        'carQuoteInsuredRealList[0].holdIdentifyType' : identifyType(entity.order.insurePerson.idCardType),
        'carQuoteInsuredRealList[0].holdType' : '01',
        'carQuoteInsuredRealList[1].serialNo' : '1',
        'carQuoteInsuredRealList[1].holdName' :  '',
        'carQuoteInsuredRealList[1].holdIdentifyNumber' :  '',
        'carQuoteInsuredRealList[1].holdIdentifyType' : identifyType(entity.order.insuredPersons[0].idCardType),
        'carQuoteInsuredRealList[1].holdType' : '02',
        'carQuoteInsuredRealList[2].serialNo' : '2',
        'carQuoteInsuredRealList[2].holdName' :  '',
        'carQuoteInsuredRealList[2].holdIdentifyNumber' :  '',
        'carQuoteInsuredRealList[2].holdIdentifyType' : identifyType(entity.order.carOwnerInfo.idCardType),
        'carQuoteInsuredRealList[2].holdType' : '03'
]
param += carQuoteInsuredRealList
def quoteData = tempValues.quoteData
def quoteInfo = [
        'biPremium' :  quoteData['biPremium'] + '',
        'ciPremium' :  quoteData['ciPremium'] + '',
        'DDAPremium' : quoteData['DDAPremium'] + '',
        'quotationId' : quoteData['quotationId'] ?: '',
        'prpCcarShipTax.taxType' : '1',
        'quotationNoBI' : quoteData['quotationNoBi'] ?: '',
        'quotationNoCI' : quoteData['quotationNoCi'] ?: '',
        'quotationNoDDA' : '',
        'quotationNoEAD' : '',
        'quotationNoYEL' : '',
        'quotationNoZDB' : '',
        'quotationNoLAI' : '',
        'quotationNoJAH' : '',
        'quotationNoEBS' : entity?.misc?.nonMotor?.accidentProposeCode ?:'',
        'quotationNoLCO' : '',
        'carcheckstatus' : autoTask.configs?.carCheckStatus ?: '1',
        'carchecker' : autoTask.configs?.carChecker ?: '',
        'carCheckerName' : autoTask.configs?.carCheckerTranslate ?: '',
        'carchecktime' : LocalDate.now().toString(),
        'agriflag' : '0',
        'aliasName' : '',
        'proposalActivityId' : '',
        'proposalActivityName' : '',
        'proposalActivityDesc' : '',
        'proposalActivityFlag' : '0',
        'clubGiftDisplayFlag' : '0',
        'costRateBI' : '',
        'costRateCI' : '',
        'marketFeeRateBI' : '',
        'marketFeeRateCI' : '',
        'HNfeProjectCode' : '0',
        'agentName' : '',
        'agentPermitNo' : '',
        'agentMobile' : '',
        'selectedOperateConfigId' :  tempValues?.selectedOperateConfigId,
        'vehicleStyle' : '',
        'szHandlerName' : '',
        'szHandlerIdentifyType' : '',
        'szHandlerIdentifyNo' : '',
        'szHandlerMobile' : '',
        'ocrIds' : '',
        'giftPackageDconfigValue' : '',
        'carcheckerFlag' : '1',
        'opconfComCode' : autoTask?.configs?.opconfComCode,
        'usedCarInvoiceValue' : '',
        'saleCompany' : '',
        'saleAreaCode' : '',
        'sale4SFlag' : '1',
        'prpRemark' : '',
        'opconfComCodeHtml' : autoTask?.configs?.opconfComCode,
        'szSalesmanName' : '',
        'szSalesmanIdNo' : '',
        'szSalesmanMobile' : '',
        'clauseType' : useProp(entity?.order?.carInfo?.useProps),
        'autoRiskEngageList' : '[]',
]
def itemKindTempList = JSON.toJSONString(quoteData['itemKindTempList'])
quoteInfo << ['kinds' : itemKindTempList]
param += quoteInfo
autoTask.tempValues['trans'] = 'trans'
def personInfo = commonMarket.personInfo(autoTask)
param += personInfo
def prpCmain = commonMarket.prpCmain(autoTask)
param += prpCmain
def prpCitemCar = commonMarket.prpCitemCar(autoTask)
param += prpCitemCar
param += [
        'ecifUserTypeCode' : '',
        'giftPackageComCode' : '',
        'giftPackageId' : '',
        'replaceableNum' : '0',
        'clubGiftPackageDesStr' : '',
        'clubGiftNum' : '',
        'registerOrNot' : '',
        'smsForClubTemplate' : '',
        'memPhone' : '',
        'taskId' : '',
        'transfer' : entity?.order?.carInfo?.isTransfer ? '1' : '0',
        'transferDate' : entity?.order?.carInfo?.transferDate?.toInstant()?.atZone(ZoneId.systemDefault())?.toLocalDate()?.toString() ?: '',
        'ciPolicyMediaType' : autoTask?.configs?.ciPolicyMediaType ?: '1',
        'biPolicyMediaType' : autoTask?.configs?.biPolicyMediaType ?: '1',
        'isNetProp' : [6, 8, 9, 10].contains(entity?.order?.insurePerson?.idCardType) ? '0' : '1',
        'NetSalesDDA' : '1',
        'isNetPropDDA' : '0',
        'elecPolicyEmail' : (getPersonSupply(autoTask, 'applicantEmail') ?: autoTask?.configs?.insureEmails) ?: '',
        'elecPolicyPhoneChangeFlag' : 'false',
        'groupCodeValidStatus' : '0',
        'monopolyCode' : autoTask?.configs?.monopolyCode ?: '',
        'monopolyName' : '',
        'custAuthorization' : '0',
        'isSendAttachment' : '0',
        'argueSolution' : '1',
        'feProjectCode' : '',
        'modelCodeAlias' : ''
]
//二手车发票金额
if (entity?.order?.carInfo?.isTransfer) {
    param << ['usedCarInvoiceValue' : autoTask.tempValues['actualVal']]
}
autoTask.params = param
