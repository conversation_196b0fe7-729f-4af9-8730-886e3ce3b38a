package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.exception.TempSkipException
import common.scripts.BaseScript_Http_Enq
import groovy.transform.BaseScript
import org.jsoup.Jsoup
import taipingyang.robot.module.Robot2011Util
import taipingyang.robot.module.RobotConstant_2011

@BaseScript BaseScript_Http_Enq _
/**
 * 太平洋精灵 - 代理点、终端及经办人选择请求模板
 */
def util = new robot_2011_util()
def header = [
        'Host'            : 'issue.cpic.com.cn',
        'macAddress'      : null,
        'Content-Type'    : 'application/json;charset=UTF-8',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin'          : 'https://issue.cpic.com.cn',
        'Referer'         : 'https://issue.cpic.com.cn/ecar/view/portal/page/common/partnerselect.html'
]

if (util.checkCookie(autoTask)) {
    initConfig(header)
    throw new TempSkipException(1, '已登陆，无需执行代理点、终端及经办人选择请求模板')
}

header = [
        'Referer': 'https://issue.cpic.com.cn/ecar/view/portal/page/common/login.html'
]
def response = doGet {
    url = RobotConstant_2011.URL.PARTNERSELECT
    headers = header
}

def doc = Jsoup.parse(response as String)

assertTrue '登录已失效，请重新登录', doc.select('body > div.login-row > div:nth-child(2) > div > div > div > h3').text() == '代理点、终端及经办人选择'


def queryConfigsResult = initConfig(header)

if (tempValues.needSM4Flag) {
    header['Content-Type'] = 'text/plain'
}

def branchCode = queryConfigsResult.getString('branchCode')

def param = [
        'meta'  : [:],
        'redata': [:]
]
def queryFastLoginInfoResponseStr = doPost {
    url = RobotConstant_2011.URL.QUERY_FAST_LOGIN_INFO
    headers = header
    body = Robot2011Util.genBody(tempValues, param)
}
def queryFastLoginInfoResponse = Robot2011Util.decodeBody2JsonObject(tempValues, queryFastLoginInfoResponseStr)
def queryFastLoginInfoResult = queryFastLoginInfoResponse.getJSONObject('result')
def fastLogin = queryFastLoginInfoResult.getBoolean('fastLogin')

def partnerCode
def agentCode
if (fastLogin) {
    partnerCode = queryFastLoginInfoResult.getString('partnerCode')
    agentCode = queryFastLoginInfoResult.getString('agentCode')
} else {
    partnerCode = config['partner_code']
    agentCode = config['agent_code']
}

def userAuthVos = queryFastLoginInfoResult.getJSONArray('userAuthVos')
def userAuthVo = userAuthVos.find({ userAuthVo ->
    userAuthVo['branchCode'] == branchCode && userAuthVo['partnerCode'] == partnerCode
}) as JSONObject
if (!userAuthVo) {
    fail('partnerCode 配置错误')
}

tempValues.put('cooperatorVo', userAuthVo.getJSONObject('cooperatorVo'))
def agentAuthVos = userAuthVo.getJSONArray('agentAuthVos')
def agentAuthVo = agentAuthVos.find({ agentAuthVo ->
    agentAuthVo['agentCode'] = agentCode
})
if (!agentAuthVo) {
    throw new InsReturnException(InsReturnException.Others, 'agentCode 配置错误')
}

def agencyVo = userAuthVo.getJSONObject('agencyVo')
def agencyCode = agencyVo.getString('agencyCode')
//def accessToken = userAuthVo.getString('accessToken')
def pid = userAuthVo.getString("pid")
def j_username = queryFastLoginInfoResult.getString('userCode')

param = [
        'meta'  : [:],
        'redata': ['agencyCode': agencyCode]
]

doPost {
    url = RobotConstant_2011.URL.QUERY_AGENCY_BY_CODE
    headers = header
    body = Robot2011Util.genBody(tempValues, param)
}


param = [
        'pid'         : pid,
        'partner_code': partnerCode,
        'j_username'  : j_username,
        'agent_code'  : agentCode,
        'mac_address' : config['mac_address'] ?: ''
]
header['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'
tempValues['ReqURL'] = 'https://issue.cpic.com.cn/ecar/j_spring_security_check'

head header
form param

private JSONObject initConfig(header) {
    def param = [
            'meta'  : [:],
            'redata': [
                    'pageCode': 'partner_select'
            ]
    ]
    def queryConfigsResponseStr = doPost {
        url = RobotConstant_2011.URL.QUERY_CONFIGS
        headers = header
        body = JSON.toJSONString(param)
    }
    def queryConfigsResponse = JSON.parseObject(queryConfigsResponseStr)
    def queryConfigsResult = queryConfigsResponse.getJSONObject('result')

    tempValues.needSM4Flag = queryConfigsResponse.containsKey('b')
    if (tempValues.needSM4Flag) {
        tempValues.put('sm4Key', Robot2011Util.rsaDecode(queryConfigsResponse.getString('b')))
    }
    return queryConfigsResult
}