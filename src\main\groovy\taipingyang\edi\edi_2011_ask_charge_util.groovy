package taipingyang.edi

import cn.hutool.core.util.NumberUtil
import cn.hutool.core.util.ObjectUtil
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.exception.InsReturnException
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.utils.CommonUtils
import com.cheche365.bc.utils.RuleUtil
import common.common_all
import taipingyang.edi.module.constants.EdiConstants_2011

import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.regex.Matcher
import java.util.regex.Pattern

//车主
def makeCarOwner(enquiry, person) {
    def carOwner = new JSONObject()
    carOwner.put("ownerName", person?.name)
    commonPerson(enquiry, person, carOwner)
    def ownerIDCardValidDate = getCertificateValidity(enquiry, 'ownerIDCardValidDate')
    if (ownerIDCardValidDate) {
        carOwner.put("certificateValidity", ownerIDCardValidDate)
    }
    return carOwner
}

def findFormSupplyParam(supplyParam, key) {
    def find = supplyParam.find({ item -> item['itemcode'] == key })
    return find ? find['itemvalue'] : ''
}

//被保人
def makeInsureder(enquiry, person) {
    def insureder = new JSONObject()
    //被保人是否开票 (1是 0否)
    insureder.put("issueInvoice", "0")
    insureder.put("insuredName", person?.name)
    commonPerson(enquiry, person, insureder)
    // 上海团体客户
    if (enquiry.insArea.province == '310000') {
        processCustomer(enquiry, insureder)
    }
    def insuredIDCardValidDate = getCertificateValidity(enquiry, 'insuredIDCardValidDate')
    if (insuredIDCardValidDate) {
        insureder.put("certificateValidity", insuredIDCardValidDate)
    }

    return insureder
}

//投保人
def makeHolder(enquiry, person, config) {
    def holder = new JSONObject()
    //(1增值税专用发票0增值税普通发票
    //核保必传，一般个人客户是普票，团体客户是专票)
    holder.put("ifSpecialTicket", "")
    holder.put("holderName", person?.name)
    commonPerson(enquiry, person, holder)

    // 上海团体客户
    if (enquiry.insArea.province == '310000') {
        processCustomer(enquiry, holder)
    }
    def applicantIDCardValidDate = getCertificateValidity(enquiry, 'applicantIDCardValidDate')
    if (applicantIDCardValidDate) {
        holder.put("certificateValidity", applicantIDCardValidDate)
    }
    def supplyParam = enquiry['supplyParam'] as List
    def email = config.policyEmail?.toString()
            ?: findFormSupplyParam(supplyParam, 'applicantEmail')?.toString()
            ?: findFormSupplyParam(supplyParam, 'insuredEmail')?.toString()
    if (email) {
//        if (ReUtil.isMatch(RegexPool.EMAIL_WITH_CHINESE, email)) {
        holder.put('email', email)
//        } else {
//            throw new RuntimeException("policyEmail有误")
//        }
    }

    return holder
}

def makeBeneficiaryer(enquiry, person) {
    def beneficiaryer = new JSONObject()
    beneficiaryer.put("benefitName", person?.name)
    commonPerson(enquiry, person, beneficiaryer)
}

def commonPerson(enquiry, person, JSONObject personJSONObject) {
    def edi_2011_common = new edi_2011_common()
    def personInfo = edi_2011_common.getPersonInfo(enquiry?.supplyParam)
    def address = edi_2011_common.addressFormat(personInfo?.insuredAddress) ?: enquiry?.deliverInfo?.address ?: ""
    personJSONObject.put("certType", edi_2011_common.getCardType(enquiry, person?.idCardType))
    personJSONObject.put("certNo", person?.idCard)
    personJSONObject.put("phoneNumber", personInfo?.insuredMobile)
    personJSONObject.put("address", address)
    personJSONObject.put("email", personInfo?.insuredEmail ?: "<EMAIL>")
    personJSONObject.put("customerType", edi_2011_common.getCustomerType(person?.idCardType?.toString()))
    if ("17".equals(personJSONObject?.certType)) {
        personJSONObject.put("passPort", person?.idCard)
    }
    //营业执照 默认使用有效期 使用保司的编码因为涉及到证件类型转换
    if ("11".equals(personJSONObject?.certType)) {
        personJSONObject.put("certificateValidity", "9999-12-31 00:00:00")
    }
    //贵州分公司必传(贵州分公司，格式为2037-10-10 12:23:40，如果是长期的，有效止期默认为9999-12-31 00:00:00)
    //内蒙地区
    //2022-06-06 增加浙江地区
    if (["520000", "150000", "330000"].contains(enquiry?.insArea?.province?.toString())) {
        personJSONObject.put("certificateValidity", "9999-12-31 00:00:00")
    }
    if (enquiry?.carOwnerInfo?.idCard?.toString()?.equals(person?.idCard?.toString())) {
        personJSONObject.put("natureType", "1")
    } else {
        personJSONObject.put("natureType", "2")
    }
    personJSONObject.put("country", "中国")
    personJSONObject.put("profession", "")
    personJSONObject.put("workAddress", "")
    //内蒙地区需要性别字段
    if ("150000".equals(enquiry?.insArea?.province?.toString())) {
        if ("0".equals(person?.sex?.toString())) {
            personJSONObject.put("personSex", "1")
        } else {
            personJSONObject.put("personSex", "2")
        }
        personJSONObject.put("profession", "保密")
        personJSONObject.put("workAddress", "保密")
    }
    if (person?.idCardType?.toString() != '0' && person?.birthday && ObjectUtil.isNotEmpty(person?.sex)) {
        personJSONObject.put('birthDate', person?.birthday)
        if ("0" == person?.sex?.toString()) {
            personJSONObject.put("personSex", "1")
        } else {
            personJSONObject.put("personSex", "2")
        }
    }
    personJSONObject
}

def makeVehicle(enquiry, tempValues, config) {
    def edi_2011_common = new edi_2011_common()
    def vehicle = new JSONObject()
    def common_all = new common_all()
    def aFormat = "yyyyMMddHHmmss"
    def aPattern = DateTimeFormatter.ofPattern(aFormat)

    vehicle.put("plateNo", enquiry?.carInfo?.plateNum)
    if (enquiry?.carInfo?.isNew && enquiry?.carInfo?.plateNum?.toString()?.contains("新车")) {
        vehicle.put("plateNo", "LS" +
                enquiry?.carInfo?.vin?.toString()?.substring(enquiry?.carInfo?.vin?.toString()?.length() - 6, enquiry?.carInfo?.vin?.toString()?.length()))
        def definedCarPriceValue = new BigDecimal(enquiry?.carInfo?.price?.toString()).setScale(0, RoundingMode.HALF_UP)
        if (!tempValues?.containsKey('definedCarPriceFlag')) {
            tempValues.definedCarPriceFlag = 1
        }
        tempValues.definedCarPrice = definedCarPriceValue
        vehicle.put("currentValue", definedCarPriceValue)
    }
    //客户协商价格
    def definedCarPrice = enquiry?.carInfo?.definedCarPrice?.toString()
    if (definedCarPrice && NumberUtil.isNumber(definedCarPrice)) {
        def definedCarPriceValue = new BigDecimal(definedCarPrice).setScale(0, RoundingMode.HALF_UP)
        if (!tempValues?.containsKey('definedCarPriceFlag')) {
            tempValues.definedCarPriceFlag = 1
        }
        tempValues.definedCarPrice = definedCarPriceValue
        vehicle.put("currentValue", definedCarPriceValue)
    }

    vehicle.put("carVin", enquiry?.carInfo?.vin)
    vehicle.put("engineNo", enquiry?.carInfo?.engineNum)
    vehicle.put("hkLicense", "01")
    def firstRegDate = enquiry?.carInfo?.firstRegDate?.toString()
    def registerDate = CommonUtils.toLocalDateTime(firstRegDate).format(aPattern)
    vehicle.put("registerDate", registerDate)
    vehicle.put("certificationDate", registerDate)
    //河北问题回复，20万以上车必须录入转移登记日期，如果不是二手车就录入初登日期
    if ("130000".equals(enquiry?.insArea?.province?.toString())) {
        vehicle.put("transferDate", registerDate)
    }
    //过户车处理逻辑
    def specialCarFlag = "0"
    if (enquiry?.carInfo?.isTransfer) {
        def transferDateString = enquiry?.carInfo?.transferDate?.toString()
        def transferDate = CommonUtils.toLocalDateTime(transferDateString).format(aPattern)
        vehicle.put("transferDate", transferDate)
        specialCarFlag = "1"
    }
    vehicle.put("specialCarFlag", specialCarFlag)
    vehicle.put("vehicleModelCode", tempValues?.vehicleModelCode)
    vehicle.put("purchasePrice", tempValues?.purchasePrice)
    //交强厂牌型号 （取车型名称，可修改为行驶证的品牌型号）
    vehicle.put("trafficMakerModel", tempValues?.moldName)
    vehicle.put("seatCount", enquiry?.carInfo?.seatCnt ?: tempValues?.seat)
    vehicle.put("vehiclePurpose", edi_2011_common.getvehiclePurpose(enquiry))
    vehicle.put("globalType", "A")
    //车辆使用性质
    vehicle.put("vehicleUsage", edi_2011_common.getvehicleUsage(enquiry?.carInfo?.useProps, enquiry?.carInfo?.carUserType))
    //号牌种类
    vehicle.put("licenseType", tempValues?.vehType ?: edi_2011_common.getPlateType(enquiry?.carInfo?.plateType))
    if (tempValues?.NEFlag) {
        //号牌种类
        vehicle.put("licenseType", tempValues?.vehType ?: "52")
    }
    //号牌底色
    vehicle.put("plateColor", edi_2011_common.getPlateColor(enquiry?.carInfo?.plateColor))
    //车辆种类
    vehicle.put("vehicleKind", edi_2011_common.getvehicleKind(enquiry?.carInfo?.syvehicletypecode))
    vehicle.put("loanVehicleFlag", "0")
    //年款 北京必传
    vehicle.put("yearPattern", tempValues?.yearPattern)
    vehicle.put("pmVehicleType", tempValues?.pmVehicleStyle ?: edi_2011_common.parseJgVehicleType(enquiry.carInfo.jgVehicleType, enquiry.carInfo.carModelName))
    if (tempValues?.vehModel) {
        vehicle.put("pmVehicleModel", tempValues?.vehModel)
    }
    vehicle.put("vehicleUsageDetail", edi_2011_common.getVehicleUsageDetail(enquiry?.carInfo?.useProps))
    //整备质量
    if (enquiry?.carInfo?.fullLoad) {
        vehicle.put("emptyWeight", new BigDecimal(enquiry?.carInfo?.fullLoad?.toString()) ?: tempValues?.emptyWeight)
    }
    //排气量
    if (enquiry?.carInfo?.displacement) {
        vehicle.put("engineCapacity", new BigDecimal(enquiry?.carInfo?.displacement?.toString()) ?: tempValues?.exhaustCapacity)
    }

    //山东、黑龙江必传  平台推荐标识(平台车型查询返回)
    if (tempValues?.modelFlag) {
        vehicle.put("modelFlag", tempValues?.modelFlag)
    }
    //第一步判断是不是新车 广东新车
    setSaleInfo(enquiry, config, vehicle)
    //宁波 旧车
    if ("330200".equals(enquiry?.insArea?.city?.toString()) && tempValues?.NingBoSumInsurd?.toString()
            && !enquiry?.carInfo?.isNew) {
        vehicle.put("currentValue", new BigDecimal(tempValues?.NingBoSumInsurd?.toString())
                .multiply(new BigDecimal("0.75"))
                .setScale(0, BigDecimal.ROUND_HALF_UP))
    }
    if (["310000", "310100"].contains(enquiry?.insArea?.city?.toString())) {
        def carOriginProofDate = ""
        if (common_all.getSupplyParam(enquiry, "carOriginProofDate")) {
            carOriginProofDate = common_all.getSupplyParam(enquiry, "carOriginProofDate") + " 00:00:00"
        }
        String purchaseinvoicesDate = carOriginProofDate ?: firstRegDate
        vehicle.put("purchaseinvoicesDate", CommonUtils.toLocalDateTime(purchaseinvoicesDate).format(aPattern))
    }

    if (enquiry.insArea.province == '310000') {
        def seatCount = tempValues.lmtLoadPerson ?: vehicle['seatCount'] ?: '',
            emptyWeight = tempValues.poWeight ?: vehicle['emptyWeight'] ?: '',
            engineCapacity = tempValues.exhaustCapacity ?: vehicle['exhaustCapacity'] ?: '',
            carryingCapacity = tempValues.lmtLoad ?: vehicle['carryingCapacity'] ?: ''
        registerDate = tempValues.vehRegDate ?: vehicle['registerDate']
        vehicle.putAll([
                'seatCount'       : seatCount, // 座位数/核定载客数  int  Y
                'emptyWeight'     : emptyWeight, // 整备质量(kg)  BigDecimal  N  （默认取精友,可修改）
                'engineCapacity'  : engineCapacity, // 引擎排量(L)  BigDecimal  N  （默认取精友,可修）
                'carryingCapacity': carryingCapacity, // 载质量（吨）  BigDecimal  N  （默认取精友,可修）
                'registerDate'    : registerDate // 车辆登记注册日期  Date  Y  20180612000000
        ])
    }
    vehicle
}


def makeBusinessPolicy(enquiry, tempValues, config) {
    def edi_2011_common = new edi_2011_common()
    def commonAll = new common_all()
    def businessPolicy = new JSONObject()
    def startDateString = enquiry?.baseSuiteInfo?.bizSuiteInfo?.start?.toString()
    def startDate = LocalDateTime.parse(startDateString, DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss'))
    def endDate = startDate.plusYears(1)
    if (startDate.toLocalDate().isLeapYear() && startDate.monthValue == 2 && startDate.dayOfMonth == 29) {
        endDate = startDate.plusYears(1).plusDays(1)
        if (startDate.hour != 0) {
            endDate = startDate.plusYears(1).plusDays(1).toLocalDate().atStartOfDay()
        }
    }

    def dtf = DateTimeFormatter.ofPattern('yyyyMMddHHmmss')
    startDate = startDate.format(dtf)
    endDate = endDate.format(dtf)
    businessPolicy.put("startDate", tempValues.startDateStringTPYBiz ?: startDate)
    businessPolicy.put("endDate", tempValues.endDateStringTPYBiz ?: endDate)
    businessPolicy.put("litigationArbitration", "2")
    businessPolicy.put("arbitrationOrgName", config?.arbitrationOrgName ?: "北京仲裁委员会")
    businessPolicy.put("isCreateEpolicy", config?.isCreateEpolicy ?: "1")
    businessPolicy.put("isSendEpolicySms", config?.isSendEpolicySms ?: "1")
    def seat = tempValues?.seat as Integer ?: 4
    def seatP = seat - 1
    def coverages = new JSONArray()
    def biz = enquiry?.baseSuiteInfo?.bizSuiteInfo?.suites as List
    biz.each {
        def coverage = new JSONObject()
        def codeList = edi_2011_common.bwSuitesCode2TaiBaoCodeList(it.code)
        if (!codeList) {
            throw new InsReturnException("大平洋EDI 存在不支持的险种")
        }
        coverage.put("coverageCode", codeList.get(0))
        if (tempValues?.NEFlag) {
            coverage.put("coverageCode", "NEWENERGY" + codeList.get(0))
        }
        coverage.put("sumInsurd", new BigDecimal(it.amount?.toString() ?: "0"))
        coverage.put("coverageName", codeList.get(1))
        def extendInfos = new JSONObject()
        if ("VehicleDamage".equals(it.code) && tempValues?.NingBoSumInsurd) {
            coverage.put("sumInsurd", new BigDecimal(tempValues?.NingBoSumInsurd?.toString())
                    .multiply(new BigDecimal("0.75"))
                    .setScale(0, BigDecimal.ROUND_HALF_UP))
        }
        if ("HolidayDouble".equals(it.code)) {
            coverage.put("sumInsurd", new BigDecimal(biz.find { "ThirdParty".equals(it.code) }?.amount?.toString()))
        }
        if ("Passenger".contains(it.code)) {
            extendInfos.put("seat", seatP.toString())
        }
        if ("RoadsideService".contains(it.code)) {
            extendInfos.put("serviceTimes", new BigDecimal(it.amount?.toString()).setScale(0, BigDecimal.ROUND_DOWN).toString())
        }
        if (["ANCVehicleDamage", "ANCThirdParty", "ANCDriver", "ANCPassenger"].contains(it.code)) {
            extendInfos.put("deductibleRate", it.amount?.toString() ?: "0.1")
        }
        if ("SendForInspection".equals(it.code)) {
            def sendForInspectionLevel = it['sendForInspectionLevel'] ?:
                    commonAll.getSupplyParam(enquiry, "sendForInspectionLevel") ?:
                            enquiry?.SQ?.sendForInspectionLevel ?:
                                    tempValues?.serviceGrade21FN ?: "A"
            extendInfos.put("serviceGrade", sendForInspectionLevel)
        }
        if ("VehicleInspection".equals(it.code)) {
            def vehicleInspectionLevel = it['vehicleInspectionLevel'] ?:
                    commonAll.getSupplyParam(enquiry, "vehicleInspectionLevel") ?:
                            enquiry?.SQ?.vehicleInspectionLevel ?:
                                    tempValues?.serviceGrade21FL ?: "D"
            List vehicleSafetyDetectionSpecialList = []
            Map vehicleSafetyDetectionSpecial = [:]
            vehicleSafetyDetectionSpecial.put("serviceGrade", vehicleInspectionLevel)
            vehicleSafetyDetectionSpecial.put("serviceTimes", new BigDecimal(it.amount?.toString()).setScale(0, BigDecimal.ROUND_DOWN).toString())
            vehicleSafetyDetectionSpecialList.add(vehicleSafetyDetectionSpecial)
            coverage.put("vehicleSafetyDetectionSpecialList", vehicleSafetyDetectionSpecialList)
            extendInfos.put("customerGrade", "10")
        }
        if ("DesignatedDriving".equals(it.code)) {
            def designatedDrivingMile = new BigDecimal(it['designatedDrivingMile'] as String ?:
                    commonAll.getSupplyParam(enquiry, "designatedDrivingMile")?.toString() ?:
                            enquiry?.SQ?.designatedDrivingMile ?:
                                    tempValues?.maxKiloNum?.toString() ?: "30").intValue()
            List substituteDrivingSpecialList = []
            Map substituteDrivingSpecial = [:]
            substituteDrivingSpecial.put("maxKiloNum", designatedDrivingMile)
            substituteDrivingSpecial.put("serviceTimes", new BigDecimal(it.amount?.toString()).setScale(0, BigDecimal.ROUND_DOWN).toString())
            substituteDrivingSpecialList.add(substituteDrivingSpecial)
            coverage.put("substituteDrivingSpecialList", substituteDrivingSpecialList)
        }
        if ("NEGridBugDamage".equals(it.code)) {
            coverage.remove("sumInsurd")
        }
        //医保外用药共享保额处理
        for (def item in EdiConstants_2011.SHARE_AMOUNT_LIST) {
            if (item.code == it.code) {
                def share = it.share as Boolean
                if (share) {
                    def amount = new BigDecimal(it.amount?.toString() ?: '0')
                    if (amount <= new BigDecimal("100")) {
                        def parentSuite = biz.find { item.parentCode == it.code }
                        amount = new BigDecimal(parentSuite.amount?.toString())
                    }
                    coverage.put("sumInsurd", amount)
                    coverage.put('coveragePlan', 'A')
                } else {
                    coverage.put('coveragePlan', 'B')
                }
            }
        }
        coverage.put("extendInfos", extendInfos)
        coverages.add(coverage)
    }
    businessPolicy.put("coverages", coverages)

    //充电桩信息
    if (biz.find { "NEChargerDuty".equals(it?.code) } || biz.find { "NEChargerDamage".equals(it?.code) }) {
        if (tempValues?.NEFlag) {
            def chargingPostList = new JSONArray()
            def chargingPileModel = commonAll.getSupplyParam(enquiry, "neChargerModel") ?: config?.NEChargerModel
            def chargingPileCode = commonAll.getSupplyParam(enquiry, "neChargerNo") ?: config?.NEChargerNo
            def chargingAddress = commonAll.getSupplyParam(enquiry, "neChargerAddress") ?: config?.NEChargerAddress
            def chargingPileType = commonAll.getSupplyParam(enquiry, "neChargerType") ?: config?.NEChargerType
            def addressType = commonAll.getSupplyParam(enquiry, "neChargerLocationType") ?: config?.NEChargerLocationType
            def chargingPileUseYears = commonAll.getSupplyParam(enquiry, "neChargerWarranty") ?: config?.NEChargerWarranty
            //将磐石的充电桩类型转换为保司的类型
            if ("3".equals(addressType.toString())) {
                addressType = "2"
            }
            def chargingPost = new JSONObject()
            chargingPost.put("type", chargingPileModel)
            chargingPost.put("code", chargingPileCode)
            chargingPost.put("address", chargingAddress)
            chargingPost.put("kind", chargingPileType)
            chargingPost.put("addressType", addressType)
            if (biz.find { "NEChargerDamage".equals(it?.code) }) {
                chargingPost.put("lossSumLimit", new BigDecimal(biz.find { "NEChargerDamage".equals(it?.code) }?.amount?.toString() ?: "0"))
            }
            if (biz.find { "NEChargerDuty".equals(it?.code) }) {
                chargingPost.put("liabilitySumLimit", new BigDecimal(biz.find { "NEChargerDuty".equals(it?.code) }?.amount?.toString() ?: "0"))
            }
            chargingPostList.add(chargingPost)
            businessPolicy.put("chargingPostList", chargingPostList)
        }
    }
    businessPolicy.put("sellingChannel", config?.sellingChannel ?: "21")
    if (tempValues.definedCarPriceFlag) {
        businessPolicy.put("preminumCaculateQueryCode", tempValues?.preminumCaculateQueryCode)
    }
    if (tempValues['geniusItem.totalDiscount']) {
        businessPolicy.put("preminumCaculateQueryCode", tempValues?.preminumCaculateQueryCode)
        def premiumFloatingItem = new JSONObject()
        premiumFloatingItem.put("independentPriceRate", new BigDecimal(tempValues['geniusItem.totalDiscount'].toString()).setScale(6, BigDecimal.ROUND_HALF_UP))
        businessPolicy.put("premiumFloatingItem", premiumFloatingItem)
    }

    if (config['fleetAgreementNo']) {
        businessPolicy.putAll([
                'fleetAgreementNo' : config['fleetAgreementNo'], // 车队协议号  String  N  车队协议出单，保费计算、预核保、核保时传
                'insurancePlanCode': config['insurancePlanCode'], // 承保方案编码  String    车队协议出单，报价、预核保、核保入参送值
                'agreementType'    : config['agreementType'] ?: '01' // 车队协议类型  String  N  车队协议出单，保费计算、预核保、核保时传
        ])
    }

    // Story#11585
    if (config['isCreateEInvoice']) {
        def insured = enquiry?.insuredPersonList[0]
        def holder = enquiry?.insurePerson
        if (insured && holder && insured?.idCardType?.toString() && holder?.idCardType?.toString()
                && edi_2011_common.getCustomerType(insured.idCardType?.toString()) == "1"
                && edi_2011_common.getCustomerType(holder.idCardType?.toString()) == "1"
        ) {
            businessPolicy['isCreateEInvoice'] = config['isCreateEInvoice']
        }
    }

    def isCheckCarFlag = config['isCheckCarFlag']?.toString()
    if (isCheckCarFlag) {
        businessPolicy['isCheckCarFlag'] = isCheckCarFlag
    }

    // Story#13647
    edi_2011_common.makeThirdPartyInfo(businessPolicy, enquiry, config);
    businessPolicy
}

def makeTrafficPolicy(enquiry, carOwner, tempValues, config) {
    def edi_2011_common = new edi_2011_common()
    def trafficPolicy = new JSONObject()
    def startDateString = enquiry?.baseSuiteInfo?.efcSuiteInfo?.start?.toString()
    def startDate = LocalDateTime.parse(startDateString, DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss'))
    def endDate = startDate.plusYears(1)
    if (startDate.toLocalDate().isLeapYear() && startDate.monthValue == 2 && startDate.dayOfMonth == 29) {
        endDate = startDate.plusYears(1).plusDays(1)
    }

    def dtf = DateTimeFormatter.ofPattern('yyyyMMddHHmmss')
    startDate = startDate.format(dtf)
    endDate = endDate.format(dtf)
    trafficPolicy.put("startDate", tempValues.startDateStringTPYEfc ?: startDate)
    trafficPolicy.put("endDate", tempValues.endDateStringTPYEfc ?: endDate)
    trafficPolicy.put("litigationArbitration", "2")
    trafficPolicy.put("arbitrationOrgName", config?.arbitrationOrgName ?: "北京仲裁委员会")
    trafficPolicy.put("isCreateEpolicy", config?.isCreateEpolicy ?: "1")
    trafficPolicy.put("isSendEpolicySms", config?.isSendEpolicySms ?: "1")
    def vehicleTax = new JSONObject()
    vehicleTax.put("customerName", carOwner.ownerName)
    vehicleTax.put("customerType", carOwner.customerType)
    vehicleTax.put("taxpayerId", carOwner.certNo)
    vehicleTax.put("certificateType", carOwner.certType)
    vehicleTax.put("certificateCode", carOwner.certNo)
    vehicleTax.put("taxType", "3")
    //vehicleTax.put("fuelType",edi_2011_common.getFuelType(tempValues?.fuelType))
    vehicleTax.put("taxVehicleType", edi_2011_common.parseJgVehicleType(enquiry.carInfo.jgVehicleType, enquiry.carInfo.carModelName))
    if ("310000".equals(enquiry?.insArea?.province?.toString())) {
        vehicleTax.put("taxVehicleType", edi_2011_common.getTaxVehicleType(enquiry, tempValues))
        //新能源车辆应当以车船税标志“M”上传
//        if (tempValues?.NEFlag) {
//            vehicleTax.put("taxType", "2")
//        }
    }
    vehicleTax.put("licenceDate", enquiry.carInfo.firstRegDate)
    trafficPolicy.put("vehicleTax", vehicleTax)
    trafficPolicy.put("sellingChannel", config?.sellingChannel ?: "21")
    if (config['fleetAgreementNo']) {
        trafficPolicy.putAll([
                'fleetAgreementNo' : config['fleetAgreementNo'], // 车队协议号  String  N  车队协议出单，保费计算、预核保、核保时传
                'insurancePlanCode': config['insurancePlanCode'], // 承保方案编码  String    车队协议出单，报价、预核保、核保入参送值
                'agreementType'    : config['agreementType'] ?: '01' // 车队协议类型  String  N  车队协议出单，保费计算、预核保、核保时传
        ])
    }

    // Story#11585
    if (config['isCreateEInvoice']) {
        def insured = enquiry?.insuredPersonList[0]
        def holder = enquiry?.insurePerson
        if (insured && holder && insured?.idCardType?.toString() && holder?.idCardType?.toString()
                && edi_2011_common.getCustomerType(insured?.idCardType?.toString()) == "1"
                && edi_2011_common.getCustomerType(holder?.idCardType?.toString()) == "1"
        ) {
            trafficPolicy['isCreateEInvoice'] = config['isCreateEInvoice']
        }
    }

    def isCheckCarFlag = config['isCheckCarFlag']?.toString()
    if (isCheckCarFlag) {
        trafficPolicy['isCheckCarFlag'] = isCheckCarFlag
    }

    // Story#13647
    edi_2011_common.makeThirdPartyInfo(trafficPolicy, enquiry, config)
    trafficPolicy
}

//报价交强险回写处理
def chargeEfcSuit(enquiry, trafficPolicy, BigDecimal totalCharge) {
    enquiry?.baseSuiteInfo?.efcSuiteInfo?.orgCharge = new BigDecimal(trafficPolicy?.standardPremium?.toString() ?: trafficPolicy?.premium?.toString() ?: "0")
    enquiry?.baseSuiteInfo?.efcSuiteInfo?.discountCharge = new BigDecimal(trafficPolicy?.premium?.toString() ?: "0")
    enquiry?.baseSuiteInfo?.efcSuiteInfo?.discountRate = new BigDecimal(trafficPolicy?.floatingRate?.toString() ?: "1")
    enquiry?.baseSuiteInfo?.efcSuiteInfo?.amount = new BigDecimal("200000")
    enquiry?.SQ?.efcCharge = new BigDecimal(trafficPolicy?.premium?.toString() ?: "0")
    enquiry?.baseSuiteInfo?.efcSuiteInfo?.start = TPYDateTimeToBC(trafficPolicy?.startDate?.toString())
    enquiry?.baseSuiteInfo?.efcSuiteInfo?.end = TPYDateTimeToBC(trafficPolicy?.endDate?.toString())
    trafficPolicy.insuranceQueryCode
    // 当年应缴
    def amount = (trafficPolicy.vehicleTax.amount ?: '0').toBigDecimal()
    // 往年补缴
    def backAmount = (trafficPolicy.vehicleTax.backAmount ?: '0').toBigDecimal()
    // 滞纳金
    def lateFee = (trafficPolicy.vehicleTax.lateFee ?: '0').toBigDecimal()
    enquiry?.baseSuiteInfo?.taxSuiteInfo?.charge = amount
    enquiry?.baseSuiteInfo?.taxSuiteInfo?.delayCharge = backAmount + lateFee
    enquiry?.SQ?.taxCharge = amount
    enquiry?.baseSuiteInfo?.taxSuiteInfo?.start = TPYDateToBC(trafficPolicy?.vehicleTax?.taxStartDate)
    enquiry?.baseSuiteInfo?.taxSuiteInfo?.end = TPYDateToBC(trafficPolicy?.vehicleTax?.taxEndDate)
    totalCharge = totalCharge.add(new BigDecimal(trafficPolicy?.premium?.toString() ?: "0"))
    totalCharge = totalCharge.add(amount + backAmount + lateFee)
    //SQ赋值 总保费
    def decimal = new BigDecimal(enquiry?.SQ?.totalCharge?.toString() ?: "0")
    enquiry?.SQ?.totalCharge = decimal.add(totalCharge)
    //支付平台影像上传
    if (!enquiry?.SQ?.misc?.branch) {
        enquiry?.SQ?.misc?.branch = trafficPolicy?.branch
    }
}

//报价商业险回写处理
def chargeBizSuit(enquiry, businessPolicy, tempValues) {
    def floatingRate = new BigDecimal(businessPolicy?.floatingRate?.toString()
            ?: businessPolicy?.premiumFloatingItem?.independentPriceRate?.toString()
            ?: businessPolicy?.coverages?.get(0)?.floatingRate?.toString()
            ?: "1").setScale(6, BigDecimal.ROUND_HALF_UP)
    tempValues?.floatingRate = floatingRate
    def premium = new BigDecimal(businessPolicy?.premium?.toString() ?: "0").setScale(2, BigDecimal.ROUND_HALF_UP)
    def standardPremium = premium.divide(floatingRate, 2, BigDecimal.ROUND_HALF_UP)
    enquiry?.baseSuiteInfo?.bizSuiteInfo?.orgCharge = standardPremium
    enquiry?.baseSuiteInfo?.bizSuiteInfo?.discountRate = floatingRate
    enquiry?.baseSuiteInfo?.bizSuiteInfo?.discountCharge = premium
    enquiry?.SQ?.bizCharge = premium
    enquiry?.baseSuiteInfo?.bizSuiteInfo?.start = TPYDateTimeToBC(businessPolicy?.startDate)
    enquiry?.baseSuiteInfo?.bizSuiteInfo?.end = TPYDateTimeToBC(businessPolicy?.endDate)
    def decimal = new BigDecimal(enquiry?.SQ?.totalCharge?.toString() ?: "0")
    //SQ赋值 总保费
    enquiry?.SQ?.totalCharge = decimal.add(premium)
    def edi_2011_common = new edi_2011_common()
    def suites = enquiry?.baseSuiteInfo?.bizSuiteInfo?.suites as List
    //适配统一定价 清除原始的商业险结构体  保司返回什么拿什么
    suites.clear()
    def policyCoverages = businessPolicy?.coverages as List
    policyCoverages.each {
        def policyList = ""
        if (tempValues?.NEFlag == "1") {
            policyList = edi_2011_common.taiBaoSuitesCode2BwCodeList(it.coverageCode.toString().replace("NEWENERGY", ""))
        } else {
            policyList = edi_2011_common.taiBaoSuitesCode2BwCodeList(it.coverageCode.toString())
        }
        if (policyList) {
            def premiumSuit = new BigDecimal(it?.premium?.toString() ?: "0").setScale(2, BigDecimal.ROUND_HALF_UP)
            def orgChargeSuit = premiumSuit.divide(floatingRate, 2, BigDecimal.ROUND_HALF_UP)
            def suit = [:]
            suit.put("code", policyList.get(0))
            suit.put("name", policyList.get(1))
            suit.put("amount", new BigDecimal(it?.sumInsurd?.toString() ?: "0"))
            suit.put("orgCharge", orgChargeSuit)
            suit.put("discountRate", floatingRate)
            suit.put("discountCharge", premiumSuit)
            if (["THIRDPARTYLEGALHOLIDAYLIMITCLAUSE", "DAMAGELOSSCOVERAGE"].contains(it.coverageCode?.toString())) {
                suit.put("amount", new BigDecimal(it?.sumInsurd?.toString() ?: "1"))
            }
            if (["ANCVehicleDamage", "ANCThirdParty", "ANCDriver", "ANCPassenger"].contains(policyList.get(0))) {
                suit.put("amount", new BigDecimal(it?.extendInfos?.deductibleRate?.toString() ?: "1"))
            }
            if (["RoadsideService"].contains(policyList.get(0))) {
                suit.put("amount", new BigDecimal(it?.extendInfos?.serviceTimes?.toString() ?: "2"))
            }
            if (["DesignatedDriving"].contains(policyList.get(0))) {
                def substituteDrivingSpecial = (it as JSONObject).getJSONArray('substituteDrivingSpecialList')[0] as JSONObject
                def designatedDrivingMile = substituteDrivingSpecial.getString('maxKiloNum')
                suit['designatedDrivingMile'] = designatedDrivingMile
                enquiry?.SQ?.designatedDrivingMile = designatedDrivingMile
                suit.put("amount", new BigDecimal(it?.substituteDrivingSpecialList[0]?.serviceTimes?.toString() ?: "1"))
            }
            if ("VehicleInspection".equals(policyList.get(0))) {
                def vehicleSafetyDetectionSpecial = (it as JSONObject).getJSONArray('vehicleSafetyDetectionSpecialList')[0] as JSONObject
                def vehicleInspectionLevel = vehicleSafetyDetectionSpecial.getString('serviceGrade')
                suit['vehicleInspectionLevel'] = vehicleInspectionLevel
                enquiry?.SQ?.vehicleInspectionLevel = it?.vehicleSafetyDetectionSpecialList[0]?.serviceGrade?.toString()
                suit.put("amount", new BigDecimal(it?.vehicleSafetyDetectionSpecialList[0]?.serviceTimes?.toString() ?: "1"))
            }
            if ("SendForInspection".equals(policyList.get(0))) {
                def extendInfos = (it as JSONObject).getJSONObject('extendInfos')
                def sendForInspectionLevel = extendInfos.getString('serviceGrade')
                suit['sendForInspectionLevel'] = sendForInspectionLevel
                enquiry?.SQ?.sendForInspectionLevel = sendForInspectionLevel
                suit.put("amount", new BigDecimal(it?.sumInsurd?.toString() ?: "1"))
            }
            if ("NEGridBugDamage".equals(policyList.get(0))) {
                suit.put("amount", new BigDecimal(it?.sumInsurd?.toString() ?: "1"))
            }
            if (EdiConstants_2011.SHARE_AMOUNT_CODE_LIST.contains(policyList.get(0))) {
                def coveragePlan = it?.coveragePlan as String
                suit.put('share', coveragePlan == 'A')
            }
            suites.add(suit)
        }
    }
    //支付平台 影像上传
    if (!enquiry?.SQ?.misc?.branch) {
        enquiry?.SQ?.misc?.branch = businessPolicy?.branch
    }
}

def TPYDateTimeToBC(TPYDate) {
    if (!TPYDate) {
        return null
    }
    DateTimeFormatter dfTPY = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
    DateTimeFormatter dfBC = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    def TPYDateLocal = LocalDateTime.parse(TPYDate, dfTPY)
    dfBC.format(TPYDateLocal)
}

def TPYDateToBC(TPYDate) {
    DateTimeFormatter dfTPY = DateTimeFormatter.ofPattern("yyyyMMdd")
    DateTimeFormatter dfBC = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    def TPYDateLocal = LocalDate.parse(TPYDate.substring(0, 8), dfTPY)
    dfBC.format(TPYDateLocal)
}

def ruleInfo(autoTask, tempValues, enquiry) {
    if (!tempValues?.askChargeFlag) {
        tempValues?.askChargeFlag = "1"
        def platformBack = [(PlatformKey.selfRate): tempValues?.premiumFloatingItem]
        def definition = enquiry?.definition
        definition.put("ruleItem.isOverThreshold", tempValues?.isOverThreshold)
        definition.put(PlatformKey.selfRate, tempValues?.premiumFloatingItem)
        platformBack['definition'] = definition
        tempValues[PlatformKey.platformBack] = platformBack
        RuleUtil.doRuleInfo(autoTask, enquiry, tempValues, '', '', '')
        def ruleInfo = tempValues['ruleInfo']
        if (ruleInfo && ruleInfo['geniusItem.totalDiscount']) {
            tempValues['geniusItem.totalDiscount'] = ruleInfo['geniusItem.totalDiscount']
            throw new InsReturnException(InsReturnException.AllowRepeat, '调用规则修改折扣，重新报价')
        }
    } else {
        if (tempValues?.premiumFloatingItem) {
            def definition = enquiry?.definition
            definition.put(PlatformKey.selfRate, tempValues?.premiumFloatingItem)
        }
    }
}

def makeAccidentPolicyList(enquiry) {
    def accidentPolicyList = new JSONArray()
    def accidentPolicy = new JSONObject()
    accidentPolicy.put("accidentPlan", enquiry?.SQ?.nonMotor?.productCode)
    accidentPolicy.put("purchaseCount", enquiry?.SQ?.nonMotor?.count)
    if (enquiry?.SQ?.nonMotor?.discountCharge) {
        accidentPolicy.put("accAmount", enquiry?.SQ?.nonMotor?.discountCharge)
    }
    accidentPolicyList.add(accidentPolicy)
    accidentPolicyList
}

def chargeNonMotor(enquiry, accidentPolicy, totalCharge) {
    def accAmount = new BigDecimal(accidentPolicy?.accAmount?.toString()).setScale(2, BigDecimal.ROUND_HALF_UP)
    enquiry?.SQ?.nonMotor?.discountCharge = accAmount
    enquiry?.SQ?.nonMotor?.productCode = accidentPolicy?.accidentPlan
    enquiry?.SQ?.nonMotor?.count = BigDecimal.ONE
    //SQ赋值 总保费
    def decimal = new BigDecimal(enquiry?.SQ?.totalCharge?.toString() ?: "0")
    def sumAmount = decimal.add(accAmount)
    enquiry?.SQ?.totalCharge = sumAmount

}

//C-用户自主投保
//B-销售引导投保（预留，暂不启用）
def setServiceFlag(config, json) {
    if ("1" == config?.partnerClientType?.toString()) {
        json.put("serviceFlag", "C")
    }
    if ("2" == config?.partnerClientType?.toString()) {
        json.put("serviceFlag", "B")
    }
    json
}

def makeInsureParam(policy, config, enquiry, litigationArbitration, cityShenZhen, comUnderWritingReq) {
    def common_all = new common_all()
    policy.put("litigationArbitration", litigationArbitration)
    if ("2".equals(litigationArbitration)) {
        policy.put("arbitrationOrgName", config?.arbitrationOrgName ?: "北京仲裁委员会")
    }
    policy.put("isCreateEpolicy", config?.isCreateEpolicy ?: "1")
    policy.put("isSendEpolicySms", config?.isSendEpolicySms ?: "1")
    policy.put("sellingChannel", config?.sellingChannel ?: "21")
    if (cityShenZhen.equals(enquiry?.insArea?.city?.toString())) {
        policy?.dualRecordSalesName = common_all.getSupplyParam(enquiry, "doubleInputSaleName") ?: config?.dualRecordSalesName
        policy?.dualRecordSalesIdCard = common_all.getSupplyParam(enquiry, "doubleInputSaleIDCardNo") ?: config?.dualRecordSalesIdCard
    }
    //网销业务必传
    if (config?.busiEtranceFlag) {
        policy.put("address", comUnderWritingReq.holder?.address)
        policy.put("contactName", comUnderWritingReq.holder?.holderName)
        policy.put("motel", comUnderWritingReq.holder?.phoneNumber)
    }
    policy.remove("specialTermItems")
}

def makeAdvanceDeductionCharge(enquiry, config, tempValues) {
    def edi_2011_common = new edi_2011_common()
    if (!tempValues.trafficPolicy) {
        throw new InsReturnException("太保垫资业务异常：未投保交强险")
    }
    if (!tempValues?.businessPolicy?.coverages?.find { it?.coverageCode?.toString()?.contains("DAMAGELOSSCOVERAGE") }) {
        throw new InsReturnException("太保垫资业务异常：未投保车损险")
    }
    //垫资金额
    def presentPremium = ""
    //商业险总保费-垫资金额
    def realBizMoney = ""
    def vehicleDamageMoney = tempValues?.businessPolicy?.coverages?.find { it?.coverageCode?.toString()?.contains("DAMAGELOSSCOVERAGE") }?.premium
    def bizMoney = enquiry?.SQ?.bizCharge as BigDecimal
    if (config?.fixedPremium) {
        def realMoney = new BigDecimal(config?.fixedPremium?.toString()).setScale(2, BigDecimal.ROUND_HALF_UP)
        def efcMoney = enquiry?.SQ?.efcCharge as BigDecimal
        realBizMoney = realMoney?.subtract(efcMoney)?.setScale(2, BigDecimal.ROUND_HALF_UP)
        //用户建议实付金额大于等于总金额
        if (realBizMoney.compareTo(bizMoney) >= 0) {
            throw new InsReturnException("太保垫资业务异常：总金额小于建议金额")
        }
        presentPremium = bizMoney.subtract(realBizMoney).setScale(2, BigDecimal.ROUND_HALF_UP)
    }
    if (config?.fixeAdvanceDeductionPremium) {
        presentPremium = new BigDecimal(config?.fixeAdvanceDeductionPremium?.toString()).setScale(2, BigDecimal.ROUND_HALF_UP)
        realBizMoney = bizMoney.subtract(presentPremium)?.setScale(2, BigDecimal.ROUND_HALF_UP)
    }
    //垫资金额大于等于车损险保费
    if (presentPremium.compareTo(vehicleDamageMoney) >= 0) {
        throw new InsReturnException("太保垫资业务异常：垫资金额大于等于车损险保费")
    }

    def presentPerson = new JSONObject()
    //投保人实缴的金额
    presentPerson.put("firstPresentPremium", realBizMoney)
    //垫资金额
    presentPerson.put("secondPresentPremium", presentPremium)
    presentPerson.put("certificateType", edi_2011_common.getCardType(enquiry, "8"))
    presentPerson.put("certificateCode", "91110116051383283J")
    presentPerson.put("customerType", edi_2011_common.getCustomerType("8"))
    presentPerson.put("name", "北京创联安吉数字科技有限公司")
    presentPerson.put("ifSpecialTicket", "1")
    //最近收到业务反馈，拆分发票的时候增值税专票开出来的变成了普票。
    //经过增值税系统排查，是电子发票的优先级高于增值税发票类型。
    //统一跟各位老师说一下，如有拆分专票需求，赠送部分开票人节点如果送了增值税发票类型，是否电子发票就送0。
    presentPerson.put("isEInvoice", "0")
    presentPerson.put("email", "")
    presentPerson.put("telephone", "010-60670366")
    presentPerson.put("customerKind", "0")
    tempValues?.businessPolicy?.presentPerson = presentPerson
    tempValues?.businessPolicy?.outerActivityCode = config?.outerActivityCode ?: "LX_MCSBXBYGDBF_2022"
    tempValues?.businessPolicy?.coverages?.find { it?.coverageCode?.toString()?.contains("DAMAGELOSSCOVERAGE") }?.presentFlag = "1"
    //垫资金额
    tempValues?.businessPolicy?.coverages?.find { it?.coverageCode?.toString()?.contains("DAMAGELOSSCOVERAGE") }?.presentPremium = presentPremium
    enquiry?.SQ?.advanceDeductionCharge = presentPremium
}

def checkDate(keywords, responseMsg) {
    Pattern p = Pattern.compile(keywords)
    Matcher m = p.matcher(responseMsg)
    def localDateTime = ""
    if (m.find()) {
        def hour = m.group(4)
        def minute = m.group(5)
        def second = 0
        if ("24" == hour) {
            hour = "23"
            minute = "59"
            second = 59
        }
        localDateTime = LocalDateTime.of(m.group(1) as Integer, m.group(2) as Integer, m.group(3) as Integer,
                hour as Integer, minute as Integer, second)
    }

    localDateTime
}

def checkPreQuoteDate(localDateTime, config) {
    if (localDateTime.getHour() > 0 || localDateTime.getMinute() > 0) {
        localDateTime = localDateTime.plusDays(1)
    }
    def between = ChronoUnit.DAYS.between(LocalDate.now().plusDays(1), LocalDate.of(localDateTime.plusYears(1).getYear(), localDateTime.getMonth(), localDateTime.getDayOfMonth()))
    def preDate = Long.valueOf(config?.preQuoteDate?.toString() ?: "55")
    if (between >= preDate) {
        return false
    }
    return true
}

def processCustomer(enquiry, customer) {
    def supplyParam = enquiry['supplyParam'] as List
    def phoneHoldName = findFormSupplyParam(supplyParam, 'phoneHoldName') ?: ''
    def phoneHoldCertificateNo = findFormSupplyParam(supplyParam, 'phoneHoldCertificateNo') ?: ''
    customer.putAll([
            'phoneHolderName'           : phoneHoldName, // 手机实际持有人姓名  String  E  上海实名认证
            'phoneHolderCertificatecode': phoneHoldCertificateNo, // 手机实际持有人身份证号码  String  E  上海实名认证
    ])

    if (customer['customerType'] == '2') {
        def liaisonName = findFormSupplyParam(supplyParam, 'liaisonName') ?: ''
        def liaisonIDCardNo = findFormSupplyParam(supplyParam, 'liaisonIDCardNo') ?: ''
        customer.putAll([
                'custConsignorName'     : liaisonName, // 客户经办人(委托人)姓名  String  E  上海实名认证 团体客户必送
                'custConsignorCardno'   : liaisonIDCardNo, // 客户经办人(委托人) 证件号码 String E 上海实名认证 团体客户必送
                'custConsignorCardtype' : '1', // 客户经办人(委托人) 证件类型 String E 上海实名认证 团体客户必送
                'custConsignorTelephone': findFormSupplyParam(supplyParam, 'insuredMobile'), // 企业业务经办人手机号  String  E  上海实名认证 团体客户必送
                'custConsignorEmail'    : findFormSupplyParam(supplyParam, 'insuredEmail') // 企业业务经办人邮箱  String  E  上海实名认证
        ])
    }
}

String getCertificateValidity(def enquiry, String itemKey) {
    def idCardValidDate = findFormSupplyParam(enquiry['supplyParam'], itemKey)
    if (idCardValidDate) {
        if (idCardValidDate == '长期') {
            idCardValidDate = '9999-12-31 00:00:00'
        } else {
            idCardValidDate = idCardValidDate + ' 00:00:00'
        }
    }
    return idCardValidDate
}

def setSaleInfo(def enquiry, def config, def vehicle) {
    // 提前返回,减少嵌套
    if (!shouldSetSaleInfo(enquiry)) {
        return
    }
    def city = enquiry.insArea?.city?.toString()
    def edi_2011_common = new edi_2011_common()
    // 从不同来源获取销售信息
    def saleInfo = edi_2011_common.getSaleInfo(enquiry.supplyParam)

    def saleData = [
            'saleCompany': saleInfo?.newCarSaleComName ?: config?.newCarSaleComName,
            'saleArea'   : saleInfo?.newCarSaleComCityCode ?: city ?: config?.newCarSaleComCityCode ?: '440100',
            'isSaleBy4S' : saleInfo?.isSaleBy4S ?: config?.isSaleBy4S ?: '0'
    ]

    vehicle.putAll(saleData)
}

private boolean shouldSetSaleInfo(enquiry) {
    return enquiry?.carInfo?.isNew &&
            ["440000"].contains(enquiry?.insArea?.province) &&
            "440300" != enquiry?.insArea?.city
}
