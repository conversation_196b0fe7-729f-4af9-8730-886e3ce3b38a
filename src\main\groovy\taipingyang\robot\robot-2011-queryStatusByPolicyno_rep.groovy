package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.task.AutoTask
import com.cheche365.bc.tools.StringUtil
import taipingyang.robot.module.Robot2011Util

AutoTask autoTask = autoTask
def TbUtil = new common_2011()


Enquiry entity = (Enquiry) autoTask?.taskEntity;

def queryQuotationPolicyResult = autoTask.backRoot;
queryQuotationPolicyResult = Robot2011Util.decodeBody(autoTask.tempValues, queryQuotationPolicyResult)
JSONObject queryQuotationPolicyResultObj = JSON.parseObject((String) queryQuotationPolicyResult);
JSONArray jsonArray = queryQuotationPolicyResultObj.getJSONArray("result");
for (int i = 0; i < jsonArray.size(); i++) {
    String productType = jsonArray.getJSONObject(i).getString("productType")
    //商业险
    if ("11024400".equals(productType)) {
        entity.bizProposeNum = jsonArray.getJSONObject(i).getString("insuredNo")
        entity.bizPolicyCode = jsonArray.getJSONObject(i).getString("policyNo")
        autoTask.tempValues.put("ExistBI", true);
    } else if ("11022400".equals(productType)) {//交强险
        entity.efcProposeNum = jsonArray.getJSONObject(i).getString("insuredNo")
        entity.efcPolicyCode = jsonArray.getJSONObject(i).getString("policyNo")
        autoTask.tempValues.put("ExistCI", true);
    }
}
JSONObject queryPolicyParam = JSON.parse(TbUtil.getBaseParam());
String quotationNo = TbUtil.getFromJson(queryQuotationPolicyResultObj, "result.[0].quotationNo").toString();
queryPolicyParam.getJSONObject("redata").put("quotationNo", quotationNo);
autoTask.tempValues.put("queryPolicyParam", StringUtil.chinesetoUnicode(queryPolicyParam.toJSONString()));

